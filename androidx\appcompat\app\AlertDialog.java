package androidx.appcompat.app.AlertDialog;
import android.content.DialogInterface;
import androidx.appcompat.app.AppCompatDialog;
import android.content.Context;
import androidx.appcompat.app.AlertController;
import android.app.Dialog;
import android.view.Window;
import android.content.DialogInterface$OnCancelListener;
import android.util.TypedValue;
import android.content.res.Resources$Theme;
import com.taobao.taobao.R$attr;
import android.widget.Button;
import android.widget.ListView;
import android.os.Bundle;
import android.view.KeyEvent;
import java.lang.CharSequence;
import android.content.DialogInterface$OnClickListener;
import android.os.Message;
import android.graphics.drawable.Drawable;
import android.view.View;

public class AlertDialog extends AppCompatDialog implements DialogInterface	// class@000552 from classes.dex
{
    public final AlertController mAlert;
    public static final int LAYOUT_HINT_NONE = 0;
    public static final int LAYOUT_HINT_SIDE = 1;

    public void AlertDialog(Context p0){
       super(p0, 0);
    }
    public void AlertDialog(Context p0,int p1){
       super(p0, AlertDialog.resolveDialogTheme(p0, p1));
       this.mAlert = new AlertController(this.getContext(), this, this.getWindow());
    }
    public void AlertDialog(Context p0,boolean p1,DialogInterface$OnCancelListener p2){
       super(p0, 0);
       this.setCancelable(p1);
       this.setOnCancelListener(p2);
    }
    public static int resolveDialogTheme(Context p0,int p1){
       if ((((p1 >> 24) & 0x00ff)) >= 1) {
          return p1;
       }
       TypedValue typedValue = new TypedValue();
       p0.getTheme().resolveAttribute(R$attr.alertDialogTheme, typedValue, true);
       return typedValue.resourceId;
    }
    public Button getButton(int p0){
       return this.mAlert.getButton(p0);
    }
    public ListView getListView(){
       return this.mAlert.getListView();
    }
    public void onCreate(Bundle p0){
       super.onCreate(p0);
       this.mAlert.installContent();
    }
    public boolean onKeyDown(int p0,KeyEvent p1){
       if (this.mAlert.onKeyDown(p0, p1)) {
          return true;
       }
       return super.onKeyDown(p0, p1);
    }
    public boolean onKeyUp(int p0,KeyEvent p1){
       if (this.mAlert.onKeyUp(p0, p1)) {
          return true;
       }
       return super.onKeyUp(p0, p1);
    }
    public void setButton(int p0,CharSequence p1,DialogInterface$OnClickListener p2){
       this.mAlert.setButton(p0, p1, p2, null, null);
    }
    public void setButton(int p0,CharSequence p1,Drawable p2,DialogInterface$OnClickListener p3){
       this.mAlert.setButton(p0, p1, p3, null, p2);
    }
    public void setButton(int p0,CharSequence p1,Message p2){
       this.mAlert.setButton(p0, p1, null, p2, null);
    }
    public void setButtonPanelLayoutHint(int p0){
       this.mAlert.setButtonPanelLayoutHint(p0);
    }
    public void setCustomTitle(View p0){
       this.mAlert.setCustomTitle(p0);
    }
    public void setIcon(int p0){
       this.mAlert.setIcon(p0);
    }
    public void setIcon(Drawable p0){
       this.mAlert.setIcon(p0);
    }
    public void setIconAttribute(int p0){
       TypedValue typedValue = new TypedValue();
       this.getContext().getTheme().resolveAttribute(p0, typedValue, true);
       this.mAlert.setIcon(typedValue.resourceId);
    }
    public void setMessage(CharSequence p0){
       this.mAlert.setMessage(p0);
    }
    public void setTitle(CharSequence p0){
       super.setTitle(p0);
       this.mAlert.setTitle(p0);
    }
    public void setView(View p0){
       this.mAlert.setView(p0);
    }
    public void setView(View p0,int p1,int p2,int p3,int p4){
       this.mAlert.setView(p0, p1, p2, p3, p4);
    }
}
