package kotlinx.serialization.descriptors.SerialDescriptorImpl$_hashCode$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.descriptors.SerialDescriptorImpl;
import java.lang.Integer;
import kotlinx.serialization.descriptors.a;
import tb.xy30;
import java.lang.Object;

public final class SerialDescriptorImpl$_hashCode$2 extends Lambda implements d1a	// class@000729 from classes11.dex
{
    public final SerialDescriptorImpl this$0;

    public void SerialDescriptorImpl$_hashCode$2(SerialDescriptorImpl p0){
       this.this$0 = p0;
       super(0);
    }
    public final Integer invoke(){
       SerialDescriptorImpl$_hashCode$2 tthis$0 = this.this$0;
       return Integer.valueOf(xy30.a(tthis$0, SerialDescriptorImpl.i(tthis$0)));
    }
    public Object invoke(){
       return this.invoke();
    }
}
