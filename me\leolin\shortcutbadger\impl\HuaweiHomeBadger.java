package me.leolin.shortcutbadger.impl.HuaweiHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.os.Bundle;
import android.os.BaseBundle;
import android.content.ContentResolver;
import android.net.Uri;

public class HuaweiHomeBadger implements po1	// class@000761 from classes11.dex
{

    public void HuaweiHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"com.huawei.android.launcher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       Bundle uBundle = new Bundle();
       uBundle.putString("package", p0.getPackageName());
       uBundle.putString("class", p1.getClassName());
       uBundle.putInt("badgenumber", p2);
       p0.getContentResolver().call(Uri.parse("content://com.huawei.android.launcher.settings/badge/"), "change_badge", null, uBundle);
    }
}
