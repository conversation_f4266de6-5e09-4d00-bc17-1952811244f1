package kotlinx.coroutines.channels.BroadcastKt$broadcast$2;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;

public final class BroadcastKt$broadcast$2 extends SuspendLambda implements u1a	// class@0004bd from classes11.dex
{
    public final ReceiveChannel $channel;
    private Object L$0;
    public Object L$1;
    public int label;

    public void BroadcastKt$broadcast$2(ReceiveChannel p0,ar4 p1){
       this.$channel = p0;
       super(2, p1);
    }
    public final ar4 create(Object p0,ar4 p1){
       BroadcastKt$broadcast$2 uobroadcast$ = new BroadcastKt$broadcast$2(this.$channel, p1);
       uobroadcast$.L$0 = p0;
       return uobroadcast$;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       BroadcastKt$broadcast$2 tlabel;
       BroadcastKt$broadcast$2 tL$0;
       Object obj1;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel == 2) {
                tlabel = this.L$1;
                tL$0 = this.L$0;
                b.b(p0);
             label_0019 :
                p0 = tL$0;
             }else {
                throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
             }
          }else {
             tlabel = this.L$1;
             tL$0 = this.L$0;
             b.b(p0);
          label_004c :
             if (p0.booleanValue()) {
                this.L$0 = tL$0;
                this.L$1 = tlabel;
                this.label = 2;
                if (tL$0.d(tlabel.next(), this) == obj) {
                   return obj;
                }else {
                   goto label_0019 ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          p0 = this.L$0;
          ChannelIterator uChannelIter = this.$channel.iterator();
       }
       this.L$0 = p0;
       this.L$1 = tlabel;
       this.label = 1;
       if ((obj1 = tlabel.a(this)) == obj) {
          return obj;
       }else {
          tL$0 = p0;
          p0 = obj1;
          goto label_004c ;
       }
    }
}
