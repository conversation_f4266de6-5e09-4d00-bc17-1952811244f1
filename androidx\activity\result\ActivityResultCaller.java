package androidx.activity.result.ActivityResultCaller;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.ActivityResultRegistry;

public interface abstract ActivityResultCaller	// class@0004ac from classes.dex
{

    ActivityResultLauncher registerForActivityResult(ActivityResultContract p0,ActivityResultCallback p1);
    ActivityResultLauncher registerForActivityResult(ActivityResultContract p0,ActivityResultRegistry p1,ActivityResultCallback p2);
}
