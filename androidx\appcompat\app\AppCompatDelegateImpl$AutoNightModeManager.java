package androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager;
import androidx.appcompat.app.AppCompatDelegateImpl;
import java.lang.Object;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager$1;
import android.content.Intent;

public abstract class AppCompatDelegateImpl$AutoNightModeManager	// class@000571 from classes.dex
{
    private BroadcastReceiver mReceiver;
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$AutoNightModeManager(AppCompatDelegateImpl p0){
       this.this$0 = p0;
       super();
    }
    public void cleanup(){
       AppCompatDelegateImpl$AutoNightModeManager tmReceiver;
       if ((tmReceiver = this.mReceiver) != null) {
          try{
             this.this$0.mContext.unregisterReceiver(tmReceiver);
             this.mReceiver = null;
          }catch(java.lang.IllegalArgumentException e0){
          }
       }
       return;
    }
    public abstract IntentFilter createIntentFilterForBroadcastReceiver();
    public abstract int getApplyableNightMode();
    public boolean isListening(){
       boolean b = (this.mReceiver != null)? true: false;
       return b;
    }
    public abstract void onChange();
    public void setup(){
       IntentFilter intentFilter;
       this.cleanup();
       if ((intentFilter = this.createIntentFilterForBroadcastReceiver()) != null && intentFilter.countActions()) {
          if (this.mReceiver == null) {
             this.mReceiver = new AppCompatDelegateImpl$AutoNightModeManager$1(this);
          }
          this.this$0.mContext.registerReceiver(this.mReceiver, intentFilter);
       }
       return;
    }
}
