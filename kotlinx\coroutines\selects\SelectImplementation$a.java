package kotlinx.coroutines.selects.SelectImplementation$a;
import kotlinx.coroutines.selects.SelectImplementation;
import java.lang.Object;
import tb.w1a;
import tb.k9p;
import tb.g1a;
import tb.v8p;
import kotlin.coroutines.d;
import java.lang.Throwable;
import tb.rr7;
import tb.ar4;
import tb.u1r;
import kotlinx.coroutines.selects.SelectKt;
import java.lang.String;
import tb.ckf;
import tb.u1a;
import tb.dv6;

public final class SelectImplementation$a	// class@0006b1 from classes11.dex
{
    public final Object a;
    public final w1a b;
    public final w1a c;
    public final Object d;
    public final Object e;
    public final w1a f;
    public Object g;
    public int h;
    public final SelectImplementation i;

    public void SelectImplementation$a(SelectImplementation p0,Object p1,w1a p2,w1a p3,Object p4,Object p5,w1a p6){
       super();
       this.i = p0;
       this.a = p1;
       this.b = p2;
       this.c = p3;
       this.d = p4;
       this.e = p5;
       this.f = p6;
       this.h = -1;
    }
    public final g1a a(k9p p0,Object p1){
       SelectImplementation$a tf;
       g1a og1a = ((tf = this.f) != null)? tf.invoke(p0, this.d, p1): null;
       return og1a;
    }
    public final void b(){
       SelectImplementation$a tg = this.g;
       Throwable throwable = null;
       if (tg instanceof v8p) {
          tg.o(this.h, throwable, this.i.getContext());
       }else if(tg instanceof rr7){
          throwable = tg;
       }
       if (throwable != null) {
          throwable.dispose();
       }
       return;
    }
    public final Object c(Object p0,ar4 p1){
       SelectImplementation$a te = this.e;
       if (this.d == SelectKt.i()) {
          ckf.e(te, "null cannot be cast to non-null type kotlin.coroutines.SuspendFunction0<R of kotlinx.coroutines.selects.SelectImplementation>");
          return te.invoke(p1);
       }else {
          ckf.e(te, "null cannot be cast to non-null type kotlin.coroutines.SuspendFunction1<kotlin.Any?, R of kotlinx.coroutines.selects.SelectImplementation>");
          return te.invoke(p0, p1);
       }
    }
    public final Object d(Object p0){
       return this.c.invoke(this.a, this.d, p0);
    }
    public final boolean e(SelectImplementation p0){
       this.b.invoke(this.a, p0, this.d);
       boolean b = (SelectImplementation.h(p0) == SelectKt.d())? true: false;
       return b;
    }
}
