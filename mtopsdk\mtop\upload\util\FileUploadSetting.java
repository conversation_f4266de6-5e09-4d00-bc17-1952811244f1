package mtopsdk.mtop.upload.util.FileUploadSetting;
import tb.t2o;
import android.util.SparseArray;
import mtopsdk.mtop.domain.EnvModeEnum;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import mtopsdk.common.util.RemoteConfig;
import mtopsdk.common.util.StringUtils;
import java.lang.Integer;
import mtopsdk.xstate.NetworkClassEnum;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import java.lang.Boolean;
import java.util.Set;

public final class FileUploadSetting	// class@000817 from classes11.dex
{
    public static IpChange $ipChange;
    private static int DEFAULT_SEGMENT_RETRY_TIMES;
    private static int DEFAULT_SEGMENT_SIZE;
    private static int DEFAULT_UPLOAD_THREAD_NUMS;
    private static final int MAX_SEGMENT_RETRY_TIMES;
    private static final int MAX_UPLOAD_THREAD_NUMS;
    private static final int MIN_SEGMENT_RETRY_TIMES;
    private static final int MIN_UPLOAD_THREAD_NUMS;
    private static final String TAG;
    public static final SparseArray uploadDomainMap;

    static {
       t2o.a(0x2590001b);
       FileUploadSetting.DEFAULT_SEGMENT_RETRY_TIMES = 2;
       FileUploadSetting.DEFAULT_UPLOAD_THREAD_NUMS = 2;
       FileUploadSetting.DEFAULT_SEGMENT_SIZE = 0x20000;
       SparseArray sparseArray = new SparseArray(3);
       FileUploadSetting.uploadDomainMap = sparseArray;
       sparseArray.put(EnvModeEnum.ONLINE.getEnvMode(), "upload.m.taobao.com");
       sparseArray.put(EnvModeEnum.PREPARE.getEnvMode(), "upload.wapa.taobao.com");
       sparseArray.put(EnvModeEnum.TEST.getEnvMode(), "upload.waptest.taobao.net");
    }
    private void FileUploadSetting(){
       super();
    }
    public static int getSegmentRetryTimes(){
       RemoteConfig segmentRetry;
       IpChange $ipChange = FileUploadSetting.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("2e681b55", objArray).intValue();
       }else if((segmentRetry = RemoteConfig.getInstance().segmentRetryTimes) >= null && segmentRetry <= 10){
          return segmentRetry;
       }else {
          return FileUploadSetting.DEFAULT_SEGMENT_RETRY_TIMES;
       }
    }
    public static int getSegmentSize(String p0,String p1){
       Integer segmentSize;
       Integer segmentSize1;
       IpChange $ipChange = FileUploadSetting.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("90e334ac", objArray).intValue();
       }else if(StringUtils.isBlank(p0)){
          return FileUploadSetting.DEFAULT_SEGMENT_SIZE;
       }else if(StringUtils.isNotBlank(p1) && ((segmentSize = RemoteConfig.getInstance().getSegmentSize(StringUtils.concatStr(p0, p1))) != null && segmentSize.intValue() > 0)){
          return segmentSize.intValue();
       }else if((segmentSize1 = RemoteConfig.getInstance().getSegmentSize(p0)) != null && segmentSize1.intValue() > 0){
          return segmentSize1.intValue();
       }else {
          return FileUploadSetting.DEFAULT_SEGMENT_SIZE;
       }
    }
    public static int getSegmentSize(NetworkClassEnum p0){
       Integer segmentSize;
       IpChange $ipChange = FileUploadSetting.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("c162c0eb", objArray).intValue();
       }else if(p0 == null){
          return FileUploadSetting.DEFAULT_SEGMENT_SIZE;
       }else if((segmentSize = RemoteConfig.getInstance().getSegmentSize(p0.getNetClass())) != null && segmentSize.intValue() > 0){
          return segmentSize.intValue();
       }else {
          return FileUploadSetting.DEFAULT_SEGMENT_SIZE;
       }
    }
    public static int getUploadThreadsNums(){
       RemoteConfig uploadThread;
       IpChange $ipChange = FileUploadSetting.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("54b8a479", objArray).intValue();
       }else if((uploadThread = RemoteConfig.getInstance().uploadThreadNums) >= 1 && uploadThread <= 10){
          return uploadThread;
       }else {
          return FileUploadSetting.DEFAULT_UPLOAD_THREAD_NUMS;
       }
    }
    public static void setSegmentRetryTimes(int p0){
       int i = 1;
       IpChange $ipChange = FileUploadSetting.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = new Integer(p0);
          $ipChange.ipc$dispatch("d0df5fed", objArray);
          return;
       }else {
          int i1 = 10;
          if (p0 >= 0 && p0 <= i1) {
             FileUploadSetting.DEFAULT_SEGMENT_RETRY_TIMES = p0;
             if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)) {
                Object[] objArray2 = new Object[i];
                objArray2[0] = Integer.valueOf(p0);
                TBSdkLog.i("mtopsdk.FileUploadSetting", String.format("[setSegmentRetryTimes] setSegmentRetryTimes succeed, segmentRetryTimes=%d", objArray2));
             }
             return;
          }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.WarnEnable)){
             Object[] objArray1 = new Object[]{Integer.valueOf(0),Integer.valueOf(i1),Integer.valueOf(p0)};
             TBSdkLog.w("mtopsdk.FileUploadSetting", String.format("[setSegmentRetryTimes] invalid parameter,range[%d,%d], segmentRetryTimes=%d", objArray1));
          }
          return;
       }
    }
    public static void setSegmentSize(NetworkClassEnum p0,int p1){
       Object[] objArray1;
       int i = 2;
       IpChange $ipChange = FileUploadSetting.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          objArray[1] = new Integer(p1);
          $ipChange.ipc$dispatch("911d0d7f", objArray);
          return;
       }else if(p0 != null && p1 > 0){
          RemoteConfig.getInstance().setSegmentSize(p0.getNetClass(), p1);
          if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)) {
             objArray1 = new Object[i];
             objArray1[0] = p0;
             objArray1[1] = Integer.valueOf(p1);
             TBSdkLog.w("mtopsdk.FileUploadSetting", String.format("[setSegmentSize] setSegmentSize succeed,networkType=%s,segmentSize=%d", objArray1));
          }
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.WarnEnable)){
          objArray1 = new Object[i];
          objArray1[0] = p0;
          objArray1[1] = Integer.valueOf(p1);
          TBSdkLog.w("mtopsdk.FileUploadSetting", String.format("[setSegmentSize] invalid parameter,networkType=%s,segmentSize=%d", objArray1));
       }
       return;
    }
    public static void setUploadThreadsNums(int p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = FileUploadSetting.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = new Integer(p0);
          $ipChange.ipc$dispatch("749ffb49", objArray);
          return;
       }else {
          int i2 = 10;
          if (p0 >= i1 && p0 <= i2) {
             FileUploadSetting.DEFAULT_UPLOAD_THREAD_NUMS = p0;
             if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)) {
                Object[] objArray2 = new Object[i1];
                objArray2[i] = Integer.valueOf(p0);
                TBSdkLog.i("mtopsdk.FileUploadSetting", String.format("[setUploadThreadsNums] setUploadThreadsNums succeed, uploadThreadsNums=%d", objArray2));
             }
             return;
          }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.WarnEnable)){
             Object[] objArray1 = new Object[]{Integer.valueOf(i1),Integer.valueOf(i2),Integer.valueOf(p0)};
             TBSdkLog.w("mtopsdk.FileUploadSetting", String.format("[setUploadThreadsNums] invalid parameter,range[%d,%d], uploadThreadsNums=%d", objArray1));
          }
          return;
       }
    }
    public static boolean useHttps(String p0){
       IpChange $ipChange = FileUploadSetting.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return RemoteConfig.getInstance().useHttpsBizcodeSets.contains(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("2740d739", objArray).booleanValue();
    }
}
