package androidx.appcompat.app.ToolbarActionBar$ToolbarMenuCallback;
import androidx.appcompat.app.AppCompatDelegateImpl$ActionBarMenuCallback;
import androidx.appcompat.app.ToolbarActionBar;
import java.lang.Object;
import android.view.View;
import android.content.Context;
import androidx.appcompat.widget.DecorToolbar;

public class ToolbarActionBar$ToolbarMenuCallback implements AppCompatDelegateImpl$ActionBarMenuCallback	// class@000587 from classes.dex
{
    public final ToolbarActionBar this$0;

    public void ToolbarActionBar$ToolbarMenuCallback(ToolbarActionBar p0){
       this.this$0 = p0;
       super();
    }
    public View onCreatePanelView(int p0){
       if (!p0) {
          return new View(this.this$0.mDecorToolbar.getContext());
       }
       return null;
    }
    public boolean onPreparePanel(int p0){
       if (!p0) {
          ToolbarActionBar$ToolbarMenuCallback tthis$0 = this.this$0;
          if (tthis$0.mToolbarMenuPrepared == null) {
             tthis$0.mDecorToolbar.setMenuPrepared();
             tthis$0.mToolbarMenuPrepared = true;
          }
       }
       return false;
    }
}
