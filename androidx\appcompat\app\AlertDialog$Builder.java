package androidx.appcompat.app.AlertDialog$Builder;
import android.content.Context;
import androidx.appcompat.app.AlertDialog;
import java.lang.Object;
import androidx.appcompat.app.AlertController$AlertParams;
import android.view.ContextThemeWrapper;
import androidx.appcompat.app.AlertController;
import android.app.Dialog;
import android.content.DialogInterface$OnCancelListener;
import android.content.DialogInterface$OnDismissListener;
import android.content.DialogInterface$OnKeyListener;
import android.widget.ListAdapter;
import android.content.DialogInterface$OnClickListener;
import android.database.Cursor;
import java.lang.String;
import android.view.View;
import android.graphics.drawable.Drawable;
import android.util.TypedValue;
import android.content.res.Resources$Theme;
import android.content.res.Resources;
import java.lang.CharSequence;
import android.content.DialogInterface$OnMultiChoiceClickListener;
import android.widget.AdapterView$OnItemSelectedListener;

public class AlertDialog$Builder	// class@000551 from classes.dex
{
    private final AlertController$AlertParams P;
    private final int mTheme;

    public void AlertDialog$Builder(Context p0){
       super(p0, AlertDialog.resolveDialogTheme(p0, 0));
    }
    public void AlertDialog$Builder(Context p0,int p1){
       super();
       this.P = new AlertController$AlertParams(new ContextThemeWrapper(p0, AlertDialog.resolveDialogTheme(p0, p1)));
       this.mTheme = p1;
    }
    public AlertDialog create(){
       AlertController$AlertParams mOnKeyListen;
       AlertDialog uAlertDialog = new AlertDialog(this.P.mContext, this.mTheme);
       this.P.apply(uAlertDialog.mAlert);
       uAlertDialog.setCancelable(this.P.mCancelable);
       if (this.P.mCancelable != null) {
          uAlertDialog.setCanceledOnTouchOutside(true);
       }
       uAlertDialog.setOnCancelListener(this.P.mOnCancelListener);
       uAlertDialog.setOnDismissListener(this.P.mOnDismissListener);
       if ((mOnKeyListen = this.P.mOnKeyListener) != null) {
          uAlertDialog.setOnKeyListener(mOnKeyListen);
       }
       return uAlertDialog;
    }
    public Context getContext(){
       return this.P.mContext;
    }
    public AlertDialog$Builder setAdapter(ListAdapter p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mAdapter = p0;
       tP.mOnClickListener = p1;
       return this;
    }
    public AlertDialog$Builder setCancelable(boolean p0){
       this.P.mCancelable = p0;
       return this;
    }
    public AlertDialog$Builder setCursor(Cursor p0,DialogInterface$OnClickListener p1,String p2){
       AlertDialog$Builder tP = this.P;
       tP.mCursor = p0;
       tP.mLabelColumn = p2;
       tP.mOnClickListener = p1;
       return this;
    }
    public AlertDialog$Builder setCustomTitle(View p0){
       this.P.mCustomTitleView = p0;
       return this;
    }
    public AlertDialog$Builder setIcon(int p0){
       this.P.mIconId = p0;
       return this;
    }
    public AlertDialog$Builder setIcon(Drawable p0){
       this.P.mIcon = p0;
       return this;
    }
    public AlertDialog$Builder setIconAttribute(int p0){
       TypedValue typedValue = new TypedValue();
       this.P.mContext.getTheme().resolveAttribute(p0, typedValue, true);
       p0.mIconId = typedValue.resourceId;
       return this;
    }
    public AlertDialog$Builder setInverseBackgroundForced(boolean p0){
       this.P.mForceInverseBackground = p0;
       return this;
    }
    public AlertDialog$Builder setItems(int p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mItems = tP.mContext.getResources().getTextArray(p0);
       p0.mOnClickListener = p1;
       return this;
    }
    public AlertDialog$Builder setItems(CharSequence[] p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mItems = p0;
       tP.mOnClickListener = p1;
       return this;
    }
    public AlertDialog$Builder setMessage(int p0){
       AlertDialog$Builder tP = this.P;
       tP.mMessage = tP.mContext.getText(p0);
       return this;
    }
    public AlertDialog$Builder setMessage(CharSequence p0){
       this.P.mMessage = p0;
       return this;
    }
    public AlertDialog$Builder setMultiChoiceItems(int p0,boolean[] p1,DialogInterface$OnMultiChoiceClickListener p2){
       AlertDialog$Builder tP = this.P;
       tP.mItems = tP.mContext.getResources().getTextArray(p0);
       AlertDialog$Builder tP1 = this.P;
       tP1.mOnCheckboxClickListener = p2;
       tP1.mCheckedItems = p1;
       tP1.mIsMultiChoice = true;
       return this;
    }
    public AlertDialog$Builder setMultiChoiceItems(Cursor p0,String p1,String p2,DialogInterface$OnMultiChoiceClickListener p3){
       AlertDialog$Builder tP = this.P;
       tP.mCursor = p0;
       tP.mOnCheckboxClickListener = p3;
       tP.mIsCheckedColumn = p1;
       tP.mLabelColumn = p2;
       tP.mIsMultiChoice = true;
       return this;
    }
    public AlertDialog$Builder setMultiChoiceItems(CharSequence[] p0,boolean[] p1,DialogInterface$OnMultiChoiceClickListener p2){
       AlertDialog$Builder tP = this.P;
       tP.mItems = p0;
       tP.mOnCheckboxClickListener = p2;
       tP.mCheckedItems = p1;
       tP.mIsMultiChoice = true;
       return this;
    }
    public AlertDialog$Builder setNegativeButton(int p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mNegativeButtonText = tP.mContext.getText(p0);
       p0.mNegativeButtonListener = p1;
       return this;
    }
    public AlertDialog$Builder setNegativeButton(CharSequence p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mNegativeButtonText = p0;
       tP.mNegativeButtonListener = p1;
       return this;
    }
    public AlertDialog$Builder setNegativeButtonIcon(Drawable p0){
       this.P.mNegativeButtonIcon = p0;
       return this;
    }
    public AlertDialog$Builder setNeutralButton(int p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mNeutralButtonText = tP.mContext.getText(p0);
       p0.mNeutralButtonListener = p1;
       return this;
    }
    public AlertDialog$Builder setNeutralButton(CharSequence p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mNeutralButtonText = p0;
       tP.mNeutralButtonListener = p1;
       return this;
    }
    public AlertDialog$Builder setNeutralButtonIcon(Drawable p0){
       this.P.mNeutralButtonIcon = p0;
       return this;
    }
    public AlertDialog$Builder setOnCancelListener(DialogInterface$OnCancelListener p0){
       this.P.mOnCancelListener = p0;
       return this;
    }
    public AlertDialog$Builder setOnDismissListener(DialogInterface$OnDismissListener p0){
       this.P.mOnDismissListener = p0;
       return this;
    }
    public AlertDialog$Builder setOnItemSelectedListener(AdapterView$OnItemSelectedListener p0){
       this.P.mOnItemSelectedListener = p0;
       return this;
    }
    public AlertDialog$Builder setOnKeyListener(DialogInterface$OnKeyListener p0){
       this.P.mOnKeyListener = p0;
       return this;
    }
    public AlertDialog$Builder setPositiveButton(int p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mPositiveButtonText = tP.mContext.getText(p0);
       p0.mPositiveButtonListener = p1;
       return this;
    }
    public AlertDialog$Builder setPositiveButton(CharSequence p0,DialogInterface$OnClickListener p1){
       AlertDialog$Builder tP = this.P;
       tP.mPositiveButtonText = p0;
       tP.mPositiveButtonListener = p1;
       return this;
    }
    public AlertDialog$Builder setPositiveButtonIcon(Drawable p0){
       this.P.mPositiveButtonIcon = p0;
       return this;
    }
    public AlertDialog$Builder setRecycleOnMeasureEnabled(boolean p0){
       this.P.mRecycleOnMeasure = p0;
       return this;
    }
    public AlertDialog$Builder setSingleChoiceItems(int p0,int p1,DialogInterface$OnClickListener p2){
       AlertDialog$Builder tP = this.P;
       tP.mItems = tP.mContext.getResources().getTextArray(p0);
       AlertDialog$Builder tP1 = this.P;
       tP1.mOnClickListener = p2;
       tP1.mCheckedItem = p1;
       tP1.mIsSingleChoice = true;
       return this;
    }
    public AlertDialog$Builder setSingleChoiceItems(Cursor p0,int p1,String p2,DialogInterface$OnClickListener p3){
       AlertDialog$Builder tP = this.P;
       tP.mCursor = p0;
       tP.mOnClickListener = p3;
       tP.mCheckedItem = p1;
       tP.mLabelColumn = p2;
       tP.mIsSingleChoice = true;
       return this;
    }
    public AlertDialog$Builder setSingleChoiceItems(ListAdapter p0,int p1,DialogInterface$OnClickListener p2){
       AlertDialog$Builder tP = this.P;
       tP.mAdapter = p0;
       tP.mOnClickListener = p2;
       tP.mCheckedItem = p1;
       tP.mIsSingleChoice = true;
       return this;
    }
    public AlertDialog$Builder setSingleChoiceItems(CharSequence[] p0,int p1,DialogInterface$OnClickListener p2){
       AlertDialog$Builder tP = this.P;
       tP.mItems = p0;
       tP.mOnClickListener = p2;
       tP.mCheckedItem = p1;
       tP.mIsSingleChoice = true;
       return this;
    }
    public AlertDialog$Builder setTitle(int p0){
       AlertDialog$Builder tP = this.P;
       tP.mTitle = tP.mContext.getText(p0);
       return this;
    }
    public AlertDialog$Builder setTitle(CharSequence p0){
       this.P.mTitle = p0;
       return this;
    }
    public AlertDialog$Builder setView(int p0){
       AlertDialog$Builder tP = this.P;
       tP.mView = null;
       tP.mViewLayoutResId = p0;
       tP.mViewSpacingSpecified = false;
       return this;
    }
    public AlertDialog$Builder setView(View p0){
       AlertDialog$Builder tP = this.P;
       tP.mView = p0;
       tP.mViewLayoutResId = 0;
       tP.mViewSpacingSpecified = false;
       return this;
    }
    public AlertDialog$Builder setView(View p0,int p1,int p2,int p3,int p4){
       AlertDialog$Builder tP = this.P;
       tP.mView = p0;
       tP.mViewLayoutResId = 0;
       tP.mViewSpacingSpecified = true;
       tP.mViewSpacingLeft = p1;
       tP.mViewSpacingTop = p2;
       tP.mViewSpacingRight = p3;
       tP.mViewSpacingBottom = p4;
       return this;
    }
    public AlertDialog show(){
       AlertDialog uAlertDialog = this.create();
       uAlertDialog.show();
       return uAlertDialog;
    }
}
