package tb.a2k;
import tb.g5p;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import org.json.JSONObject;
import tb.v5p;
import tb.ja0;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import org.json.JSONArray;
import java.util.ArrayList;
import com.taobao.search.searchdoor.ai.AISearchSuggestBean;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.wsq;
import java.util.List;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class a2k extends g5p	// class@001746 from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x33200326);
    }
    public void a2k(){
       super();
    }
    public static Object ipc$super(a2k p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/search/searchdoor/ai/AISuggestConverter");
    }
    public Object b(JSONObject p0,v5p p1){
       return this.d(p0, p1);
    }
    public Object c(){
       return this.f();
    }
    public ja0 d(JSONObject p0,v5p p1){
       IpChange $ipChange = a2k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("61bbd5e5", objArray);
       }else {
          ja0 oja0 = new ja0();
          if (p0 == null) {
             return oja0;
          }
          this.e(p0, oja0);
          return oja0;
       }
    }
    public final void e(JSONObject p0,ja0 p1){
       IpChange $ipChange = a2k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("b047806c", objArray);
          return;
       }else {
          p1.d = p0.optString("suggest_rn");
          this.i(p0, p1);
          return;
       }
    }
    public ja0 f(){
       IpChange $ipChange = a2k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new ja0();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2bb7fcc4", objArray);
    }
    public void i(JSONObject p0,ja0 p1){
       JSONObject jSONObject;
       JSONObject jSONObject1;
       JSONArray jSONArray1;
       String str4;
       object oobject = p0;
       object oobject1 = p1;
       IpChange $ipChange = a2k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,oobject,oobject1};
          $ipChange.ipc$dispatch("dc09b1f8", objArray);
          return;
       }else {
          ckf.g(oobject, "jsonObject");
          ckf.g(oobject1, "result");
          JSONArray jSONArray = oobject.optJSONArray("result");
          oobject1.g = new ArrayList();
          if (jSONArray != null && jSONArray.length()) {
             int i = jSONArray.length();
             int i1 = 0;
             while (i1 < i) {
                if ((jSONObject = jSONArray.optJSONObject(i1)) != null && (jSONObject1 = jSONObject.optJSONObject("info")) != null) {
                   String str = jSONObject1.optString("showText");
                   AISearchSuggestBean uAISearchSug = new AISearchSuggestBean();
                   if (!TextUtils.isEmpty(str)) {
                      uAISearchSug.setShowText(str);
                      String str1 = jSONObject1.optString("hLightQuery");
                      if (!TextUtils.isEmpty(str1)) {
                         ckf.d(str);
                         ckf.d(str1);
                         uAISearchSug.setHLightStartIndex(wsq.Y(str, str1, 0, false, 6, null));
                         uAISearchSug.setHLightQuery(str1);
                      }
                      if ((jSONArray1 = jSONObject1.optJSONArray("hLightTerms")) != null && jSONArray1.length() > 0) {
                         ArrayList uArrayList = new ArrayList();
                         int i2 = jSONArray1.length();
                         int i3 = 0;
                         while (i3 < i2) {
                            String str2 = jSONArray1.optString(i3);
                            if (!TextUtils.isEmpty(str2)) {
                               uArrayList.add(str2);
                            }
                            i3 = i3 + 1;
                         }
                         uAISearchSug.setHLightTerms(uArrayList);
                      }
                      if ((jSONObject = jSONObject.optJSONObject("trace")) != null) {
                         HashMap hashMap = new HashMap();
                         Iterator iterator = jSONObject.keys();
                         ckf.f(iterator, "keys\(...\)");
                         while (iterator.hasNext()) {
                            String str3 = iterator.next();
                            if ((str4 = jSONObject.optString(str3)) != null) {
                               hashMap.put(str3, str4);
                            }
                         }
                         uAISearchSug.setTraceMap(hashMap);
                      }
                      oobject1.g.add(uAISearchSug);
                   }
                }
                i1 = i1 + 1;
             }
          }
          return;
       }
    }
}
