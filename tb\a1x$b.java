package tb.a1x$b;
import tb.uq;
import java.lang.Object;
import tb.jr;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.String;

public final class a1x$b implements uq	// class@001e5b from classes10.dex
{
    public static IpChange $ipChange;
    public static final a1x$b INSTANCE;

    static {
       a1x$b.INSTANCE = new a1x$b();
    }
    public void a1x$b(){
       super();
    }
    public final void a(jr p0,boolean p1){
       IpChange $ipChange = a1x$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("3310cd60", objArray);
       }
       return;
    }
}
