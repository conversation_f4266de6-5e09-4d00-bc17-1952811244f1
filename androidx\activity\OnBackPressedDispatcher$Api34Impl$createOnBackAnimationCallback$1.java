package androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1;
import android.window.OnBackAnimationCallback;
import tb.g1a;
import tb.d1a;
import java.lang.Object;
import android.window.BackEvent;
import java.lang.String;
import tb.ckf;
import androidx.activity.BackEventCompat;

public final class OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1 implements OnBackAnimationCallback	// class@00045c from classes.dex
{
    public final d1a $onBackCancelled;
    public final d1a $onBackInvoked;
    public final g1a $onBackProgressed;
    public final g1a $onBackStarted;

    public void OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1(g1a p0,g1a p1,d1a p2,d1a p3){
       this.$onBackStarted = p0;
       this.$onBackProgressed = p1;
       this.$onBackInvoked = p2;
       this.$onBackCancelled = p3;
       super();
    }
    public void onBackCancelled(){
       this.$onBackCancelled.invoke();
    }
    public void onBackInvoked(){
       this.$onBackInvoked.invoke();
    }
    public void onBackProgressed(BackEvent p0){
       ckf.g(p0, "backEvent");
       this.$onBackProgressed.invoke(new BackEventCompat(p0));
    }
    public void onBackStarted(BackEvent p0){
       ckf.g(p0, "backEvent");
       this.$onBackStarted.invoke(new BackEventCompat(p0));
    }
}
