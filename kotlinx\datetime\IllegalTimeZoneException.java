package kotlinx.datetime.IllegalTimeZoneException;
import java.lang.IllegalArgumentException;
import java.lang.String;
import java.lang.Object;
import tb.ckf;
import java.lang.Throwable;

public final class IllegalTimeZoneException extends IllegalArgumentException	// class@0006d3 from classes11.dex
{

    public void IllegalTimeZoneException(){
       super();
    }
    public void IllegalTimeZoneException(String p0){
       ckf.g(p0, "message");
       super(p0);
    }
    public void IllegalTimeZoneException(String p0,Throwable p1){
       ckf.g(p0, "message");
       ckf.g(p1, "cause");
       super(p0, p1);
    }
    public void IllegalTimeZoneException(Throwable p0){
       ckf.g(p0, "cause");
       super(p0);
    }
}
