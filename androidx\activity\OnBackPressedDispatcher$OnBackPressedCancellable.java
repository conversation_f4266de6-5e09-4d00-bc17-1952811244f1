package androidx.activity.OnBackPressedDispatcher$OnBackPressedCancellable;
import androidx.activity.Cancellable;
import androidx.activity.OnBackPressedDispatcher;
import androidx.activity.OnBackPressedCallback;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.ob1;
import tb.d1a;

public final class OnBackPressedDispatcher$OnBackPressedCancellable implements Cancellable	// class@00045f from classes.dex
{
    private final OnBackPressedCallback onBackPressedCallback;
    public final OnBackPressedDispatcher this$0;

    public void OnBackPressedDispatcher$OnBackPressedCancellable(OnBackPressedDispatcher p0,OnBackPressedCallback p1){
       ckf.g(p1, "onBackPressedCallback");
       this.this$0 = p0;
       super();
       this.onBackPressedCallback = p1;
    }
    public void cancel(){
       d1a enabledChang;
       OnBackPressedDispatcher.access$getOnBackPressedCallbacks$p(this.this$0).remove(this.onBackPressedCallback);
       if (ckf.b(OnBackPressedDispatcher.access$getInProgressCallback$p(this.this$0), this.onBackPressedCallback)) {
          this.onBackPressedCallback.handleOnBackCancelled();
          OnBackPressedDispatcher.access$setInProgressCallback$p(this.this$0, null);
       }
       this.onBackPressedCallback.removeCancellable(this);
       if ((enabledChang = this.onBackPressedCallback.getEnabledChangedCallback$activity_release()) != null) {
          enabledChang.invoke();
       }
       this.onBackPressedCallback.setEnabledChangedCallback$activity_release(null);
       return;
    }
}
