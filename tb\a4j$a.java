package tb.a4j$a;
import tb.t2o;
import java.lang.Object;
import tb.a07;
import com.alibaba.android.ultron.vfw.instance.a;
import tb.ko5;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ckf;
import tb.evb;
import tb.dkx;
import tb.ckx;
import tb.dn5;
import tb.mo5;
import tb.lj5;
import tb.lo5;
import tb.no5;
import tb.tt5;
import tb.nq5;
import tb.kn5;
import tb.ml5;
import tb.ikx$a;
import tb.qub;
import tb.jkx$a;
import tb.ekx$a;
import tb.a86$a;
import tb.ivh$a;
import com.taobao.unit.center.mdc.dinamicx.dataParse.DXTsKVDataParser;

public final class a4j$a	// class@001ae7 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2ef00205);
    }
    public void a4j$a(){
       super();
    }
    public void a4j$a(a07 p0){
       super();
    }
    public final void a(a p0,ko5 p1){
       int i = 0;
       IpChange $ipChange = a4j$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("b4ea86e9", objArray);
          return;
       }else {
          ckf.h(p0, "ultronInstance");
          ckf.h(p1, "mtb2022EquityNumberChangeData");
          p0.B0(0x8d0435571734e210, p1);
          p0.B0(0x9ad979826b780331, new dkx(i));
          p0.B0(0x78712108e071a72a, new ckx(i));
          p0.B0(0x8a7ed49ec0836fe3, new dn5());
          p0.B0(0x95cb55eacc069199, new mo5("mytaobao"));
          p0.B0(0x76d5d5a54e9dcd04, new lj5());
          p0.B0(0x3b701ca4c749228c, new lo5());
          p0.B0(0x840c1b8188e35372, new no5());
          p0.B0(0x8bffb7ca50196977, new tt5());
          p0.B0(0xdd0f9abcabf28a21, new nq5());
          p0.B0(0xb7b74dad0ce132bc, new kn5());
          p0.B0(0x5a6ba861786d35e0, new ml5());
          p0.C0(0x399d079497d08399, new ikx$a());
          p0.C0(0x4f0a35ad6d8e26ca, new jkx$a());
          p0.C0(0x9748d583c438bfae, new ekx$a());
          p0.C0(0x4b6aea9b5842387b, new a86$a());
          p0.C0(0xd06ec4a00be4383e, new ivh$a());
          p0.B0(0x8e483874c, new DXTsKVDataParser());
          return;
       }
    }
}
