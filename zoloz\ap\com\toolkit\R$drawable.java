package zoloz.ap.com.toolkit.R$drawable;
import com.taobao.taobao.R$drawable;
import java.lang.Object;

public final class R$drawable	// class@0010ff from classes11.dex
{
    public static int bar_line;
    public static int dialog_white_bg;
    public static int icon_success;
    public static int separate;
    public static int simple_toast_bg;
    public static int title_bar_back;

    static {
       R$drawable.bar_line = R$drawable.bar_line;
       R$drawable.dialog_white_bg = R$drawable.dialog_white_bg;
       R$drawable.icon_success = R$drawable.icon_success;
       R$drawable.separate = R$drawable.separate;
       R$drawable.simple_toast_bg = R$drawable.simple_toast_bg;
       R$drawable.title_bar_back = R$drawable.title_bar_back;
    }
    public void R$drawable(){
       super();
    }
}
