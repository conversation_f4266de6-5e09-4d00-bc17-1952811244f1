package kotlinx.coroutines.CoroutineDispatcher$Key$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import kotlin.coroutines.d$b;
import kotlinx.coroutines.CoroutineDispatcher;

public final class CoroutineDispatcher$Key$1 extends Lambda implements g1a	// class@000491 from classes11.dex
{
    public static final CoroutineDispatcher$Key$1 INSTANCE;

    static {
       CoroutineDispatcher$Key$1.INSTANCE = new CoroutineDispatcher$Key$1();
    }
    public void CoroutineDispatcher$Key$1(){
       super(1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final CoroutineDispatcher invoke(d$b p0){
       if (p0 instanceof CoroutineDispatcher) {
       }else {
          p0 = null;
       }
       return p0;
    }
}
