package kotlinx.coroutines.a;
import tb.ar4;
import tb.uu4;
import kotlinx.coroutines.JobSupport;
import kotlin.coroutines.d;
import kotlinx.coroutines.m;
import kotlin.coroutines.d$c;
import kotlin.coroutines.d$b;
import java.lang.String;
import java.lang.Object;
import tb.ov6;
import kotlinx.coroutines.CoroutineContextKt;
import java.lang.StringBuilder;
import tb.fa4;
import java.lang.Throwable;
import tb.g1a;
import tb.ia4;
import tb.suf;
import kotlinx.coroutines.CoroutineStart;
import tb.u1a;
import tb.tu4;

public abstract class a extends JobSupport implements ar4, uu4	// class@0004b1 from classes11.dex
{
    private final d e;

    public void a(d p0,boolean p1,boolean p2){
       super(p2);
       if (p1) {
          this.y0(p0.get(m.Key));
       }
       this.e = p0.plus(this);
       return;
    }
    public static void s1(){
    }
    public String R(){
       return ov6.a(this).concat(" was cancelled");
    }
    public String R0(){
       String str;
       if ((str = CoroutineContextKt.b(this.e)) == null) {
          return super.R0();
       }
       return "\""+str+"\":"+super.R0();
    }
    public final void Z0(Object p0){
       if (p0 instanceof fa4) {
          this.t1(p0.a, p0.a());
       }else {
          this.u1(p0);
       }
       return;
    }
    public final d getContext(){
       return this.e;
    }
    public d getCoroutineContext(){
       return this.e;
    }
    public boolean isActive(){
       return super.isActive();
    }
    public void r1(Object p0){
       this.G(p0);
    }
    public final void resumeWith(Object p0){
       if ((p0 = this.P0(ia4.d(p0, null, 1, null))) == suf.COMPLETING_WAITING_CHILDREN) {
          return;
       }
       this.r1(p0);
       return;
    }
    public void t1(Throwable p0,boolean p1){
    }
    public void u1(Object p0){
    }
    public final void v1(CoroutineStart p0,Object p1,u1a p2){
       p0.invoke(p2, p1, this);
    }
    public final void x0(Throwable p0){
       tu4.a(this.e, p0);
    }
}
