package tb.a1p$a;
import tb.t2o;
import java.lang.Object;
import tb.a07;
import org.json.JSONObject;
import tb.a1p;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ckf;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.Date;
import java.text.DateFormat;

public final class a1p$a	// class@001ad4 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x332000d3);
    }
    public void a1p$a(){
       super();
    }
    public void a1p$a(a07 p0){
       super();
    }
    public final a1p a(JSONObject p0){
       int i = 0;
       IpChange $ipChange = a1p$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("bc4dea3e", objArray);
       }else {
          ckf.g(p0, "jsonObject");
          String str = p0.optString("url");
          if (TextUtils.isEmpty(str)) {
             return null;
          }
          SimpleDateFormat simpleDateFo = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
          ckf.d(str);
          a1p v10 = new a1p(str, p0.optInt("totalCount", i), p0.optInt("sepCount", i), simpleDateFo.parse(p0.optString("startTime")).getTime(), simpleDateFo.parse(p0.optString("endTime")).getTime());
          return v10;
       }
    }
}
