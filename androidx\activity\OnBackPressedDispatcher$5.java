package androidx.activity.OnBackPressedDispatcher$5;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.OnBackPressedDispatcher;
import java.lang.Object;
import tb.xhv;

public final class OnBackPressedDispatcher$5 extends Lambda implements d1a	// class@00045a from classes.dex
{
    public final OnBackPressedDispatcher this$0;

    public void OnBackPressedDispatcher$5(OnBackPressedDispatcher p0){
       this.this$0 = p0;
       super(0);
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       this.this$0.onBackPressed();
    }
}
