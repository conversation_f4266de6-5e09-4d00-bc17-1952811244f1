package mtopsdk.mtop.domain.MockResponse;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;

public class MockResponse	// class@0007bb from classes11.dex
{
    public String api;
    public byte[] byteData;
    public Map headers;
    public int statusCode;
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000d2);
    }
    public void MockResponse(){
       super();
    }
    public String toString(){
       IpChange $ipChange = MockResponse.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "MockResponse{api=\'"+this.api+"\', statusCode="+this.statusCode+", headers="+this.headers+", byteData="+new String(this.byteData)+'}';
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
