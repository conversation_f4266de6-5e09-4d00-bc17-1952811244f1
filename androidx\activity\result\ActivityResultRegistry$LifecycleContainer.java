package androidx.activity.result.ActivityResultRegistry$LifecycleContainer;
import androidx.lifecycle.Lifecycle;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import java.util.ArrayList;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleObserver;
import java.util.List;
import java.lang.Iterable;
import java.util.Iterator;

public final class ActivityResultRegistry$LifecycleContainer	// class@0004b6 from classes.dex
{
    private final Lifecycle lifecycle;
    private final List observers;

    public void ActivityResultRegistry$LifecycleContainer(Lifecycle p0){
       ckf.g(p0, "lifecycle");
       super();
       this.lifecycle = p0;
       this.observers = new ArrayList();
    }
    public final void addObserver(LifecycleEventObserver p0){
       ckf.g(p0, "observer");
       this.lifecycle.addObserver(p0);
       this.observers.add(p0);
    }
    public final void clearObservers(){
       Iterator iterator = this.observers.iterator();
       while (iterator.hasNext()) {
          this.lifecycle.removeObserver(iterator.next());
       }
       this.observers.clear();
       return;
    }
    public final Lifecycle getLifecycle(){
       return this.lifecycle;
    }
}
