package kotlinx.datetime.format.DateTimeComponents$offsetSecondsOfMinute$2;
import kotlin.jvm.internal.MutablePropertyReference0Impl;
import java.lang.Object;
import tb.d230;
import java.lang.Class;
import java.lang.String;
import kotlin.jvm.internal.CallableReference;
import java.lang.Integer;

public final class DateTimeComponents$offsetSecondsOfMinute$2 extends MutablePropertyReference0Impl	// class@0006e0 from classes11.dex
{

    public void DateTimeComponents$offsetSecondsOfMinute$2(Object p0){
       super(p0, d230.class, "secondsOfMinute", "getSecondsOfMinute\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(){
       return this.receiver.f();
    }
    public void set(Object p0){
       this.receiver.B(p0);
    }
}
