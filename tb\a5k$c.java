package tb.a5k$c;
import tb.jfw;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import android.content.Context;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import tb.nwv;
import android.net.Uri;
import java.util.Map;

public class a5k$c extends jfw	// class@001762 from classes9.dex
{
    public boolean A0;
    public String w0;
    public HashMap x0;
    public String y0;
    public Map z0;
    public static IpChange $ipChange;

    static {
       t2o.a(0x20100125);
    }
    public void a5k$c(){
       super();
    }
    public static Object ipc$super(a5k$c p0,String p1,Object[] p2){
       if (p1.hashCode() != -998046064) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/tao/flexbox/layoutmanager/component/NodeComponent$NodeParams");
       }
       super.B(p2[0], p2[1]);
       return null;
    }
    public void B(Context p0,HashMap p1){
       a5k$c tw0;
       HashMap hashMap;
       Map map;
       int i = 0;
       IpChange $ipChange = a5k$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("c4830690", objArray);
          return;
       }else {
          super.B(p0, p1);
          this.w0 = nwv.B(p1.get("src"));
          this.x0 = new HashMap();
          if ((tw0 = this.w0) != null && (hashMap = nwv.q0(Uri.parse(tw0))) != null) {
             this.x0.putAll(hashMap);
          }
          if ((map = nwv.z(p1.get("query"), null)) == null || map.isEmpty()) {
             map = nwv.z(p1.get("data"), null);
          }
          if (map != null) {
             this.x0.putAll(map);
          }
          this.z0 = nwv.z(p1.get("options"), null);
          this.A0 = nwv.o(p1.get("disabled-page-tracker"), i);
          nwv.B(p1.get("type"));
          nwv.B(p1.get("local-src"));
          nwv.B(p1.get("init-data-key"));
          this.y0 = nwv.I(p1.get("error-page"), "");
          return;
       }
    }
}
