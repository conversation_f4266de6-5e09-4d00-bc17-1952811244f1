package tb.a8;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;

public class a8	// class@00184f from classes5.dex
{
    public final int a;
    public final String b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x15d00006);
    }
    public void a8(int p0,String p1){
       super();
       this.a = p0;
       this.b = p1;
    }
    public int a(){
       IpChange $ipChange = a8.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("828feb07", objArray).intValue();
    }
    public String b(){
       IpChange $ipChange = a8.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e1cc388a", objArray);
    }
}
