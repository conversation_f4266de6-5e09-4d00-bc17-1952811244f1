package androidx.activity.FullyDrawnReporterKt;
import androidx.activity.FullyDrawnReporter;
import tb.g1a;
import tb.ar4;
import java.lang.Object;
import androidx.activity.FullyDrawnReporterKt$reportWhenComplete$1;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import tb.xhv;

public final class FullyDrawnReporterKt	// class@00044d from classes.dex
{

    public static final Object reportWhenComplete(FullyDrawnReporter p0,g1a p1,ar4 p2){
       FullyDrawnReporterKt$reportWhenComplete$1 oreportWhenC;
       FullyDrawnReporterKt$reportWhenComplete$1 label1;
       FullyDrawnReporterKt$reportWhenComplete$1 l$0;
       if (p2 instanceof FullyDrawnReporterKt$reportWhenComplete$1) {
          oreportWhenC = p2;
          FullyDrawnReporterKt$reportWhenComplete$1 label = oreportWhenC.label;
          int i = Integer.MIN_VALUE;
          if ((label & i)) {
             oreportWhenC.label = label - i;
          label_0018 :
             FullyDrawnReporterKt$reportWhenComplete$1 result = oreportWhenC.result;
             Object obj = dkf.d();
             if ((label1 = oreportWhenC.label) != null) {
                if (label1 == 1) {
                   l$0 = oreportWhenC.L$0;
                   b.b(result);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                p0.addReporter();
                if (p0.isFullyDrawnReported()) {
                   return xhv.INSTANCE;
                }else {
                   oreportWhenC.L$0 = p0;
                   oreportWhenC.label = 1;
                   if (p1.invoke(oreportWhenC) == obj) {
                      return obj;
                   }
                }
             }
             l$0.removeReporter();
             return xhv.INSTANCE;
          }
       }
       oreportWhenC = new FullyDrawnReporterKt$reportWhenComplete$1(p2);
       goto label_0018 ;
    }
    private static final Object reportWhenComplete$$forInline(FullyDrawnReporter p0,g1a p1,ar4 p2){
       p0.addReporter();
       if (p0.isFullyDrawnReported()) {
          return xhv.INSTANCE;
       }
       p1.invoke(p2);
       p0.removeReporter();
       return xhv.INSTANCE;
    }
}
