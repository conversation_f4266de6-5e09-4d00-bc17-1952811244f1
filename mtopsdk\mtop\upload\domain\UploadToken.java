package mtopsdk.mtop.upload.domain.UploadToken;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicLong;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import mtopsdk.mtop.upload.domain.FileBaseInfo;
import mtopsdk.common.util.StringUtils;
import java.lang.StringBuilder;

public class UploadToken	// class@000811 from classes11.dex
{
    public String bizCode;
    public String domain;
    public FileBaseInfo fileBaseInfo;
    public long retryCount;
    public long segmentSize;
    public String token;
    public Map tokenParams;
    public AtomicLong uploadedLength;
    public boolean useHttps;
    public static IpChange $ipChange;

    static {
       t2o.a(0x25900015);
    }
    public void UploadToken(){
       super();
       this.uploadedLength = new AtomicLong();
    }
    public boolean isValid(){
       UploadToken tfileBaseInf;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = UploadToken.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("3fef87d", objArray).booleanValue();
       }else if((tfileBaseInf = this.fileBaseInfo) != null && ((tfileBaseInf.fileSize) > 0 && ((this.segmentSize) > 0 && (!StringUtils.isBlank(this.token) && !StringUtils.isBlank(this.domain))))){
          i = true;
       }
       return i;
    }
    public String toString(){
       IpChange $ipChange = UploadToken.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(64)+"UploadToken [token="+this.token+", domain="+this.domain+", tokenParams="+this.tokenParams+", retryCount="+this.retryCount+", patchSize="+this.segmentSize+", fileBaseInfo="+this.fileBaseInfo+"]";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
