package tb.a2s$c;
import android.text.TextWatcher;
import tb.t2o;
import tb.a2s;
import android.widget.EditText;
import java.lang.String;
import java.lang.Object;
import android.text.Editable;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import java.lang.Integer;
import android.view.View;
import java.util.ArrayList;
import tb.sf;

public class a2s$c implements TextWatcher	// class@001829 from classes5.dex
{
    public final EditText a;
    public final String b;
    public final a2s c;
    public static IpChange $ipChange;

    static {
       t2o.a(0x13c0002e);
    }
    public void a2s$c(a2s p0,EditText p1,String p2){
       super();
       this.c = p0;
       this.a = p1;
       this.b = p2;
    }
    public void afterTextChanged(Editable p0){
       IpChange $ipChange = a2s$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("77fdbb29", objArray);
       }
       return;
    }
    public void beforeTextChanged(CharSequence p0,int p1,int p2,int p3){
       IpChange $ipChange = a2s$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),new Integer(p2),new Integer(p3)};
          $ipChange.ipc$dispatch("acba1d0", objArray);
       }
       return;
    }
    public void onTextChanged(CharSequence p0,int p1,int p2,int p3){
       int i = 5;
       IpChange $ipChange = a2s$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[2] = new Integer(p1);
          objArray[3] = new Integer(p2);
          objArray[4] = new Integer(p3);
          $ipChange.ipc$dispatch("67397830", objArray);
          return;
       }else if(this.a.isFocusable()){
          String str = "input";
          if (str.equals(this.b)) {
             ArrayList uArrayList = new ArrayList(i);
             uArrayList.add(str);
             uArrayList.add(this.a.getText());
             this.a.setTag(sf.ID_DX_INPUT_TAG, uArrayList);
             a2s.n0(this.c, this.a.getText());
          }
       }
       return;
    }
}
