package mtopsdk.mtop.upload.FileUploadMgr$1;
import tb.z6e;
import mtopsdk.mtop.upload.FileUploadMgr;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.Map;

public class FileUploadMgr$1 implements z6e	// class@000801 from classes11.dex
{
    public final FileUploadMgr this$0;
    public final UploadFileInfo val$fileInfo;
    public static IpChange $ipChange;

    public void FileUploadMgr$1(FileUploadMgr p0,UploadFileInfo p1){
       this.this$0 = p0;
       this.val$fileInfo = p1;
       super();
    }
    public String getBizType(){
       IpChange $ipChange = FileUploadMgr$1.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.val$fileInfo.getBizCode();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9c07dca2", objArray);
    }
    public String getFilePath(){
       IpChange $ipChange = FileUploadMgr$1.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.val$fileInfo.getFilePath();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1bcb7a22", objArray);
    }
    public String getFileType(){
       IpChange $ipChange = FileUploadMgr$1.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("105a7e2d", objArray);
    }
    public Map getMetaInfo(){
       IpChange $ipChange = FileUploadMgr$1.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8d01c005", objArray);
    }
}
