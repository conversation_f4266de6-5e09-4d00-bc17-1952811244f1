package androidx.activity.ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import android.view.View;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.view.ViewParent;

public final class ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1 extends Lambda implements g1a	// class@000470 from classes.dex
{
    public static final ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1 INSTANCE;

    static {
       ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1.INSTANCE = new ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1();
    }
    public void ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1(){
       super(1);
    }
    public final View invoke(View p0){
       ckf.g(p0, "it");
       ViewParent parent = p0.getParent();
       if (parent instanceof View) {
       }else {
          parent = null;
       }
       return parent;
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
}
