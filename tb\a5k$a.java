package tb.a5k$a;
import com.taobao.tao.flexbox.layoutmanager.core.b$b0;
import tb.a5k;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.tao.flexbox.layoutmanager.core.e;
import tb.jfw;
import com.taobao.tao.flexbox.layoutmanager.core.n$f;
import com.taobao.tao.flexbox.layoutmanager.core.TNodeView;
import tb.a5k$c;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import com.taobao.tao.flexbox.layoutmanager.core.n;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.nwv;

public class a5k$a extends b$b0	// class@001760 from classes9.dex
{
    public static IpChange $ipChange;

    public void a5k$a(a5k p0){
       super();
    }
    public static Object ipc$super(a5k$a p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/tao/flexbox/layoutmanager/component/NodeComponent$1");
    }
    public void c(e p0,Object p1,jfw p2,n$f p3){
       this.m(p0, p1, p2, p3);
    }
    public boolean i(){
       int i = 1;
       IpChange $ipChange = a5k$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          i = $ipChange.ipc$dispatch("805570d3", objArray).booleanValue();
       }
       return i;
    }
    public void j(n p0,jfw p1,String p2,Object p3){
       this.n(p0, p1, p2, p3);
    }
    public void m(e p0,TNodeView p1,a5k$c p2,n$f p3){
       IpChange $ipChange = a5k$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("1e8bbf67", objArray);
          return;
       }else if(p1 != null && p2 != null){
          p1.setErrorPage(p2.y0);
       }
       return;
    }
    public void n(n p0,a5k$c p1,String p2,Object p3){
       IpChange $ipChange = a5k$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("b4ff9599", objArray);
          return;
       }else if(TextUtils.equals(p2, "error-page")){
          p1.y0 = nwv.I(p3, "");
       }
       return;
    }
}
