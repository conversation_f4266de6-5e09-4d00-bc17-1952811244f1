package androidx.activity.ComponentActivity$activityResultRegistry$1;
import androidx.activity.result.ActivityResultRegistry;
import androidx.activity.ComponentActivity;
import android.content.IntentSender$SendIntentException;
import androidx.activity.result.contract.ActivityResultContract$SynchronousResult;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.content.Intent;
import java.io.Serializable;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.core.app.ActivityOptionsCompat;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import tb.wa4;
import java.lang.Runnable;
import android.os.Bundle;
import java.lang.ClassLoader;
import android.app.Activity;
import androidx.core.app.ActivityCompat;
import android.os.Parcelable;
import androidx.activity.result.IntentSenderRequest;
import android.content.IntentSender;
import tb.xa4;

public final class ComponentActivity$activityResultRegistry$1 extends ActivityResultRegistry	// class@00043b from classes.dex
{
    public final ComponentActivity this$0;

    public void ComponentActivity$activityResultRegistry$1(ComponentActivity p0){
       this.this$0 = p0;
       super();
    }
    public static void b(ComponentActivity$activityResultRegistry$1 p0,int p1,IntentSender$SendIntentException p2){
       ComponentActivity$activityResultRegistry$1.onLaunch$lambda$1(p0, p1, p2);
    }
    public static void c(ComponentActivity$activityResultRegistry$1 p0,int p1,ActivityResultContract$SynchronousResult p2){
       ComponentActivity$activityResultRegistry$1.onLaunch$lambda$0(p0, p1, p2);
    }
    private static final void onLaunch$lambda$0(ComponentActivity$activityResultRegistry$1 p0,int p1,ActivityResultContract$SynchronousResult p2){
       ckf.g(p0, "this$0");
       p0.dispatchResult(p1, p2.getValue());
    }
    private static final void onLaunch$lambda$1(ComponentActivity$activityResultRegistry$1 p0,int p1,IntentSender$SendIntentException p2){
       ckf.g(p0, "this$0");
       ckf.g(p2, "$e");
       p0.dispatchResult(p1, 0, new Intent().setAction("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST").putExtra("androidx.activity.result.contract.extra.SEND_INTENT_EXCEPTION", p2));
    }
    public void onLaunch(int p0,ActivityResultContract p1,Object p2,ActivityOptionsCompat p3){
       ActivityResultContract$SynchronousResult synchronousR;
       Bundle uBundle;
       String[] stringArrayE;
       ckf.g(p1, "contract");
       ComponentActivity$activityResultRegistry$1 tthis$0 = this.this$0;
       if ((synchronousR = p1.getSynchronousResult(tthis$0, p2)) != null) {
          new Handler(Looper.getMainLooper()).post(new wa4(this, p0, synchronousR));
          return;
       }else {
          Intent intent = p1.createIntent(tthis$0, p2);
          if (intent.getExtras() != null) {
             p2 = intent.getExtras();
             ckf.d(p2);
             if (p2.getClassLoader() == null) {
                intent.setExtrasClassLoader(tthis$0.getClassLoader());
             }
          }
          p2 = "androidx.activity.result.contract.extra.ACTIVITY_OPTIONS_BUNDLE";
          if (intent.hasExtra(p2)) {
             intent.removeExtra(p2);
             uBundle = intent.getBundleExtra(p2);
          }else if(p3 != null){
             p2 = p3.toBundle();
          }else {
             int i = 0;
          }
          uBundle = p2;
          if (ckf.b("androidx.activity.result.contract.action.REQUEST_PERMISSIONS", intent.getAction())) {
             if ((stringArrayE = intent.getStringArrayExtra("androidx.activity.result.contract.extra.PERMISSIONS")) == null) {
                stringArrayE = new String[0];
             }
             ActivityCompat.requestPermissions(tthis$0, stringArrayE, p0);
          }else if(ckf.b("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST", intent.getAction())){
             IntentSenderRequest parcelableEx = intent.getParcelableExtra("androidx.activity.result.contract.extra.INTENT_SENDER_REQUEST");
             try{
                ckf.d(parcelableEx);
                ActivityCompat.startIntentSenderForResult(tthis$0, parcelableEx.getIntentSender(), p0, parcelableEx.getFillInIntent(), parcelableEx.getFlagsMask(), parcelableEx.getFlagsValues(), 0, uBundle);
             }catch(android.content.IntentSender$SendIntentException e11){
                new Handler(Looper.getMainLooper()).post(new xa4(this, p0, e11));
             }
          }else {
             ActivityCompat.startActivityForResult(tthis$0, intent, p0, uBundle);
          }
          return;
       }
    }
}
