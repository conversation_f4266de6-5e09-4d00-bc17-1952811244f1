package tb.a8q;
import java.lang.Runnable;
import tb.b8q$a;
import java.lang.String;
import com.taobao.android.diagnose.common.DiagnoseType;
import java.lang.Object;

public final class a8q implements Runnable	// class@001852 from classes5.dex
{
    public final b8q$a a;
    public final String b;
    public final DiagnoseType c;

    public void a8q(b8q$a p0,String p1,DiagnoseType p2){
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
    }
    public final void run(){
       b8q$a.a(this.a, this.b, this.c);
    }
}
