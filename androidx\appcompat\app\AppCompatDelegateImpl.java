package androidx.appcompat.app.AppCompatDelegateImpl;
import androidx.appcompat.view.menu.MenuBuilder$Callback;
import android.view.LayoutInflater$Factory2;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.collection.SimpleArrayMap;
import android.os.Build;
import java.lang.Object;
import java.lang.String;
import android.app.Activity;
import androidx.appcompat.app.AppCompatCallback;
import android.content.Context;
import android.view.Window;
import android.app.Dialog;
import androidx.appcompat.app.AppCompatDelegateImpl$2;
import androidx.appcompat.app.AppCompatActivity;
import java.lang.Class;
import java.lang.Integer;
import androidx.appcompat.widget.AppCompatDrawableManager;
import android.os.Build$VERSION;
import androidx.core.os.LocaleListCompat;
import android.content.res.Resources;
import android.content.res.Configuration;
import androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager;
import android.view.View;
import androidx.appcompat.widget.ContentFrameLayout;
import com.taobao.taobao.R$styleable;
import android.content.res.TypedArray;
import android.util.TypedValue;
import android.view.Window$Callback;
import androidx.appcompat.app.AppCompatDelegateImpl$AppCompatWindowCallback;
import android.util.AttributeSet;
import androidx.appcompat.widget.TintTypedArray;
import android.graphics.drawable.Drawable;
import android.window.OnBackInvokedDispatcher;
import java.lang.IllegalStateException;
import android.view.ViewGroup;
import android.view.LayoutInflater;
import com.taobao.taobao.R$layout;
import android.content.res.Resources$Theme;
import com.taobao.taobao.R$attr;
import androidx.appcompat.view.ContextThemeWrapper;
import com.taobao.taobao.R$id;
import androidx.appcompat.widget.DecorContentParent;
import androidx.appcompat.app.AppCompatDelegateImpl$3;
import androidx.core.view.OnApplyWindowInsetsListener;
import androidx.core.view.ViewCompat;
import android.widget.TextView;
import androidx.appcompat.widget.ViewUtils;
import android.widget.FrameLayout;
import androidx.appcompat.app.AppCompatDelegateImpl$5;
import androidx.appcompat.widget.ContentFrameLayout$OnAttachListener;
import java.lang.IllegalArgumentException;
import java.lang.StringBuilder;
import java.lang.CharSequence;
import android.text.TextUtils;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState;
import androidx.appcompat.app.AppCompatDelegateImpl$Api24Impl;
import androidx.core.util.ObjectsCompat;
import androidx.appcompat.app.AppCompatDelegateImpl$Api26Impl;
import android.content.pm.PackageManager;
import android.content.ComponentName;
import android.content.pm.ActivityInfo;
import androidx.appcompat.app.AppCompatDelegateImpl$AutoBatteryNightModeManager;
import androidx.appcompat.app.AppCompatDelegateImpl$AutoTimeNightModeManager;
import androidx.appcompat.app.TwilightManager;
import androidx.appcompat.app.WindowDecorActionBar;
import androidx.appcompat.app.AppCompatDelegateImpl$PanelMenuPresenterCallback;
import androidx.appcompat.view.menu.MenuPresenter$Callback;
import androidx.appcompat.view.menu.MenuView;
import androidx.appcompat.app.AppCompatDelegateImpl$ListMenuDecorView;
import androidx.appcompat.view.menu.MenuBuilder;
import java.lang.Runnable;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.media.AudioManager;
import android.view.Menu;
import android.view.WindowManager;
import android.view.ViewGroup$LayoutParams;
import android.view.ViewParent;
import android.view.WindowManager$LayoutParams;
import android.view.ViewManager;
import androidx.appcompat.app.ToolbarActionBar;
import androidx.appcompat.app.AppCompatDelegateImpl$ActionMenuPresenterCallback;
import android.os.Bundle;
import android.view.KeyCharacterMap;
import android.util.AndroidRuntimeException;
import android.content.ContextWrapper;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.Lifecycle$State;
import androidx.core.app.ActivityCompat;
import android.util.DisplayMetrics;
import androidx.appcompat.app.ResourcesFlusher;
import com.taobao.taobao.R$color;
import androidx.core.content.ContextCompat;
import android.view.ContextThemeWrapper;
import com.taobao.taobao.R$style;
import androidx.core.content.res.ResourcesCompat$ThemeCompat;
import androidx.appcompat.app.LocaleOverlayHelper;
import java.util.Locale;
import androidx.appcompat.app.AppCompatDelegateImpl$Api21Impl;
import androidx.appcompat.app.AppCompatViewInflater;
import java.lang.ClassLoader;
import java.lang.reflect.Constructor;
import androidx.appcompat.app.LayoutIncludeDetector;
import org.xmlpull.v1.XmlPullParser;
import androidx.appcompat.widget.VectorEnabledTintResources;
import android.widget.PopupWindow;
import androidx.core.view.KeyEventDispatcher$Component;
import androidx.appcompat.app.AppCompatDialog;
import androidx.core.view.KeyEventDispatcher;
import android.os.BaseBundle;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.appcompat.app.ActionBarDrawerToggle$Delegate;
import androidx.appcompat.app.AppCompatDelegateImpl$ActionBarDrawableToggleImpl;
import android.view.MenuInflater;
import androidx.appcompat.view.SupportMenuInflater;
import java.lang.System;
import android.view.LayoutInflater$Factory;
import androidx.core.view.LayoutInflaterCompat;
import android.app.UiModeManager;
import androidx.appcompat.view.ActionMode;
import androidx.core.app.NavUtils;
import android.view.MenuItem;
import androidx.appcompat.app.AppCompatDelegateImpl$Api33Impl;
import androidx.appcompat.widget.Toolbar;
import androidx.appcompat.app.AppCompatDelegateImpl$ActionBarMenuCallback;
import androidx.appcompat.view.ActionMode$Callback;
import androidx.appcompat.app.AppCompatDelegateImpl$ActionModeCallbackWrapperV9;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.core.widget.PopupWindowCompat;
import androidx.appcompat.app.AppCompatDelegateImpl$6;
import androidx.appcompat.widget.ViewStubCompat;
import androidx.appcompat.view.StandaloneActionMode;
import androidx.appcompat.app.AppCompatDelegateImpl$7;
import androidx.core.view.ViewPropertyAnimatorListener;
import android.window.OnBackInvokedCallback;
import androidx.core.view.WindowInsetsCompat;
import android.graphics.Rect;
import android.view.ViewGroup$MarginLayoutParams;
import android.widget.FrameLayout$LayoutParams;

public class AppCompatDelegateImpl extends AppCompatDelegate implements MenuBuilder$Callback, LayoutInflater$Factory2	// class@000578 from classes.dex
{
    public ActionBar mActionBar;
    private AppCompatDelegateImpl$ActionMenuPresenterCallback mActionMenuPresenterCallback;
    public ActionMode mActionMode;
    public PopupWindow mActionModePopup;
    public ActionBarContextView mActionModeView;
    private int mActivityHandlesConfigFlags;
    private boolean mActivityHandlesConfigFlagsChecked;
    public final AppCompatCallback mAppCompatCallback;
    private AppCompatViewInflater mAppCompatViewInflater;
    private AppCompatDelegateImpl$AppCompatWindowCallback mAppCompatWindowCallback;
    private AppCompatDelegateImpl$AutoNightModeManager mAutoBatteryNightModeManager;
    private AppCompatDelegateImpl$AutoNightModeManager mAutoTimeNightModeManager;
    private OnBackInvokedCallback mBackCallback;
    private boolean mBaseContextAttached;
    private boolean mClosingActionMenu;
    public final Context mContext;
    private boolean mCreated;
    private DecorContentParent mDecorContentParent;
    public boolean mDestroyed;
    private OnBackInvokedDispatcher mDispatcher;
    private Configuration mEffectiveConfiguration;
    private boolean mEnableDefaultActionBarUp;
    public ViewPropertyAnimatorCompat mFadeAnim;
    private boolean mFeatureIndeterminateProgress;
    private boolean mFeatureProgress;
    private boolean mHandleNativeActionModes;
    public boolean mHasActionBar;
    public final Object mHost;
    public int mInvalidatePanelMenuFeatures;
    public boolean mInvalidatePanelMenuPosted;
    private final Runnable mInvalidatePanelMenuRunnable;
    public boolean mIsFloating;
    private LayoutIncludeDetector mLayoutIncludeDetector;
    private int mLocalNightMode;
    private boolean mLongPressBackDown;
    public MenuInflater mMenuInflater;
    public boolean mOverlayActionBar;
    public boolean mOverlayActionMode;
    private AppCompatDelegateImpl$PanelMenuPresenterCallback mPanelMenuPresenterCallback;
    private AppCompatDelegateImpl$PanelFeatureState[] mPanels;
    private AppCompatDelegateImpl$PanelFeatureState mPreparedPanel;
    public Runnable mShowActionModePopup;
    private View mStatusGuard;
    public ViewGroup mSubDecor;
    private boolean mSubDecorInstalled;
    private Rect mTempRect1;
    private Rect mTempRect2;
    private int mThemeResId;
    private CharSequence mTitle;
    private TextView mTitleView;
    public Window mWindow;
    public boolean mWindowNoTitle;
    public static final String EXCEPTION_HANDLER_MESSAGE_SUFFIX = ". If the resource you are trying to use is a vector resource, you may be referencing it in an unsupported way. See AppCompatDelegate.setCompatVectorFromResourcesEnabled\(\) for more info.";
    private static final boolean IS_PRE_LOLLIPOP;
    private static final boolean sCanReturnDifferentContext;
    private static boolean sInstalledExceptionHandler;
    private static final SimpleArrayMap sLocalNightModes;
    private static final int[] sWindowBackgroundStyleable;

    static {
       AppCompatDelegateImpl.sLocalNightModes = new SimpleArrayMap();
       AppCompatDelegateImpl.IS_PRE_LOLLIPOP = false;
       int[] ointArray = new int[]{0x1010054};
       AppCompatDelegateImpl.sWindowBackgroundStyleable = ointArray;
       AppCompatDelegateImpl.sCanReturnDifferentContext = "robolectric".equals(Build.FINGERPRINT) ^ 0x01;
    }
    public void AppCompatDelegateImpl(Activity p0,AppCompatCallback p1){
       super(p0, null, p1, p0);
    }
    public void AppCompatDelegateImpl(Dialog p0,AppCompatCallback p1){
       super(p0.getContext(), p0.getWindow(), p1, p0);
    }
    public void AppCompatDelegateImpl(Context p0,Activity p1,AppCompatCallback p2){
       super(p0, null, p2, p1);
    }
    public void AppCompatDelegateImpl(Context p0,Window p1,AppCompatCallback p2){
       super(p0, p1, p2, p0);
    }
    private void AppCompatDelegateImpl(Context p0,Window p1,AppCompatCallback p2,Object p3){
       AppCompatActivity uAppCompatAc;
       Integer integer;
       super();
       this.mFadeAnim = null;
       this.mHandleNativeActionModes = true;
       this.mLocalNightMode = -100;
       this.mInvalidatePanelMenuRunnable = new AppCompatDelegateImpl$2(this);
       this.mContext = p0;
       this.mAppCompatCallback = p2;
       this.mHost = p3;
       if (this.mLocalNightMode == -100 && (p3 instanceof Dialog && (uAppCompatAc = this.tryUnwrapContext()) != null)) {
          this.mLocalNightMode = uAppCompatAc.getDelegate().getLocalNightMode();
       }
       if (this.mLocalNightMode == -100) {
          SimpleArrayMap sLocalNightM = AppCompatDelegateImpl.sLocalNightModes;
          if ((integer = sLocalNightM.get(p3.getClass().getName())) != null) {
             this.mLocalNightMode = integer.intValue();
             sLocalNightM.remove(p3.getClass().getName());
          }
       }
       if (p1 != null) {
          this.attachToWindow(p1);
       }
       AppCompatDrawableManager.preload();
       return;
    }
    private boolean applyApplicationSpecificConfig(boolean p0){
       return this.applyApplicationSpecificConfig(p0, true);
    }
    private boolean applyApplicationSpecificConfig(boolean p0,boolean p1){
       AppCompatDelegateImpl tmAutoBatter;
       if (this.mDestroyed != null) {
          return false;
       }
       int i = this.calculateNightMode();
       int i1 = this.mapNightMode(this.mContext, i);
       LocaleListCompat localeListCo = (Build$VERSION.SDK_INT < 33)? this.calculateApplicationLocales(this.mContext): null;
       if (!p1 && localeListCo != null) {
          localeListCo = this.getConfigurationLocales(this.mContext.getResources().getConfiguration());
       }
       p0 = this.updateAppConfiguration(i1, localeListCo, p0);
       if (!i) {
          this.getAutoTimeNightModeManager(this.mContext).setup();
       }else if((tmAutoBatter = this.mAutoTimeNightModeManager) != null){
          tmAutoBatter.cleanup();
       }
       if (i == 3) {
          this.getAutoBatteryNightModeManager(this.mContext).setup();
       }else if((tmAutoBatter = this.mAutoBatteryNightModeManager) != null){
          tmAutoBatter.cleanup();
       }
       return p0;
    }
    private void applyFixedSizeWindow(){
       ContentFrameLayout uContentFram = this.mSubDecor.findViewById(0x1020002);
       View decorView = this.mWindow.getDecorView();
       int paddingLeft = decorView.getPaddingLeft();
       int paddingTop = decorView.getPaddingTop();
       int paddingRight = decorView.getPaddingRight();
       uContentFram.setDecorPadding(paddingLeft, paddingTop, paddingRight, decorView.getPaddingBottom());
       TypedArray typedArray = this.mContext.obtainStyledAttributes(R$styleable.AppCompatTheme);
       typedArray.getValue(R$styleable.AppCompatTheme_windowMinWidthMajor, uContentFram.getMinWidthMajor());
       typedArray.getValue(R$styleable.AppCompatTheme_windowMinWidthMinor, uContentFram.getMinWidthMinor());
       paddingLeft = R$styleable.AppCompatTheme_windowFixedWidthMajor;
       if (typedArray.hasValue(paddingLeft)) {
          typedArray.getValue(paddingLeft, uContentFram.getFixedWidthMajor());
       }
       paddingLeft = R$styleable.AppCompatTheme_windowFixedWidthMinor;
       if (typedArray.hasValue(paddingLeft)) {
          typedArray.getValue(paddingLeft, uContentFram.getFixedWidthMinor());
       }
       paddingLeft = R$styleable.AppCompatTheme_windowFixedHeightMajor;
       if (typedArray.hasValue(paddingLeft)) {
          typedArray.getValue(paddingLeft, uContentFram.getFixedHeightMajor());
       }
       paddingLeft = R$styleable.AppCompatTheme_windowFixedHeightMinor;
       if (typedArray.hasValue(paddingLeft)) {
          typedArray.getValue(paddingLeft, uContentFram.getFixedHeightMinor());
       }
       typedArray.recycle();
       uContentFram.requestLayout();
       return;
    }
    private void attachToWindow(Window p0){
       Drawable drawableIfKn;
       String str = "AppCompat has already installed itself into the Window";
       if (this.mWindow != null) {
          throw new IllegalStateException(str);
       }
       Window$Callback callback = p0.getCallback();
       if (callback instanceof AppCompatDelegateImpl$AppCompatWindowCallback) {
          throw new IllegalStateException(str);
       }
       AppCompatDelegateImpl$AppCompatWindowCallback str1 = new AppCompatDelegateImpl$AppCompatWindowCallback(this, callback);
       this.mAppCompatWindowCallback = str1;
       p0.setCallback(str1);
       TintTypedArray tintTypedArr = TintTypedArray.obtainStyledAttributes(this.mContext, null, AppCompatDelegateImpl.sWindowBackgroundStyleable);
       if ((drawableIfKn = tintTypedArr.getDrawableIfKnown(0)) != null) {
          p0.setBackgroundDrawable(drawableIfKn);
       }
       tintTypedArr.recycle();
       this.mWindow = p0;
       if (Build$VERSION.SDK_INT >= 33 && this.mDispatcher == null) {
          this.setOnBackInvokedDispatcher(null);
       }
       return;
    }
    private int calculateNightMode(){
       AppCompatDelegateImpl tmLocalNight;
       if ((tmLocalNight = this.mLocalNightMode) == -100) {
          tmLocalNight = AppCompatDelegate.getDefaultNightMode();
       }
       return tmLocalNight;
    }
    private void cleanupAutoManagers(){
       AppCompatDelegateImpl tmAutoTimeNi;
       if ((tmAutoTimeNi = this.mAutoTimeNightModeManager) != null) {
          tmAutoTimeNi.cleanup();
       }
       if ((tmAutoTimeNi = this.mAutoBatteryNightModeManager) != null) {
          tmAutoTimeNi.cleanup();
       }
       return;
    }
    private Configuration createOverrideAppConfiguration(Context p0,int p1,LocaleListCompat p2,Configuration p3,boolean p4){
       int i;
       if (p1 != 1) {
          if (p1 != 2) {
             i = (p4)? 0: p0.getApplicationContext().getResources().getConfiguration().uiMode & 0x30;
          }else {
             i = 32;
          }
       }else {
          i = 16;
       }
       Configuration uConfigurati = new Configuration();
       uConfigurati.fontScale = 0;
       if (p3 != null) {
          uConfigurati.setTo(p3);
       }
       uConfigurati.uiMode = i | (uConfigurati.uiMode & 0xcf);
       if (p2 != null) {
          this.setConfigurationLocales(uConfigurati, p2);
       }
       return uConfigurati;
    }
    private ViewGroup createSubDecor(){
       ViewGroup viewGroup;
       ViewGroup viewGroup1;
       TypedArray typedArray = this.mContext.obtainStyledAttributes(R$styleable.AppCompatTheme);
       int appCompatThe = R$styleable.AppCompatTheme_windowActionBar;
       if (typedArray.hasValue(appCompatThe)) {
          boolean b = false;
          int i = 1;
          if (typedArray.getBoolean(R$styleable.AppCompatTheme_windowNoTitle, b)) {
             this.requestWindowFeature(i);
          }else if(typedArray.getBoolean(appCompatThe, b)){
             this.requestWindowFeature(108);
          }
          int i1 = 109;
          if (typedArray.getBoolean(R$styleable.AppCompatTheme_windowActionBarOverlay, b)) {
             this.requestWindowFeature(i1);
          }
          if (typedArray.getBoolean(R$styleable.AppCompatTheme_windowActionModeOverlay, b)) {
             this.requestWindowFeature(10);
          }
          this.mIsFloating = typedArray.getBoolean(R$styleable.AppCompatTheme_android_windowIsFloating, b);
          typedArray.recycle();
          this.ensureWindow();
          this.mWindow.getDecorView();
          LayoutInflater layoutInflat = LayoutInflater.from(this.mContext);
          if (this.mWindowNoTitle == null) {
             if (this.mIsFloating != null) {
                viewGroup = layoutInflat.inflate(R$layout.abc_dialog_title_material, null);
                this.mOverlayActionBar = b;
                this.mHasActionBar = b;
             }else if(this.mHasActionBar != null){
                TypedValue typedValue = new TypedValue();
                this.mContext.getTheme().resolveAttribute(R$attr.actionBarTheme, typedValue, i);
                ContextThemeWrapper uContextThem = (typedValue.resourceId != null)? new ContextThemeWrapper(this.mContext, typedValue.resourceId): this.mContext;
                viewGroup = LayoutInflater.from(uContextThem).inflate(R$layout.abc_screen_toolbar, null);
                DecorContentParent uDecorConten = viewGroup.findViewById(R$id.decor_content_parent);
                this.mDecorContentParent = uDecorConten;
                uDecorConten.setWindowCallback(this.getWindowCallback());
                if (this.mOverlayActionBar != null) {
                   this.mDecorContentParent.initFeature(i1);
                }
                if (this.mFeatureProgress != null) {
                   this.mDecorContentParent.initFeature(2);
                }
                if (this.mFeatureIndeterminateProgress != null) {
                   this.mDecorContentParent.initFeature(5);
                }
             }else {
                viewGroup = null;
             }
          }else if(this.mOverlayActionMode != null){
             viewGroup = layoutInflat.inflate(R$layout.abc_screen_simple_overlay_action_mode, null);
          }else {
             viewGroup = layoutInflat.inflate(R$layout.abc_screen_simple, null);
          }
          if (viewGroup != null) {
             ViewCompat.setOnApplyWindowInsetsListener(viewGroup, new AppCompatDelegateImpl$3(this));
             if (this.mDecorContentParent == null) {
                this.mTitleView = viewGroup.findViewById(R$id.title);
             }
             ViewUtils.makeOptionalFitsSystemWindows(viewGroup);
             ContentFrameLayout uContentFram = viewGroup.findViewById(R$id.action_bar_activity_content);
             if ((viewGroup1 = this.mWindow.findViewById(0x1020002)) != null) {
                while (viewGroup1.getChildCount() > 0) {
                   viewGroup1.removeViewAt(b);
                   uContentFram.addView(viewGroup1.getChildAt(b));
                }
                viewGroup1.setId(-1);
                uContentFram.setId(0x1020002);
                if (viewGroup1 instanceof FrameLayout) {
                   viewGroup1.setForeground(null);
                }
             }
             this.mWindow.setContentView(viewGroup);
             uContentFram.setAttachListener(new AppCompatDelegateImpl$5(this));
             return viewGroup;
          }else {
             throw new IllegalArgumentException("AppCompat does not support the current theme features: { windowActionBar: "+this.mHasActionBar+", windowActionBarOverlay: "+this.mOverlayActionBar+", android:windowIsFloating: "+this.mIsFloating+", windowActionModeOverlay: "+this.mOverlayActionMode+", windowNoTitle: "+this.mWindowNoTitle+" }");
          }
       }else {
          typedArray.recycle();
          throw new IllegalStateException("You need to use a Theme.AppCompat theme \(or descendant\) with this activity.");
       }
    }
    private void ensureSubDecor(){
       AppCompatDelegateImpl tmDecorConte;
       if (this.mSubDecorInstalled == null) {
          this.mSubDecor = this.createSubDecor();
          CharSequence title = this.getTitle();
          if (!TextUtils.isEmpty(title)) {
             if ((tmDecorConte = this.mDecorContentParent) != null) {
                tmDecorConte.setWindowTitle(title);
             }else if(this.peekSupportActionBar() != null){
                this.peekSupportActionBar().setWindowTitle(title);
             }else if((tmDecorConte = this.mTitleView) != null){
                tmDecorConte.setText(title);
             }
          }
          this.applyFixedSizeWindow();
          this.onSubDecorInstalled(this.mSubDecor);
          this.mSubDecorInstalled = true;
          AppCompatDelegateImpl$PanelFeatureState panelState = this.getPanelState(0, 0);
          if (this.mDestroyed == null && (panelState == null && panelState.menu != null)) {
             this.invalidatePanelMenu(108);
          }
       }
       return;
    }
    private void ensureWindow(){
       if (this.mWindow == null) {
          AppCompatDelegateImpl tmHost = this.mHost;
          if (tmHost instanceof Activity) {
             this.attachToWindow(tmHost.getWindow());
          }
       }
       if (this.mWindow != null) {
          return;
       }else {
          throw new IllegalStateException("We have not been given a Window");
       }
    }
    private static Configuration generateConfigDelta(Configuration p0,Configuration p1){
       int sDK_INT;
       Configuration uConfigurati = new Configuration();
       uConfigurati.fontScale = 0;
       if (p1 != null && p0.diff(p1)) {
          Configuration fontScale = p1.fontScale;
          if (fontScale - p0.fontScale) {
             uConfigurati.fontScale = fontScale;
          }
          fontScale = p1.mcc;
          if (p0.mcc != fontScale) {
             uConfigurati.mcc = fontScale;
          }
          fontScale = p1.mnc;
          if (p0.mnc != fontScale) {
             uConfigurati.mnc = fontScale;
          }
          if ((sDK_INT = Build$VERSION.SDK_INT) >= 24) {
             AppCompatDelegateImpl$Api24Impl.generateConfigDelta_locale(p0, p1, uConfigurati);
          }else if(!ObjectsCompat.equals(p0.locale, p1.locale)){
             uConfigurati.locale = p1.locale;
          }
          Configuration touchscreen = p1.touchscreen;
          if (p0.touchscreen != touchscreen) {
             uConfigurati.touchscreen = touchscreen;
          }
          touchscreen = p1.keyboard;
          if (p0.keyboard != touchscreen) {
             uConfigurati.keyboard = touchscreen;
          }
          touchscreen = p1.keyboardHidden;
          if (p0.keyboardHidden != touchscreen) {
             uConfigurati.keyboardHidden = touchscreen;
          }
          touchscreen = p1.navigation;
          if (p0.navigation != touchscreen) {
             uConfigurati.navigation = touchscreen;
          }
          touchscreen = p1.navigationHidden;
          if (p0.navigationHidden != touchscreen) {
             uConfigurati.navigationHidden = touchscreen;
          }
          touchscreen = p1.orientation;
          if (p0.orientation != touchscreen) {
             uConfigurati.orientation = touchscreen;
          }
          touchscreen = p1.screenLayout;
          if (((p0.screenLayout & 0x0f)) != (touchscreen & 0x0f)) {
             uConfigurati.screenLayout = uConfigurati.screenLayout | (touchscreen & 0x0f);
          }
          touchscreen = p1.screenLayout;
          if (((p0.screenLayout & 0x00c0)) != (touchscreen & 0x00c0)) {
             uConfigurati.screenLayout = uConfigurati.screenLayout | (touchscreen & 0x00c0);
          }
          touchscreen = p1.screenLayout;
          if (((p0.screenLayout & 0x30)) != (touchscreen & 0x30)) {
             uConfigurati.screenLayout = uConfigurati.screenLayout | (touchscreen & 0x30);
          }
          touchscreen = p1.screenLayout;
          if (((p0.screenLayout & 0x0300)) != (touchscreen & 0x0300)) {
             uConfigurati.screenLayout = uConfigurati.screenLayout | (touchscreen & 0x0300);
          }
          if (sDK_INT >= 26) {
             AppCompatDelegateImpl$Api26Impl.generateConfigDelta_colorMode(p0, p1, uConfigurati);
          }
          fontScale = p1.uiMode;
          if (((p0.uiMode & 0x0f)) != (fontScale & 0x0f)) {
             uConfigurati.uiMode = uConfigurati.uiMode | (fontScale & 0x0f);
          }
          fontScale = p1.uiMode;
          if (((p0.uiMode & 0x30)) != (fontScale & 0x30)) {
             uConfigurati.uiMode = uConfigurati.uiMode | (fontScale & 0x30);
          }
          fontScale = p1.screenWidthDp;
          if (p0.screenWidthDp != fontScale) {
             uConfigurati.screenWidthDp = fontScale;
          }
          fontScale = p1.screenHeightDp;
          if (p0.screenHeightDp != fontScale) {
             uConfigurati.screenHeightDp = fontScale;
          }
          fontScale = p1.smallestScreenWidthDp;
          if (p0.smallestScreenWidthDp != fontScale) {
             uConfigurati.smallestScreenWidthDp = fontScale;
          }
          p1 = p1.densityDpi;
          if (p0.densityDpi != p1) {
             uConfigurati.densityDpi = p1;
          }
       }
       return uConfigurati;
    }
    private int getActivityHandlesConfigChangesFlags(Context p0){
       PackageManager packageManag;
       int sDK_INT;
       ActivityInfo activityInfo;
       if (this.mActivityHandlesConfigFlagsChecked == null && this.mHost instanceof Activity) {
          if ((packageManag = p0.getPackageManager()) == null) {
             return 0;
          }else if((sDK_INT = Build$VERSION.SDK_INT) >= 29){
             sDK_INT = 0x100c0000;
          }else if(sDK_INT >= 24){
             sDK_INT = 0xc0000;
          }else {
             sDK_INT = 0;
          }
          if ((activityInfo = packageManag.getActivityInfo(new ComponentName(p0, this.mHost.getClass()), sDK_INT)) != null) {
             this.mActivityHandlesConfigFlags = activityInfo.configChanges;
          }
       }
    }
    private AppCompatDelegateImpl$AutoNightModeManager getAutoBatteryNightModeManager(Context p0){
       if (this.mAutoBatteryNightModeManager == null) {
          this.mAutoBatteryNightModeManager = new AppCompatDelegateImpl$AutoBatteryNightModeManager(this, p0);
       }
       return this.mAutoBatteryNightModeManager;
    }
    private AppCompatDelegateImpl$AutoNightModeManager getAutoTimeNightModeManager(Context p0){
       if (this.mAutoTimeNightModeManager == null) {
          this.mAutoTimeNightModeManager = new AppCompatDelegateImpl$AutoTimeNightModeManager(this, TwilightManager.getInstance(p0));
       }
       return this.mAutoTimeNightModeManager;
    }
    private void initWindowDecorActionBar(){
       this.ensureSubDecor();
       if (this.mHasActionBar != null && this.mActionBar == null) {
          AppCompatDelegateImpl tmHost = this.mHost;
          if (tmHost instanceof Activity) {
             this.mActionBar = new WindowDecorActionBar(this.mHost, this.mOverlayActionBar);
          }else if(tmHost instanceof Dialog){
             this.mActionBar = new WindowDecorActionBar(this.mHost);
          }
          if ((tmHost = this.mActionBar) != null) {
             tmHost.setDefaultDisplayHomeAsUpEnabled(this.mEnableDefaultActionBarUp);
          }
       }
       return;
    }
    private boolean initializePanelContent(AppCompatDelegateImpl$PanelFeatureState p0){
       AppCompatDelegateImpl$PanelFeatureState createdPanel = p0.createdPanelView;
       boolean b = true;
       if (createdPanel != null) {
          p0.shownPanelView = createdPanel;
          return b;
       }else if(p0.menu == null){
          return false;
       }else if(this.mPanelMenuPresenterCallback == null){
          this.mPanelMenuPresenterCallback = new AppCompatDelegateImpl$PanelMenuPresenterCallback(this);
       }
       View listMenuView = p0.getListMenuView(this.mPanelMenuPresenterCallback);
       p0.shownPanelView = listMenuView;
       if (listMenuView == null) {
          b = false;
       }
       return b;
    }
    private boolean initializePanelDecor(AppCompatDelegateImpl$PanelFeatureState p0){
       p0.setStyle(this.getActionBarThemedContext());
       p0.decorView = new AppCompatDelegateImpl$ListMenuDecorView(this, p0.listPresenterContext);
       p0.gravity = 81;
       return true;
    }
    private boolean initializePanelMenu(AppCompatDelegateImpl$PanelFeatureState p0){
       AppCompatDelegateImpl$PanelFeatureState panelFeature;
       Resources$Theme theme1;
       AppCompatDelegateImpl tmContext = this.mContext;
       if ((panelFeature = p0.featureId) == null || (panelFeature == 108 && this.mDecorContentParent != null)) {
          TypedValue typedValue = new TypedValue();
          Resources$Theme theme = tmContext.getTheme();
          theme.resolveAttribute(R$attr.actionBarTheme, typedValue, true);
          if (typedValue.resourceId != null) {
             theme1 = tmContext.getResources().newTheme();
             theme1.setTo(theme);
             theme1.applyStyle(typedValue.resourceId, true);
             theme1.resolveAttribute(R$attr.actionBarWidgetTheme, typedValue, true);
          }else {
             theme.resolveAttribute(R$attr.actionBarWidgetTheme, typedValue, true);
             theme1 = null;
          }
          if (typedValue.resourceId != null) {
             if (theme1 == null) {
                theme1 = tmContext.getResources().newTheme();
                theme1.setTo(theme);
             }
             theme1.applyStyle(typedValue.resourceId, true);
          }
          if (theme1 != null) {
             ContextThemeWrapper uContextThem = new ContextThemeWrapper(tmContext, 0);
             uContextThem.getTheme().setTo(theme1);
             tmContext = uContextThem;
          }
       }
       MenuBuilder menuBuilder = new MenuBuilder(tmContext);
       menuBuilder.setCallback(this);
       p0.setMenu(menuBuilder);
       return true;
    }
    private void invalidatePanelMenu(int p0){
       this.mInvalidatePanelMenuFeatures = (1 << p0) | this.mInvalidatePanelMenuFeatures;
       if (this.mInvalidatePanelMenuPosted == null) {
          ViewCompat.postOnAnimation(this.mWindow.getDecorView(), this.mInvalidatePanelMenuRunnable);
          this.mInvalidatePanelMenuPosted = true;
       }
       return;
    }
    private boolean onKeyDownPanel(int p0,KeyEvent p1){
       if (!p1.getRepeatCount()) {
          AppCompatDelegateImpl$PanelFeatureState panelState = this.getPanelState(p0, true);
          if (panelState.isOpen == null) {
             return this.preparePanel(panelState, p1);
          }
       }
       return false;
    }
    private boolean onKeyUpPanel(int p0,KeyEvent p1){
       AppCompatDelegateImpl tmDecorConte;
       AudioManager systemServic;
       AppCompatDelegateImpl$PanelFeatureState isOpen;
       boolean b1;
       if (this.mActionMode != null) {
          return false;
       }
       boolean b = true;
       AppCompatDelegateImpl$PanelFeatureState panelState = this.getPanelState(p0, b);
       if (!p0 && ((tmDecorConte = this.mDecorContentParent) != null && (tmDecorConte.canShowOverflowMenu() && !ViewConfiguration.get(this.mContext).hasPermanentMenuKey()))) {
          if (!this.mDecorContentParent.isOverflowMenuShowing()) {
             if (this.mDestroyed == null && this.preparePanel(panelState, p1)) {
                b = this.mDecorContentParent.showOverflowMenu();
             }else {
             label_0062 :
                b = false;
             }
          }else {
             b = this.mDecorContentParent.hideOverflowMenu();
          }
       }else if((isOpen = panelState.isOpen) == null && panelState.isHandled == null){
          if (panelState.isPrepared != null) {
             if (panelState.refreshMenuContent != null) {
                panelState.isPrepared = false;
                b1 = this.preparePanel(panelState, p1);
             }else {
                b1 = true;
             }
             if (b1) {
                this.openPanel(panelState, p1);
             }else {
                goto label_0062 ;
             }
          }else {
             goto label_0062 ;
          }
       }else {
          this.closePanel(panelState, b);
          b = isOpen;
       }
       if (b != null && (systemServic = this.mContext.getApplicationContext().getSystemService("audio")) != null) {
          systemServic.playSoundEffect(false);
       }
       return b;
    }
    private void openPanel(AppCompatDelegateImpl$PanelFeatureState p0,KeyEvent p1){
       Window$Callback windowCallba;
       WindowManager systemServic;
       ViewGroup$LayoutParams layoutParams;
       int i1;
       WindowManager$LayoutParams false;
       if (p0.isOpen == null && this.mDestroyed == null) {
          if (p0.featureId == null && ((this.mContext.getResources().getConfiguration().screenLayout & 0x0f)) == 4) {
             return;
          }else if((windowCallba = this.getWindowCallback()) != null && !windowCallba.onMenuOpened(p0.featureId, p0.menu)){
             this.closePanel(p0, true);
             return;
          }else if((systemServic = this.mContext.getSystemService("window")) == null){
             return;
          }else if(!this.preparePanel(p0, p1)){
             return;
          }else {
             AppCompatDelegateImpl$PanelFeatureState decorView = p0.decorView;
             int i = -2;
             if (decorView != null && p0.refreshDecorView == null) {
                if ((decorView = p0.createdPanelView) != null && ((layoutParams = decorView.getLayoutParams()) != null && layoutParams.width == -1)) {
                   i1 = -1;
                label_00c8 :
                   p0.isHandled = false;
                   false = new WindowManager$LayoutParams(i1, -2, p0.x, p0.y, 1002, 0x820000, -3);
                   layoutParams.gravity = p0.gravity;
                   layoutParams.windowAnimations = p0.windowAnimations;
                   systemServic.addView(p0.decorView, false);
                   if ((p0.isOpen = true) == null) {
                      this.updateBackInvokedCallbackState();
                   }
                   return;
                }
             }else if(decorView == null){
                if (!this.initializePanelDecor(p0) || p0.decorView == null) {
                   return;
                }
             }else if(p0.refreshDecorView != null && decorView.getChildCount() > 0){
                p0.decorView.removeAllViews();
             }
             if (this.initializePanelContent(p0) && p0.hasPanelItems()) {
                if ((layoutParams = p0.shownPanelView.getLayoutParams()) == null) {
                   layoutParams = new ViewGroup$LayoutParams(i, i);
                }
                p0.decorView.setBackgroundResource(p0.background);
                ViewParent parent = p0.shownPanelView.getParent();
                if (parent instanceof ViewGroup) {
                   parent.removeView(p0.shownPanelView);
                }
                p0.decorView.addView(p0.shownPanelView, layoutParams);
                if (!p0.shownPanelView.hasFocus()) {
                   p0.shownPanelView.requestFocus();
                }
             }else {
                p0.refreshDecorView = true;
             }
             i1 = -2;
             goto label_00c8 ;
          }
       }
       return;
    }
    private boolean performPanelShortcut(AppCompatDelegateImpl$PanelFeatureState p0,int p1,KeyEvent p2,int p3){
       AppCompatDelegateImpl$PanelFeatureState menu;
       boolean b = false;
       if (p2.isSystem()) {
          return b;
       }
       if (p0.isPrepared != null || (this.preparePanel(p0, p2) && (menu = p0.menu) != null)) {
          b = menu.performShortcut(p1, p2, p3);
       }
       if (b && (!((p3 & 0x01)) && this.mDecorContentParent == null)) {
          this.closePanel(p0, true);
       }
       return b;
    }
    private boolean preparePanel(AppCompatDelegateImpl$PanelFeatureState p0,KeyEvent p1){
       AppCompatDelegateImpl tmPreparedPa;
       Window$Callback windowCallba;
       AppCompatDelegateImpl tmDecorConte;
       AppCompatDelegateImpl$PanelFeatureState menu;
       AppCompatDelegateImpl tmDecorConte1;
       AppCompatDelegateImpl tmDecorConte2;
       boolean deviceId;
       if (this.mDestroyed != null) {
          return false;
       }
       if (p0.isPrepared != null) {
          return true;
       }
       if ((tmPreparedPa = this.mPreparedPanel) != null && tmPreparedPa != p0) {
          this.closePanel(tmPreparedPa, false);
       }
       if ((windowCallba = this.getWindowCallback()) != null) {
          p0.createdPanelView = windowCallba.onCreatePanelView(p0.featureId);
       }
       AppCompatDelegateImpl$PanelFeatureState panelFeature = ((panelFeature = p0.featureId) != null && panelFeature != 108)? 0: 1;
       if (panelFeature && (tmDecorConte = this.mDecorContentParent) != null) {
          tmDecorConte.setMenuPrepared();
       }
       if (p0.createdPanelView == null && (!panelFeature && this.peekSupportActionBar() instanceof ToolbarActionBar)) {
          if ((menu = p0.menu) == null || p0.refreshMenuContent != null) {
             if (menu == null && (!this.initializePanelMenu(p0) && p0.menu != null)) {
                return false;
             }else if(panelFeature && this.mDecorContentParent != null){
                if (this.mActionMenuPresenterCallback == null) {
                   this.mActionMenuPresenterCallback = new AppCompatDelegateImpl$ActionMenuPresenterCallback(this);
                }
                this.mDecorContentParent.setMenu(p0.menu, this.mActionMenuPresenterCallback);
             }
             p0.menu.stopDispatchingItemsChanged();
             if (!windowCallba.onCreatePanelMenu(p0.featureId, p0.menu)) {
                p0.setMenu(null);
                if (panelFeature && (tmDecorConte1 = this.mDecorContentParent) != null) {
                   tmDecorConte1.setMenu(null, this.mActionMenuPresenterCallback);
                }
                return false;
             }else {
                p0.refreshMenuContent = false;
             }
          }
          p0.menu.stopDispatchingItemsChanged();
          if ((menu = p0.frozenActionViewState) != null) {
             p0.menu.restoreActionViewStates(menu);
             p0.frozenActionViewState = null;
          }
          if (!windowCallba.onPreparePanel(false, p0.createdPanelView, p0.menu)) {
             if (panelFeature && (tmDecorConte2 = this.mDecorContentParent) != null) {
                tmDecorConte2.setMenu(null, this.mActionMenuPresenterCallback);
             }
             p0.menu.startDispatchingItemsChanged();
             return false;
          }else if(p1 != null){
             deviceId = p1.getDeviceId();
          }else {
             deviceId = -1;
          }
          deviceId = (KeyCharacterMap.load(deviceId).getKeyboardType() != 1)? true: false;
          p0.qwertyMode = deviceId;
          p0.menu.setQwertyMode(deviceId);
          p0.menu.startDispatchingItemsChanged();
       }
       p0.isPrepared = true;
       p0.isHandled = false;
       this.mPreparedPanel = p0;
       return true;
    }
    private void reopenMenu(boolean p0){
       AppCompatDelegateImpl$PanelFeatureState panelState;
       AppCompatDelegateImpl$PanelFeatureState menu;
       AppCompatDelegateImpl tmDecorConte = this.mDecorContentParent;
       int i = 1;
       if (tmDecorConte != null && (tmDecorConte.canShowOverflowMenu() && (!ViewConfiguration.get(this.mContext).hasPermanentMenuKey() && !this.mDecorContentParent.isOverflowMenuShowPending()))) {
          Window$Callback windowCallba = this.getWindowCallback();
          if (this.mDecorContentParent.isOverflowMenuShowing() && p0) {
             this.mDecorContentParent.hideOverflowMenu();
             if (this.mDestroyed == null) {
                windowCallba.onPanelClosed(108, this.getPanelState(0, i).menu);
             }
          }else if(windowCallba != null && this.mDestroyed == null){
             if (this.mInvalidatePanelMenuPosted != null && ((this.mInvalidatePanelMenuFeatures & i))) {
                this.mWindow.getDecorView().removeCallbacks(this.mInvalidatePanelMenuRunnable);
                this.mInvalidatePanelMenuRunnable.run();
             }
             panelState = this.getPanelState(0, i);
             if ((menu = panelState.menu) != null && (panelState.refreshMenuContent == null && windowCallba.onPreparePanel(0, panelState.createdPanelView, menu))) {
                windowCallba.onMenuOpened(108, panelState.menu);
                this.mDecorContentParent.showOverflowMenu();
             }
          }
          return;
       }else {
          panelState = this.getPanelState(0, i);
          panelState.refreshDecorView = i;
          this.closePanel(panelState, 0);
          this.openPanel(panelState, null);
          return;
       }
    }
    private int sanitizeWindowFeatureId(int p0){
       if (p0 == 8) {
          return 108;
       }
       if (p0 == 9) {
          p0 = 109;
       }
       return p0;
    }
    private boolean shouldInheritContext(ViewParent p0){
       if (p0 == null) {
          return false;
       }
       View decorView = this.mWindow.getDecorView();
       while (true) {
          if (p0 == null) {
             return true;
          }
          if (p0 != decorView && (p0 instanceof View && !p0.isAttachedToWindow())) {
             p0 = p0.getParent();
          }else {
             break ;
          }
       }
       return false;
    }
    private void throwFeatureRequestIfSubDecorInstalled(){
       if (this.mSubDecorInstalled == null) {
          return;
       }
       throw new AndroidRuntimeException("Window feature must be requested before adding content");
    }
    private AppCompatActivity tryUnwrapContext(){
       for (AppCompatDelegateImpl tmContext = this.mContext; tmContext != null; tmContext = tmContext.getBaseContext()) {
          if (tmContext instanceof AppCompatActivity) {
             return tmContext;
          }
          if (tmContext instanceof ContextWrapper) {
          }else {
             break ;
          }
       }
       return null;
    }
    private void updateActivityConfiguration(Configuration p0){
       AppCompatDelegateImpl tmHost = this.mHost;
       if (tmHost instanceof LifecycleOwner) {
          if (tmHost.getLifecycle().getCurrentState().isAtLeast(Lifecycle$State.CREATED)) {
             tmHost.onConfigurationChanged(p0);
          }
       }else if(this.mCreated != null && this.mDestroyed == null){
          tmHost.onConfigurationChanged(p0);
       }
       return;
    }
    private boolean updateAppConfiguration(int p0,LocaleListCompat p1,boolean p2){
       AppCompatDelegateImpl tmEffectiveC;
       AppCompatDelegateImpl tmHost;
       Configuration uConfigurati = this.createOverrideAppConfiguration(this.mContext, p0, p1, null, false);
       int activityHand = this.getActivityHandlesConfigChangesFlags(this.mContext);
       if ((tmEffectiveC = this.mEffectiveConfiguration) == null) {
          tmEffectiveC = this.mContext.getResources().getConfiguration();
       }
       int i = tmEffectiveC.uiMode & 0x30;
       int i1 = uConfigurati.uiMode & 0x30;
       LocaleListCompat configuratio = this.getConfigurationLocales(tmEffectiveC);
       LocaleListCompat localeListCo = (p1 == null)? null: this.getConfigurationLocales(uConfigurati);
       boolean b = false;
       i = (i != i1)? 512: 0;
       if (localeListCo != null && !configuratio.equals(localeListCo)) {
          i = i | 0x2004;
       }
       boolean b1 = true;
       if ((((~ activityHand) & i)) && (p2 && (this.mBaseContextAttached != null && (AppCompatDelegateImpl.sCanReturnDifferentContext && this.mCreated == null)))) {
          tmHost = this.mHost;
          if (tmHost instanceof Activity && !tmHost.isChild()) {
             if (Build$VERSION.SDK_INT >= 31 && ((i & 0x2000))) {
                this.mHost.getWindow().getDecorView().setLayoutDirection(uConfigurati.getLayoutDirection());
             }
             ActivityCompat.recreate(this.mHost);
             tmHost = 1;
          label_008d :
             if (!tmHost && i) {
                if (((i & activityHand)) == i) {
                   b = true;
                }
                this.updateResourcesConfiguration(i1, localeListCo, b, null);
             }else {
                b1 = tmHost;
             }
             if (b1) {
                tmHost = this.mHost;
                if (tmHost instanceof AppCompatActivity) {
                   if ((i & 0x0200)) {
                      tmHost.onNightModeChanged(p0);
                   }
                   if ((i & 0x04)) {
                      this.mHost.onLocalesChanged(p1);
                   }
                }
             }
             if (localeListCo != null) {
                this.setDefaultLocalesForLocaleList(this.getConfigurationLocales(this.mContext.getResources().getConfiguration()));
             }
             return b1;
          }
       }
       tmHost = 0;
       goto label_008d ;
    }
    private void updateResourcesConfiguration(int p0,LocaleListCompat p1,boolean p2,Configuration p3){
       AppCompatDelegateImpl tmThemeResId;
       Resources resources = this.mContext.getResources();
       Configuration uConfigurati = new Configuration(resources.getConfiguration());
       if (p3 != null) {
          uConfigurati.updateFrom(p3);
       }
       uConfigurati.uiMode = p0 | (resources.getConfiguration().uiMode & 0xcf);
       if (p1 != null) {
          this.setConfigurationLocales(uConfigurati, p1);
       }
       resources.updateConfiguration(uConfigurati, null);
       if ((p0 = Build$VERSION.SDK_INT) < 26) {
          ResourcesFlusher.flush(resources);
       }
       if ((tmThemeResId = this.mThemeResId) != null) {
          this.mContext.setTheme(tmThemeResId);
          if (p0 >= 23) {
             this.mContext.getTheme().applyStyle(this.mThemeResId, true);
          }
       }
       if (p2 && this.mHost instanceof Activity) {
          this.updateActivityConfiguration(uConfigurati);
       }
       return;
    }
    private void updateStatusGuardColor(View p0){
       int color = ((ViewCompat.getWindowSystemUiVisibility(p0) & 0x2000))? ContextCompat.getColor(this.mContext, R$color.abc_decor_view_status_guard_light): ContextCompat.getColor(this.mContext, R$color.abc_decor_view_status_guard);
       p0.setBackgroundColor(color);
       return;
    }
    public void addContentView(View p0,ViewGroup$LayoutParams p1){
       this.ensureSubDecor();
       this.mSubDecor.findViewById(0x1020002).addView(p0, p1);
       this.mAppCompatWindowCallback.bypassOnContentChanged(this.mWindow.getCallback());
    }
    public boolean applyAppLocales(){
       if (AppCompatDelegate.isAutoStorageOptedIn(this.mContext) && (AppCompatDelegate.getRequestedAppLocales() != null && !AppCompatDelegate.getRequestedAppLocales().equals(AppCompatDelegate.getStoredAppLocales()))) {
          this.asyncExecuteSyncRequestedAndStoredLocales(this.mContext);
       }
       return this.applyApplicationSpecificConfig(true);
    }
    public boolean applyDayNight(){
       return this.applyApplicationSpecificConfig(true);
    }
    public Context attachBaseContext2(Context p0){
       this.mBaseContextAttached = true;
       int i = this.mapNightMode(p0, this.calculateNightMode());
       if (AppCompatDelegate.isAutoStorageOptedIn(p0)) {
          AppCompatDelegate.syncRequestedAndStoredLocales(p0);
       }
       LocaleListCompat localeListCo = this.calculateApplicationLocales(p0);
       if (p0 instanceof ContextThemeWrapper) {
          Configuration uConfigurati = this.createOverrideAppConfiguration(p0, i, localeListCo, null, false);
          try{
             p0.applyOverrideConfiguration(uConfigurati);
             return p0;
          }catch(java.lang.IllegalStateException e0){
          }
          if (p0 instanceof ContextThemeWrapper) {
             uConfigurati = this.createOverrideAppConfiguration(p0, e0, localeListCo, null, false);
             try{
                p0.applyOverrideConfiguration(uConfigurati);
                return p0;
             }catch(java.lang.IllegalStateException e0){
             }
             if (!AppCompatDelegateImpl.sCanReturnDifferentContext) {
                return super.attachBaseContext2(p0);
             }else {
                uConfigurati = new Configuration();
                uConfigurati.uiMode = -1;
                uConfigurati.fontScale = 0;
                uConfigurati = p0.createConfigurationContext(uConfigurati).getResources().getConfiguration();
                Configuration configuratio = p0.getResources().getConfiguration();
                uConfigurati.uiMode = configuratio.uiMode;
                uConfigurati = (!uConfigurati.equals(configuratio))? AppCompatDelegateImpl.generateConfigDelta(uConfigurati, configuratio): 0;
                Configuration uConfigurati1 = uConfigurati;
                Configuration uConfigurati2 = this.createOverrideAppConfiguration(p0, e0, localeListCo, uConfigurati1, true);
                super(p0, R$style.Theme_AppCompat_Empty);
                try{
                   this.applyOverrideConfiguration(uConfigurati2);
                   if (p0.getTheme() != null) {
                      ResourcesCompat$ThemeCompat.rebase(this.getTheme());
                   }
                   return super.attachBaseContext2(this);
                }catch(java.lang.NullPointerException e0){
                }
             }
          }else {
          }
       }else {
       }
    }
    public LocaleListCompat calculateApplicationLocales(Context p0){
       LocaleListCompat requestedApp;
       LocaleListCompat localeListCo1;
       int sDK_INT = Build$VERSION.SDK_INT;
       LocaleListCompat localeListCo = null;
       if (sDK_INT >= 33) {
          return localeListCo;
       }
       if ((requestedApp = AppCompatDelegate.getRequestedAppLocales()) == null) {
          return localeListCo;
       }
       LocaleListCompat configuratio = this.getConfigurationLocales(p0.getApplicationContext().getResources().getConfiguration());
       if (sDK_INT >= 24) {
          localeListCo1 = LocaleOverlayHelper.combineLocalesIfOverlayExists(requestedApp, configuratio);
       }else if(requestedApp.isEmpty()){
          localeListCo1 = LocaleListCompat.getEmptyLocaleList();
       }else {
          localeListCo1 = LocaleListCompat.forLanguageTags(AppCompatDelegateImpl$Api21Impl.toLanguageTag(requestedApp.get(0)));
       }
       if (!localeListCo1.isEmpty()) {
          configuratio = localeListCo1;
       }
       return configuratio;
    }
    public void callOnPanelClosed(int p0,AppCompatDelegateImpl$PanelFeatureState p1,Menu p2){
       object oobject;
       AppCompatDelegateImpl$PanelFeatureState menu;
       if (p2 == null) {
          if (p1 == null && p0 >= 0) {
             AppCompatDelegateImpl tmPanels = this.mPanels;
             if (p0 < tmPanels.length) {
                oobject = tmPanels[p0];
             }
          }
          if (oobject != null) {
             menu = oobject.menu;
          }
       }
       if (oobject != null && oobject.isOpen == null) {
          return;
       }else if(this.mDestroyed == null){
          this.mAppCompatWindowCallback.bypassOnPanelClosed(this.mWindow.getCallback(), p0, menu);
       }
       return;
    }
    public void checkCloseActionMenu(MenuBuilder p0){
       Window$Callback windowCallba;
       if (this.mClosingActionMenu != null) {
          return;
       }
       this.mClosingActionMenu = true;
       this.mDecorContentParent.dismissPopups();
       if ((windowCallba = this.getWindowCallback()) != null && this.mDestroyed == null) {
          windowCallba.onPanelClosed(108, p0);
       }
       this.mClosingActionMenu = false;
       return;
    }
    public void closePanel(int p0){
       this.closePanel(this.getPanelState(p0, true), true);
    }
    public void closePanel(AppCompatDelegateImpl$PanelFeatureState p0,boolean p1){
       AppCompatDelegateImpl tmDecorConte;
       WindowManager systemServic;
       AppCompatDelegateImpl$PanelFeatureState decorView;
       if (p1 && (p0.featureId == null && ((tmDecorConte = this.mDecorContentParent) != null && tmDecorConte.isOverflowMenuShowing()))) {
          this.checkCloseActionMenu(p0.menu);
          return;
       }else if((systemServic = this.mContext.getSystemService("window")) != null && (p0.isOpen != null && (decorView = p0.decorView) != null)){
          systemServic.removeView(decorView);
          if (p1) {
             this.callOnPanelClosed(p0.featureId, p0, null);
          }
       }
       p0.isPrepared = false;
       p0.isHandled = false;
       p0.isOpen = false;
       p0.shownPanelView = null;
       p0.refreshDecorView = true;
       if (this.mPreparedPanel == p0) {
          this.mPreparedPanel = null;
       }
       if (p0.featureId == null) {
          this.updateBackInvokedCallbackState();
       }
       return;
    }
    public View createView(View p0,String p1,Context p2,AttributeSet p3){
       String str;
       boolean iS_PRE_LOLLI;
       boolean b;
       int i = 0;
       if (this.mAppCompatViewInflater == null) {
          TypedArray typedArray = this.mContext.obtainStyledAttributes(R$styleable.AppCompatTheme);
          str = typedArray.getString(R$styleable.AppCompatTheme_viewInflaterClass);
          typedArray.recycle();
          if (str == null) {
             this.mAppCompatViewInflater = new AppCompatViewInflater();
          }else {
             Class[] uClassArray = new Class[i];
             Object[] objArray = new Object[i];
             this.mAppCompatViewInflater = this.mContext.getClassLoader().loadClass(str).getDeclaredConstructor(uClassArray).newInstance(objArray);
          }
       }
       if (iS_PRE_LOLLI = AppCompatDelegateImpl.IS_PRE_LOLLIPOP) {
          if (this.mLayoutIncludeDetector == null) {
             this.mLayoutIncludeDetector = new LayoutIncludeDetector();
          }
          str = 1;
          if (this.mLayoutIncludeDetector.detect(p3)) {
             b = true;
          }else if(p3 instanceof XmlPullParser){
             if (p3.getDepth() > str) {
                i = 1;
             }
          }else {
             i = this.shouldInheritContext(p0);
          }
          b = i;
       }else {
          b = false;
       }
       return this.mAppCompatViewInflater.createView(p0, p1, p2, p3, b, iS_PRE_LOLLI, true, VectorEnabledTintResources.shouldBeUsed());
    }
    public void dismissPopups(){
       AppCompatDelegateImpl tmDecorConte;
       AppCompatDelegateImpl$PanelFeatureState panelState;
       if ((tmDecorConte = this.mDecorContentParent) != null) {
          tmDecorConte.dismissPopups();
       }
       if (this.mActionModePopup != null) {
          try{
             this.mWindow.getDecorView().removeCallbacks(this.mShowActionModePopup);
             if (this.mActionModePopup.isShowing()) {
                this.mActionModePopup.dismiss();
             }
             this.mActionModePopup = null;
          }catch(java.lang.IllegalArgumentException e0){
          }
       }
       this.endOnGoingFadeAnimation();
       if ((panelState = this.getPanelState(0, 0)) != null && (panelState = panelState.menu) != null) {
          panelState.close();
       }
       return;
    }
    public boolean dispatchKeyEvent(KeyEvent p0){
       View decorView;
       AppCompatDelegateImpl tmHost = this.mHost;
       if (tmHost instanceof KeyEventDispatcher$Component || (tmHost instanceof AppCompatDialog && ((decorView = this.mWindow.getDecorView()) != null && KeyEventDispatcher.dispatchBeforeHierarchy(decorView, p0)))) {
          return true;
       }
       if (p0.getKeyCode() == 82 && this.mAppCompatWindowCallback.bypassDispatchKeyEvent(this.mWindow.getCallback(), p0)) {
          return true;
       }
       int keyCode = p0.getKeyCode();
       boolean b = (!p0.getAction())? this.onKeyDown(keyCode, p0): this.onKeyUp(keyCode, p0);
       return b;
    }
    public void doInvalidatePanelMenu(int p0){
       AppCompatDelegateImpl$PanelFeatureState panelState1;
       boolean b = true;
       AppCompatDelegateImpl$PanelFeatureState panelState = this.getPanelState(p0, b);
       if (panelState.menu != null) {
          Bundle uBundle = new Bundle();
          panelState.menu.saveActionViewStates(uBundle);
          if (uBundle.size() > 0) {
             panelState.frozenActionViewState = uBundle;
          }
          panelState.menu.stopDispatchingItemsChanged();
          panelState.menu.clear();
       }
       panelState.refreshMenuContent = b;
       panelState.refreshDecorView = b;
       if (p0 == 108 || (!p0 && this.mDecorContentParent != null)) {
          p0 = 0;
          if ((panelState1 = this.getPanelState(p0, p0)) != null) {
             panelState1.isPrepared = p0;
             this.preparePanel(panelState1, null);
          }
       }
       return;
    }
    public void endOnGoingFadeAnimation(){
       AppCompatDelegateImpl tmFadeAnim;
       if ((tmFadeAnim = this.mFadeAnim) != null) {
          tmFadeAnim.cancel();
       }
       return;
    }
    public AppCompatDelegateImpl$PanelFeatureState findMenuPanel(Menu p0){
       object oobject;
       AppCompatDelegateImpl tmPanels = this.mPanels;
       int i = 0;
       int len = (tmPanels != null)? tmPanels.length: 0;
       while (true) {
          if (i >= len) {
             return null;
          }
          if ((oobject = tmPanels[i]) != null && oobject.menu == p0) {
             break ;
          }else {
             i = i + 1;
          }
       }
       return oobject;
    }
    public View findViewById(int p0){
       this.ensureSubDecor();
       return this.mWindow.findViewById(p0);
    }
    public final Context getActionBarThemedContext(){
       ActionBar supportActio;
       Context themedContex = ((supportActio = this.getSupportActionBar()) != null)? supportActio.getThemedContext(): null;
       if (themedContex == null) {
          themedContex = this.mContext;
       }
       return themedContex;
    }
    public final AppCompatDelegateImpl$AutoNightModeManager getAutoTimeNightModeManager(){
       return this.getAutoTimeNightModeManager(this.mContext);
    }
    public LocaleListCompat getConfigurationLocales(Configuration p0){
       if (Build$VERSION.SDK_INT >= 24) {
          return AppCompatDelegateImpl$Api24Impl.getLocales(p0);
       }
       return LocaleListCompat.forLanguageTags(AppCompatDelegateImpl$Api21Impl.toLanguageTag(p0.locale));
    }
    public Context getContextForDelegate(){
       return this.mContext;
    }
    public final ActionBarDrawerToggle$Delegate getDrawerToggleDelegate(){
       return new AppCompatDelegateImpl$ActionBarDrawableToggleImpl(this);
    }
    public int getLocalNightMode(){
       return this.mLocalNightMode;
    }
    public MenuInflater getMenuInflater(){
       AppCompatDelegateImpl tmActionBar;
       if (this.mMenuInflater == null) {
          this.initWindowDecorActionBar();
          Context themedContex = ((tmActionBar = this.mActionBar) != null)? tmActionBar.getThemedContext(): this.mContext;
          this.mMenuInflater = new SupportMenuInflater(themedContex);
       }
       return this.mMenuInflater;
    }
    public AppCompatDelegateImpl$PanelFeatureState getPanelState(int p0,boolean p1){
       AppCompatDelegateImpl tmPanels;
       object oobject;
       if ((tmPanels = this.mPanels) == null || tmPanels.length <= p0) {
          AppCompatDelegateImpl$PanelFeatureState[] panelFeature = new AppCompatDelegateImpl$PanelFeatureState[(p0 + 1)];
          if (tmPanels != null) {
             System.arraycopy(tmPanels, 0, panelFeature, 0, tmPanels.length);
          }
          this.mPanels = panelFeature;
          tmPanels = panelFeature;
       }
       if ((oobject = tmPanels[p0]) == null) {
          oobject = new AppCompatDelegateImpl$PanelFeatureState(p0);
          tmPanels[p0] = oobject;
       }
       return oobject;
    }
    public ViewGroup getSubDecor(){
       return this.mSubDecor;
    }
    public ActionBar getSupportActionBar(){
       this.initWindowDecorActionBar();
       return this.mActionBar;
    }
    public final CharSequence getTitle(){
       AppCompatDelegateImpl tmHost = this.mHost;
       if (tmHost instanceof Activity) {
          return tmHost.getTitle();
       }
       return this.mTitle;
    }
    public final Window$Callback getWindowCallback(){
       return this.mWindow.getCallback();
    }
    public boolean hasWindowFeature(int p0){
       AppCompatDelegateImpl tmOverlayAct;
       int i = this.sanitizeWindowFeatureId(p0);
       boolean b = true;
       if (i != b) {
          if (i != 2) {
             if (i != 5) {
                if (i != 10) {
                   if (i != 108) {
                      i = (i != 109)? 0: this.mOverlayActionBar;
                   }else {
                      tmOverlayAct = this.mHasActionBar;
                   }
                }else {
                   tmOverlayAct = this.mOverlayActionMode;
                }
             }else {
                tmOverlayAct = this.mFeatureIndeterminateProgress;
             }
          }else {
             tmOverlayAct = this.mFeatureProgress;
          }
       }else {
          tmOverlayAct = this.mWindowNoTitle;
       }
       if (i == null && !this.mWindow.hasFeature(p0)) {
          b = false;
       }
       return b;
    }
    public void installViewFactory(){
       LayoutInflater layoutInflat = LayoutInflater.from(this.mContext);
       if (layoutInflat.getFactory() == null) {
          LayoutInflaterCompat.setFactory2(layoutInflat, this);
       }else {
          v0 = layoutInflat.getFactory2() instanceof AppCompatDelegateImpl;
       }
       return;
    }
    public void invalidateOptionsMenu(){
       if (this.peekSupportActionBar() != null && !this.getSupportActionBar().invalidateOptionsMenu()) {
          this.invalidatePanelMenu(0);
       }
       return;
    }
    public boolean isHandleNativeActionModesEnabled(){
       return this.mHandleNativeActionModes;
    }
    public int mapNightMode(Context p0,int p1){
       if (p1 == -100) {
          return -1;
       }
       if (p1 != -1) {
          if (p1) {
             if (p1 != 1 && p1 != 2) {
                if (p1 == 3) {
                   return this.getAutoBatteryNightModeManager(p0).getApplyableNightMode();
                }else {
                   throw new IllegalStateException("Unknown value set for night mode. Please use one of the MODE_NIGHT values from AppCompatDelegate.");
                }
             }
          }else if(Build$VERSION.SDK_INT >= 23 && !p0.getApplicationContext().getSystemService("uimode").getNightMode()){
             return -1;
          }else {
             return this.getAutoTimeNightModeManager(p0).getApplyableNightMode();
          }
       }
       return p1;
    }
    public boolean onBackPressed(){
       AppCompatDelegateImpl$PanelFeatureState panelState;
       ActionBar supportActio;
       AppCompatDelegateImpl tmLongPressB = this.mLongPressBackDown;
       this.mLongPressBackDown = false;
       if ((panelState = this.getPanelState(false, false)) != null && panelState.isOpen != null) {
          if (tmLongPressB == null) {
             this.closePanel(panelState, true);
          }
          return true;
       }else if((tmLongPressB = this.mActionMode) != null){
          tmLongPressB.finish();
          return true;
       }else if((supportActio = this.getSupportActionBar()) != null && supportActio.collapseActionView()){
          return true;
       }else {
          return false;
       }
    }
    public void onConfigurationChanged(Configuration p0){
       ActionBar supportActio;
       if (this.mHasActionBar != null && (this.mSubDecorInstalled != null && (supportActio = this.getSupportActionBar()) != null)) {
          supportActio.onConfigurationChanged(p0);
       }
       AppCompatDrawableManager.get().onConfigurationChanged(this.mContext);
       this.mEffectiveConfiguration = new Configuration(this.mContext.getResources().getConfiguration());
       this.applyApplicationSpecificConfig(false, false);
       return;
    }
    public void onCreate(Bundle p0){
       String parentActivi;
       ActionBar uActionBar;
       this.mBaseContextAttached = true;
       this.applyApplicationSpecificConfig(false);
       this.ensureWindow();
       AppCompatDelegateImpl tmHost = this.mHost;
       if (tmHost instanceof Activity) {
          try{
             parentActivi = NavUtils.getParentActivityName(tmHost);
          }catch(java.lang.IllegalArgumentException e0){
             parentActivi = null;
          }
          if (parentActivi != null) {
             if ((uActionBar = this.peekSupportActionBar()) == null) {
                this.mEnableDefaultActionBarUp = true;
             }else {
                uActionBar.setDefaultDisplayHomeAsUpEnabled(true);
             }
          }
          AppCompatDelegate.addActiveDelegate(this);
       }
       this.mEffectiveConfiguration = new Configuration(this.mContext.getResources().getConfiguration());
       this.mCreated = true;
       return;
    }
    public final View onCreateView(View p0,String p1,Context p2,AttributeSet p3){
       return this.createView(p0, p1, p2, p3);
    }
    public View onCreateView(String p0,Context p1,AttributeSet p2){
       return this.onCreateView(null, p0, p1, p2);
    }
    public void onDestroy(){
       if (this.mHost instanceof Activity) {
          AppCompatDelegate.removeActivityDelegate(this);
       }
       if (this.mInvalidatePanelMenuPosted != null) {
          this.mWindow.getDecorView().removeCallbacks(this.mInvalidatePanelMenuRunnable);
       }
       this.mDestroyed = true;
       if (this.mLocalNightMode != -100) {
          AppCompatDelegateImpl tmHost = this.mHost;
          if (tmHost instanceof Activity && tmHost.isChangingConfigurations()) {
             AppCompatDelegateImpl.sLocalNightModes.put(this.mHost.getClass().getName(), Integer.valueOf(this.mLocalNightMode));
          label_0054 :
             if ((tmHost = this.mActionBar) != null) {
                tmHost.onDestroy();
             }
             this.cleanupAutoManagers();
             return;
          }
       }
       AppCompatDelegateImpl.sLocalNightModes.remove(this.mHost.getClass().getName());
       goto label_0054 ;
    }
    public boolean onKeyDown(int p0,KeyEvent p1){
       boolean b = true;
       if (p0 != 4) {
          if (p0 == 82) {
             this.onKeyDownPanel(false, p1);
             return b;
          }
       }else if((p1.getFlags() & 0x0080)){
          b = false;
       }
       this.mLongPressBackDown = b;
       return false;
    }
    public boolean onKeyShortcut(int p0,KeyEvent p1){
       ActionBar supportActio;
       AppCompatDelegateImpl tmPreparedPa;
       if ((supportActio = this.getSupportActionBar()) != null && supportActio.onKeyShortcut(p0, p1)) {
          return true;
       }
       if ((tmPreparedPa = this.mPreparedPanel) != null && this.performPanelShortcut(tmPreparedPa, p1.getKeyCode(), p1, true)) {
          if ((tmPreparedPa = this.mPreparedPanel) != null) {
             tmPreparedPa.isHandled = true;
          }
          return true;
       }else if(this.mPreparedPanel == null){
          AppCompatDelegateImpl$PanelFeatureState panelState = this.getPanelState(0, true);
          this.preparePanel(panelState, p1);
          boolean b = this.performPanelShortcut(panelState, p1.getKeyCode(), p1, true);
          panelState.isPrepared = false;
          if (b) {
             return true;
          }
       }
       return 0;
    }
    public boolean onKeyUp(int p0,KeyEvent p1){
       if (p0 != 4) {
          if (p0 == 82) {
             this.onKeyUpPanel(false, p1);
             return true;
          }
       }else if(this.onBackPressed()){
          return true;
       }
       return false;
    }
    public boolean onMenuItemSelected(MenuBuilder p0,MenuItem p1){
       Window$Callback windowCallba;
       AppCompatDelegateImpl$PanelFeatureState panelFeature;
       if ((windowCallba = this.getWindowCallback()) != null && (this.mDestroyed == null && (panelFeature = this.findMenuPanel(p0.getRootMenu())) != null)) {
          return windowCallba.onMenuItemSelected(panelFeature.featureId, p1);
       }
       return false;
    }
    public void onMenuModeChange(MenuBuilder p0){
       this.reopenMenu(true);
    }
    public void onMenuOpened(int p0){
       ActionBar supportActio;
       if (p0 == 108 && (supportActio = this.getSupportActionBar()) != null) {
          supportActio.dispatchMenuVisibilityChanged(true);
       }
       return;
    }
    public void onPanelClosed(int p0){
       ActionBar supportActio;
       if (p0 == 108) {
          if ((supportActio = this.getSupportActionBar()) != null) {
             supportActio.dispatchMenuVisibilityChanged(false);
          }
       }else if(!p0){
          AppCompatDelegateImpl$PanelFeatureState panelState = this.getPanelState(p0, true);
          if (panelState.isOpen != null) {
             this.closePanel(panelState, false);
          }
       }
       return;
    }
    public void onPostCreate(Bundle p0){
       this.ensureSubDecor();
    }
    public void onPostResume(){
       ActionBar supportActio;
       if ((supportActio = this.getSupportActionBar()) != null) {
          supportActio.setShowHideAnimationEnabled(true);
       }
       return;
    }
    public void onSaveInstanceState(Bundle p0){
    }
    public void onStart(){
       this.applyApplicationSpecificConfig(true, false);
    }
    public void onStop(){
       ActionBar supportActio;
       if ((supportActio = this.getSupportActionBar()) != null) {
          supportActio.setShowHideAnimationEnabled(false);
       }
       return;
    }
    public void onSubDecorInstalled(ViewGroup p0){
    }
    public final ActionBar peekSupportActionBar(){
       return this.mActionBar;
    }
    public boolean requestWindowFeature(int p0){
       p0 = this.sanitizeWindowFeatureId(p0);
       if (this.mWindowNoTitle != null && p0 == 108) {
          return false;
       }
       if (this.mHasActionBar != null && p0 == 1) {
          this.mHasActionBar = false;
       }
       if (p0 != 1) {
          if (p0 != 2) {
             if (p0 != 5) {
                if (p0 != 10) {
                   if (p0 != 108) {
                      if (p0 != 109) {
                         return this.mWindow.requestFeature(p0);
                      }else {
                         this.throwFeatureRequestIfSubDecorInstalled();
                         this.mOverlayActionBar = true;
                         return true;
                      }
                   }else {
                      this.throwFeatureRequestIfSubDecorInstalled();
                      this.mHasActionBar = true;
                      return true;
                   }
                }else {
                   this.throwFeatureRequestIfSubDecorInstalled();
                   this.mOverlayActionMode = true;
                   return true;
                }
             }else {
                this.throwFeatureRequestIfSubDecorInstalled();
                this.mFeatureIndeterminateProgress = true;
                return true;
             }
          }else {
             this.throwFeatureRequestIfSubDecorInstalled();
             this.mFeatureProgress = true;
             return true;
          }
       }else {
          this.throwFeatureRequestIfSubDecorInstalled();
          this.mWindowNoTitle = true;
          return true;
       }
    }
    public void setConfigurationLocales(Configuration p0,LocaleListCompat p1){
       if (Build$VERSION.SDK_INT >= 24) {
          AppCompatDelegateImpl$Api24Impl.setLocales(p0, p1);
       }else {
          p0.setLocale(p1.get(0));
          p0.setLayoutDirection(p1.get(0));
       }
       return;
    }
    public void setContentView(int p0){
       this.ensureSubDecor();
       ViewGroup viewGroup = this.mSubDecor.findViewById(0x1020002);
       viewGroup.removeAllViews();
       LayoutInflater.from(this.mContext).inflate(p0, viewGroup);
       this.mAppCompatWindowCallback.bypassOnContentChanged(this.mWindow.getCallback());
    }
    public void setContentView(View p0){
       this.ensureSubDecor();
       ViewGroup viewGroup = this.mSubDecor.findViewById(0x1020002);
       viewGroup.removeAllViews();
       viewGroup.addView(p0);
       this.mAppCompatWindowCallback.bypassOnContentChanged(this.mWindow.getCallback());
    }
    public void setContentView(View p0,ViewGroup$LayoutParams p1){
       this.ensureSubDecor();
       ViewGroup viewGroup = this.mSubDecor.findViewById(0x1020002);
       viewGroup.removeAllViews();
       viewGroup.addView(p0, p1);
       this.mAppCompatWindowCallback.bypassOnContentChanged(this.mWindow.getCallback());
    }
    public void setDefaultLocalesForLocaleList(LocaleListCompat p0){
       if (Build$VERSION.SDK_INT >= 24) {
          AppCompatDelegateImpl$Api24Impl.setDefaultLocales(p0);
       }else {
          Locale.setDefault(p0.get(0));
       }
       return;
    }
    public void setHandleNativeActionModesEnabled(boolean p0){
       this.mHandleNativeActionModes = p0;
    }
    public void setLocalNightMode(int p0){
       if (this.mLocalNightMode != p0) {
          this.mLocalNightMode = p0;
          if (this.mBaseContextAttached != null) {
             this.applyDayNight();
          }
       }
       return;
    }
    public void setOnBackInvokedDispatcher(OnBackInvokedDispatcher p0){
       AppCompatDelegateImpl tmDispatcher;
       AppCompatDelegateImpl tmBackCallba;
       super.setOnBackInvokedDispatcher(p0);
       if ((tmDispatcher = this.mDispatcher) != null && (tmBackCallba = this.mBackCallback) != null) {
          AppCompatDelegateImpl$Api33Impl.unregisterOnBackInvokedCallback(tmDispatcher, tmBackCallba);
          this.mBackCallback = null;
       }
       if (p0 == null) {
          tmDispatcher = this.mHost;
          if (tmDispatcher instanceof Activity && tmDispatcher.getWindow() != null) {
             this.mDispatcher = AppCompatDelegateImpl$Api33Impl.getOnBackInvokedDispatcher(this.mHost);
          label_002e :
             this.updateBackInvokedCallbackState();
             return;
          }
       }
       this.mDispatcher = p0;
       goto label_002e ;
    }
    public void setSupportActionBar(Toolbar p0){
       if (!this.mHost instanceof Activity) {
          return;
       }
       ActionBar supportActio = this.getSupportActionBar();
       if (supportActio instanceof WindowDecorActionBar) {
          throw new IllegalStateException("This Activity already has an action bar supplied by the window decor. Do not request Window.FEATURE_SUPPORT_ACTION_BAR and set windowActionBar to false in your theme to use a Toolbar instead.");
       }
       MenuInflater menuInflater = null;
       this.mMenuInflater = menuInflater;
       if (supportActio != null) {
          supportActio.onDestroy();
       }
       this.mActionBar = menuInflater;
       if (p0 != null) {
          ToolbarActionBar supportActio1 = new ToolbarActionBar(p0, this.getTitle(), this.mAppCompatWindowCallback);
          this.mActionBar = supportActio1;
          this.mAppCompatWindowCallback.setActionBarCallback(supportActio1.mMenuCallback);
          p0.setBackInvokedCallbackEnabled(true);
       }else {
          this.mAppCompatWindowCallback.setActionBarCallback(menuInflater);
       }
       this.invalidateOptionsMenu();
       return;
    }
    public void setTheme(int p0){
       this.mThemeResId = p0;
    }
    public final void setTitle(CharSequence p0){
       AppCompatDelegateImpl tmDecorConte;
       this.mTitle = p0;
       if ((tmDecorConte = this.mDecorContentParent) != null) {
          tmDecorConte.setWindowTitle(p0);
       }else if(this.peekSupportActionBar() != null){
          this.peekSupportActionBar().setWindowTitle(p0);
       }else if((tmDecorConte = this.mTitleView) != null){
          tmDecorConte.setText(p0);
       }
       return;
    }
    public final boolean shouldAnimateActionModeView(){
       AppCompatDelegateImpl tmSubDecor;
       boolean b = (this.mSubDecorInstalled != null && ((tmSubDecor = this.mSubDecor) != null && tmSubDecor.isLaidOut()))? true: false;
       return b;
    }
    public boolean shouldRegisterBackInvokedCallback(){
       AppCompatDelegateImpl$PanelFeatureState panelState;
       if (this.mDispatcher == null) {
          return false;
       }
       if ((panelState = this.getPanelState(false, false)) != null && panelState.isOpen != null) {
          return true;
       }
       if (this.mActionMode != null) {
          return true;
       }
       return false;
    }
    public ActionMode startSupportActionMode(ActionMode$Callback p0){
       AppCompatDelegateImpl tmActionMode;
       ActionBar supportActio;
       AppCompatDelegateImpl tmAppCompatC;
       if (p0 == null) {
          throw new IllegalArgumentException("ActionMode callback can not be null.");
       }
       if ((tmActionMode = this.mActionMode) != null) {
          tmActionMode.finish();
       }
       AppCompatDelegateImpl$ActionModeCallbackWrapperV9 uActionModeC = new AppCompatDelegateImpl$ActionModeCallbackWrapperV9(this, p0);
       if ((supportActio = this.getSupportActionBar()) != null) {
          ActionMode uActionMode = supportActio.startActionMode(uActionModeC);
          this.mActionMode = uActionMode;
          if (uActionMode != null && (tmAppCompatC = this.mAppCompatCallback) != null) {
             tmAppCompatC.onSupportActionModeStarted(uActionMode);
          }
       }
       if (this.mActionMode == null) {
          this.mActionMode = this.startSupportActionModeFromWindow(uActionModeC);
       }
       this.updateBackInvokedCallbackState();
       return this.mActionMode;
    }
    public ActionMode startSupportActionModeFromWindow(ActionMode$Callback p0){
       AppCompatDelegateImpl tmActionMode;
       AppCompatDelegateImpl$ActionModeCallbackWrapperV9 uActionModeC;
       ActionMode uActionMode;
       AppCompatDelegateImpl tmActionMode1;
       Resources$Theme theme1;
       ContextThemeWrapper uContextThem;
       PopupWindow theme1;
       ViewStubCompat viewStubComp;
       this.endOnGoingFadeAnimation();
       if ((tmActionMode = this.mActionMode) != null) {
          tmActionMode.finish();
       }
       if (!p0 instanceof AppCompatDelegateImpl$ActionModeCallbackWrapperV9) {
          uActionModeC = new AppCompatDelegateImpl$ActionModeCallbackWrapperV9(this, p0);
       }
       tmActionMode = this.mAppCompatCallback;
       AttributeSet uAttributeSe = null;
       if (tmActionMode != null && this.mDestroyed == null) {
          try{
             uActionMode = tmActionMode.onWindowStartingSupportActionMode(uActionModeC);
          label_0024 :
             if (uActionMode != null) {
                this.mActionMode = uActionMode;
             }else {
                boolean b = true;
                if (this.mActionModeView == null) {
                   if (this.mIsFloating != null) {
                      TypedValue typedValue = new TypedValue();
                      Resources$Theme theme = this.mContext.getTheme();
                      theme.resolveAttribute(R$attr.actionBarTheme, typedValue, b);
                      if (typedValue.resourceId != null) {
                         theme1 = this.mContext.getResources().newTheme();
                         theme1.setTo(theme);
                         theme1.applyStyle(typedValue.resourceId, b);
                         uContextThem = new ContextThemeWrapper(this.mContext, 0);
                         uContextThem.getTheme().setTo(theme1);
                      }else {
                         uContextThem = this.mContext;
                      }
                      this.mActionModeView = new ActionBarContextView(uContextThem);
                      theme1 = new PopupWindow(uContextThem, uAttributeSe, R$attr.actionModePopupWindowStyle);
                      this.mActionModePopup = theme1;
                      PopupWindowCompat.setWindowLayoutType(theme1, 2);
                      this.mActionModePopup.setContentView(this.mActionModeView);
                      this.mActionModePopup.setWidth(-1);
                      uContextThem.getTheme().resolveAttribute(R$attr.actionBarSize, typedValue, b);
                      this.mActionModeView.setContentHeight(TypedValue.complexToDimensionPixelSize(typedValue.data, uContextThem.getResources().getDisplayMetrics()));
                      this.mActionModePopup.setHeight(-2);
                      this.mShowActionModePopup = new AppCompatDelegateImpl$6(this);
                   }else if((viewStubComp = this.mSubDecor.findViewById(R$id.action_mode_bar_stub)) != null){
                      viewStubComp.setLayoutInflater(LayoutInflater.from(this.getActionBarThemedContext()));
                      this.mActionModeView = viewStubComp.inflate();
                   }
                }
                if (this.mActionModeView != null) {
                   this.endOnGoingFadeAnimation();
                   this.mActionModeView.killMode();
                   Context context = this.mActionModeView.getContext();
                   AppCompatDelegateImpl tmActionMode2 = this.mActionModeView;
                   if (this.mActionModePopup != null) {
                      b = false;
                   }
                   StandaloneActionMode standaloneAc = new StandaloneActionMode(context, tmActionMode2, uActionModeC, b);
                   if (uActionModeC.onCreateActionMode(standaloneAc, standaloneAc.getMenu())) {
                      standaloneAc.invalidate();
                      this.mActionModeView.initForMode(standaloneAc);
                      this.mActionMode = standaloneAc;
                      float f = 1.00f;
                      if (this.shouldAnimateActionModeView()) {
                         this.mActionModeView.setAlpha(0);
                         ViewPropertyAnimatorCompat viewProperty = ViewCompat.animate(this.mActionModeView).alpha(f);
                         this.mFadeAnim = viewProperty;
                         viewProperty.setListener(new AppCompatDelegateImpl$7(this));
                      }else {
                         this.mActionModeView.setAlpha(f);
                         this.mActionModeView.setVisibility(0);
                         if (this.mActionModeView.getParent() instanceof View) {
                            ViewCompat.requestApplyInsets(this.mActionModeView.getParent());
                         }
                      }
                      if (this.mActionModePopup != null) {
                         this.mWindow.getDecorView().post(this.mShowActionModePopup);
                      }
                   }else {
                      this.mActionMode = uAttributeSe;
                   }
                }
             }
             if ((tmActionMode1 = this.mActionMode) != null && (tmActionMode = this.mAppCompatCallback) != null) {
                tmActionMode.onSupportActionModeStarted(tmActionMode1);
             }
             this.updateBackInvokedCallbackState();
             return this.mActionMode;
          }catch(java.lang.AbstractMethodError e0){
          }
       label_0023 :
          uActionMode = uAttributeSe;
          goto label_0024 ;
       }else {
          goto label_0023 ;
       }
    }
    public void updateBackInvokedCallbackState(){
       boolean b;
       AppCompatDelegateImpl tmBackCallba;
       if (Build$VERSION.SDK_INT >= 33) {
          if ((b = this.shouldRegisterBackInvokedCallback()) && this.mBackCallback == null) {
             this.mBackCallback = AppCompatDelegateImpl$Api33Impl.registerOnBackPressedCallback(this.mDispatcher, this);
          }else if(!b && (tmBackCallba = this.mBackCallback) != null){
             AppCompatDelegateImpl$Api33Impl.unregisterOnBackInvokedCallback(this.mDispatcher, tmBackCallba);
             this.mBackCallback = null;
          }
       }
       return;
    }
    public final int updateStatusGuard(WindowInsetsCompat p0,Rect p1){
       int systemWindow;
       AppCompatDelegateImpl tmActionMode;
       WindowInsetsCompat rootWindowIn;
       int i4;
       AppCompatDelegateImpl tmStatusGuar;
       int i5;
       AppCompatDelegateImpl tmStatusGuar1;
       ViewGroup$MarginLayoutParams layoutParams2;
       int i = 0;
       if (p0 != null) {
          systemWindow = p0.getSystemWindowInsetTop();
       }else if(p1 != null){
          systemWindow = p1.top;
       }else {
          systemWindow = 0;
       }
       if ((tmActionMode = this.mActionModeView) != null && tmActionMode.getLayoutParams() instanceof ViewGroup$MarginLayoutParams) {
          ViewGroup$MarginLayoutParams layoutParams = this.mActionModeView.getLayoutParams();
          int i1 = 1;
          if (this.mActionModeView.isShown()) {
             if (this.mTempRect1 == null) {
                this.mTempRect1 = new Rect();
                this.mTempRect2 = new Rect();
             }
             AppCompatDelegateImpl tmTempRect1 = this.mTempRect1;
             AppCompatDelegateImpl tmTempRect2 = this.mTempRect2;
             if (p0 == null) {
                tmTempRect1.set(p1);
             }else {
                tmTempRect1.set(p0.getSystemWindowInsetLeft(), p0.getSystemWindowInsetTop(), p0.getSystemWindowInsetRight(), p0.getSystemWindowInsetBottom());
             }
             ViewUtils.computeFitSystemWindows(this.mSubDecor, tmTempRect1, tmTempRect2);
             Rect top = tmTempRect1.top;
             p1 = tmTempRect1.left;
             Rect right = tmTempRect1.right;
             int i2 = ((rootWindowIn = ViewCompat.getRootWindowInsets(this.mSubDecor)) == null)? 0: rootWindowIn.getSystemWindowInsetLeft();
             int i3 = (rootWindowIn == null)? 0: rootWindowIn.getSystemWindowInsetRight();
             if (layoutParams.topMargin == top && (layoutParams.leftMargin == p1 && layoutParams.rightMargin == right)) {
                tmStatusGuar1 = 0;
             }else {
                layoutParams.topMargin = top;
                layoutParams.leftMargin = p1;
                layoutParams.rightMargin = right;
                i4 = 1;
             }
             if (top > null && this.mStatusGuard == null) {
                View view = new View(this.mContext);
                this.mStatusGuard = view;
                view.setVisibility(8);
                FrameLayout$LayoutParams layoutParams1 = new FrameLayout$LayoutParams(-1, layoutParams.topMargin, 51);
                layoutParams1.leftMargin = i2;
                layoutParams1.rightMargin = i3;
                this.mSubDecor.addView(this.mStatusGuard, -1, layoutParams1);
             }else if((tmStatusGuar = this.mStatusGuard) != null){
                layoutParams2 = tmStatusGuar.getLayoutParams();
                ViewGroup$MarginLayoutParams topMargin = layoutParams.topMargin;
                if (layoutParams2.height != topMargin || (layoutParams2.leftMargin != i2 || layoutParams2.rightMargin != i3)) {
                   layoutParams2.height = topMargin;
                   layoutParams2.leftMargin = i2;
                   layoutParams2.rightMargin = i3;
                   this.mStatusGuard.setLayoutParams(layoutParams2);
                }
             }
             if ((tmStatusGuar = this.mStatusGuard) == null) {
                i1 = 0;
             }
             if (i1 && tmStatusGuar.getVisibility()) {
                this.updateStatusGuardColor(this.mStatusGuard);
             }
             if (this.mOverlayActionMode == null && i1) {
                systemWindow = 0;
             }
             i5 = i1;
             i1 = i4;
          }else if(layoutParams.topMargin != null){
             layoutParams.topMargin = i;
             layoutParams2 = 0;
          }else {
             layoutParams2 = 0;
             i1 = 0;
          }
          if (i1) {
             this.mActionModeView.setLayoutParams(layoutParams);
          }
       }else {
          layoutParams2 = 0;
       }
       if ((tmStatusGuar1 = this.mStatusGuard) != null) {
          if (!i5) {
             i = 8;
          }
          tmStatusGuar1.setVisibility(i);
       }
       return systemWindow;
    }
}
