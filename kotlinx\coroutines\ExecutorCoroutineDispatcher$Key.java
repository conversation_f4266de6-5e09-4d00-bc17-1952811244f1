package kotlinx.coroutines.ExecutorCoroutineDispatcher$Key;
import kotlin.coroutines.b;
import kotlinx.coroutines.CoroutineDispatcher;
import kotlinx.coroutines.ExecutorCoroutineDispatcher$Key$1;
import kotlin.coroutines.d$c;
import tb.g1a;
import tb.a07;

public final class ExecutorCoroutineDispatcher$Key extends b	// class@00049b from classes11.dex
{

    public void ExecutorCoroutineDispatcher$Key(){
       super(CoroutineDispatcher.Key, ExecutorCoroutineDispatcher$Key$1.INSTANCE);
    }
    public void ExecutorCoroutineDispatcher$Key(a07 p0){
       super();
    }
}
