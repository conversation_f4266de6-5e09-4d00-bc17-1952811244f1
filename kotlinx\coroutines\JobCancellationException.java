package kotlinx.coroutines.JobCancellationException;
import tb.tt4;
import java.util.concurrent.CancellationException;
import java.lang.String;
import java.lang.Throwable;
import kotlinx.coroutines.m;
import tb.dv6;
import java.lang.Object;
import tb.ckf;
import java.lang.StackTraceElement;
import java.lang.StringBuilder;

public final class JobCancellationException extends CancellationException implements tt4	// class@0004a1 from classes11.dex
{
    public final m job;

    public void JobCancellationException(String p0,Throwable p1,m p2){
       super(p0);
       this.job = p2;
       if (p1 != null) {
          this.initCause(p1);
       }
       return;
    }
    public Throwable createCopy(){
       return this.createCopy();
    }
    public JobCancellationException createCopy(){
       if (!dv6.b()) {
          return null;
       }
       String message = this.getMessage();
       ckf.d(message);
       return new JobCancellationException(message, this, this.job);
    }
    public boolean equals(Object p0){
       boolean b = (p0 != this && (!p0 instanceof JobCancellationException && (ckf.b(p0.getMessage(), this.getMessage()) && (ckf.b(p0.job, this.job) && ckf.b(p0.getCause(), this.getCause())))))? false: true;
       return b;
    }
    public Throwable fillInStackTrace(){
       if (dv6.b()) {
          return super.fillInStackTrace();
       }
       StackTraceElement[] stackTraceEl = new StackTraceElement[0];
       this.setStackTrace(stackTraceEl);
       return this;
    }
    public int hashCode(){
       Throwable cause;
       String message = this.getMessage();
       ckf.d(message);
       int i = ((message.hashCode() * 31) + this.job.hashCode()) * 31;
       int i1 = ((cause = this.getCause()) != null)? cause.hashCode(): 0;
       return (i + i1);
    }
    public String toString(){
       return super.toString()+"; job="+this.job;
    }
}
