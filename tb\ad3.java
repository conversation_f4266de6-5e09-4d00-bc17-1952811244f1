package tb.ad3;
import tb.lmb;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.lcu;
import com.android.alibaba.ip.runtime.IpChange;
import tb.fdv;
import java.util.HashMap;
import java.util.Map;
import tb.ux;
import tb.kmb;

public class ad3 extends lmb	// class@0016f2 from classes6.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1e700094);
    }
    public void ad3(){
       super();
    }
    public static Object ipc$super(ad3 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/icart/event/CartReQuerySubscriber");
    }
    public void j(lcu p0){
       IpChange $ipChange = ad3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8fbad8af", objArray);
          return;
       }else {
          fdv.d(p0);
          HashMap hashMap = new HashMap();
          hashMap.put("isPopLayerRequest", "true");
          hashMap.put("isPopLayerFirstPage", "true");
          this.j.f0(1, hashMap, null);
          return;
       }
    }
}
