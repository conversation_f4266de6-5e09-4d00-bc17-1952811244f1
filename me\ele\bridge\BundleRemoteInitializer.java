package me.ele.bridge.BundleRemoteInitializer;
import java.io.Serializable;
import java.lang.Object;
import android.os.Handler;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.app.Application;
import com.alibaba.ariver.app.api.Page;
import com.alibaba.ariver.engine.api.bridge.model.ApiContext;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ariver.engine.api.bridge.extension.BridgeCallback;
import java.lang.Integer;
import java.lang.Thread;
import android.os.Looper;
import android.os.HandlerThread;
import android.os.Bundle;
import android.os.BaseBundle;
import me.ele.bridge.PizzaApi;
import android.content.Context;
import java.lang.Class;
import tb.xdp$b;
import tb.xdp;
import me.ele.bridge.BundleRemoteInitializer$a;
import tb.b02$b;
import tb.b02$a;
import tb.b02;
import tb.xcq$e;
import tb.xcq;
import tb.ucq;
import tb.vcq;
import tb.bdt;

public class BundleRemoteInitializer implements Serializable	// class@000757 from classes11.dex
{
    public static IpChange $ipChange;
    private static final String BUNDLE_REMOTE_INITIALIZER;
    private static final String INIT_KEY;
    public static final String PIZZA;
    private static Handler mHandler;

    public void BundleRemoteInitializer(){
       super();
    }
    public static Handler access$000(){
       IpChange $ipChange = BundleRemoteInitializer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return BundleRemoteInitializer.mHandler;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("1554edef", objArray);
    }
    public static Handler access$002(Handler p0){
       IpChange $ipChange = BundleRemoteInitializer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("7fe0f4fd", objArray);
       }else {
          BundleRemoteInitializer.mHandler = p0;
          return p0;
       }
    }
    public static void sendPizza(Application p0,Page p1,ApiContext p2,String p3,String p4,JSONObject p5,JSONObject p6,JSONObject p7,int p8,JSONObject p9,JSONObject p10,String p11,BridgeCallback p12){
       object oobject = p0;
       IpChange $ipChange = BundleRemoteInitializer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[13];
          objArray[0] = oobject;
          objArray[1] = p1;
          objArray[2] = p2;
          objArray[3] = p3;
          objArray[4] = p4;
          objArray[5] = p5;
          objArray[6] = p6;
          objArray[7] = p7;
          objArray[8] = new Integer(p8);
          objArray[9] = p9;
          objArray[10] = p10;
          objArray[11] = p11;
          objArray[12] = p12;
          $ipChange.ipc$dispatch("d7f50560", objArray);
          return;
       }else {
          String str = "BundleRemoteInitializer";
          if (Thread.currentThread().getId() - Looper.getMainLooper().getThread().getId()) {
             HandlerThread $ipChange1 = new HandlerThread(str);
             $ipChange1.start();
             BundleRemoteInitializer.mHandler = new Handler($ipChange1.getLooper());
          }else {
             BundleRemoteInitializer.mHandler = new Handler();
          }
          Bundle uBundle = new Bundle();
          uBundle.putString("init_key", str);
          BundleRemoteInitializer$a uoa = v12;
          BundleRemoteInitializer$a uoa1 = v12;
          uoa = new BundleRemoteInitializer$a(p0, p1, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12);
          vcq.a(p0).c(xcq.c().m(xdp.l(oobject, PizzaApi.class).f(uoa1).d(uBundle).a()).n());
          return;
       }
    }
}
