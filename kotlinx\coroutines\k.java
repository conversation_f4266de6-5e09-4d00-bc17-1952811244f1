package kotlinx.coroutines.k;
import kotlinx.coroutines.h;
import kotlinx.coroutines.l;
import java.lang.Object;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import java.util.concurrent.atomic.AtomicIntegerFieldUpdater;
import tb.dv6;
import tb.uk8;
import tb.h30;
import tb.uch;
import java.lang.Runnable;
import java.lang.System;
import kotlinx.coroutines.k$d;
import tb.ort;
import tb.nrt;
import kotlinx.coroutines.k$c;
import tb.ckf;
import tb.g1a;
import tb.sk8;
import tb.hfn;
import kotlin.coroutines.d;
import tb.rr7;
import kotlinx.coroutines.h$a;
import tb.q23;
import kotlinx.coroutines.k$a;
import tb.s23;
import tb.ar4;
import kotlinx.coroutines.g;
import java.lang.IllegalStateException;
import kotlinx.coroutines.k$b;
import tb.o5k;
import tb.mqt;

public abstract class k extends l implements h	// class@00069d from classes11.dex
{
    private Object g;
    private Object h;
    private int i;
    private static final AtomicReferenceFieldUpdater s;
    private static final AtomicReferenceFieldUpdater t;
    private static final AtomicIntegerFieldUpdater u;

    static {
       k.s = AtomicReferenceFieldUpdater.newUpdater(k.class, Object.class, "g");
       k.t = AtomicReferenceFieldUpdater.newUpdater(k.class, Object.class, "h");
       k.u = AtomicIntegerFieldUpdater.newUpdater(k.class, "i");
    }
    public void k(){
       super();
       this.i = 0;
    }
    public static final boolean d1(k p0){
       return p0.j1();
    }
    private final void e1(){
       AtomicReferenceFieldUpdater uAtomicRefer = k.r();
       while (true) {
          Object obj = uAtomicRefer.get(this);
          u1r b = uk8.b;
          if (obj == null) {
             if (h30.a(k.r(), this, null, b)) {
                return;
             }
             continue ;
          }else if(obj instanceof uch){
             obj.d();
             return;
          }else if(obj == b){
             return;
          }else {
             uch ouch = new uch(8, true);
             ouch.a(obj);
             if (h30.a(k.r(), this, obj, ouch)) {
                break ;
             }
          }
       }
       return;
    }
    private final Object f(){
       return this.h;
    }
    private final Runnable g1(){
       Object obj;
       Object obj2;
       AtomicReferenceFieldUpdater uAtomicRefer = k.r();
       while (true) {
          obj = uAtomicRefer.get(this);
          Runnable runnable = null;
          if (obj == null) {
             return runnable;
          }
          if (obj instanceof uch) {
             Object obj1 = obj;
             if ((obj2 = obj1.j()) != uch.REMOVE_FROZEN) {
                return obj2;
             }
             h30.a(k.r(), this, obj, obj1.i());
          }else if(obj == uk8.b){
             return runnable;
          }else if(h30.a(k.r(), this, obj, runnable)){
             break ;
          }
       }
       return obj;
    }
    private static final AtomicReferenceFieldUpdater i(){
       return k.t;
    }
    private final boolean i1(Runnable p0){
       boolean b1;
       int i;
       AtomicReferenceFieldUpdater uAtomicRefer = k.r();
       while (true) {
          Object obj = uAtomicRefer.get(this);
          boolean b = false;
          if (this.j1()) {
             return b;
          }
          b1 = true;
          if (obj == null) {
             if (h30.a(k.r(), this, null, p0)) {
                return b1;
             }
             continue ;
          }else if(obj instanceof uch){
             Object obj1 = obj;
             if (!(i = obj1.a(p0))) {
                return b1;
             }
             if (i != b1) {
                if (i == 2) {
                   return b;
                }
                continue ;
             }else {
                h30.a(k.r(), this, obj, obj1.i());
             }
          }else if(obj == uk8.b){
             return b;
          }else {
             uch ouch = new uch(8, b1);
             ouch.a(obj);
             ouch.a(p0);
             if (h30.a(k.r(), this, obj, ouch)) {
                break ;
             }
          }
       }
       return b1;
    }
    private final boolean j1(){
       boolean b = (k.l().get(this))? true: false;
       return b;
    }
    private final int k(){
       return this.i;
    }
    private static final AtomicIntegerFieldUpdater l(){
       return k.u;
    }
    private final void l1(){
       k$d uod;
       k$c uoc;
       long l = System.nanoTime();
       while ((uod = k.i().get(this)) != null && (uoc = uod.i()) != null) {
          this.b1(l, uoc);
       }
       return;
    }
    private final int o1(long p0,k$c p1){
       k$d uod;
       if (this.j1()) {
          return 1;
       }
       if ((uod = k.i().get(this)) == null) {
          h30.a(k.i(), this, null, new k$d(p0));
          uod = k.i().get(this);
          ckf.d(uod);
       }
       return p1.g(p0, uod, this);
    }
    private final Object q(){
       return this.g;
    }
    private final void q1(boolean p0){
       k.l().set(this, p0);
    }
    private static final AtomicReferenceFieldUpdater r(){
       return k.s;
    }
    private final boolean r1(k$c p0){
       k$d uod;
       k$c uoc = ((uod = k.i().get(this)) != null)? uod.e(): null;
       boolean b = (uoc == p0)? true: false;
       return b;
    }
    private final void s(Object p0,AtomicReferenceFieldUpdater p1,g1a p2){
       while (true) {
          p2.invoke(p1.get(p0));
       }
    }
    private final void t(Object p0){
       this.h = p0;
    }
    private final void u(int p0){
       this.i = p0;
    }
    private final void v(Object p0){
       this.g = p0;
    }
    public long R0(){
       k$c uoc;
       if (!(super.R0())) {
          return 0;
       }
       k$d obj = k.r().get(this);
       long l = Long.MAX_VALUE;
       if (obj != null) {
          if (obj instanceof uch) {
             if (!obj.g()) {
                return 0;
             }
          }else if(obj == uk8.b){
             return l;
          }else {
             return 0;
          }
       }
       if ((obj = k.i().get(this)) != null && (uoc = obj.e()) != null) {
          return hfn.d((uoc.a - System.nanoTime()), 0);
       }else {
          return l;
       }
    }
    public boolean U0(){
       k$d uod;
       Object obj;
       boolean b = false;
       if (!this.W0()) {
          return b;
       }
       if ((uod = k.i().get(this)) != null && !uod.d()) {
          return b;
       }
       if ((obj = k.r().get(this)) == null) {
       label_0026 :
          b = true;
       }else if(obj instanceof uch){
          b = obj.g();
       }else if(obj == uk8.b){
          goto label_0026 ;
       }
       return b;
    }
    public long X0(){
       k$d uod;
       Runnable runnable;
       boolean b;
       if (this.Y0()) {
          return 0;
       }
       if ((uod = k.i().get(this)) != null && !uod.d()) {
          long l = System.nanoTime();
          while (true) {
             _monitor_enter(uod);
             ort oort = uod.b();
             int i = 0;
             if (oort == null) {
                _monitor_exit(uod);
             }else if(oort.h(l)){
                b = this.i1(oort);
             }else {
                b = false;
             }
             if (b) {
                i = uod.h(0);
             }
             _monitor_exit(uod);
             if (i != null) {
                continue ;
             }
          }
          runnable.run();
          return 0;
       }
       if ((runnable = this.g1()) != null) {
       }else {
          return this.R0();
       }
    }
    public rr7 b(long p0,Runnable p1,d p2){
       return h$a.b(this, p0, p1, p2);
    }
    public void c(long p0,q23 p1){
       p0 = uk8.b(p0);
       if ((p0 - 0x3fffffffffffffff) < 0) {
          long l = System.nanoTime();
          k$a uoa = new k$a(this, (p0 + l), p1);
          this.n1(l, uoa);
          s23.a(p1, uoa);
       }
       return;
    }
    public final void dispatch(d p0,Runnable p1){
       this.h1(p1);
    }
    public Object f1(long p0,ar4 p1){
       return h$a.a(this, p0, p1);
    }
    public void h1(Runnable p0){
       if (this.i1(p0)) {
          this.c1();
       }else {
          g.INSTANCE.h1(p0);
       }
       return;
    }
    public final void m1(){
       k.r().set(this, null);
       k.i().set(this, null);
    }
    public final void n1(long p0,k$c p1){
       int i;
       if (i = this.o1(p0, p1)) {
          if (i != 1) {
             if (i != 2) {
                throw new IllegalStateException("unexpected result");
             }
          }else {
             this.b1(p0, p1);
          }
       }else if(this.r1(p1)){
          this.c1();
       }
       return;
    }
    public final rr7 p1(long p0,Runnable p1){
       k$b uob;
       p0 = uk8.b(p0);
       if ((p0 - 0x3fffffffffffffff) < 0) {
          long l = System.nanoTime();
          uob = new k$b((p0 + l), p1);
          this.n1(l, uob);
       }else {
          uob = o5k.INSTANCE;
       }
       return uob;
    }
    public void shutdown(){
       mqt.INSTANCE.b();
       this.q1(true);
       this.e1();
       do {
       } while ((this.X0()) > 0);
       this.l1();
       return;
    }
}
