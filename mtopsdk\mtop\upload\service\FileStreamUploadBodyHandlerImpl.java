package mtopsdk.mtop.upload.service.FileStreamUploadBodyHandlerImpl;
import anetwork.channel.IBodyHandler;
import tb.t2o;
import java.io.InputStream;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import java.lang.Number;
import java.io.BufferedInputStream;
import java.lang.Throwable;
import mtopsdk.common.util.TBSdkLog;

public class FileStreamUploadBodyHandlerImpl implements IBodyHandler	// class@000812 from classes11.dex
{
    private BufferedInputStream bis;
    private long fileSize;
    private InputStream fileStream;
    private boolean isCompleted;
    private long offset;
    private long patchSize;
    private int postedLength;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x25900016);
       t2o.a(0x264001d4);
    }
    public void FileStreamUploadBodyHandlerImpl(InputStream p0,long p1,long p2,long p3){
       super();
       this.isCompleted = false;
       this.postedLength = 0;
       this.bis = null;
       this.fileStream = p0;
       this.fileSize = p1;
       this.offset = p2;
       this.patchSize = p3;
    }
    public boolean isCompleted(){
       IpChange $ipChange = FileStreamUploadBodyHandlerImpl.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.isCompleted;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8c6bb44c", objArray).booleanValue();
    }
    public int read(byte[] p0){
       FileStreamUploadBodyHandlerImpl tbis;
       int i2;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = FileStreamUploadBodyHandlerImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("9ed24497", objArray).intValue();
       }else if(p0 != null && (p0.length && this.fileStream != null)){
          FileStreamUploadBodyHandlerImpl tpostedLengt = this.postedLength;
          if (((long)tpostedLengt - this.patchSize) >= 0) {
             this.isCompleted = i;
             return i1;
          }else {
             try{
                FileStreamUploadBodyHandlerImpl tfileSize = this.fileSize;
                if ((this.offset - tfileSize) < 0 && ((long)tpostedLengt - tfileSize) < 0) {
                   if (this.bis == null) {
                      this.bis = new BufferedInputStream(this.fileStream);
                   }
                   if ((i2 = this.bis.read(p0)) != -1) {
                      tpostedLengt = this.postedLength;
                      tfileSize = this.patchSize;
                      if (((long)(tpostedLengt + i2) - tfileSize) > 0) {
                         i2 = (int)(tfileSize - (long)tpostedLengt);
                      }
                      i1 = i2;
                      int i3 = tpostedLengt + i1;
                      this.postedLength = i3;
                      long l = this.offset + (long)i1;
                      this.offset = l;
                      if (((long)i3 - tfileSize) >= 0 || (l - this.fileSize) >= 0) {
                         this.isCompleted = i;
                      }
                      if (this.isCompleted == null) {
                         this.bis.mark((int)this.fileSize);
                         this.bis.reset();
                      }
                   }
                   if ((tbis = this.bis) != null && this.isCompleted != null) {
                      tbis.close();
                   }
                }else {
                   this.isCompleted = i;
                   if ((tbis = this.bis) != null) {
                      try{
                         tbis.close();
                      }catch(java.io.IOException e12){
                         TBSdkLog.e("mtopsdk.FileStreamUploadBodyHandlerImpl", "close inputStream error", e12);
                      }
                   }
                   return i1;
                }
             }catch(java.lang.Exception e12){
                TBSdkLog.e("mtopsdk.FileStreamUploadBodyHandlerImpl", "[read]write Body error", e12);
                this.isCompleted = i;
                if ((tbis = this.bis) != null) {
                   tbis.close();
                }
             }catch(java.io.IOException e12){
                TBSdkLog.e("mtopsdk.FileStreamUploadBodyHandlerImpl", "close inputStream error", e12);
             }
             return i1;
          }
       }else {
          TBSdkLog.e("mtopsdk.FileStreamUploadBodyHandlerImpl", "[read\(byte[] buffer\)]parameter buffer or fileStream is null");
          this.isCompleted = i;
          return i1;
       }
    }
}
