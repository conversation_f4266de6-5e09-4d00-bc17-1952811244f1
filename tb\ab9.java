package tb.ab9;
import tb.g1a;
import kotlin.jvm.internal.Ref$ObjectRef;
import java.lang.Object;
import tb.bi8;
import tb.xhv;
import tb.qb9;

public final class ab9 implements g1a	// class@001b18 from classes8.dex
{
    public final Ref$ObjectRef a;

    public void ab9(Ref$ObjectRef p0){
       super();
       this.a = p0;
    }
    public final Object invoke(Object p0){
       return qb9.l(this.a, p0);
    }
}
