package mtopsdk.common.util.SymbolExpUtil;
import tb.t2o;
import java.lang.Object;

public class SymbolExpUtil	// class@000777 from classes11.dex
{
    public static final String CHARSET_UTF8 = "utf-8";
    public static final String STRING_FALSE = "false";
    public static final String STRING_TRUE = "true";
    public static final String SYMBOL_AND = "&";
    public static final String SYMBOL_COLON = ":";
    public static final String SYMBOL_COMMA = ",";
    public static final String SYMBOL_DOLLAR = "$";
    public static final String SYMBOL_DOT = ".";
    public static final String SYMBOL_EQUAL = "=";
    public static final String SYMBOL_SEMICOLON = ";";
    public static final String SYMBOL_VERTICALBAR = "\\|";

    static {
       t2o.a(0x2530005a);
    }
    public void SymbolExpUtil(){
       super();
    }
}
