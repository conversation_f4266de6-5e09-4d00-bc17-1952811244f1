package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$takeWhile$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;

public final class ChannelsKt__DeprecatedKt$takeWhile$1 extends SuspendLambda implements u1a	// class@0004fb from classes11.dex
{
    public final u1a $predicate;
    public final ReceiveChannel $this_takeWhile;
    private Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;

    public void ChannelsKt__DeprecatedKt$takeWhile$1(ReceiveChannel p0,u1a p1,ar4 p2){
       this.$this_takeWhile = p0;
       this.$predicate = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$takeWhile$1 otakeWhile$1 = new ChannelsKt__DeprecatedKt$takeWhile$1(this.$this_takeWhile, this.$predicate, p1);
       otakeWhile$1.L$0 = p0;
       return otakeWhile$1;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$takeWhile$1 tlabel;
       ChannelsKt__DeprecatedKt$takeWhile$1 tL$0;
       ChannelsKt__DeprecatedKt$takeWhile$1 obj1;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel != 2) {
                if (tlabel == 3) {
                   tlabel = this.L$1;
                   tL$0 = this.L$0;
                   b.b(p0);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                tlabel = this.L$2;
                tL$0 = this.L$1;
                obj1 = this.L$0;
                b.b(p0);
             label_007c :
                if (!p0.booleanValue()) {
                   return xhv.INSTANCE;
                }else {
                   this.L$0 = obj1;
                   this.L$1 = tL$0;
                   this.L$2 = 0;
                   this.label = 3;
                   if (obj1.d(tlabel, this) == obj) {
                      return obj;
                   }else {
                      tlabel = tL$0;
                      tL$0 = obj1;
                   }
                }
             }
          }else {
             tlabel = this.L$1;
             tL$0 = this.L$0;
             b.b(p0);
          label_005a :
             if (p0.booleanValue()) {
                p0 = tlabel.next();
                this.L$0 = tL$0;
                this.L$1 = tlabel;
                this.L$2 = p0;
                this.label = 2;
                if ((obj1 = this.$predicate.invoke(p0, this)) == obj) {
                   return obj;
                }else {
                   tlabel = p0;
                   p0 = obj1;
                   obj1 = tL$0;
                   tL$0 = tlabel;
                   goto label_007c ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          ChannelIterator uChannelIter = this.$this_takeWhile.iterator();
          tL$0 = this.L$0;
       }
       this.L$0 = tL$0;
       this.L$1 = tlabel;
       this.label = 1;
       if ((p0 = tlabel.a(this)) == obj) {
          return obj;
       }else {
          goto label_005a ;
       }
    }
}
