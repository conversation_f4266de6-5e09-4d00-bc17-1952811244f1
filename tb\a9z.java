package tb.a9z;
import java.lang.Runnable;
import com.taobao.search.searchdoor.activate.hotspot.impl.HotSpotsViewHolder;
import tb.v6b;
import tb.x6b;
import java.lang.Object;

public final class a9z implements Runnable	// class@001777 from classes9.dex
{
    public final HotSpotsViewHolder a;
    public final v6b b;
    public final int c;
    public final x6b d;

    public void a9z(HotSpotsViewHolder p0,v6b p1,int p2,x6b p3){
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
       this.d = p3;
    }
    public final void run(){
       HotSpotsViewHolder.j(this.a, this.b, this.c, this.d);
    }
}
