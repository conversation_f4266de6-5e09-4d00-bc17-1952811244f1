package kotlinx.coroutines.CoroutineDispatcher$Key;
import kotlin.coroutines.b;
import kotlin.coroutines.c;
import kotlinx.coroutines.CoroutineDispatcher$Key$1;
import kotlin.coroutines.d$c;
import tb.g1a;
import tb.a07;

public final class CoroutineDispatcher$Key extends b	// class@000492 from classes11.dex
{

    public void CoroutineDispatcher$Key(){
       super(c.Key, CoroutineDispatcher$Key$1.INSTANCE);
    }
    public void CoroutineDispatcher$Key(a07 p0){
       super();
    }
}
