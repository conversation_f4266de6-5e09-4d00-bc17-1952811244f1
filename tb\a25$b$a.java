package tb.a25$b$a;
import java.lang.Thread;
import tb.a25$b;
import java.lang.Runnable;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import android.os.Process;
import java.lang.Throwable;
import tb.fs7;

public class a25$b$a extends Thread	// class@001840 from classes7.dex
{
    public final a25$b a;
    public static IpChange $ipChange;

    public void a25$b$a(a25$b p0,Runnable p1,String p2){
       this.a = p0;
       super(p1, p2);
    }
    public static Object ipc$super(a25$b$a p0,String p1,Object[] p2){
       if (p1.hashCode() != 0x5c510192) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/downloader/adapter/CustomThreadImpl$DownloadThreadFactory$1");
       }
       super.run();
       return null;
    }
    public void run(){
       IpChange $ipChange = a25$b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          Process.setThreadPriority(this.a.b);
          super.run();
          return;
       }
    }
}
