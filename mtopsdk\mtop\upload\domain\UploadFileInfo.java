package mtopsdk.mtop.upload.domain.UploadFileInfo;
import tb.t2o;
import java.lang.Object;
import mtopsdk.mtop.upload.domain.FileUploadTypeEnum;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import java.lang.Class;
import mtopsdk.mtop.upload.domain.FileStreamInfo;
import mtopsdk.mtop.upload.FileUploadBaseListener;
import java.lang.Number;
import mtopsdk.common.util.StringUtils;
import mtopsdk.mtop.upload.FileUploadListener;
import mtopsdk.mtop.upload.DefaultFileUploadListener;
import java.lang.StringBuilder;

public class UploadFileInfo	// class@00080f from classes11.dex
{
    public String bizCode;
    public String filePath;
    public FileStreamInfo fileStreamInfo;
    public FileUploadBaseListener listener;
    public String ownerNick;
    public String privateData;
    public FileUploadTypeEnum type;
    public boolean useHttps;
    public static IpChange $ipChange;

    static {
       t2o.a(0x25900013);
    }
    public void UploadFileInfo(){
       super();
       this.type = FileUploadTypeEnum.RESUMABLE;
       this.useHttps = false;
    }
    public boolean equals(Object p0){
       UploadFileInfo tbizCode;
       int i = 1;
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6c2a9726", objArray).booleanValue();
       }else if(this == p0){
          return i;
       }else if(p0 == null){
          return 0;
       }else if(this.getClass() != p0.getClass()){
          return 0;
       }else if((tbizCode = this.bizCode) == null){
          if (p0.bizCode != null) {
             return 0;
          }
       }else if(!tbizCode.equals(p0.bizCode)){
          return 0;
       }
       if ((tbizCode = this.filePath) == null) {
          if (p0.filePath != null) {
             return 0;
          }
       }else if(!tbizCode.equals(p0.filePath)){
          return 0;
       }
       if ((tbizCode = this.fileStreamInfo) == null) {
          if (p0.fileStreamInfo != null) {
             return 0;
          }
       }else if(!tbizCode.equals(p0.fileStreamInfo)){
          return 0;
       }
       if ((tbizCode = this.listener) == null) {
          if (p0.listener != null) {
             return 0;
          }
       }else if(!tbizCode.equals(p0.listener)){
          return 0;
       }
       if ((tbizCode = this.ownerNick) == null) {
          if (p0.ownerNick != null) {
             return 0;
          }
       }else if(!tbizCode.equals(p0.ownerNick)){
          return 0;
       }
       if ((tbizCode = this.privateData) == null) {
          if (p0.privateData != null) {
             return 0;
          }
       }else if(!tbizCode.equals(p0.privateData)){
          return 0;
       }
       if (this.type != p0.type) {
          i = false;
       }
       return i;
    }
    public String getBizCode(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.bizCode;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("826ffbcf", objArray);
    }
    public String getFilePath(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.filePath;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1bcb7a22", objArray);
    }
    public FileStreamInfo getFileStreamInfo(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.fileStreamInfo;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dd9896c7", objArray);
    }
    public FileUploadBaseListener getListener(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.listener;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4613636e", objArray);
    }
    public String getOwnerNick(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.ownerNick;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6f0a10f9", objArray);
    }
    public String getPrivateData(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.privateData;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8f6aacc2", objArray);
    }
    public FileUploadTypeEnum getType(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.type;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("28822329", objArray);
    }
    public int hashCode(){
       UploadFileInfo tbizCode;
       int i1;
       UploadFileInfo tfilePath;
       UploadFileInfo ttype;
       int i = 0;
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("53a9ab15", objArray).intValue();
       }else if((tbizCode = this.bizCode) == null){
          i1 = 0;
       }else {
          i1 = tbizCode.hashCode();
       }
       i1 = (i1 + 31) * 31;
       int i2 = ((tfilePath = this.filePath) == null)? 0: tfilePath.hashCode();
       i1 = (i1 + i2) * 31;
       i2 = ((tfilePath = this.fileStreamInfo) == null)? 0: tfilePath.hashCode();
       i1 = (i1 + i2) * 31;
       i2 = ((tfilePath = this.listener) == null)? 0: tfilePath.hashCode();
       i1 = (i1 + i2) * 31;
       i2 = ((tfilePath = this.ownerNick) == null)? 0: tfilePath.hashCode();
       i1 = (i1 + i2) * 31;
       i2 = ((tfilePath = this.privateData) == null)? 0: tfilePath.hashCode();
       i1 = (i1 + i2) * 31;
       if ((ttype = this.type) != null) {
          i = ttype.hashCode();
       }
       return (i1 + i);
    }
    public boolean isUseHttps(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.useHttps;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1da8b479", objArray).booleanValue();
    }
    public boolean isValid(){
       UploadFileInfo tfileStreamI;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("3fef87d", objArray).booleanValue();
       }else if(StringUtils.isBlank(this.bizCode)){
          return i;
       }else if(StringUtils.isBlank(this.filePath) && ((tfileStreamI = this.fileStreamInfo) == null && tfileStreamI.isValid())){
          i = true;
       }
       return i;
    }
    public void setBizCode(String p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d29306ef", objArray);
          return;
       }else if(p0 != null){
          this.bizCode = p0;
       }
       return;
    }
    public void setFilePath(String p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e8812494", objArray);
          return;
       }else if(p0 != null){
          this.filePath = p0;
       }
       return;
    }
    public void setFileStreamInfo(FileStreamInfo p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a85d42dd", objArray);
          return;
       }else {
          this.fileStreamInfo = p0;
          return;
       }
    }
    public void setListener(FileUploadBaseListener p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d9cd9e26", objArray);
          return;
       }else {
          this.listener = p0;
          return;
       }
    }
    public void setListener(FileUploadListener p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("33c14337", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if(p0 instanceof FileUploadBaseListener){
          this.listener = p0;
       }else {
          this.listener = new DefaultFileUploadListener(p0);
       }
       return;
    }
    public void setOwnerNick(String p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f475ca05", objArray);
          return;
       }else {
          this.ownerNick = p0;
          return;
       }
    }
    public void setPrivateData(String p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("75a21a5c", objArray);
          return;
       }else {
          this.privateData = p0;
          return;
       }
    }
    public void setType(FileUploadTypeEnum p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7fe4b19f", objArray);
          return;
       }else if(p0 != null){
          this.type = p0;
       }
       return;
    }
    public void setUseHttps(boolean p0){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("666558c7", objArray);
          return;
       }else {
          this.useHttps = p0;
          return;
       }
    }
    public String toString(){
       IpChange $ipChange = UploadFileInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(64)+"UploadFileInfo [filePath="+this.filePath+", fileStreamInfo="+this.fileStreamInfo+", bizCode="+this.bizCode+", ownerNick="+this.ownerNick+", privateData="+this.type+", listener="+this.listener+"]";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
