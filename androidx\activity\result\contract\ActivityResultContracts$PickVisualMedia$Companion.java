package androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$Companion;
import java.lang.Object;
import tb.a07;
import android.content.Context;
import android.content.pm.ResolveInfo;
import java.lang.String;
import tb.ckf;
import android.content.pm.PackageManager;
import android.content.Intent;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$ImageOnly;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VideoOnly;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$SingleMimeType;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$ImageAndVideo;
import kotlin.NoWhenBranchMatchedException;
import android.os.Build$VERSION;
import tb.ec0;

public final class ActivityResultContracts$PickVisualMedia$Companion	// class@0004d1 from classes.dex
{

    private void ActivityResultContracts$PickVisualMedia$Companion(){
       super();
    }
    public void ActivityResultContracts$PickVisualMedia$Companion(a07 p0){
       super();
    }
    public static void getACTION_SYSTEM_FALLBACK_PICK_IMAGES$annotations(){
    }
    public static void getEXTRA_SYSTEM_FALLBACK_PICK_IMAGES_MAX$annotations(){
    }
    public final ResolveInfo getGmsPicker$activity_release(Context p0){
       ckf.g(p0, "context");
       return p0.getPackageManager().resolveActivity(new Intent("com.google.android.gms.provider.action.PICK_IMAGES"), 0x110000);
    }
    public final ResolveInfo getSystemFallbackPicker$activity_release(Context p0){
       ckf.g(p0, "context");
       return p0.getPackageManager().resolveActivity(new Intent("androidx.activity.result.contract.action.PICK_IMAGES"), 0x110000);
    }
    public final String getVisualMimeType$activity_release(ActivityResultContracts$PickVisualMedia$VisualMediaType p0){
       String str;
       ckf.g(p0, "input");
       if (p0 instanceof ActivityResultContracts$PickVisualMedia$ImageOnly) {
          str = "image/*";
       }else if(p0 instanceof ActivityResultContracts$PickVisualMedia$VideoOnly){
          str = "video/*";
       }else if(p0 instanceof ActivityResultContracts$PickVisualMedia$SingleMimeType){
          str = p0.getMimeType();
       }else if(p0 instanceof ActivityResultContracts$PickVisualMedia$ImageAndVideo){
          str = null;
       }else {
          throw new NoWhenBranchMatchedException();
       }
       return str;
    }
    public final boolean isGmsPickerAvailable$activity_release(Context p0){
       ckf.g(p0, "context");
       boolean b = (this.getGmsPicker$activity_release(p0) != null)? true: false;
       return b;
    }
    public final boolean isPhotoPickerAvailable(){
       return this.isSystemPickerAvailable$activity_release();
    }
    public final boolean isPhotoPickerAvailable(Context p0){
       ckf.g(p0, "context");
       boolean b = (!this.isSystemPickerAvailable$activity_release() && (!this.isSystemFallbackPickerAvailable$activity_release(p0) && !this.isGmsPickerAvailable$activity_release(p0)))? false: true;
       return b;
    }
    public final boolean isSystemFallbackPickerAvailable$activity_release(Context p0){
       ckf.g(p0, "context");
       boolean b = (this.getSystemFallbackPicker$activity_release(p0) != null)? true: false;
       return b;
    }
    public final boolean isSystemPickerAvailable$activity_release(){
       int sDK_INT = Build$VERSION.SDK_INT;
       boolean b = true;
       if (sDK_INT < 33) {
          int i = 30;
          if (sDK_INT < i || ec0.a(i) < 2) {
             b = false;
          }
       }
       return b;
    }
}
