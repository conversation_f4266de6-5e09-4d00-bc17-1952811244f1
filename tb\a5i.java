package tb.a5i;
import com.taobao.tao.flexbox.layoutmanager.view.StaticLayoutView$d;
import com.taobao.tao.flexbox.layoutmanager.component.RichTextContainerComponent;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.taobao.tao.flexbox.layoutmanager.core.Component;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.tao.flexbox.layoutmanager.ac.d$j;
import com.alibaba.fastjson.JSONObject;
import com.taobao.tao.flexbox.layoutmanager.ac.d$k;
import java.lang.Boolean;
import android.content.Context;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.tao.flexbox.layoutmanager.view.StaticLayoutView;
import com.taobao.tao.flexbox.layoutmanager.core.n;
import tb.nwv;
import tb.s6o;
import com.taobao.tao.flexbox.layoutmanager.core.n$g;
import java.util.Map;
import tb.hk8;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.ymt;
import android.text.TextUtils$TruncateAt;
import java.util.HashMap;

public class a5i extends RichTextContainerComponent implements StaticLayoutView$d	// class@00175f from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2010011e);
       t2o.a(0x201003df);
       t2o.a(0x20100252);
    }
    public void a5i(){
       super();
    }
    public static Object ipc$super(a5i p0,String p1,Object[] p2){
       int i = p1.hashCode();
       int i1 = 0;
       if (i == -1814733277) {
          return super.onCreateView(p2[i1]);
       }
       if (i == -1619485803) {
          return new Boolean(super.invoke(p2[i1], p2[1], p2[2], p2[3]));
       }
       if (i != -613926416) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/tao/flexbox/layoutmanager/component/MarqueeExComponent");
       }
       super.onLayout();
       return null;
    }
    public boolean invoke(d$j p0,String p1,JSONObject p2,d$k p3){
       int i = 3;
       int i1 = 2;
       int i2 = 1;
       IpChange $ipChange = a5i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          return $ipChange.ipc$dispatch("9f789b95", objArray).booleanValue();
       }else if(this.view != null){
          p1.hashCode();
          int i3 = -1;
          switch (p1.hashCode()){
              case 0x360802:
                if (p1.equals("stop")) {
                   i3 = 0;
                }
                break;
              case 0x65825f6:
                if (p1.equals("pause")) {
                   i3 = 1;
                }
                break;
              case 0x68ac462:
                if (p1.equals("start")) {
                   i3 = 2;
                }
                break;
              default:
          }
          switch (i3){
              case 0:
                this.view.setMarqueeState(i);
                this.view.stopMarquee();
                break;
              case 1:
                this.view.setMarqueeState(i1);
                this.view.pauseMarquee();
                break;
              case 2:
                this.view.setMarqueeState(i2);
                this.view.startMarquee();
                break;
              default:
             label_0089 :
                i2 = 0;
          }
       }else {
          goto label_0089 ;
       }
       if (!i2) {
          i2 = super.invoke(p0, p1, p2, p3);
       }
       return i2;
    }
    public View onCreateView(Context p0){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = a5i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("93d55e23", objArray);
       }else {
          StaticLayoutView staticLayout = super.onCreateView(p0);
          staticLayout.enableNewMarquee(i, this);
          staticLayout.setLoopCount(nwv.t(this.node.H("loop"), -1));
          staticLayout.setLoopDelay(nwv.t(this.node.H("loopdelay"), i1));
          staticLayout.setDirection(nwv.I(this.node.H("direction"), "left"));
          Component tnode = this.node;
          staticLayout.setScrollAmount(s6o.U(tnode, p0, nwv.r(tnode.H("scrollamount"), 6)));
          tnode = this.node;
          staticLayout.setMarqueeScrollGap(s6o.U(tnode, p0, (float)nwv.t(tnode.H("scrollgap"), i1)));
          return staticLayout;
       }
    }
    public boolean onHandleMessage(n$g p0){
       IpChange $ipChange = a5i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("1e782cf4", objArray).booleanValue();
       }else if(!p0.d.equals("onwilldisappear") && (!p0.d.equals("onwillappear") || this.view == null)){
          if (p0.d.equals("onwillappear")) {
             if (this.view.getMarqueeState() == 1) {
                this.view.startMarquee();
             }
          }else {
             this.view.pauseMarquee();
          }
       }
       return 0;
    }
    public boolean onHandleTNodeMessage(n p0,n p1,String p2,String p3,Map p4,hk8 p5){
       int i = 0;
       IpChange $ipChange = a5i.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return i;
       }
       Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4,p5};
       return $ipChange.ipc$dispatch("abab8f80", objArray).booleanValue();
    }
    public void onLayout(){
       IpChange $ipChange = a5i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("db6839f0", objArray);
          return;
       }else {
          super.onLayout();
          String str = nwv.I(this.node.H("direction"), "left");
          if (TextUtils.equals(str, "left") || TextUtils.equals(str, "right")) {
             this.viewParams.F0 = TextUtils$TruncateAt.MARQUEE;
          }
          return;
       }
    }
    public void onStateChange(String p0){
       IpChange $ipChange = a5i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("36319953", objArray);
          return;
       }else {
          HashMap hashMap = new HashMap();
          hashMap.put("state", p0);
          this.sendMessage(this.node, "onstatechanged", null, hashMap, null);
          return;
       }
    }
}
