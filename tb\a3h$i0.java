package tb.a3h$i0;
import java.lang.Runnable;
import tb.a3h;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;
import com.taobao.trtc.utils.TrtcLog;
import tb.a3h$v0;

public class a3h$i0 implements Runnable	// class@001e74 from classes10.dex
{
    public final int a;
    public final String b;
    public final a3h c;
    public static IpChange $ipChange;

    public void a3h$i0(a3h p0,int p1,String p2){
       this.c = p0;
       this.a = p1;
       this.b = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$i0.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "artc so load onError, errorCode: "+this.a+" ,errorMsg: "+this.b);
          if (a3h.y(this.c) != null) {
             a3h.y(this.c).o(0, this.a, this.b);
          }
          return;
       }
    }
}
