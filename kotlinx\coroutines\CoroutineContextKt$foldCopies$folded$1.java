package kotlinx.coroutines.CoroutineContextKt$foldCopies$folded$1;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import kotlin.jvm.internal.Ref$ObjectRef;
import java.lang.Object;
import kotlin.coroutines.d;
import kotlin.coroutines.d$b;
import tb.st4;
import kotlin.coroutines.d$c;

public final class CoroutineContextKt$foldCopies$folded$1 extends Lambda implements u1a	// class@00048e from classes11.dex
{
    public final boolean $isNewCoroutine;
    public final Ref$ObjectRef $leftoverContext;

    public void CoroutineContextKt$foldCopies$folded$1(Ref$ObjectRef p0,boolean p1){
       this.$leftoverContext = p0;
       this.$isNewCoroutine = p1;
       super(2);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final d invoke(d p0,d$b p1){
       d$b uob;
       st4 ost4;
       if (!p1 instanceof st4) {
          return p0.plus(p1);
       }
       if ((uob = this.$leftoverContext.element.get(p1.getKey())) == null) {
          if (this.$isNewCoroutine != null) {
             ost4 = p1.e();
          }
          return p0.plus(ost4);
       }else {
          CoroutineContextKt$foldCopies$folded$1 t$leftoverCo = this.$leftoverContext;
          t$leftoverCo.element = t$leftoverCo.element.minusKey(p1.getKey());
          return p0.plus(p1.Z(uob));
       }
    }
}
