package tb.aao;
import tb.t2o;
import mtopsdk.mtop.domain.MtopResponse;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import mtopsdk.common.util.StringUtils;

public class aao	// class@00185f from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2d100018);
    }
    public static String a(MtopResponse p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = aao.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("299b86e6", objArray);
       }else {
          String[] ret = p0.getRet();
          if (ret.length < i1) {
             return null;
          }
          object oobject = ret[i];
          if (StringUtils.isNotBlank(oobject)) {
             ret = oobject.split("::");
             if (ret.length > i1) {
                return ret[i1];
             }
          }
          return null;
       }
    }
}
