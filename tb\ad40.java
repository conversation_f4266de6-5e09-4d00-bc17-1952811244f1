package tb.ad40;
import tb.p330;
import tb.t2o;
import tb.rw10;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.h540;
import tb.xhv;
import com.android.alibaba.ip.runtime.IpChange;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.zc40;
import tb.ck40;
import tb.d1a;
import tb.dk40;
import java.lang.IllegalStateException;
import java.lang.Boolean;

public final class ad40 extends p330	// class@001b22 from classes8.dex
{
    public Object c;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3e700024);
    }
    public void ad40(rw10 p0){
       ckf.g(p0, "beanDefinition");
       super(p0);
    }
    public static xhv e(ad40 p0,h540 p1){
       return ad40.f(p0, p1);
    }
    public static final xhv f(ad40 p0,h540 p1){
       IpChange $ipChange = ad40.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("76540a51", objArray);
       }else if(!p0.h(p1)){
          p0.c = p0.b(p1);
       }
       return xhv.INSTANCE;
    }
    public static Object ipc$super(ad40 p0,String p1,Object[] p2){
       if (p1.hashCode() == 0x3de937e3) {
          return super.b(p2[0]);
       }
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/kiwi/di/internal/SingleInstanceFactory");
    }
    public Object b(h540 p0){
       IpChange $ipChange = ad40.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("3de937e3", objArray);
       }else {
          ckf.g(p0, "context");
          p0 = (this.c == null)? super.b(p0): this.g();
          return p0;
       }
    }
    public Object c(h540 p0){
       IpChange $ipChange = ad40.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("1b1a993d", objArray);
       }else {
          ckf.g(p0, "context");
          dk40.a(this, new zc40(this, p0));
          return this.g();
       }
    }
    public final Object g(){
       ad40 tc;
       IpChange $ipChange = ad40.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("6045ccb0", objArray);
       }else if((tc = this.c) != null){
          return tc;
       }else {
          throw new IllegalStateException("Single instance created couldn\'t return value");
       }
    }
    public boolean h(h540 p0){
       int i = 1;
       IpChange $ipChange = ad40.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("eeb59691", objArray).booleanValue();
       }else if(this.c != null){
          i = false;
       }
       return i;
    }
}
