package tb.a4l;
import tb.m8;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.n8;
import tb.k8;
import tb.u8;
import tb.c8;
import tb.tk6;
import com.android.alibaba.ip.runtime.IpChange;
import tb.f8;
import android.content.Context;
import android.content.Intent;
import tb.c2u;
import com.taobao.android.order.bundle.base.TBOrderBaseActivity;
import android.app.Activity;

public class a4l extends m8	// class@001835 from classes5.dex
{
    public static IpChange $ipChange;
    public static final String OPENNOTIFICATIONSETTING;

    static {
       t2o.a(0x2d00003d);
    }
    public void a4l(){
       super();
    }
    public static Object ipc$super(a4l p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/order/bundle/dinamicX/ability/OpenNotificationSettingAbility");
    }
    public c8 f(n8 p0,k8 p1,u8 p2){
       return this.i(p0, p1, p2);
    }
    public c8 i(n8 p0,tk6 p1,u8 p2){
       Context uContext;
       IpChange $ipChange = a4l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("f0bde67e", objArray);
       }else if(p1 == null){
          return new f8();
       }else if((uContext = p1.d()) == null){
          return new f8();
       }else {
          Intent intent = c2u.f(uContext);
          if (uContext instanceof TBOrderBaseActivity) {
             uContext.startActivityForResult(intent, 1001);
          }else {
             uContext.startActivity(intent);
          }
          return new f8();
       }
    }
}
