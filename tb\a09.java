package tb.a09;
import android.content.Context;
import java.lang.Object;
import com.alibaba.wireless.security.open.SecurityGuardManager;
import com.alibaba.wireless.security.open.dynamicdataencrypt.IDynamicDataEncryptComponent;
import java.lang.Throwable;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.StringBuilder;
import tb.vlp;
import java.lang.Boolean;

public class a09	// class@00182f from classes7.dex
{
    public final IDynamicDataEncryptComponent a;
    public static IpChange $ipChange;
    public static final String KEY_MEMBERS;

    public void a09(Context p0){
       SecurityGuardManager instance;
       try{
          super();
          if ((instance = SecurityGuardManager.getInstance(p0)) != null) {
             this.a = instance.getDynamicDataEncryptComp();
          }
       }catch(com.alibaba.wireless.security.open.SecException e1){
          e1.printStackTrace();
       }
       return;
    }
    public String a(String p0){
       IpChange $ipChange = a09.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("98bb6cdb", objArray);
       }else if(TextUtils.isEmpty(p0)){
          return null;
       }else {
          return this.b("familyMembers_"+p0);
       }
    }
    public String b(String p0){
       IpChange $ipChange = a09.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ba42c148", objArray);
       }else if(this.a == null){
          return "";
       }else {
          p0 = vlp.b(p0);
          if (TextUtils.isEmpty(p0)) {
             return "";
          }
          try{
             return this.a.dynamicDecryptDDp(p0);
          }catch(com.alibaba.wireless.security.open.SecException e5){
             e5.printStackTrace();
             return v1;
          }
       }
    }
    public boolean c(String p0,String p1){
       int i = 0;
       IpChange $ipChange = a09.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("c3a0f6da", objArray).booleanValue();
       }else if(TextUtils.isEmpty(p1)){
          return i;
       }else {
          return this.d("familyMembers_"+p1, p0);
       }
    }
    public boolean d(String p0,String p1){
       IpChange $ipChange = a09.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("cb567a1f", objArray).booleanValue();
       }else if(this.a != null && !TextUtils.isEmpty(p1)){
          p1 = this.a.dynamicEncryptDDp(p1);
          if (TextUtils.isEmpty(p1)) {
             return 0;
          }
          vlp.c(p0, p1);
          return 1;
       }
    }
}
