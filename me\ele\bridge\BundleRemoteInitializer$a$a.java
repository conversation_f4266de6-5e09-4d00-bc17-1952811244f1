package me.ele.bridge.BundleRemoteInitializer$a$a;
import java.lang.Runnable;
import me.ele.bridge.BundleRemoteInitializer$a;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import me.ele.bridge.PizzaApi;
import com.alibaba.ariver.app.api.App;
import com.alibaba.ariver.app.api.Page;
import android.app.Application;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ariver.engine.api.bridge.extension.BridgeCallback;
import android.util.Log;
import android.os.Handler;
import me.ele.bridge.BundleRemoteInitializer;
import android.os.Looper;

public class BundleRemoteInitializer$a$a implements Runnable	// class@000755 from classes11.dex
{
    public final Object a;
    public final BundleRemoteInitializer$a b;
    public static IpChange $ipChange;

    public void BundleRemoteInitializer$a$a(BundleRemoteInitializer$a p0,Object p1){
       this.b = p0;
       this.a = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = BundleRemoteInitializer$a$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          BundleRemoteInitializer$a$a tb = this.b;
          tb = this.b;
          this.a.sendPizza(tb.a, tb.b.getApp().getAppId(), tb.c, tb.d, tb.e, tb.f, tb.g, tb.h, tb.i, tb.j, tb.k, tb.l);
          Log.e("pizza_sdk", "BUNDLE_REMOTE_INITIALIZER init success");
          if (BundleRemoteInitializer.access$000().getLooper() != Looper.getMainLooper()) {
             BundleRemoteInitializer.access$000().getLooper().quitSafely();
             BundleRemoteInitializer.access$002(null);
          }
          return;
       }
    }
}
