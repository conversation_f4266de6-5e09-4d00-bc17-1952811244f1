package androidx.activity.ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import android.view.View;
import androidx.activity.OnBackPressedDispatcherOwner;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import com.taobao.taobao.R$id;

public final class ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2 extends Lambda implements g1a	// class@000474 from classes.dex
{
    public static final ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2 INSTANCE;

    static {
       ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2.INSTANCE = new ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2();
    }
    public void ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2(){
       super(1);
    }
    public final OnBackPressedDispatcherOwner invoke(View p0){
       ckf.g(p0, "it");
       OnBackPressedDispatcherOwner tag = p0.getTag(R$id.view_tree_on_back_pressed_dispatcher_owner);
       if (tag instanceof OnBackPressedDispatcherOwner) {
       }else {
          tag = null;
       }
       return tag;
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
}
