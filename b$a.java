package b$a;
import java.lang.Object;
import tb.a07;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;

public final class b$a	// class@001d43 from classes2.dex
{
    public static IpChange $ipChange;

    public void b$a(){
       super();
    }
    public void b$a(a07 p0){
       super();
    }
    public final int a(){
       IpChange $ipChange = b$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.a();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c54b9c7", objArray).intValue();
    }
    public final int b(){
       IpChange $ipChange = b$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.b();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("32418641", objArray).intValue();
    }
    public final int c(){
       IpChange $ipChange = b$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.d();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("3d5833ba", objArray).intValue();
    }
    public final int d(){
       IpChange $ipChange = b$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.e();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2bdb3a3b", objArray).intValue();
    }
    public final int e(){
       IpChange $ipChange = b$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.f();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2eaa1383", objArray).intValue();
    }
    public final int f(){
       IpChange $ipChange = b$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.g();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("12844585", objArray).intValue();
    }
    public final int g(){
       IpChange $ipChange = b$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.h();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("76f58cc8", objArray).intValue();
    }
}
