package tb.abh;
import tb.t2o;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Double;
import java.lang.Object;
import java.lang.String;
import java.lang.Number;
import java.lang.Math;
import java.lang.Integer;

public class abh	// class@001b1a from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x31900087);
    }
    public static double a(double p0){
       IpChange $ipChange = abh.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (abh.b(p0) / 60.00f);
       }
       Object[] objArray = new Object[]{new Double(p0)};
       return $ipChange.ipc$dispatch("b905fbdf", objArray).doubleValue();
    }
    public static double b(double p0){
       IpChange $ipChange = abh.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ((Math.sin((((90.00f - p0) * 3.14f) / 180.00f)) * 40075.36f) / 360.00f);
       }
       Object[] objArray = new Object[]{new Double(p0)};
       return $ipChange.ipc$dispatch("37af38bb", objArray).doubleValue();
    }
    public static double c(double p0){
       IpChange $ipChange = abh.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (abh.a(p0) / 60.00f);
       }
       Object[] objArray = new Object[]{new Double(p0)};
       return $ipChange.ipc$dispatch("ca0252b2", objArray).doubleValue();
    }
    public static double d(int p0,int p1,int p2,int p3,double p4){
       IpChange $ipChange = abh.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0),new Integer(p1),new Integer(p2),new Integer(p3),new Double(p4)};
          return $ipChange.ipc$dispatch("37bb93aa", objArray).doubleValue();
       }else {
          double d = (double)p1 / p4;
          double d1 = (double)p3 / p4;
          return Math.sqrt((((((((double)p0 / p4) - ((double)p2 / p4)) * 3600.00f) * abh.c(((d / 2.00f) + (d1 / 2.00f)))) * (((((double)p0 / p4) - ((double)p2 / p4)) * 3600.00f) * abh.c(((d / 2.00f) + (d1 / 2.00f))))) + ((((d1 - d) * 3600.00f) * 0.03f) * (((d1 - d) * 3600.00f) * 0.03f))));
       }
    }
}
