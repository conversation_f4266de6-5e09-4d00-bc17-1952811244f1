package androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState$SavedState$1;
import android.os.Parcelable$ClassLoaderCreator;
import java.lang.Object;
import android.os.Parcel;
import androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState$SavedState;
import java.lang.ClassLoader;

public class AppCompatDelegateImpl$PanelFeatureState$SavedState$1 implements Parcelable$ClassLoaderCreator	// class@000574 from classes.dex
{

    public void AppCompatDelegateImpl$PanelFeatureState$SavedState$1(){
       super();
    }
    public AppCompatDelegateImpl$PanelFeatureState$SavedState createFromParcel(Parcel p0){
       return AppCompatDelegateImpl$PanelFeatureState$SavedState.readFromParcel(p0, null);
    }
    public AppCompatDelegateImpl$PanelFeatureState$SavedState createFromParcel(Parcel p0,ClassLoader p1){
       return AppCompatDelegateImpl$PanelFeatureState$SavedState.readFromParcel(p0, p1);
    }
    public Object createFromParcel(Parcel p0){
       return this.createFromParcel(p0);
    }
    public Object createFromParcel(Parcel p0,ClassLoader p1){
       return this.createFromParcel(p0, p1);
    }
    public AppCompatDelegateImpl$PanelFeatureState$SavedState[] newArray(int p0){
       AppCompatDelegateImpl$PanelFeatureState$SavedState[] panelFeature = new AppCompatDelegateImpl$PanelFeatureState$SavedState[p0];
       return panelFeature;
    }
    public Object[] newArray(int p0){
       return this.newArray(p0);
    }
}
