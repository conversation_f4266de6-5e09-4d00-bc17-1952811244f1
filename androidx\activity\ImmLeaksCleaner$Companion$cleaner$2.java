package androidx.activity.ImmLeaksCleaner$Companion$cleaner$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.ImmLeaksCleaner$Cleaner;
import android.view.inputmethod.InputMethodManager;
import java.lang.String;
import java.lang.reflect.Field;
import java.lang.Class;
import java.lang.reflect.AccessibleObject;
import androidx.activity.ImmLeaksCleaner$ValidCleaner;
import androidx.activity.ImmLeaksCleaner$FailedInitialization;
import java.lang.Object;

public final class ImmLeaksCleaner$Companion$cleaner$2 extends Lambda implements d1a	// class@000450 from classes.dex
{
    public static final ImmLeaksCleaner$Companion$cleaner$2 INSTANCE;

    static {
       ImmLeaksCleaner$Companion$cleaner$2.INSTANCE = new ImmLeaksCleaner$Companion$cleaner$2();
    }
    public void ImmLeaksCleaner$Companion$cleaner$2(){
       super(0);
    }
    public final ImmLeaksCleaner$Cleaner invoke(){
       ImmLeaksCleaner$ValidCleaner validCleaner;
       try{
          Field declaredFiel = InputMethodManager.class.getDeclaredField("mServedView");
          declaredFiel.setAccessible(true);
          Field declaredFiel1 = InputMethodManager.class.getDeclaredField("mNextServedView");
          declaredFiel1.setAccessible(true);
          Field declaredFiel2 = InputMethodManager.class.getDeclaredField("mH");
          declaredFiel2.setAccessible(true);
          validCleaner = new ImmLeaksCleaner$ValidCleaner(declaredFiel2, declaredFiel, declaredFiel1);
       }catch(java.lang.NoSuchFieldException e0){
          validCleaner = ImmLeaksCleaner$FailedInitialization.INSTANCE;
       }
       return validCleaner;
    }
    public Object invoke(){
       return this.invoke();
    }
}
