package tb.a210;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.LinkedHashMap;
import tb.lc10;
import java.lang.Boolean;
import java.lang.Long;
import kotlin.random.Random;
import kotlin.random.Random$Default;
import tb.jv00;
import java.lang.StringBuilder;
import java.util.List;
import java.util.Iterator;
import tb.gc10;
import tb.ivs;
import java.lang.Integer;
import tb.ckf;

public final class a210	// class@001ad6 from classes8.dex
{
    public static IpChange $ipChange;
    public static final a210 INSTANCE;

    static {
       t2o.a(0x3f8000ab);
       a210.INSTANCE = new a210();
    }
    public void a210(){
       super();
    }
    public final Map a(String p0,String p1,String p2,String p3){
       IpChange $ipChange = a210.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          return $ipChange.ipc$dispatch("f1248b8a", objArray);
       }else {
          LinkedHashMap linkedHashMa = new LinkedHashMap();
          linkedHashMa.put("pageCode", p0);
          if (p1 != null) {
             p0 = linkedHashMa.put("pageNode", p1);
          }
          if (p2 != null) {
             linkedHashMa.put("errorCode", p2);
          }
          if (p3 != null) {
             linkedHashMa.put("errorMsg", p3);
          }
          linkedHashMa.put("gl_track_version", "3");
          return linkedHashMa;
       }
    }
    public final void b(lc10 p0,boolean p1,long p2){
       int i1;
       int i = 0;
       IpChange $ipChange = a210.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1),new Long(p2)};
          $ipChange.ipc$dispatch("b07e3b9a", objArray);
          return;
       }else if(p0 == null){
          return;
       }else {
          jv00 iNSTANCE = jv00.INSTANCE;
          StringBuilder str = "renderP | pageCode="+p0.g()+" node="+p0.h()+" isRefresh="+p0.m()+" duration="+p0.i()+"   expressionTime="+p0.f()+" dimensionCalculateTime="+p0.b()+" track=";
          boolean b = (!(i1 = Random.Default.nextInt() % 20))? true: false;
          iNSTANCE.d("LVTrack", str+b);
          if (i1) {
             return;
          }else {
             Map map = this.a(p0.g(), p0.h(), null, null);
             String str1 = (p0.m())? "refresh": "render";
             map.put("renderType", str1);
             map.put("renderTime", String.valueOf(p0.i()));
             map.put("expressionTime", String.valueOf(p0.f()));
             Iterator iterator = p0.k().iterator();
             long l = 0;
             while (iterator.hasNext()) {
                gc10 ogc10 = iterator.next();
                map.put(ogc10.d(), String.valueOf(ogc10.e()));
                l = l + ogc10.e();
                i = i + 1;
             }
             p2 = (p1)? p2 - p0.j(): p0.i();
             String str2 = String.valueOf(p2);
             map.put("totalTime", str2);
             map.put("templateNum", String.valueOf(i));
             map.put("templateTime", String.valueOf(l));
             str2 = (p1)? "1": "0";
             map.put("pipeline", str2);
             ivs.INSTANCE.track4Click("Page_TaobaoLiveWatch", "taolive_gl_p", map);
             return;
          }
       }
    }
    public final void c(String p0,int p1,String p2){
       IpChange $ipChange = a210.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("207ee2b6", objArray);
          return;
       }else {
          ckf.g(p0, "code");
          Map map = this.a(p0, null, null, null);
          map.put("source", String.valueOf(p1));
          map.put("gl_type", "configHit");
          if (p2 != null) {
             map.put("reason", p2);
          }
          map.put("monitorKey", "tlgl_lv_config_hit");
          ivs.INSTANCE.track4Click("Page_TaobaoLiveWatch", "tlglMonitor", map);
          return;
       }
    }
    public final void d(String p0,String p1,String p2){
       IpChange $ipChange = a210.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("b83d3817", objArray);
          return;
       }else {
          ckf.g(p0, "page");
          Map map = this.a(p0, null, p1, p2);
          map.put("gl_type", "createView");
          map.put("monitorKey", "tlgl_ultron_render_error");
          ivs.INSTANCE.track4Click("Page_TaobaoLiveWatch", "tlglMonitor", map);
          return;
       }
    }
    public final void e(lc10 p0){
       IpChange $ipChange = a210.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("807c4d6e", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if(!p0.l()){
          return;
       }else {
          jv00.INSTANCE.b("LVTrack", "trackRenderResult | has Error. code="+p0.c()+"   page="+p0.g()+"   node="+p0.h());
          Map map = this.a(p0.g(), p0.h(), p0.c(), p0.d());
          Iterator iterator = p0.e().iterator();
          while (iterator.hasNext()) {
             gc10 ogc10 = iterator.next();
             String str = ogc10.d();
             map.put(str, "".append(ogc10.b()).append('_').append(ogc10.c()).append('_').append(ogc10.f()).toString());
          }
          map.put("gl_type", "renderView");
          map.put("errorDXSize", String.valueOf(p0.e().size()));
          map.put("monitorKey", "tlgl_ultron_render_error");
          ivs.INSTANCE.track4Click("Page_TaobaoLiveWatch", "tlglMonitor", map);
          return;
       }
    }
}
