package tv.danmaku.ijk.media.player.TaobaoAudioOnlyPlayer$AudioEventHandler;
import android.os.Handler;
import tb.t2o;
import tv.danmaku.ijk.media.player.TaobaoAudioOnlyPlayer;
import android.os.Looper;
import java.lang.ref.WeakReference;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.os.Message;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.ref.Reference;
import tv.danmaku.ijk.media.player.EventData;
import tb.feh;
import tv.danmaku.ijk.media.player.MonitorMediaPlayer;
import com.taobao.taobaoavsdk.AVSDKLog;
import tv.danmaku.ijk.media.player.AbstractMediaPlayer;
import tv.danmaku.ijk.media.player.IMediaPlayer;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioSeekStartListener;
import java.util.Iterator;
import java.util.List;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioInfoListener;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioErrorListener;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioCompletionListener;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioSeekCompletionListener;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.Boolean;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioPreCompletionListener;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioLoopCompletionListener;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioPreparedListener;

public class TaobaoAudioOnlyPlayer$AudioEventHandler extends Handler	// class@0010f9 from classes11.dex
{
    private boolean bFirstFrameRendered;
    private WeakReference mWeakPlayer;
    public static IpChange $ipChange;

    static {
       t2o.a(0x30b0017c);
    }
    public void TaobaoAudioOnlyPlayer$AudioEventHandler(TaobaoAudioOnlyPlayer p0,Looper p1){
       super(p1);
       this.bFirstFrameRendered = false;
       this.mWeakPlayer = new WeakReference(p0);
    }
    public static Object ipc$super(TaobaoAudioOnlyPlayer$AudioEventHandler p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in tv/danmaku/ijk/media/player/TaobaoAudioOnlyPlayer$AudioEventHandler");
    }
    public void handleMessage(Message p0){
       TaobaoAudioOnlyPlayer taobaoAudioO;
       Message message;
       Message what;
       AbstractMediaPlayer mOnAudioSeek;
       Iterator iterator;
       EventData arg1;
       AbstractMediaPlayer mOnAudioInfo;
       EventData uEventData1;
       AbstractMediaPlayer mOnAudioInfo1;
       Iterator iterator1;
       AbstractMediaPlayer mOnAudioPreC;
       object oobject = this;
       object oobject1 = p0;
       int i = 1;
       int i1 = 0;
       int i2 = 2;
       IpChange $ipChange = TaobaoAudioOnlyPlayer$AudioEventHandler.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i2];
          objArray[i1] = oobject;
          objArray[i] = oobject1;
          $ipChange.ipc$dispatch("282a8c19", objArray);
          return;
       }else if((taobaoAudioO = oobject.mWeakPlayer.get()) != null && (TaobaoAudioOnlyPlayer.access$100(taobaoAudioO))){
          if ((message = oobject1.obj) != null) {
             AVSDKLog.e(taobaoAudioO.getLogContext(), "handleMessage: what="+oobject1.what+", arg1="+message.arg1+", arg2="+message.arg2+", arg3="+message.arg3+", obj="+message.obj);
          }
          EventData uEventData = message.obj;
          if (uEventData.isEmpty()) {
             AVSDKLog.e(taobaoAudioO.getLogContext(), "token is empty");
             return;
          }else if((what = oobject1.what) != i){
             if (what != i2) {
                if (what != 4) {
                   if (what != 100) {
                      if (what != 200) {
                         if (what == 400) {
                            AVSDKLog.e(taobaoAudioO.getLogContext(), "AUDIO_MEDIA_SEEK_START is "+message.arg1);
                            if ((mOnAudioSeek = taobaoAudioO.mOnAudioSeekStartListener) != null) {
                               mOnAudioSeek.onSeekStart(taobaoAudioO, uEventData);
                            }
                            if ((mOnAudioSeek = taobaoAudioO.mOnAudioSeekStartListeners) != null) {
                               iterator = mOnAudioSeek.iterator();
                               while (iterator.hasNext()) {
                                  iterator.next().onSeekStart(taobaoAudioO, uEventData);
                               }
                            }
                         }
                         return;
                      }else {
                         AVSDKLog.e(taobaoAudioO.getLogContext(), "AUDIO_MEDIA_INFO is "+message.arg1);
                         arg1 = message.arg1;
                         if (!(arg1 - 0x2f80)) {
                            if ((mOnAudioInfo = taobaoAudioO.mOnAudioInfoListener) != null) {
                               uEventData1 = uEventData;
                               what = message;
                               mOnAudioInfo.onInfo(taobaoAudioO, arg1, message.arg2, message.arg3, message.obj, uEventData1);
                            }else {
                               uEventData1 = uEventData;
                               what = message;
                            }
                            if ((mOnAudioInfo1 = taobaoAudioO.mOnAudioInfoListeners) != null) {
                               iterator1 = mOnAudioInfo1.iterator();
                               while (iterator1.hasNext()) {
                                  iterator1.next().onInfo(taobaoAudioO, what.arg1, what.arg2, what.arg3, what.obj, uEventData1);
                               }
                            }
                         }
                      }
                   }else {
                      uEventData1 = uEventData;
                      what = message;
                      if ((mOnAudioInfo1 = taobaoAudioO.mOnAudioErrorListener) == null || (!mOnAudioInfo1.onError(taobaoAudioO, (int)what.arg1, (int)what.arg2, uEventData1) && (mOnAudioInfo1 = taobaoAudioO.mOnAudioCompletionListener) != null)) {
                         mOnAudioInfo1.onCompletion(taobaoAudioO, uEventData1);
                      }
                      if ((mOnAudioInfo1 = taobaoAudioO.mOnAudioErrorListeners) != null) {
                         iterator1 = mOnAudioInfo1.iterator();
                         while (iterator1.hasNext()) {
                            iterator1.next().onError(taobaoAudioO, (int)what.arg1, (int)what.arg2, uEventData1);
                         }
                      }
                      return;
                   }
                }else {
                   uEventData1 = uEventData;
                   if ((mOnAudioSeek = taobaoAudioO.mOnAudioSeekCompletionListener) != null) {
                      mOnAudioSeek.onSeekComplete(taobaoAudioO, uEventData1);
                   }
                   if ((mOnAudioSeek = taobaoAudioO.mOnAudioSeekCompletionListeners) != null) {
                      iterator = mOnAudioSeek.iterator();
                      while (iterator.hasNext()) {
                         iterator.next().onSeekComplete(taobaoAudioO, uEventData1);
                      }
                   }
                }
                return;
             }else {
                uEventData1 = uEventData;
                boolean b = (TaobaoAudioOnlyPlayer.access$300(taobaoAudioO).containsKey(uEventData1))? TaobaoAudioOnlyPlayer.access$300(taobaoAudioO).get(uEventData1).booleanValue(): false;
                if (TaobaoAudioOnlyPlayer.access$400(taobaoAudioO).containsKey(uEventData1)) {
                   i1 = TaobaoAudioOnlyPlayer.access$400(taobaoAudioO).get(uEventData1).booleanValue();
                }
                if ((mOnAudioPreC = taobaoAudioO.mOnAudioPreCompletionListeners) != null) {
                   Iterator iterator2 = mOnAudioPreC.iterator();
                   while (iterator2.hasNext()) {
                      iterator2.next().onPreCompletion(taobaoAudioO, uEventData1);
                   }
                }
                if (i1) {
                   if ((mOnAudioInfo1 = taobaoAudioO.mOnAudioLoopCompletionListeners) != null) {
                      iterator1 = mOnAudioInfo1.iterator();
                      while (iterator1.hasNext()) {
                         iterator1.next().onLoopCompletion(taobaoAudioO, uEventData1);
                      }
                   }
                   if (taobaoAudioO.bPauseInBackground != null && b) {
                      AVSDKLog.d("avsdk", "playback complete but in pause state");
                      return;
                   }else {
                      taobaoAudioO.start(uEventData1);
                   }
                }else {
                   TaobaoAudioOnlyPlayer.access$200(taobaoAudioO).remove(uEventData1);
                   TaobaoAudioOnlyPlayer.access$300(taobaoAudioO).put(uEventData1, Boolean.TRUE);
                   if ((mOnAudioSeek = taobaoAudioO.mOnAudioCompletionListener) != null) {
                      mOnAudioSeek.onCompletion(taobaoAudioO, uEventData1);
                   }
                   if ((mOnAudioSeek = taobaoAudioO.mOnAudioCompletionListeners) != null) {
                      iterator = mOnAudioSeek.iterator();
                      while (iterator.hasNext()) {
                         iterator.next().onCompletion(taobaoAudioO, uEventData1);
                      }
                   }
                }
                return;
             }
          }else {
             uEventData1 = uEventData;
             what = message;
             if ((mOnAudioInfo1 = taobaoAudioO.mOnAudioPreparedListener) != null) {
                mOnAudioInfo1.onPrepared(taobaoAudioO, what.obj);
             }
             if ((mOnAudioInfo1 = taobaoAudioO.mOnAudioPreparedListeners) != null) {
                iterator1 = mOnAudioInfo1.iterator();
                while (iterator1.hasNext()) {
                   iterator1.next().onPrepared(taobaoAudioO, what.obj);
                }
             }
             if (!TaobaoAudioOnlyPlayer.access$200(taobaoAudioO).containsKey(uEventData1)) {
                taobaoAudioO.pause(uEventData1);
             }else {
                taobaoAudioO.start(uEventData1);
             }
          }
       }
       return;
    }
}
