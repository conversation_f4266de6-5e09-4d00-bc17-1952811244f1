package tv.danmaku.ijk.media.player.TaobaoAudioOnlyPlayer$1;
import java.lang.Runnable;
import tv.danmaku.ijk.media.player.TaobaoAudioOnlyPlayer;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class TaobaoAudioOnlyPlayer$1 implements Runnable	// class@0010f8 from classes11.dex
{
    public final TaobaoAudioOnlyPlayer this$0;
    public static IpChange $ipChange;

    public void TaobaoAudioOnlyPlayer$1(TaobaoAudioOnlyPlayer p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          TaobaoAudioOnlyPlayer.access$000(this.this$0);
          return;
       }
    }
}
