package tb.a10$a;
import java.lang.Runnable;
import tb.a10;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.android.dinamicx.DinamicXEngine;
import java.lang.StringBuilder;
import tb.h36;
import java.util.HashSet;

public class a10$a implements Runnable	// class@00181d from classes5.dex
{
    public final boolean a;
    public final Object b;
    public final Object c;
    public final String d;
    public final a10 e;
    public static IpChange $ipChange;

    public void a10$a(a10 p0,boolean p1,Object p2,Object p3,String p4){
       this.e = p0;
       this.a = p1;
       this.b = p2;
       this.c = p3;
       this.d = p4;
       super();
    }
    public void run(){
       IpChange $ipChange = a10$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(this.a != null){
          if (DinamicXEngine.j0()) {
             String[] stringArray = new String[]{"batch beforeDataExpose exposed at key: "+this.b};
             h36.g("DXExposure", stringArray);
          }
          this.e.f.add(this.b);
          this.e.G(this, this.b, this.c, true, this.d);
          this.e.y();
       }else {
          this.e.G(this, this.b, this.c, false, this.d);
       }
       return;
    }
}
