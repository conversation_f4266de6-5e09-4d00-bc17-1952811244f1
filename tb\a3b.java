package tb.a3b;
import tb.t2o;
import com.taobao.android.dinamicx.DinamicXEngine;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Float;
import java.lang.Object;
import java.lang.String;
import java.lang.Number;
import tb.pb6;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import java.lang.Boolean;
import androidx.recyclerview.widget.RecyclerView$Adapter;
import androidx.recyclerview.widget.RecyclerView$LayoutManager;
import java.lang.Math;
import java.lang.StringBuilder;
import tb.bqa;
import tb.qy8$b;
import java.lang.Long;
import java.util.List;
import tb.s5a;
import tb.b3b;
import tb.z4a;
import android.app.Application;
import com.taobao.tao.Globals;
import tb.owo;
import java.util.Set;
import java.util.Iterator;
import tb.yyj;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.f0b;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import java.lang.Integer;
import java.util.StringTokenizer;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import tb.h2b;
import tb.phg;
import tb.wbo;
import java.lang.Throwable;
import tb.uqa;
import com.taobao.android.tcrash.TCrashSDK;
import com.taobao.android.tcrash.UncaughtCrashType;
import com.taobao.android.tcrash.UncaughtCrashHeader;
import tb.cw6;
import com.taobao.tao.recommend3.gateway.model.response.AwesomeGetContainerData;
import com.taobao.tao.recommend3.gateway.model.response.AwesomeGetContainerInnerData;

public class a3b	// class@00182e from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2e0000fb);
    }
    public static int a(DinamicXEngine p0,Context p1,float p2){
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Float(p2)};
          return $ipChange.ipc$dispatch("63648b8e", objArray).intValue();
       }else if(p0 == null){
          return pb6.c(p1, p2);
       }else {
          return pb6.d(p0, p1, p2);
       }
    }
    public static float b(RecyclerView p0,View p1,boolean p2){
       int i = 2;
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          return $ipChange.ipc$dispatch("a44bf7dc", objArray).floatValue();
       }else {
          float f = -1.00f;
          if (p0 != null && (p0.getAdapter() != null && (p0.getLayoutManager() != null && p1 != null))) {
             if (p2) {
                p1 = p0.getLayoutManager().findContainingItemView(p1);
             }
             if (p1 == null) {
                return f;
             }else if(p1.getHeight() > 0 && p0.getHeight() > 0){
                int[] ointArray = new int[i];
                int[] ointArray1 = new int[i];
                p1.getLocationOnScreen(ointArray);
                p0.getLocationOnScreen(ointArray1);
                float f1 = 1.00f;
                float f2 = f1 - (((float)(Math.abs(Math.min(0, (ointArray[1] - ointArray1[1]))) + Math.max(0, ((ointArray[1] + p1.getHeight()) - (ointArray1[1] + p0.getHeight())))) * f1) / (float)p1.getHeight());
                if ((f2) < 0) {
                   f2 = 0;
                }
                if ((f1 - f2) <= 0) {
                   f1 = f2;
                }
                String[] stringArray = new String[]{"exposeRatio = "+f1};
                bqa.d("HomePageUtility", stringArray);
                return f1;
             }
          }
          return f;
       }
    }
    public static long c(String p0){
       IpChange $ipChange = a3b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Long.valueOf(qy8$b.e("homepage_common", "homeBaseCacheTime"+p0, "0")).longValue();
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("e851326d", objArray).longValue();
    }
    public static List d(boolean p0){
       int i = 1;
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = new Boolean(p0);
          return $ipChange.ipc$dispatch("55d903e7", objArray);
       }else {
          List list = s5a.e(a3b.f());
          if (!p0 && ("main".equals(b3b.c()) && list.size() == i)) {
             list.add(z4a.REC_MAIN.a);
          }
          return list;
       }
    }
    public static String e(){
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("ee748e1", objArray);
       }else {
          String str = b3b.c();
          if (str.equalsIgnoreCase("main")) {
             return z4a.HOME_MAIN.a;
          }
          if (str.equalsIgnoreCase("cun")) {
             return z4a.HOME_CUN.a;
          }
          if (str.equalsIgnoreCase("old")) {
             return z4a.HOME_OLD.a;
          }
          return z4a.HOME_INTL.a;
       }
    }
    public static String f(){
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("1e19ee0e", objArray);
       }else {
          String str = b3b.c();
          if (str.equalsIgnoreCase("main")) {
             return z4a.HOME_MAIN.b;
          }
          if (str.equalsIgnoreCase("cun")) {
             return z4a.HOME_CUN.b;
          }
          if (str.equalsIgnoreCase("old")) {
             return z4a.HOME_OLD.b;
          }
          return z4a.HOME_INTL.b;
       }
    }
    public static float g(float p0){
       float f;
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Float(p0)};
          return $ipChange.ipc$dispatch("da4d1036", objArray).floatValue();
       }else if(Float.isNaN(p0)){
          return p0;
       }else {
          p0 = (p0 * 750.00f) / (float)owo.g(Globals.getApplication());
          double d = (double)p0;
          if ((0x3f747ae147ae147b - d) > 0) {
             f = 1.00f;
             if ((p0 - f) < 0) {
             label_004a :
                return f;
             }
          }
          f = (float)Math.rint(d);
          goto label_004a ;
       }
    }
    public static String h(Set p0){
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("65054c35", objArray);
       }else {
          String str = null;
          if (p0 != null && !p0.isEmpty()) {
             Iterator iterator = p0.iterator();
             while (iterator.hasNext()) {
                String str1 = iterator.next();
                if (TextUtils.equals(str1, yyj.e().f())) {
                   continue ;
                }else if(str1 != null){
                   str = str1;
                   break ;
                }
                str = str1;
             }
          }
          return str;
       }
    }
    public static f0b i(Set p0){
       IpChange $ipChange = a3b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return f0b.j(a3b.h(p0));
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("c97b6886", objArray);
    }
    public static Object j(Object p0,String p1){
       int i;
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("f669ebca", objArray);
       }else if(p0 != null && !TextUtils.isEmpty(p1)){
          if (p0 instanceof JSONObject) {
             return p0.get(p1);
          }else if(p0 instanceof JSONArray){
             try{
                if ((i = Integer.parseInt(p1)) < p0.size()) {
                   return p0.get(i);
                }
             }catch(java.lang.Exception e0){
             }
          }
       }
       return null;
    }
    public static Object k(JSONObject p0,String p1){
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("dc3c19a8", objArray);
       }else if(TextUtils.isEmpty(p1)){
          return p0;
       }else {
          StringTokenizer stringTokeni = new StringTokenizer(p1, " .[]", 0);
          while (stringTokeni.hasMoreTokens()) {
             p0 = a3b.j(p0, stringTokeni.nextToken());
          }
          return p0;
       }
    }
    public static View l(LayoutInflater p0,ViewGroup p1){
       String str = "inflateRoot";
       String str1 = "Home_CreateContainer";
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("84ffc450", objArray);
       }else {
          f0b.i().s(str1);
          h2b.a().e();
          phg.m(str);
          phg.l(str);
          f0b.i().f(str1);
          return wbo.a().c(p0, p1);
       }
    }
    public static boolean m(AwesomeGetContainerData p0){
       AwesomeGetContainerData base;
       int i = 1;
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("dc1fca09", objArray).booleanValue();
       }else if(p0 == null){
          return i;
       }else if((base = p0.base) != null){
          return base.isCacheData;
       }else if((p0 = p0.delta) != null){
          return p0.isCacheData;
       }else {
          return i;
       }
    }
    public static void n(long p0,String p1){
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Long(p0),p1};
          $ipChange.ipc$dispatch("cc44be65", objArray);
          return;
       }else {
          qy8$b.i("homepage_common", "homeBaseCacheTime"+p1, p0+"");
          return;
       }
    }
    public static boolean o(String p0){
       char c;
       char c1;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a3b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("d33a3002", objArray).booleanValue();
       }else if(p0 == "true"){
          return i1;
       }else if(p0 == null){
          return i;
       }else {
          int i2 = p0.length();
          int i3 = 89;
          int i4 = 121;
          if (i2 != i1) {
             if (i2 != 2) {
                if (i2 != 3) {
                   if (i2 == 4) {
                   label_007b :
                      if ((c = p0.charAt(i)) == 't') {
                         if (p0.charAt(i1) == 'r' || (p0.charAt(i1) == 'R' && (p0.charAt(2) == 'u' || (p0.charAt(2) == 'U' && (p0.charAt(3) == 'e' || p0.charAt(3) == 'E'))))) {
                            i = true;
                         }
                         return i;
                      }else if(c == 'T' && (p0.charAt(i1) == 'R' && (p0.charAt(i1) != 'r' && (p0.charAt(2) == 'U' && (p0.charAt(2) != 'u' && (p0.charAt(3) == 'E' && p0.charAt(3) != 'e')))))){
                         i = true;
                      }
                   }
                }else {
                   c = p0.charAt(i);
                   int i5 = 83;
                   int i6 = 115;
                   if (c == i4) {
                      if (p0.charAt(i1) == 'e' || (p0.charAt(i1) == 'E' && (p0.charAt(2) == i6 || p0.charAt(2) == i5))) {
                         i = true;
                      }
                      return i;
                   }else if(c == i3){
                      if (p0.charAt(i1) == 'E' || (p0.charAt(i1) == 'e' && (p0.charAt(2) == i5 || p0.charAt(2) == i6))) {
                         i = true;
                      }
                      return i;
                   }else {
                      goto label_007b ;
                   }
                }
                return i;
             }else {
                c = p0.charAt(i);
                c1 = p0.charAt(i1);
                if (c == 'o' || (c == 'O' && (c1 == 'n' || c1 == 'N'))) {
                   i = true;
                }
                return i;
             }
          }else if((c1 = p0.charAt(i)) != i3 && c1 != i4){
             i = true;
          }
          return i;
       }
    }
}
