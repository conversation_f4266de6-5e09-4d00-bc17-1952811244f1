package androidx.activity.OnBackPressedDispatcher$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.OnBackPressedDispatcher;
import java.lang.Object;
import androidx.activity.BackEventCompat;
import tb.xhv;
import java.lang.String;
import tb.ckf;

public final class OnBackPressedDispatcher$1 extends Lambda implements g1a	// class@000456 from classes.dex
{
    public final OnBackPressedDispatcher this$0;

    public void OnBackPressedDispatcher$1(OnBackPressedDispatcher p0){
       this.this$0 = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(BackEventCompat p0){
       ckf.g(p0, "backEvent");
       OnBackPressedDispatcher.access$onBackStarted(this.this$0, p0);
    }
}
