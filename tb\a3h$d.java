package tb.a3h$d;
import java.lang.Runnable;
import tb.a3h;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.a3h$w0;

public class a3h$d implements Runnable	// class@001e6b from classes10.dex
{
    public final String a;
    public final a3h b;
    public static IpChange $ipChange;

    public void a3h$d(a3h p0,String p1){
       this.b = p0;
       this.a = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$d.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.B(this.b) != null){
          a3h.B(this.b).g(this.a);
       }
       return;
    }
}
