package tb.a2h;
import tb.t2o;
import android.content.Context;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import tb.arq;
import com.taobao.android.nav.Nav;
import java.lang.Integer;
import android.os.Bundle;

public class a2h	// class@001adc from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x32900243);
    }
    public static void a(Context p0,String p1){
       IpChange $ipChange = a2h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("fa1fe2fa", objArray);
          return;
       }else if(p0 != null && !arq.a(p1)){
          Nav.from(p0).toUri(p1.trim());
       }
       return;
    }
    public static void b(Context p0,String p1,int p2){
       IpChange $ipChange = a2h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Integer(p2)};
          $ipChange.ipc$dispatch("f254603c", objArray);
          return;
       }else if(p0 != null && !arq.a(p1)){
          Nav nav = Nav.from(p0);
          if (p2 != -1) {
             nav.forResult(p2);
          }
          nav.toUri(p1.trim());
       }
       return;
    }
    public static void c(Context p0,String p1,Bundle p2){
       IpChange $ipChange = a2h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          $ipChange.ipc$dispatch("32bd0062", objArray);
          return;
       }else if(p0 != null && !arq.a(p1)){
          Nav.from(p0).withExtras(p2).toUri(p1.trim());
       }
       return;
    }
}
