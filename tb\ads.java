package tb.ads;
import tb.t2o;
import java.lang.Object;
import tb.bbs;
import android.view.ViewGroup;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.swd;
import com.taobao.themis.kernel.page.ITMSPage;
import tb.tll;
import tb.x5d;
import tb.zcs;

public final class ads	// class@001ec5 from classes10.dex
{
    public static IpChange $ipChange;
    public static final ads INSTANCE;

    static {
       t2o.a(0x35300039);
       ads.INSTANCE = new ads();
    }
    public void ads(){
       super();
    }
    public final ViewGroup a(bbs p0){
       swd oswd;
       ITMSPage topPage;
       tll pageContext;
       x5d pageContaine;
       IpChange $ipChange = ads.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d3256bf0", objArray);
       }else {
          ViewGroup viewGroup = null;
          if (p0 == null || ((oswd = p0.W()) == null || ((topPage = oswd.getTopPage()) == null || ((pageContext = topPage.getPageContext()) == null || (pageContaine = pageContext.getPageContainer()) == null)))) {
             return viewGroup;
          }
          if (pageContaine instanceof zcs) {
          }else {
             pageContaine = viewGroup;
          }
          if (pageContaine != null) {
             viewGroup = pageContaine.f();
          }
          return viewGroup;
       }
    }
}
