package kotlinx.datetime.format.DateTimeComponents$hour$2;
import kotlin.jvm.internal.MutablePropertyReference0Impl;
import java.lang.Object;
import tb.c230;
import java.lang.Class;
import java.lang.String;
import kotlin.jvm.internal.CallableReference;
import java.lang.Integer;

public final class DateTimeComponents$hour$2 extends MutablePropertyReference0Impl	// class@0006da from classes11.dex
{

    public void DateTimeComponents$hour$2(Object p0){
       super(p0, c230.class, "hour", "getHour\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(){
       return this.receiver.b();
    }
    public void set(Object p0){
       this.receiver.D(p0);
    }
}
