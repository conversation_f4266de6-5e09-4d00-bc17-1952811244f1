package kotlinx.datetime.format.DateTimeComponents$offsetMinutesOfHour$2;
import kotlin.jvm.internal.MutablePropertyReference0Impl;
import java.lang.Object;
import tb.d230;
import java.lang.Class;
import java.lang.String;
import kotlin.jvm.internal.CallableReference;
import java.lang.Integer;

public final class DateTimeComponents$offsetMinutesOfHour$2 extends MutablePropertyReference0Impl	// class@0006df from classes11.dex
{

    public void DateTimeComponents$offsetMinutesOfHour$2(Object p0){
       super(p0, d230.class, "minutesOfHour", "getMinutesOfHour\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(){
       return this.receiver.A();
    }
    public void set(Object p0){
       this.receiver.s(p0);
    }
}
