package mtopsdk.mtop.stat.PrefetchStatistics;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import mtopsdk.mtop.stat.IUploadStats;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.common.util.TBSdkLog;
import java.util.HashSet;
import java.util.Set;
import java.lang.StringBuilder;
import java.lang.Throwable;
import java.util.HashMap;
import java.lang.Double;
import java.util.Map;

public class PrefetchStatistics	// class@0007fc from classes11.dex
{
    private String seqNo;
    private IUploadStats uploadStats;
    public static IpChange $ipChange;
    private static final String MTOP_PREFETCH;
    private static final String MTOP_STATS_MODULE;
    private static final String TAG;
    private static AtomicBoolean isRegistered;

    static {
       t2o.a(0x25300122);
       PrefetchStatistics.isRegistered = new AtomicBoolean(false);
    }
    public void PrefetchStatistics(IUploadStats p0){
       super();
       this.seqNo = "";
       this.uploadStats = p0;
    }
    private void registerPrefetchStats(){
       PrefetchStatistics tuploadStats;
       String str = "mtopsdk.PrefetchStatistics";
       IpChange $ipChange = PrefetchStatistics.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("1a6a910c", objArray);
          return;
       }else if(this.uploadStats == null){
          TBSdkLog.e(str, this.seqNo, "[registerPrefetchStats]register MtopStats error, uploadStats=null");
          return;
       }else {
          HashSet hashSet = new HashSet();
          hashSet.add("api");
          hashSet.add("version");
          hashSet.add("key");
          HashSet hashSet1 = new HashSet();
          hashSet1.add("time");
          hashSet1.add("type");
          if ((tuploadStats = this.uploadStats) != null) {
             tuploadStats.onRegister("mtopsdk", "mtopPrefetch", hashSet, hashSet1, false);
          }
          return;
       }
    }
    public void doPrefetchCommit(String p0,HashMap p1){
       int i = 2;
       int i1 = 1;
       int i2 = 0;
       int i3 = 3;
       IpChange $ipChange = PrefetchStatistics.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i3];
          objArray[i2] = this;
          objArray[i1] = p0;
          objArray[i] = p1;
          $ipChange.ipc$dispatch("f4007429", objArray);
          return;
       }else if(p1 == null){
          return;
       }else {
          String str = p1.get("data_seq");
          this.seqNo = str;
          if (this.uploadStats == null) {
             TBSdkLog.e("mtopsdk.PrefetchStatistics", str, "[doPrefetchCommit]register MtopStats error, uploadStats=null");
             return;
          }else if("TYPE_HIT".equals(p0)){
             i = 1;
          }else if("TYPE_MISS".equals(p0)){
             if ("TYPE_EXPIRE".equals(p0)) {
                i = 3;
             }else if("TYPE_CLEAR".equals(p0)){
                i = 4;
             }else if("TYPE_MERGE".equals(p0)){
                i = 5;
             }else {
                i = 0;
             }
          }
          if (PrefetchStatistics.isRegistered.compareAndSet(i2, i1)) {
             this.registerPrefetchStats();
          }
          HashMap hashMap = new HashMap();
          hashMap.put("api", p1.get("data_api"));
          hashMap.put("version", p1.get("data_version"));
          hashMap.put("key", p1.get("data_key"));
          HashMap hashMap1 = new HashMap();
          hashMap1.put("time", Double.valueOf(Double.parseDouble(p1.get("data_cost_time"))));
          hashMap1.put("type", Double.valueOf((double)i));
          this.uploadStats.onCommit("mtopsdk", "mtopPrefetch", hashMap, hashMap1);
          return;
       }
    }
}
