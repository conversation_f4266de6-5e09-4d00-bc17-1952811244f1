package androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState;
import java.lang.Object;
import android.os.Bundle;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.view.menu.MenuPresenter;
import androidx.appcompat.view.menu.MenuPresenter$Callback;
import androidx.appcompat.view.menu.MenuView;
import androidx.appcompat.view.menu.ListMenuPresenter;
import com.taobao.taobao.R$layout;
import android.content.Context;
import android.view.ViewGroup;
import android.widget.ListAdapter;
import android.widget.Adapter;
import android.os.Parcelable;
import androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState$SavedState;
import android.util.TypedValue;
import android.content.res.Resources;
import android.content.res.Resources$Theme;
import com.taobao.taobao.R$attr;
import com.taobao.taobao.R$style;
import androidx.appcompat.view.ContextThemeWrapper;
import com.taobao.taobao.R$styleable;
import android.content.res.TypedArray;

public final class AppCompatDelegateImpl$PanelFeatureState	// class@000576 from classes.dex
{
    public int background;
    public View createdPanelView;
    public ViewGroup decorView;
    public int featureId;
    public Bundle frozenActionViewState;
    public Bundle frozenMenuState;
    public int gravity;
    public boolean isHandled;
    public boolean isOpen;
    public boolean isPrepared;
    public ListMenuPresenter listMenuPresenter;
    public Context listPresenterContext;
    public MenuBuilder menu;
    public boolean qwertyMode;
    public boolean refreshDecorView;
    public boolean refreshMenuContent;
    public View shownPanelView;
    public boolean wasLastOpen;
    public int windowAnimations;
    public int x;
    public int y;

    public void AppCompatDelegateImpl$PanelFeatureState(int p0){
       super();
       this.featureId = p0;
       this.refreshDecorView = false;
    }
    public void applyFrozenState(){
       AppCompatDelegateImpl$PanelFeatureState tmenu;
       AppCompatDelegateImpl$PanelFeatureState tfrozenMenuS;
       if ((tmenu = this.menu) != null && (tfrozenMenuS = this.frozenMenuState) != null) {
          tmenu.restorePresenterStates(tfrozenMenuS);
          this.frozenMenuState = null;
       }
       return;
    }
    public void clearMenuPresenters(){
       AppCompatDelegateImpl$PanelFeatureState tmenu;
       if ((tmenu = this.menu) != null) {
          tmenu.removeMenuPresenter(this.listMenuPresenter);
       }
       this.listMenuPresenter = null;
       return;
    }
    public MenuView getListMenuView(MenuPresenter$Callback p0){
       if (this.menu == null) {
          return null;
       }
       if (this.listMenuPresenter == null) {
          ListMenuPresenter listMenuPres = new ListMenuPresenter(this.listPresenterContext, R$layout.abc_list_menu_item_layout);
          this.listMenuPresenter = listMenuPres;
          listMenuPres.setCallback(p0);
          this.menu.addMenuPresenter(this.listMenuPresenter);
       }
       return this.listMenuPresenter.getMenuView(this.decorView);
    }
    public boolean hasPanelItems(){
       boolean b = false;
       if (this.shownPanelView == null) {
          return b;
       }
       if (this.createdPanelView != null) {
          return true;
       }
       if (this.listMenuPresenter.getAdapter().getCount() > 0) {
          b = true;
       }
       return b;
    }
    public void onRestoreInstanceState(Parcelable p0){
       this.featureId = p0.featureId;
       this.wasLastOpen = p0.isOpen;
       this.frozenMenuState = p0.menuState;
       this.shownPanelView = null;
       this.decorView = null;
    }
    public Parcelable onSaveInstanceState(){
       AppCompatDelegateImpl$PanelFeatureState$SavedState panelFeature = new AppCompatDelegateImpl$PanelFeatureState$SavedState();
       panelFeature.featureId = this.featureId;
       panelFeature.isOpen = this.isOpen;
       if (this.menu != null) {
          Bundle uBundle = new Bundle();
          panelFeature.menuState = uBundle;
          this.menu.savePresenterStates(uBundle);
       }
       return panelFeature;
    }
    public void setMenu(MenuBuilder p0){
       AppCompatDelegateImpl$PanelFeatureState tmenu = this.menu;
       if (p0 == tmenu) {
          return;
       }
       if (tmenu != null) {
          tmenu.removeMenuPresenter(this.listMenuPresenter);
       }
       this.menu = p0;
       if (p0 != null && (tmenu = this.listMenuPresenter) != null) {
          p0.addMenuPresenter(tmenu);
       }
       return;
    }
    public void setStyle(Context p0){
       TypedValue resourceId;
       TypedValue typedValue = new TypedValue();
       Resources$Theme theme = p0.getResources().newTheme();
       theme.setTo(p0.getTheme());
       theme.resolveAttribute(R$attr.actionBarPopupTheme, typedValue, true);
       if ((resourceId = typedValue.resourceId) != null) {
          theme.applyStyle(resourceId, true);
       }
       theme.resolveAttribute(R$attr.panelMenuListTheme, typedValue, true);
       if ((typedValue = typedValue.resourceId) != null) {
          theme.applyStyle(typedValue, true);
       }else {
          theme.applyStyle(R$style.Theme_AppCompat_CompactMenu, true);
       }
       ContextThemeWrapper typedValue1 = new ContextThemeWrapper(p0, 0);
       typedValue1.getTheme().setTo(theme);
       this.listPresenterContext = typedValue1;
       TypedArray typedArray = typedValue1.obtainStyledAttributes(R$styleable.AppCompatTheme);
       this.background = typedArray.getResourceId(R$styleable.AppCompatTheme_panelBackground, 0);
       this.windowAnimations = typedArray.getResourceId(R$styleable.AppCompatTheme_android_windowAnimationStyle, 0);
       typedArray.recycle();
       return;
    }
}
