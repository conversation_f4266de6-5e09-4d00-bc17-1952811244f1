package tb.a15;
import android.text.style.MetricAffectingSpan;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.graphics.Paint;
import com.android.alibaba.ip.runtime.IpChange;
import android.text.TextPaint;

public class a15 extends MetricAffectingSpan	// class@001ad0 from classes8.dex
{
    public final float a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1f1000b0);
    }
    public void a15(float p0){
       super();
       this.a = p0;
    }
    public static Object ipc$super(a15 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/infoflow/core/subservice/base/item/dxservice/impl/dinamic3/view/span/CustomLetterSpacingSpan");
    }
    public final void a(Paint p0){
       IpChange $ipChange = a15.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("bd95da0a", objArray);
          return;
       }else {
          p0.setLetterSpacing(this.a);
          return;
       }
    }
    public void updateDrawState(TextPaint p0){
       IpChange $ipChange = a15.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c21f6b6b", objArray);
          return;
       }else {
          this.a(p0);
          return;
       }
    }
    public void updateMeasureState(TextPaint p0){
       IpChange $ipChange = a15.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("77906dc3", objArray);
          return;
       }else {
          this.a(p0);
          return;
       }
    }
}
