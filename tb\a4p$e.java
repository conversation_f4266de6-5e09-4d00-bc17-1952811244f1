package tb.a4p$e;
import java.lang.Runnable;
import android.content.Context;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a4p;
import java.lang.Boolean;

public class a4p$e implements Runnable	// class@001af0 from classes8.dex
{
    public final Context a;
    public static IpChange $ipChange;

    public void a4p$e(Context p0){
       this.a = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = a4p$e.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          a4p.d = Boolean.valueOf(a4p.d(this.a));
          return;
       }
    }
}
