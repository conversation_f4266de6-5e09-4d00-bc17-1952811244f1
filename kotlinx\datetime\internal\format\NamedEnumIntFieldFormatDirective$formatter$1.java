package kotlinx.datetime.internal.format.NamedEnumIntFieldFormatDirective$formatter$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.Object;
import tb.jm30;
import java.lang.Class;
import java.lang.String;
import kotlin.jvm.internal.CallableReference;

public final class NamedEnumIntFieldFormatDirective$formatter$1 extends FunctionReferenceImpl implements g1a	// class@0006f5 from classes11.dex
{

    public void NamedEnumIntFieldFormatDirective$formatter$1(Object p0){
       super(1, p0, jm30.class, "getStringValue", "getStringValue\(Ljava/lang/Object;\)Ljava/lang/String;", 0);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final String invoke(Object p0){
       jm30.a(this.receiver, p0);
       throw null;
    }
}
