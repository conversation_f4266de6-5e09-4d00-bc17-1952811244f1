package tb.a7m;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import tb.feh;
import java.lang.String;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.io.File;
import com.taobao.taobaoavsdk.cache.library.StorageUtils;
import android.net.Uri;
import java.lang.StringBuilder;
import java.lang.Throwable;
import com.taobao.taobaoavsdk.AVSDKLog;
import tb.e2n;
import java.lang.Boolean;
import tb.t7b;
import tb.t7b$d;

public class a7m	// class@00176f from classes9.dex
{
    public static IpChange $ipChange;
    public static final String ALI_DROP_0_REF_VF;
    public static final String ALI_DROP_SKIP_REF_VF;
    public static final String ALI_FLV_RETAIN;
    public static final String BIZ_CODE;
    public static final String CDN_IP;
    public static final String CONNECT_TIMEOUT;
    public static final String LIVE_BACKUP_IP;
    public static final String ONLY_VIDEO;
    public static final String PLAY_TOKEN_ID;
    public static final String PRELOAD_NET_CUT_SIZE;
    public static final String PRE_LOAD;
    public static final String READ_TIMEOUT;
    public static final String RETRY_TIME;
    public static final String TOP_ANCHOR;
    public static final String USE_TBNET_PROXY;
    public static final String VIDEO_CACHE_ID;
    public static final String VIDEO_DEFINE;
    public static final String VIDEO_ID;
    public static final String VIDEO_LENGTH;
    public static final String VIDEO_PLAY_SCENES;
    public static t7b a;
    public static AtomicBoolean b;
    public static AtomicBoolean c;
    public static final feh d;
    public static String e;

    static {
       t2o.a(0x30b000d0);
       a7m.b = new AtomicBoolean(false);
       a7m.c = new AtomicBoolean(false);
       a7m.d = new feh("PlayerEnvironment", "");
    }
    public static String a(Context p0,String p1){
       IpChange $ipChange = a7m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("dca679ea", objArray);
       }else if(!TextUtils.isEmpty(p1) && p0 != null){
          if (TextUtils.isEmpty(a7m.e)) {
             a7m.e = StorageUtils.getIndividualCacheDirectory(p0).getAbsolutePath();
          }
          File uFile = new File(a7m.e, p1);
          if (uFile.exists() && (uFile.canRead() && (uFile.length() - 1024) > 0)) {
             return uFile.getAbsolutePath();
          }
       }
       return null;
    }
    public static String b(Context p0,String p1){
       String queryParamet;
       IpChange $ipChange = a7m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("46081c2d", objArray);
       }else if(TextUtils.isEmpty(a7m.e)){
          a7m.e = StorageUtils.getIndividualCacheDirectory(p0).getAbsolutePath();
       }
       try{
          queryParamet = Uri.parse(p1).getQueryParameter("playTokenId");
          if (!TextUtils.isEmpty(queryParamet)) {
             a7m.d.c(queryParamet);
          }
       }catch(java.lang.Exception e6){
          AVSDKLog.e(a7m.d, "get playToken From url error"+e6.getMessage());
       }
       queryParamet = e2n.h(p1);
       if (TextUtils.isEmpty(queryParamet)) {
          return null;
       }else {
          File uFile = new File(a7m.e, queryParamet);
          if (uFile.exists() && (uFile.canRead() && (uFile.length() - 1024) > 0)) {
             return uFile.getAbsolutePath();
          }else {
             return null;
          }
       }
    }
    public static boolean c(){
       IpChange $ipChange = a7m.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a7m.c.get();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("dfbe0e3e", objArray).booleanValue();
    }
    public static t7b d(Context p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a7m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("990ac68f", objArray);
       }else if(a7m.b.compareAndSet(i, i1)){
          t7b ot7b = a7m.e(p0);
          a7m.a = ot7b;
          return ot7b;
       }else {
          return a7m.a;
       }
    }
    public static t7b e(Context p0){
       IpChange $ipChange = a7m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("63f89dd9", objArray);
       }else {
          AVSDKLog.e(a7m.d, "HttpProxyCacheServer start init, newProxy");
          return new t7b$d(p0.getApplicationContext()).b();
       }
    }
    public static void f(boolean p0){
       IpChange $ipChange = a7m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Boolean(p0)};
          $ipChange.ipc$dispatch("201c774e", objArray);
          return;
       }else {
          a7m.c.set(p0);
          return;
       }
    }
}
