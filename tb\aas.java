package tb.aas;
import tb.rrb;
import tb.t4c;
import tb.t2o;
import java.lang.Object;
import java.util.LinkedHashMap;
import tb.bbs;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.t4c$a;
import tb.ckf;
import java.util.Map;

public final class aas implements rrb, t4c	// class@001eb7 from classes10.dex
{
    public final Map a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x349000ab);
       t2o.a(0x349000a9);
       t2o.a(0x349000bd);
    }
    public void aas(){
       super();
       this.a = new LinkedHashMap();
    }
    public void N(bbs p0){
       IpChange $ipChange = aas.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3dc73485", objArray);
          return;
       }else {
          t4c$a.a(this, p0);
          return;
       }
    }
    public void c(){
       IpChange $ipChange = aas.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("896696a2", objArray);
          return;
       }else {
          t4c$a.b(this);
          return;
       }
    }
    public void h0(String p0,Object p1){
       IpChange $ipChange = aas.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("8d0b8b59", objArray);
          return;
       }else {
          ckf.g(p0, "key");
          ckf.g(p1, "value");
          this.a.put(p0, p1);
          return;
       }
    }
    public Object n0(String p0){
       IpChange $ipChange = aas.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("7624f4a1", objArray);
       }else {
          ckf.g(p0, "key");
          return this.a.get(p0);
       }
    }
}
