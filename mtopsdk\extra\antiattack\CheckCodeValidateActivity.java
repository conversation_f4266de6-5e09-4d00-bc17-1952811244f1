package mtopsdk.extra.antiattack.CheckCodeValidateActivity;
import android.app.Activity;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.content.Context;
import android.os.Bundle;
import tb.acq;
import java.net.URL;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Throwable;
import tb.rfh;
import android.content.Intent;
import android.widget.LinearLayout;
import android.widget.LinearLayout$LayoutParams;
import android.view.ViewGroup$LayoutParams;
import android.view.View;
import android.taobao.windvane.extra.uc.WVUCWebView;
import android.view.ViewGroup;
import mtopsdk.extra.antiattack.CheckCodeValidateActivity$a;
import com.uc.webview.export.WebViewClient;
import com.uc.webview.export.WebView;

public class CheckCodeValidateActivity extends Activity	// class@00077b from classes11.dex
{
    public WVUCWebView a;
    public String b;
    public static IpChange $ipChange;

    public void CheckCodeValidateActivity(){
       super();
       this.a = null;
       this.b = "";
    }
    public static void a(CheckCodeValidateActivity p0,String p1){
       IpChange $ipChange = CheckCodeValidateActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("b7bdeff", objArray);
          return;
       }else {
          p0.c(p1);
          return;
       }
    }
    public static Object ipc$super(CheckCodeValidateActivity p0,String p1,Object[] p2){
       int i = 0;
       switch (p1.hashCode()){
           case 0xa6532022:
             super.onDestroy();
             return null;
           case 0xd9c272d2:
             super.onCreate(p2[i]);
             return null;
           case 0x88afc63:
             super.onBackPressed();
             return null;
           case 0x1eb0a9a8:
             super.attachBaseContext(p2[i]);
             return null;
           default:
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/extra/antiattack/CheckCodeValidateActivity");
       }
    }
    public void attachBaseContext(Context p0){
       IpChange $ipChange = CheckCodeValidateActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1eb0a9a8", objArray);
          return;
       }else {
          super.attachBaseContext(p0);
          acq.B(p0);
          return;
       }
    }
    public final String b(String p0){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = CheckCodeValidateActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("88d4b267", objArray);
       }else {
          String query = new URL(p0).getQuery();
          StringBuilder str = new StringBuilder(32);
          if (!TextUtils.isEmpty(query)) {
             String[] stringArray = query.split("&");
             String str1 = null;
             while (i1 < stringArray.length) {
                object oobject = stringArray[i1];
                if (oobject.startsWith("http_referer=")) {
                   this.b = oobject.substring(13);
                   str1 = oobject;
                }else if(oobject.equalsIgnoreCase("native=1")){
                   str = str.append(oobject).append("&");
                }
                i1 = i1 + i;
             }
             str = str+"tmd_nc=1";
             if (str1 != null) {
                str = str+"&"+str1;
             }
             return p0.replace(query, str);
          }else {
             str = str+p0;
             if (!p0.endsWith("?")) {
                str = str+"?";
             }
             return str+"tmd_nc=1";
          }
       }
    }
    public final void c(String p0){
       IpChange $ipChange = CheckCodeValidateActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("9f8b2696", objArray);
          return;
       }else {
          rfh.a(16, "mtopsdk.CheckActivity", "sendResult: "+p0, null);
          Intent intent = new Intent("mtopsdk.extra.antiattack.result.notify.action");
          intent.setPackage(this.getApplicationContext().getPackageName());
          intent.putExtra("Result", p0);
          this.sendBroadcast(intent);
          return;
       }
    }
    public void onBackPressed(){
       IpChange $ipChange = CheckCodeValidateActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("88afc63", objArray);
          return;
       }else {
          super.onBackPressed();
          this.c("cancel");
          this.finish();
          return;
       }
    }
    public void onCreate(Bundle p0){
       int i1;
       int i = 1;
       String str = "mtopsdk.CheckActivity";
       String str1 = "load url. ";
       String str2 = "origin load url. ";
       IpChange $ipChange = CheckCodeValidateActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d9c272d2", objArray);
          return;
       }else {
          super.onCreate(p0);
          try{
             i1 = 16;
             LinearLayout $ipChange1 = new LinearLayout(this);
             $ipChange1.setOrientation(i);
             $ipChange1.setLayoutParams(new LinearLayout$LayoutParams(-1, -1));
             this.setContentView($ipChange1);
             WVUCWebView i2 = new WVUCWebView(this);
             this.a = i2;
             $ipChange1.addView(i2, new ViewGroup$LayoutParams(-1, -1));
             this.a.setWebViewClient(new CheckCodeValidateActivity$a(this, this));
             String stringExtra = this.getIntent().getStringExtra("Location");
             rfh.a(i1, str, str2+stringExtra, null);
             stringExtra = this.b(stringExtra);
             rfh.a(i1, str, str1+stringExtra, null);
             this.a.loadUrl(stringExtra);
          }catch(java.lang.Exception e0){
             rfh.a(i1, str, "onCreate failed.", e0);
             this.c("fail");
             this.finish();
          }
          return;
       }
    }
    public void onDestroy(){
       CheckCodeValidateActivity ta;
       IpChange $ipChange = CheckCodeValidateActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a6532022", objArray);
          return;
       }else {
          super.onDestroy();
          if ((ta = this.a) != null) {
             int i = 8;
             try{
                ta.setVisibility(i);
                this.a.removeAllViews();
                this.a.coreDestroy();
                this.a = null;
             }catch(java.lang.Exception e0){
                rfh.a(16, "mtopsdk.CheckActivity", "WVUCWebView onDestroy error.", e0);
             }
          }
          return;
       }
    }
}
