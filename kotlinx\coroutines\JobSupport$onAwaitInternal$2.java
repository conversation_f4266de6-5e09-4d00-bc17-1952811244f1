package kotlinx.coroutines.JobSupport$onAwaitInternal$2;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.JobSupport;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;

public final class JobSupport$onAwaitInternal$2 extends FunctionReferenceImpl implements w1a	// class@0004aa from classes11.dex
{
    public static final JobSupport$onAwaitInternal$2 INSTANCE;

    static {
       JobSupport$onAwaitInternal$2.INSTANCE = new JobSupport$onAwaitInternal$2();
    }
    public void JobSupport$onAwaitInternal$2(){
       super(3, JobSupport.class, "onAwaitInternalProcessResFunc", "onAwaitInternalProcessResFunc\(Ljava/lang/Object;Ljava/lang/Object;\)Ljava/lang/Object;", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final Object invoke(JobSupport p0,Object p1,Object p2){
       JobSupport.y(p0, p1, p2);
       return p2;
    }
}
