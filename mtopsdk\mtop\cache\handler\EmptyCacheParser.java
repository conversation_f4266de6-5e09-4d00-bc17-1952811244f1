package mtopsdk.mtop.cache.handler.EmptyCacheParser;
import mtopsdk.mtop.cache.handler.ICacheParser;
import tb.t2o;
import java.lang.Object;
import mtopsdk.mtop.domain.ResponseSource;
import android.os.Handler;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;

public class EmptyCacheParser implements ICacheParser	// class@000797 from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x253000aa);
       t2o.a(0x253000af);
    }
    public void EmptyCacheParser(){
       super();
    }
    public void parse(ResponseSource p0,Handler p1){
       IpChange $ipChange = EmptyCacheParser.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("ec0249bc", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
          TBSdkLog.i("mtopsdk.EmptyCacheParser", "[parse]EmptyCacheParser parse called");
       }
       return;
    }
}
