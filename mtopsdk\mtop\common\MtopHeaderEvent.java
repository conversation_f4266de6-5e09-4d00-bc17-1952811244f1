package mtopsdk.mtop.common.MtopHeaderEvent;
import mtopsdk.mtop.common.MtopEvent;
import tb.t2o;
import java.util.Map;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;

public class MtopHeaderEvent extends MtopEvent	// class@0007a9 from classes11.dex
{
    private int code;
    private Map headers;
    public String seqNo;
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000bc);
    }
    public void MtopHeaderEvent(int p0,Map p1){
       super();
       this.code = p0;
       this.headers = p1;
    }
    public static Object ipc$super(MtopHeaderEvent p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/common/MtopHeaderEvent");
    }
    public int getCode(){
       IpChange $ipChange = MtopHeaderEvent.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.code;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("480bb15d", objArray).intValue();
    }
    public Map getHeader(){
       IpChange $ipChange = MtopHeaderEvent.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.headers;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("65ea693f", objArray);
    }
    public String toString(){
       IpChange $ipChange = MtopHeaderEvent.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(128)+"MtopHeaderEvent [seqNo="+this.seqNo+", code="+this.code+", headers="+this.headers+"]";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
