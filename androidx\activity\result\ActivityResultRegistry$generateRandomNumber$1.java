package androidx.activity.result.ActivityResultRegistry$generateRandomNumber$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Integer;
import kotlin.random.Random;
import kotlin.random.Random$Default;
import java.lang.Object;

public final class ActivityResultRegistry$generateRandomNumber$1 extends Lambda implements d1a	// class@0004b7 from classes.dex
{
    public static final ActivityResultRegistry$generateRandomNumber$1 INSTANCE;

    static {
       ActivityResultRegistry$generateRandomNumber$1.INSTANCE = new ActivityResultRegistry$generateRandomNumber$1();
    }
    public void ActivityResultRegistry$generateRandomNumber$1(){
       super(0);
    }
    public final Integer invoke(){
       return Integer.valueOf((Random.Default.nextInt(0x7fff0000) + 0x10000));
    }
    public Object invoke(){
       return this.invoke();
    }
}
