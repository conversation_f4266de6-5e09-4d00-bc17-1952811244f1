package kotlinx.serialization.SealedClassSerializer$descriptor$2$1$elementDescriptor$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.SealedClassSerializer;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;
import java.util.Map;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import tb.x530;
import kotlinx.serialization.descriptors.a;
import tb.qb40;
import java.util.List;

public final class SealedClassSerializer$descriptor$2$1$elementDescriptor$1 extends Lambda implements g1a	// class@000712 from classes11.dex
{
    public final SealedClassSerializer this$0;

    public void SealedClassSerializer$descriptor$2$1$elementDescriptor$1(SealedClassSerializer p0){
       this.this$0 = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       ckf.g(p0, "$this$buildSerialDescriptor");
       Iterator iterator = SealedClassSerializer.access$getSerialName2Serializer$p(this.this$0).entrySet().iterator();
       while (iterator.hasNext()) {
          Map$Entry uEntry = iterator.next();
          f520.b(p0, uEntry.getKey(), uEntry.getValue().getDescriptor(), null, false, 12, null);
       }
       return;
    }
}
