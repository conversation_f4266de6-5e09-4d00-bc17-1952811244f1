package mtopsdk.mtop.upload.TaskListenerAdapter;
import tb.mzd;
import tb.t2o;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import mtopsdk.mtop.upload.DefaultFileUploadListenerWrapper;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.mtop.upload.FileUploadMgr;
import tb.z6e;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import tb.ndt;
import java.lang.Integer;
import tb.ozd;

public class TaskListenerAdapter implements mzd	// class@000808 from classes11.dex
{
    private DefaultFileUploadListenerWrapper listenerWrapper;
    private UploadFileInfo uploadFileInfo;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x2590000c);
    }
    public void TaskListenerAdapter(UploadFileInfo p0,DefaultFileUploadListenerWrapper p1){
       super();
       this.listenerWrapper = p1;
       this.uploadFileInfo = p0;
    }
    private void doRemove(){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("48c891f6", objArray);
          return;
       }else {
          FileUploadMgr.getInstance().removeArupTask(this.uploadFileInfo);
          return;
       }
    }
    public void onCancel(z6e p0){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c7d021ed", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
          TBSdkLog.i("mtopsdk.TaskListenerAdapter", "onCancel called.");
       }
       return;
    }
    public void onFailure(z6e p0,ndt p1){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7789334b", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
          TBSdkLog.i("mtopsdk.TaskListenerAdapter", "onFailure called.");
       }
       this.listenerWrapper.onError(p1.a, p1.b, p1.c);
       this.doRemove();
       return;
    }
    public void onPause(z6e p0){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c85aa60f", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
          TBSdkLog.i("mtopsdk.TaskListenerAdapter", "onPause called.");
       }
       return;
    }
    public void onProgress(z6e p0,int p1){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1)};
          $ipChange.ipc$dispatch("34b23fa9", objArray);
          return;
       }else {
          this.listenerWrapper.onProgress(p1);
          return;
       }
    }
    public void onResume(z6e p0){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e581d4da", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
          TBSdkLog.i("mtopsdk.TaskListenerAdapter", "onResume called.");
       }
       return;
    }
    public void onStart(z6e p0){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f33e623", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
          TBSdkLog.i("mtopsdk.TaskListenerAdapter", "onStart called.");
       }
       this.listenerWrapper.onStart();
       return;
    }
    public void onSuccess(z6e p0,ozd p1){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("10b3127c", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
          TBSdkLog.i("mtopsdk.TaskListenerAdapter", "onSuccess called.");
       }
       this.listenerWrapper.onFinish(this.uploadFileInfo, p1.a());
       this.doRemove();
       return;
    }
    public void onWait(z6e p0){
       IpChange $ipChange = TaskListenerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e3e24ed2", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
          TBSdkLog.i("mtopsdk.TaskListenerAdapter", "onWait called.");
       }
       return;
    }
}
