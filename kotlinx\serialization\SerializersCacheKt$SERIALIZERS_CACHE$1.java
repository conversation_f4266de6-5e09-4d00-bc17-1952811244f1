package kotlinx.serialization.SerializersCacheKt$SERIALIZERS_CACHE$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.wyf;
import tb.x530;
import java.lang.String;
import tb.ckf;
import tb.sb40;

public final class SerializersCacheKt$SERIALIZERS_CACHE$1 extends Lambda implements g1a	// class@00071f from classes11.dex
{
    public static final SerializersCacheKt$SERIALIZERS_CACHE$1 INSTANCE;

    static {
       SerializersCacheKt$SERIALIZERS_CACHE$1.INSTANCE = new SerializersCacheKt$SERIALIZERS_CACHE$1();
    }
    public void SerializersCacheKt$SERIALIZERS_CACHE$1(){
       super(1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final x530 invoke(wyf p0){
       ckf.g(p0, "it");
       return sb40.d(p0);
    }
}
