package me.ele.bridge.BundleRemoteInitializer$a;
import tb.b02$b;
import android.app.Application;
import com.alibaba.ariver.app.api.Page;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ariver.engine.api.bridge.extension.BridgeCallback;
import java.lang.Object;
import android.os.Bundle;
import com.android.alibaba.ip.runtime.IpChange;
import android.util.Log;
import android.os.BaseBundle;
import me.ele.bridge.PizzaApi;
import android.os.Handler;
import me.ele.bridge.BundleRemoteInitializer;
import me.ele.bridge.BundleRemoteInitializer$a$a;
import java.lang.Runnable;

public final class BundleRemoteInitializer$a implements b02$b	// class@000756 from classes11.dex
{
    public final Application a;
    public final Page b;
    public final String c;
    public final String d;
    public final JSONObject e;
    public final JSONObject f;
    public final JSONObject g;
    public final int h;
    public final JSONObject i;
    public final JSONObject j;
    public final String k;
    public final BridgeCallback l;
    public static IpChange $ipChange;

    public void BundleRemoteInitializer$a(Application p0,Page p1,String p2,String p3,JSONObject p4,JSONObject p5,JSONObject p6,int p7,JSONObject p8,JSONObject p9,String p10,BridgeCallback p11){
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
       this.d = p3;
       this.e = p4;
       this.f = p5;
       this.g = p6;
       this.h = p7;
       this.i = p8;
       this.j = p9;
       this.k = p10;
       this.l = p11;
    }
    public void a(String p0,Bundle p1){
       IpChange $ipChange = BundleRemoteInitializer$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("96fce058", objArray);
          return;
       }else {
          Log.e("pizza_sdk", "BUNDLE_REMOTE_INITIALIZER init failed");
          return;
       }
    }
    public void b(Object p0,Bundle p1){
       IpChange $ipChange = BundleRemoteInitializer$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("425b1026", objArray);
          return;
       }else {
          String str = "init_key";
          String str1 = p1.getString(str);
          if (p1.containsKey(str) && (str1 != null && (str1.equals("BundleRemoteInitializer") && (p0 instanceof PizzaApi && BundleRemoteInitializer.access$000() != null)))) {
             BundleRemoteInitializer.access$000().post(new BundleRemoteInitializer$a$a(this, p0));
          }
          return;
       }
    }
}
