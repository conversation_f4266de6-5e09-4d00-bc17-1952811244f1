package tb.a4p$b;
import tb.gjb;
import tb.a4p$d;
import android.content.Context;
import java.lang.Object;
import tb.qzl;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.alibaba.mtl.appmonitor.AppMonitor$Alarm;
import tb.a4p$b$a;
import java.lang.Runnable;
import com.taobao.android.task.Coordinator;
import java.lang.StringBuilder;
import tb.c4p;
import android.app.Activity;
import com.taobao.taobao.R$string;
import com.alibaba.ability.localization.Localization;
import tb.kl7;
import tb.a6p;
import android.app.Application;
import com.taobao.tao.Globals;
import java.lang.CharSequence;
import android.widget.Toast;

public class a4p$b implements gjb	// class@001aed from classes8.dex
{
    public final a4p$d a;
    public final Context b;
    public final boolean c;
    public static IpChange $ipChange;

    public void a4p$b(a4p$d p0,Context p1,boolean p2){
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
    }
    public void a(qzl p0){
       int i = 0;
       IpChange $ipChange = a4p$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("475e28c8", objArray);
          return;
       }else {
          int i1 = p0.b[i];
          object oobject = p0.c[i];
          String str = "requireLocationPermission";
          if (!i1) {
             AppMonitor$Alarm.commitSuccess("tbsearch", str);
             Coordinator.execute(new a4p$b$a(this));
          }else if(i1 == -1){
             AppMonitor$Alarm.commitFail("tbsearch", str, String.valueOf(i1), oobject);
             c4p.m("SearchLocationService", "checkBizPermission: denied with msg "+oobject);
             a4p$b tb = this.b;
             if (tb instanceof Activity) {
                kl7.d(tb, Localization.q(R$string.taobao_app_1005_1_16699)+Localization.q(R$string.taobao_app_1005_1_16668));
             }else {
                a6p.c("", Localization.q(R$string.taobao_app_1005_1_16657), i);
             }
          }else {
             AppMonitor$Alarm.commitFail("tbsearch", str, String.valueOf(i1), oobject);
             Toast.makeText(Globals.getApplication(), Localization.q(R$string.taobao_app_1005_1_16657), i).show();
          }
          return;
       }
    }
}
