package tb.a22;
import tb.ma4;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.share.globalmodel.TBShareContent;
import com.android.alibaba.ip.runtime.IpChange;

public class a22 extends ma4	// class@001744 from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x29d000dd);
    }
    public void a22(){
       super();
    }
    public static Object ipc$super(a22 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/tao/sharepanel/normal/template/BaseTemplateComponent");
    }
    public void g(TBShareContent p0){
       IpChange $ipChange = a22.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8b9d306f", objArray);
       }
       return;
    }
}
