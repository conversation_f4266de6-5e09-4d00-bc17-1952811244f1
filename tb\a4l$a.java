package tb.a4l$a;
import tb.w8;
import tb.t2o;
import java.lang.Object;
import tb.a4l;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.m8;

public class a4l$a implements w8	// class@001834 from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2d00003e);
       t2o.a(0x15d00022);
    }
    public void a4l$a(){
       super();
    }
    public a4l a(Object p0){
       IpChange $ipChange = a4l$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new a4l();
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("820b4341", objArray);
    }
    public m8 build(Object p0){
       return this.a(p0);
    }
}
