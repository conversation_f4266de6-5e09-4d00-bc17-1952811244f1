package me.ele.bridge.ElemePizzaBridgeExtension;
import com.alibaba.ariver.kernel.api.extension.bridge.BridgeExtension;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.alibaba.ariver.kernel.api.security.Permission;
import com.alibaba.ariver.app.api.Page;
import com.alibaba.ariver.engine.api.bridge.model.ApiContext;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ariver.engine.api.bridge.extension.BridgeCallback;
import java.lang.Integer;
import android.app.Activity;
import android.app.Application;
import me.ele.bridge.BundleRemoteInitializer;

public class ElemePizzaBridgeExtension implements BridgeExtension	// class@000758 from classes11.dex
{
    public static IpChange $ipChange;

    public void ElemePizzaBridgeExtension(){
       super();
    }
    public void onFinalized(){
       IpChange $ipChange = ElemePizzaBridgeExtension.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("fd1b9dee", objArray);
       }
       return;
    }
    public void onInitialized(){
       IpChange $ipChange = ElemePizzaBridgeExtension.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("94f2f17c", objArray);
       }
       return;
    }
    public Permission permit(){
       IpChange $ipChange = ElemePizzaBridgeExtension.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("edbd77f9", objArray);
    }
    public void sendPizza(Page p0,ApiContext p1,String p2,String p3,JSONObject p4,JSONObject p5,JSONObject p6,int p7,JSONObject p8,JSONObject p9,String p10,BridgeCallback p11){
       IpChange $ipChange = ElemePizzaBridgeExtension.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[13];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[2] = p1;
          objArray[3] = p2;
          objArray[4] = p3;
          objArray[5] = p4;
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = new Integer(p7);
          objArray[9] = p8;
          objArray[10] = p9;
          objArray[11] = p10;
          objArray[12] = p11;
          $ipChange.ipc$dispatch("fd31e4f1", objArray);
          return;
       }else {
          BundleRemoteInitializer.sendPizza(p1.getActivity().getApplication(), p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11);
          return;
       }
    }
}
