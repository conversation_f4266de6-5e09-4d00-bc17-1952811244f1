package tb.a3h$k0;
import com.taobao.living.api.TBConstants$TBMediaSDKNetworkStauts;
import java.lang.Enum;
import com.taobao.living.api.TBConstants$TBMediaSDKState;

public class a3h$k0	// class@001e78 from classes10.dex
{
    public static final int[] $SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKNetworkStauts;
    public static final int[] $SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState;

    static {
       int[] ointArray = new int[TBConstants$TBMediaSDKNetworkStauts.values().length];
       a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKNetworkStauts = ointArray;
       try{
          int i = 1;
          ointArray[TBConstants$TBMediaSDKNetworkStauts.TBMediaSDKNetworkWorse.ordinal()] = i;
          int i1 = 2;
          try{
             a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKNetworkStauts[TBConstants$TBMediaSDKNetworkStauts.TBMediaSDKNetworkNormal.ordinal()] = i1;
             int i2 = 3;
             try{
                a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKNetworkStauts[TBConstants$TBMediaSDKNetworkStauts.TBMediaSDKNetworkExcellent.ordinal()] = i2;
                int[] ointArray1 = new int[TBConstants$TBMediaSDKState.values().length];
                try{
                   a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState = ointArray1;
                   ointArray1[TBConstants$TBMediaSDKState.TBMediaSDKStateStarted.ordinal()] = i;
                   try{
                      a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState[TBConstants$TBMediaSDKState.TBMediaSDKStateAvaiable.ordinal()] = e0;
                      try{
                         a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState[TBConstants$TBMediaSDKState.TBMediaSDKStateCreateChannel.ordinal()] = i2;
                         try{
                            a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState[TBConstants$TBMediaSDKState.TBMediaSDKStateError.ordinal()] = 4;
                            try{
                               a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState[TBConstants$TBMediaSDKState.TBMediaSDKStateEnded.ordinal()] = 5;
                               try{
                                  a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState[TBConstants$TBMediaSDKState.TBMediaSDKStateConnectionRetry.ordinal()] = 6;
                                  try{
                                     a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState[TBConstants$TBMediaSDKState.TBMediaSDKStateConnected.ordinal()] = 7;
                                     try{
                                        a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState[TBConstants$TBMediaSDKState.TBMediaSDKStateReConnection.ordinal()] = 8;
                                     }catch(java.lang.NoSuchFieldError e0){
                                     }
                                  }catch(java.lang.NoSuchFieldError e0){
                                  }
                               }catch(java.lang.NoSuchFieldError e0){
                               }
                            }catch(java.lang.NoSuchFieldError e0){
                            }
                         }catch(java.lang.NoSuchFieldError e0){
                         }
                      }catch(java.lang.NoSuchFieldError e0){
                      }
                   }catch(java.lang.NoSuchFieldError e0){
                   }
                }catch(java.lang.NoSuchFieldError e0){
                }
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
