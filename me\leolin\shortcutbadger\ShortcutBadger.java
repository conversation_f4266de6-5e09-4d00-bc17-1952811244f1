package me.leolin.shortcutbadger.ShortcutBadger;
import java.util.LinkedList;
import java.lang.Object;
import me.leolin.shortcutbadger.impl.AdwHomeBadger;
import java.util.List;
import me.leolin.shortcutbadger.impl.ApexHomeBadger;
import me.leolin.shortcutbadger.impl.DefaultBadger;
import me.leolin.shortcutbadger.impl.NewHtcHomeBadger;
import me.leolin.shortcutbadger.impl.NovaHomeBadger;
import me.leolin.shortcutbadger.impl.SonyHomeBadger;
import me.leolin.shortcutbadger.impl.AsusHomeBadger;
import me.leolin.shortcutbadger.impl.HuaweiHomeBadger;
import me.leolin.shortcutbadger.impl.OPPOHomeBader;
import me.leolin.shortcutbadger.impl.SamsungHomeBadger;
import me.leolin.shortcutbadger.impl.ZukHomeBadger;
import me.leolin.shortcutbadger.impl.VivoHomeBadger;
import me.leolin.shortcutbadger.impl.ZTEHomeBadger;
import me.leolin.shortcutbadger.impl.EverythingMeHomeBadger;
import android.content.Context;
import me.leolin.shortcutbadger.ShortcutBadgeException;
import java.lang.String;
import android.content.ComponentName;
import tb.po1;
import java.lang.Exception;
import android.app.Notification;
import android.os.Build;
import java.lang.Class;
import java.lang.reflect.Field;
import java.lang.Integer;
import java.lang.reflect.Method;
import android.content.pm.PackageManager;
import android.content.Intent;
import java.lang.StringBuilder;
import android.util.Log;
import java.util.Iterator;
import android.content.pm.ResolveInfo;
import android.content.pm.ActivityInfo;
import java.lang.Boolean;
import java.lang.Throwable;

public final class ShortcutBadger	// class@00075b from classes11.dex
{
    private static final List BADGERS;
    private static final String LOG_TAG;
    private static final int SUPPORTED_CHECK_ATTEMPTS;
    private static ComponentName sComponentName;
    private static final Object sCounterSupportedLock;
    private static Boolean sIsBadgeCounterSupported;
    private static po1 sShortcutBadger;

    static {
       LinkedList linkedList = new LinkedList();
       ShortcutBadger.BADGERS = linkedList;
       ShortcutBadger.sCounterSupportedLock = new Object();
       linkedList.add(AdwHomeBadger.class);
       linkedList.add(ApexHomeBadger.class);
       linkedList.add(DefaultBadger.class);
       linkedList.add(NewHtcHomeBadger.class);
       linkedList.add(NovaHomeBadger.class);
       linkedList.add(SonyHomeBadger.class);
       linkedList.add(AsusHomeBadger.class);
       linkedList.add(HuaweiHomeBadger.class);
       linkedList.add(OPPOHomeBader.class);
       linkedList.add(SamsungHomeBadger.class);
       linkedList.add(ZukHomeBadger.class);
       linkedList.add(VivoHomeBadger.class);
       linkedList.add(ZTEHomeBadger.class);
       linkedList.add(EverythingMeHomeBadger.class);
    }
    private void ShortcutBadger(){
       super();
    }
    public static boolean applyCount(Context p0,int p1){
       try{
          ShortcutBadger.applyCountOrThrow(p0, p1);
          return true;
       }catch(me.leolin.shortcutbadger.ShortcutBadgeException e0){
          return false;
       }
    }
    public static void applyCountOrThrow(Context p0,int p1){
       if (ShortcutBadger.sShortcutBadger == null && !ShortcutBadger.initBadger(p0)) {
          throw new ShortcutBadgeException("No default launcher available");
       }
       try{
          ShortcutBadger.sShortcutBadger.b(p0, ShortcutBadger.sComponentName, p1);
          return;
       }catch(java.lang.Exception e2){
          throw new ShortcutBadgeException("Unable to execute badge", e2);
       }
    }
    public static void applyNotification(Context p0,Notification p1,int p2){
       try{
          int i = 1;
          if (Build.MANUFACTURER.equalsIgnoreCase("Xiaomi")) {
             p1 = p1.getClass().getDeclaredField("extraNotification").get(p1);
             Class[] uClassArray = new Class[i];
             uClassArray[0] = Integer.TYPE;
             Object[] objArray = new Object[i];
             objArray[0] = Integer.valueOf(p2);
             p1.getClass().getDeclaredMethod("setMessageCount", uClassArray).invoke(p1, objArray);
          }
          return;
       }catch(java.lang.Exception e0){
       }
    }
    private static boolean initBadger(Context p0){
       Intent launchIntent;
       po1 opo1;
       if ((launchIntent = p0.getPackageManager().getLaunchIntentForPackage(p0.getPackageName())) == null) {
          Log.e("ShortcutBadger", "Unable to find launch intent for package "+p0.getPackageName());
          return false;
       }else {
          ShortcutBadger.sComponentName = launchIntent.getComponent();
          launchIntent = new Intent("android.intent.action.MAIN");
          launchIntent.addCategory("android.intent.category.HOME");
          Iterator iterator = p0.getPackageManager().queryIntentActivities(launchIntent, 0x10000).iterator();
          while (iterator.hasNext()) {
             ActivityInfo packageName = iterator.next().activityInfo.packageName;
             Iterator iterator1 = ShortcutBadger.BADGERS.iterator();
             while (iterator1.hasNext()) {
                Class uClass = iterator1.next();
                try{
                   opo1 = uClass.newInstance();
                }catch(java.lang.Exception e0){
                   opo1 = null;
                }
             }
             ShortcutBadger.sShortcutBadger = opo1;
          }
          if (ShortcutBadger.sShortcutBadger == null) {
             String mANUFACTURER = Build.MANUFACTURER;
             if (mANUFACTURER.equalsIgnoreCase("ZUK")) {
                ShortcutBadger.sShortcutBadger = new ZukHomeBadger();
             }else if(mANUFACTURER.equalsIgnoreCase("OPPO")){
                ShortcutBadger.sShortcutBadger = new OPPOHomeBader();
             }else if(mANUFACTURER.equalsIgnoreCase("VIVO")){
                ShortcutBadger.sShortcutBadger = new VivoHomeBadger();
             }else if(mANUFACTURER.equalsIgnoreCase("ZTE")){
                ShortcutBadger.sShortcutBadger = new ZTEHomeBadger();
             }else {
                ShortcutBadger.sShortcutBadger = new DefaultBadger();
             }
          }
          return true;
       }
    }
    public static boolean isBadgeCounterSupported(Context p0){
       int i = 0;
       if (ShortcutBadger.sIsBadgeCounterSupported == null) {
          Object sCounterSupp = ShortcutBadger.sCounterSupportedLock;
          _monitor_enter(sCounterSupp);
          if (ShortcutBadger.sIsBadgeCounterSupported == null) {
             String str = null;
             int i1 = 0;
             while (true) {
                int i2 = 3;
                if (i1 < i2) {
                   try{
                      int i3 = i1 + 1;
                      Object[] objArray = new Object[2];
                      objArray[i] = Integer.valueOf(i3);
                      objArray[1] = Integer.valueOf(i2);
                      StringBuilder str1 = "".append("Checking if platform supports badge counters, attempt ").append(String.format("%d/%d.", objArray));
                      if (ShortcutBadger.initBadger(p0)) {
                         ShortcutBadger.sShortcutBadger.b(p0, ShortcutBadger.sComponentName, i);
                         ShortcutBadger.sIsBadgeCounterSupported = Boolean.TRUE;
                         break ;
                      }else {
                         str = "Failed to initialize the badge counter.";
                      }
                   }catch(java.lang.Exception e3){
                      str = e3.getMessage();
                   }
                }else if(ShortcutBadger.sIsBadgeCounterSupported == null){
                   StringBuilder str2 = "Badge counter seems not supported for this platform: "+str;
                   ShortcutBadger.sIsBadgeCounterSupported = Boolean.FALSE;
                }
             }
          }
          _monitor_exit(sCounterSupp);
       }
       return ShortcutBadger.sIsBadgeCounterSupported.booleanValue();
    }
    public static boolean removeCount(Context p0){
       return ShortcutBadger.applyCount(p0, 0);
    }
    public static void removeCountOrThrow(Context p0){
       ShortcutBadger.applyCountOrThrow(p0, 0);
    }
}
