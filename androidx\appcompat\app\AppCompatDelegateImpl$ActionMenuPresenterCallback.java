package androidx.appcompat.app.AppCompatDelegateImpl$ActionMenuPresenterCallback;
import androidx.appcompat.view.menu.MenuPresenter$Callback;
import androidx.appcompat.app.AppCompatDelegateImpl;
import java.lang.Object;
import androidx.appcompat.view.menu.MenuBuilder;
import android.view.Window$Callback;
import android.view.Menu;

public final class AppCompatDelegateImpl$ActionMenuPresenterCallback implements MenuPresenter$Callback	// class@000567 from classes.dex
{
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$ActionMenuPresenterCallback(AppCompatDelegateImpl p0){
       this.this$0 = p0;
       super();
    }
    public void onCloseMenu(MenuBuilder p0,boolean p1){
       this.this$0.checkCloseActionMenu(p0);
    }
    public boolean onOpenSubMenu(MenuBuilder p0){
       Window$Callback windowCallba;
       if ((windowCallba = this.this$0.getWindowCallback()) != null) {
          windowCallba.onMenuOpened(108, p0);
       }
       return true;
    }
}
