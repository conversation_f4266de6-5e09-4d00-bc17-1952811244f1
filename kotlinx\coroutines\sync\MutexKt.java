package kotlinx.coroutines.sync.MutexKt;
import tb.u1r;
import java.lang.String;
import tb.ofj;
import kotlinx.coroutines.sync.MutexImpl;
import java.lang.Object;
import tb.d1a;
import tb.ar4;
import kotlinx.coroutines.sync.MutexKt$withLock$1;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;

public final class MutexKt	// class@0006ca from classes11.dex
{
    public static final u1r a;
    public static final u1r b;

    static {
       MutexKt.a = new u1r("NO_OWNER");
       MutexKt.b = new u1r("ALREADY_LOCKED_BY_OWNER");
    }
    public static final ofj a(boolean p0){
       return new MutexImpl(p0);
    }
    public static ofj b(boolean p0,int p1,Object p2){
       if ((p1 & 0x01)) {
          p0 = false;
       }
       return MutexKt.a(p0);
    }
    public static final Object c(ofj p0,Object p1,d1a p2,ar4 p3){
       MutexKt$withLock$1 owithLock$1;
       MutexKt$withLock$1 label1;
       MutexKt$withLock$1 owithLock$11;
       MutexKt$withLock$1 l$0;
       if (p3 instanceof MutexKt$withLock$1) {
          owithLock$1 = p3;
          MutexKt$withLock$1 label = owithLock$1.label;
          int i = Integer.MIN_VALUE;
          if ((label & i)) {
             owithLock$1.label = label - i;
          label_0018 :
             MutexKt$withLock$1 result = owithLock$1.result;
             Object obj = dkf.d();
             if ((label1 = owithLock$1.label) != null) {
                if (label1 == 1) {
                   owithLock$11 = owithLock$1.L$2;
                   p1 = owithLock$1.L$1;
                   l$0 = owithLock$1.L$0;
                   b.b(result);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                owithLock$1.L$0 = p0;
                owithLock$1.L$1 = p1;
                owithLock$1.L$2 = p2;
                owithLock$1.label = 1;
                if (p0.b(p1, owithLock$1) == obj) {
                   return obj;
                }
             }
             l$0.unlock(p1);
             return owithLock$11.invoke();
          }
       }
       owithLock$1 = new MutexKt$withLock$1(p3);
       goto label_0018 ;
    }
}
