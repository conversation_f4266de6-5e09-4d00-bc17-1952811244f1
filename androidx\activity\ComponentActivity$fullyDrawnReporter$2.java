package androidx.activity.ComponentActivity$fullyDrawnReporter$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.ComponentActivity;
import androidx.activity.FullyDrawnReporter;
import androidx.activity.ComponentActivity$ReportFullyDrawnExecutor;
import androidx.activity.ComponentActivity$fullyDrawnReporter$2$1;
import java.util.concurrent.Executor;
import java.lang.Object;

public final class ComponentActivity$fullyDrawnReporter$2 extends Lambda implements d1a	// class@00043e from classes.dex
{
    public final ComponentActivity this$0;

    public void ComponentActivity$fullyDrawnReporter$2(ComponentActivity p0){
       this.this$0 = p0;
       super(0);
    }
    public final FullyDrawnReporter invoke(){
       return new FullyDrawnReporter(ComponentActivity.access$getReportFullyDrawnExecutor$p(this.this$0), new ComponentActivity$fullyDrawnReporter$2$1(this.this$0));
    }
    public Object invoke(){
       return this.invoke();
    }
}
