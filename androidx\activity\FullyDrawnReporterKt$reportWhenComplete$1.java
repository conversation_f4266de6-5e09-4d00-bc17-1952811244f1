package androidx.activity.FullyDrawnReporterKt$reportWhenComplete$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import androidx.activity.FullyDrawnReporter;
import tb.g1a;
import androidx.activity.FullyDrawnReporterKt;

public final class FullyDrawnReporterKt$reportWhenComplete$1 extends ContinuationImpl	// class@00044c from classes.dex
{
    public Object L$0;
    public int label;
    public Object result;

    public void FullyDrawnReporterKt$reportWhenComplete$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return FullyDrawnReporterKt.reportWhenComplete(null, null, this);
    }
}
