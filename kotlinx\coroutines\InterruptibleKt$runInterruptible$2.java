package kotlinx.coroutines.InterruptibleKt$runInterruptible$2;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import tb.d1a;
import tb.ar4;
import java.lang.Object;
import tb.uu4;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import kotlin.coroutines.d;
import tb.ujf;
import java.lang.IllegalStateException;
import java.lang.String;

public final class InterruptibleKt$runInterruptible$2 extends SuspendLambda implements u1a	// class@0004a0 from classes11.dex
{
    public final d1a $block;
    private Object L$0;
    public int label;

    public void InterruptibleKt$runInterruptible$2(d1a p0,ar4 p1){
       this.$block = p0;
       super(2, p1);
    }
    public final ar4 create(Object p0,ar4 p1){
       InterruptibleKt$runInterruptible$2 orunInterrup = new InterruptibleKt$runInterruptible$2(this.$block, p1);
       orunInterrup.L$0 = p0;
       return orunInterrup;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(uu4 p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       dkf.d();
       if (this.label != null) {
          throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
       }
       b.b(p0);
       return ujf.a(this.L$0.getCoroutineContext(), this.$block);
    }
}
