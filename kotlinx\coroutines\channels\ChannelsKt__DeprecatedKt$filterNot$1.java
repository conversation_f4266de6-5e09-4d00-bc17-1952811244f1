package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$filterNot$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import tb.ar4;
import java.lang.Object;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import tb.gk2;

public final class ChannelsKt__DeprecatedKt$filterNot$1 extends SuspendLambda implements u1a	// class@0004e7 from classes11.dex
{
    public final u1a $predicate;
    public Object L$0;
    public int label;

    public void ChannelsKt__DeprecatedKt$filterNot$1(u1a p0,ar4 p1){
       this.$predicate = p0;
       super(2, p1);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$filterNot$1 uofilterNot$ = new ChannelsKt__DeprecatedKt$filterNot$1(this.$predicate, p1);
       uofilterNot$.L$0 = p0;
       return uofilterNot$;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(Object p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$filterNot$1 tlabel;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel == 1) {
             b.b(p0);
          }else {
             throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
          }
       }else {
          b.b(p0);
          this.label = 1;
          if ((p0 = this.$predicate.invoke(this.L$0, this)) == obj) {
             return obj;
          }
       }
       return gk2.a((p0.booleanValue() ^ 1));
    }
}
