package androidx.activity.ViewTreeFullyDrawnReporterOwner;
import android.view.View;
import androidx.activity.FullyDrawnReporterOwner;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.activity.ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1;
import tb.g1a;
import tb.sbp;
import tb.acp;
import androidx.activity.ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2;
import tb.dcp;
import com.taobao.taobao.R$id;

public final class ViewTreeFullyDrawnReporterOwner	// class@000472 from classes.dex
{

    public static final FullyDrawnReporterOwner get(View p0){
       ckf.g(p0, "<this>");
       return dcp.r(dcp.v(acp.f(p0, ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1.INSTANCE), ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2.INSTANCE));
    }
    public static final void set(View p0,FullyDrawnReporterOwner p1){
       ckf.g(p0, "<this>");
       ckf.g(p1, "fullyDrawnReporterOwner");
       p0.setTag(R$id.report_drawn, p1);
    }
}
