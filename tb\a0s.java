package tb.a0s;
import tb.t2o;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.Boolean;
import com.taobao.orange.OrangeConfig;

public class a0s	// class@001e52 from classes10.dex
{
    public static IpChange $ipChange;
    public static final String WX_V2_CONFIG;

    static {
       t2o.a(0x30a0001c);
    }
    public static boolean a(){
       IpChange $ipChange = a0s.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "true".equals(OrangeConfig.getInstance().getConfig("android_weex_common_config", "disableInitInUIThread", "false"));
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("635c326", objArray).booleanValue();
    }
    public static String b(){
       IpChange $ipChange = a0s.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return OrangeConfig.getInstance().getConfig("android_weex_common_config", "loadingList", "[\'https://market.m.taobao.com/app/vip/receiver-address/pages/list\',\'https://market.m.taobao.com/app/vip/receiver-address/pages/address\',\'https://market.m.taobao.com/app/vip/receiver-address/pages/lbs-list\',\'https://market.m.taobao.com/app/vip/receiver-address/pages/change\',\'https://market.m.taobao.com/apps/market/shop/weex_v2.html\']");
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("8d1f69b", objArray);
    }
}
