package tb.adk;
import tb.t2o;
import java.lang.Object;
import java.lang.Class;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.reflect.Field;
import tb.gun;
import java.lang.NoSuchFieldException;
import java.lang.StringBuilder;
import java.lang.reflect.AccessibleObject;
import java.lang.reflect.Modifier;
import java.lang.reflect.Method;
import java.lang.Void;
import java.lang.NoSuchMethodException;

public class adk	// class@00187a from classes5.dex
{
    public final Object a;
    public final Class b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1a500008);
    }
    public void adk(Object p0,Class p1){
       super();
       this.a = p0;
       if (p0 != null) {
          p1 = p0.getClass();
       }
       this.b = p1;
       return;
    }
    public static adk i(Object p0){
       IpChange $ipChange = adk.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("f940a509", objArray);
       }else {
          p0.getClass();
          if (p0 instanceof Class) {
             return new adk(null, p0);
          }
          return new adk(p0, p0.getClass());
       }
    }
    public final Field a(String p0){
       Field uField;
       IpChange $ipChange = adk.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f5045e08", objArray);
       }else {
          adk tb = this.b;
          try{
          label_001a :
             uField = gun.a(tb, p0);
          }catch(java.lang.Exception e0){
             if ((tb = e0.getSuperclass()) == Object.class) {
                uField = null;
             }else {
                goto label_001a ;
             }
          }
          if (uField != null) {
             return uField;
          }
          throw new NoSuchFieldException(this.b.getName()+":"+p0);
       }
    }
    public adk b(String p0){
       IpChange $ipChange = adk.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c(this.a(p0));
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("a3a7c54f", objArray);
    }
    public final adk c(Field p0){
       p0.setAccessible(true);
       adk uoadk = (Modifier.isStatic(p0.getModifiers()))? null: this.a;
       Object obj = p0.get(uoadk);
       return new adk(obj, p0.getType());
    }
    public adk d(String p0){
       int i = 0;
       IpChange $ipChange = adk.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("4e5ff6ed", objArray);
       }else {
          Class[] uClassArray = new Class[i];
          Object[] objArray1 = new Object[i];
          return this.e(p0, uClassArray, objArray1);
       }
    }
    public adk e(String p0,Class[] p1,Object[] p2){
       Method method;
       if ((method = gun.c(this.b, p0, p1)) == null) {
          throw new NoSuchMethodException();
       }
       method.setAccessible(true);
       Class returnType = method.getReturnType();
       adk uoadk = null;
       adk uoadk1 = (Modifier.isStatic(method.getModifiers()))? uoadk: this.a;
       if (method.getReturnType() != Void.TYPE) {
          uoadk = new adk(method.invoke(uoadk1, p2), returnType);
       }else {
          method.invoke(uoadk1, p2);
       }
       return uoadk;
    }
    public adk f(String p0,Object p1){
       IpChange $ipChange = adk.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("f87f7927", objArray);
       }else {
          this.g(this.a(p0), p1);
          return this;
       }
    }
    public final adk g(Field p0,Object p1){
       p0.setAccessible(true);
       if (Modifier.isStatic(p0.getModifiers())) {
          p0.set(null, p1);
       }else {
          p0.set(this.a, p1);
       }
       return this;
    }
    public Object h(){
       IpChange $ipChange = adk.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1af82031", objArray);
    }
}
