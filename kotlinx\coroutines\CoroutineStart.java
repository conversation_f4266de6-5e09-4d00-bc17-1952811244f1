package kotlinx.coroutines.CoroutineStart;
import java.lang.Enum;
import java.lang.String;
import tb.fg8;
import kotlin.enums.a;
import java.lang.Class;
import java.lang.Object;
import tb.u1a;
import tb.ar4;
import kotlinx.coroutines.CoroutineStart$a;
import kotlin.NoWhenBranchMatchedException;
import tb.pgv;
import tb.er4;
import tb.g1a;
import tb.u23;

public final class CoroutineStart extends Enum	// class@000495 from classes11.dex
{
    private static final fg8 $ENTRIES;
    private static final CoroutineStart[] $VALUES;
    public static final CoroutineStart ATOMIC;
    public static final CoroutineStart DEFAULT;
    public static final CoroutineStart LAZY;
    public static final CoroutineStart UNDISPATCHED;

    private static final CoroutineStart[] $values(){
       CoroutineStart[] uCoroutineSt = new CoroutineStart[]{CoroutineStart.DEFAULT,CoroutineStart.LAZY,CoroutineStart.ATOMIC,CoroutineStart.UNDISPATCHED};
       return uCoroutineSt;
    }
    static {
       CoroutineStart.DEFAULT = new CoroutineStart("DEFAULT", 0);
       CoroutineStart.LAZY = new CoroutineStart("LAZY", 1);
       CoroutineStart.ATOMIC = new CoroutineStart("ATOMIC", 2);
       CoroutineStart.UNDISPATCHED = new CoroutineStart("UNDISPATCHED", 3);
       CoroutineStart[] uCoroutineSt = CoroutineStart.$values();
       CoroutineStart.$VALUES = uCoroutineSt;
       CoroutineStart.$ENTRIES = a.a(uCoroutineSt);
    }
    private void CoroutineStart(String p0,int p1){
       super(p0, p1);
    }
    public static fg8 getEntries(){
       return CoroutineStart.$ENTRIES;
    }
    public static void isLazy$annotations(){
    }
    public static CoroutineStart valueOf(String p0){
       return Enum.valueOf(CoroutineStart.class, p0);
    }
    public static CoroutineStart[] values(){
       return CoroutineStart.$VALUES.clone();
    }
    public final void invoke(u1a p0,Object p1,ar4 p2){
       int i;
       if ((i = CoroutineStart$a.$EnumSwitchMapping$0[this.ordinal()]) != 1) {
          if (i != 2) {
             if (i != 3) {
                if (i != 4) {
                   throw new NoWhenBranchMatchedException();
                }
             }else {
                pgv.b(p0, p1, p2);
             }
          }else {
             er4.b(p0, p1, p2);
          }
       }else {
          u23.e(p0, p1, p2, null, 4, null);
       }
       return;
    }
    public final boolean isLazy(){
       boolean b = (this == CoroutineStart.LAZY)? true: false;
       return b;
    }
}
