package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$consumesAll$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;
import tb.bj3;
import tb.sm8;

public final class ChannelsKt__DeprecatedKt$consumesAll$1 extends Lambda implements g1a	// class@0004dd from classes11.dex
{
    public final ReceiveChannel[] $channels;

    public void ChannelsKt__DeprecatedKt$consumesAll$1(ReceiveChannel[] p0){
       this.$channels = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(Throwable p0){
       ChannelsKt__DeprecatedKt$consumesAll$1 t$channels = this.$channels;
       int len = t$channels.length;
       int i = 0;
       while (i < len) {
          bj3.b(t$channels[i], p0);
          i = i + 1;
       }
       if (!null) {
          return;
       }
       throw null;
    }
}
