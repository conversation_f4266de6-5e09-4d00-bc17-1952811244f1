package androidx.activity.result.contract.ActivityResultContract;
import java.lang.Object;
import android.content.Context;
import android.content.Intent;
import androidx.activity.result.contract.ActivityResultContract$SynchronousResult;
import java.lang.String;
import tb.ckf;

public abstract class ActivityResultContract	// class@0004c5 from classes.dex
{

    public void ActivityResultContract(){
       super();
    }
    public abstract Intent createIntent(Context p0,Object p1);
    public ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,Object p1){
       ckf.g(p0, "context");
       return null;
    }
    public abstract Object parseResult(int p0,Intent p1);
}
