package tb.a4t;
import tb.t2o;
import java.lang.Object;
import kotlin.Pair;
import tb.jpu;
import com.taobao.kmp.live.updown.TaoLiveKtUpDownRequestType;
import java.lang.String;
import java.util.Map;
import kotlin.collections.a;
import com.android.alibaba.ip.runtime.IpChange;
import tb.pxs;
import com.taobao.kmp.live.updown.model.TaoLiveKtSwitchModel;
import java.lang.StringBuilder;
import com.taobao.uniinfra_kmp.common_utils.serialization.KMPJsonObject;
import com.alibaba.fastjson.JSONObject;
import com.taobao.kmp.live.updown.model.TaoLiveKtAlimamaInfo;
import tb.ckf;

public final class a4t	// class@001af2 from classes8.dex
{
    public static IpChange $ipChange;
    public static final String FIRST_ENTRY;
    public static final a4t INSTANCE;
    public static final Map a;

    static {
       t2o.a(0x3f8000af);
       a4t.INSTANCE = new a4t();
       Pair[] pairArray = new Pair[]{jpu.a("FirstEntry", "0"),jpu.a(TaoLiveKtUpDownRequestType.FirstBatch.getValue(), "1"),jpu.a(TaoLiveKtUpDownRequestType.NextBatch.getValue(), "2"),jpu.a(TaoLiveKtUpDownRequestType.ForceBatch.getValue(), "3"),jpu.a(TaoLiveKtUpDownRequestType.NormalRealtime.getValue(), "4"),jpu.a(TaoLiveKtUpDownRequestType.AdRealtime.getValue(), "5"),jpu.a(TaoLiveKtUpDownRequestType.AutoRealtime.getValue(), "6")};
       a4t.a = a.j(pairArray);
    }
    public void a4t(){
       super();
    }
    public final Map a(){
       IpChange $ipChange = a4t.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a4t.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("fe016bb8", objArray);
    }
    public final String b(Map p0,pxs p1){
       TaoLiveKtSwitchModel taoLiveKtSwi;
       String str3;
       Map recommendCar;
       String str4;
       TaoLiveKtSwitchModel taoLiveKtSwi1;
       IpChange $ipChange = a4t.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("669cc9b8", objArray);
       }else {
          String str = null;
          if (p0 == null || p1 == null) {
             return str;
          }
          if ((taoLiveKtSwi = p1.d()) == null) {
             return str;
          }
          StringBuilder str1 = "";
          String str2 = "officialLiveInfo";
          if (p0.get(str2) != null) {
             if ((str3 = p0.get(str2)) == null) {
                str3 = str;
             }
             str1 = str1+"#officialLive="+new KMPJsonObject(str3).getString("officialLive");
          }
          if (taoLiveKtSwi.getRecommendCardInfo() != null) {
             str3 = "#dxTemplateName=";
             str4 = ((recommendCar = taoLiveKtSwi.getRecommendCardInfo()) != null)? recommendCar.get("dxTemplateName"): str;
             str1 = str1+str3+str4;
          }
          if (taoLiveKtSwi.getReserveItemCardInfo() != null) {
             str3 = taoLiveKtSwi.getReserveItemCardInfo();
             str4 = "cardInfo";
             str3 = (str3 != null)? str3.get(str4): str;
             if (str3 != null) {
                str3 = ((str3 = taoLiveKtSwi.getReserveItemCardInfo()) != null)? str3.get(str4): str;
                if (str3 instanceof Map) {
                }else {
                   str3 = str;
                }
                if (str3 != null) {
                   str = str3.get("dxTemplateName");
                }
                if (str != null) {
                   str1 = str1+"#dxTemplateName="+str3.get("dxTemplateName");
                }
             }
          }
          if (taoLiveKtSwi.getAlimama() != null) {
             str1 = str1+"#isAD";
          }
          if ((taoLiveKtSwi1 = p1.e((p1.c() - 1))) != null && taoLiveKtSwi1.getNextGuideShown()) {
             str1 = str1+"#fromLiveGuide";
          }
          return str1;
       }
    }
    public final String c(Map p0,pxs p1){
       TaoLiveKtSwitchModel taoLiveKtSwi;
       IpChange $ipChange = a4t.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("9faf3afe", objArray);
       }else {
          String str = null;
          if (p0 == null || p1 == null) {
             return str;
          }
          if ((taoLiveKtSwi = p1.d()) == null) {
             return str;
          }
          if (p0.get("officialLiveInfo") != null && ckf.b(p0.get("roomStatus"), "1")) {
             return "official";
          }
          if (taoLiveKtSwi.isGoodsCard()) {
             return "goodsCard";
          }
          return "live";
       }
    }
    public final String d(Map p0){
       String str;
       IpChange $ipChange = a4t.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("e225d8f3", objArray);
       }else if(p0 == null){
          return null;
       }else if(p0.get("timeMovingPlayInfo") != null){
          str = "timemove";
       }else if(ckf.b(p0.get("roomStatus"), "2")){
          str = "playback";
       }else {
          str = "live";
       }
       return str;
    }
    public final String e(pxs p0){
       TaoLiveKtSwitchModel taoLiveKtSwi;
       IpChange $ipChange = a4t.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f5c55517", objArray);
       }else if(p0 != null && (taoLiveKtSwi = p0.d()) != null){
          return taoLiveKtSwi.getRequestType();
       }else {
          return null;
       }
    }
}
