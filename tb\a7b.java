package tb.a7b;
import tb.xqd;
import com.taobao.search.searchdoor.activate.hotspot.impl.HotSpotsViewHolder;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import com.taobao.search.searchdoor.DoorListViewModel;
import android.app.Activity;
import tb.abx;
import tb.ckf;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView$ViewHolder;
import com.taobao.search.searchdoor.activate.hotspot.impl.HotSpotTabViewHolder;
import android.content.Context;
import com.taobao.search.searchdoor.DoorListViewModel$a;

public final class a7b implements xqd	// class@00176e from classes9.dex
{
    public final HotSpotsViewHolder a;
    public static IpChange $ipChange;

    public void a7b(HotSpotsViewHolder p0){
       super();
       this.a = p0;
    }
    public void onItemSelected(int p0){
       RecyclerView recyclerView;
       IpChange $ipChange = a7b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("fcb34d2f", objArray);
          return;
       }else if(p0 >= 0){
          DoorListViewModel$a companion = DoorListViewModel.Companion;
          Activity activity = this.a.getActivity();
          ckf.f(activity, "getActivity\(...\)");
          if ((recyclerView = HotSpotsViewHolder.y2(this.a)) != null) {
             RecyclerView$ViewHolder viewHolder = recyclerView.findViewHolderForAdapterPosition(p0);
             ckf.e(viewHolder, "null cannot be cast to non-null type com.taobao.search.searchdoor.activate.hotspot.impl.HotSpotTabViewHolder");
             companion.b(activity, viewHolder.e0());
          }else {
             ckf.y("viewPager");
             throw null;
          }
       }
       HotSpotsViewHolder.B2(this.a, p0);
       return;
    }
}
