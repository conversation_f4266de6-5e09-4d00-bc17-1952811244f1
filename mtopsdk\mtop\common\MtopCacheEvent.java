package mtopsdk.mtop.common.MtopCacheEvent;
import mtopsdk.mtop.common.MtopFinishEvent;
import tb.t2o;
import mtopsdk.mtop.domain.MtopResponse;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;

public class MtopCacheEvent extends MtopFinishEvent	// class@0007a0 from classes11.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000b3);
    }
    public void MtopCacheEvent(MtopResponse p0){
       super(p0);
    }
    public static Object ipc$super(MtopCacheEvent p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/common/MtopCacheEvent");
    }
    public String toString(){
       IpChange $ipChange = MtopCacheEvent.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(128)+"MtopCacheEvent [seqNo="+this.seqNo+", mtopResponse="+this.mtopResponse+"]";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
