package kotlinx.coroutines.channels.ChannelIterator$next0$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlinx.coroutines.channels.ChannelIterator$DefaultImpls;

public final class ChannelIterator$next0$1 extends ContinuationImpl	// class@0004d3 from classes11.dex
{
    public Object L$0;
    public int label;
    public Object result;

    public void ChannelIterator$next0$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return ChannelIterator$DefaultImpls.a(null, this);
    }
}
