package androidx.appcompat.app.WindowDecorActionBar$2;
import androidx.core.view.ViewPropertyAnimatorListenerAdapter;
import androidx.appcompat.app.WindowDecorActionBar;
import android.view.View;

public class WindowDecorActionBar$2 extends ViewPropertyAnimatorListenerAdapter	// class@00058d from classes.dex
{
    public final WindowDecorActionBar this$0;

    public void WindowDecorActionBar$2(WindowDecorActionBar p0){
       this.this$0 = p0;
       super();
    }
    public void onAnimationEnd(View p0){
       WindowDecorActionBar$2 tthis$0 = this.this$0;
       tthis$0.mCurrentShowAnim = null;
       tthis$0.mContainerView.requestLayout();
    }
}
