package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$map$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;
import java.lang.Throwable;
import tb.bj3;

public final class ChannelsKt__DeprecatedKt$map$1 extends SuspendLambda implements u1a	// class@0004f2 from classes11.dex
{
    public final ReceiveChannel $this_map;
    public final u1a $transform;
    private Object L$0;
    public Object L$1;
    public Object L$2;
    public Object L$3;
    public Object L$4;
    public int label;

    public void ChannelsKt__DeprecatedKt$map$1(ReceiveChannel p0,u1a p1,ar4 p2){
       this.$this_map = p0;
       this.$transform = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$map$1 omap$1 = new ChannelsKt__DeprecatedKt$map$1(this.$this_map, this.$transform, p1);
       omap$1.L$0 = p0;
       return omap$1;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$map$1 tlabel;
       ChannelsKt__DeprecatedKt$map$1 tL$2;
       ChannelsKt__DeprecatedKt$map$1 tL$1;
       ChannelsKt__DeprecatedKt$map$1 obj1;
       ChannelsKt__DeprecatedKt$map$1 omap$1;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel != 2) {
                if (tlabel == 3) {
                   tlabel = this.L$3;
                   tL$2 = this.L$2;
                   tL$1 = this.L$1;
                   b.b(p0);
                   p0 = this.L$0;
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                tlabel = this.L$4;
                tL$2 = this.L$3;
                tL$1 = this.L$2;
                obj1 = this.L$1;
                omap$1 = this.L$0;
                b.b(p0);
             label_00ad :
                this.L$0 = omap$1;
                this.L$1 = obj1;
                this.L$2 = tL$1;
                this.L$3 = tL$2;
                this.L$4 = 0;
                this.label = 3;
                if (tlabel.d(p0, this) == obj) {
                   return obj;
                }else {
                   tlabel = tL$2;
                   tL$2 = tL$1;
                   tL$1 = obj1;
                   p0 = omap$1;
                }
             }
          }else {
             tlabel = this.L$3;
             tL$2 = this.L$2;
             tL$1 = this.L$1;
             obj1 = this.L$0;
             b.b(p0);
          label_0089 :
             if (p0.booleanValue()) {
                this.L$0 = obj1;
                this.L$1 = tL$1;
                this.L$2 = tL$2;
                this.L$3 = tlabel;
                this.L$4 = obj1;
                this.label = 2;
                if ((p0 = tL$1.invoke(tlabel.next(), this)) == obj) {
                   return obj;
                }else {
                   omap$1 = obj1;
                   obj1 = tL$1;
                   tL$1 = tL$2;
                   tL$2 = tlabel;
                   tlabel = omap$1;
                   goto label_00ad ;
                }
             }else {
                bj3.b(tL$2, null);
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          p0 = this.L$0;
          tL$2 = this.$this_map;
          tL$1 = this.$transform;
          ChannelIterator uChannelIter = tL$2.iterator();
       }
       this.L$0 = p0;
       this.L$1 = tL$1;
       this.L$2 = tL$2;
       this.L$3 = tlabel;
       this.label = 1;
       if ((obj1 = tlabel.a(this)) == obj) {
          return obj;
       }else {
          obj1 = p0;
          p0 = obj1;
          goto label_0089 ;
       }
    }
}
