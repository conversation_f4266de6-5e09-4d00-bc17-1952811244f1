package tb.a9v;
import tb.tsm;
import tb.t2o;
import android.view.ViewGroup;
import tb.bbs;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import com.android.alibaba.ip.runtime.IpChange;
import tb.tsm$a;
import tb.uwd;
import com.taobao.themis.kernel.container.ui.splash.ISplashView;
import tb.eas;
import java.util.Map;
import com.taobao.themis.kernel.utils.TMSAssertUtils;

public final class a9v implements tsm	// class@001eb0 from classes10.dex
{
    public final ViewGroup a;
    public final bbs b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x34500057);
       t2o.a(0x34500052);
    }
    public void a9v(ViewGroup p0,bbs p1){
       ckf.g(p0, "splashContainer");
       ckf.g(p1, "instance");
       super();
       this.a = p0;
       this.b = p1;
    }
    public void N(bbs p0){
       IpChange $ipChange = a9v.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3dc73485", objArray);
          return;
       }else {
          tsm$a.a(this, p0);
          return;
       }
    }
    public void a(){
       uwd ouwd;
       ISplashView splashView;
       IpChange $ipChange = a9v.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("7f837597", objArray);
          return;
       }else {
          this.b();
          if ((ouwd = this.b.Z()) != null && (splashView = ouwd.getSplashView()) != null) {
             splashView.a();
          }
          return;
       }
    }
    public void a(eas p0){
       uwd ouwd;
       ISplashView splashView;
       IpChange $ipChange = a9v.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6ce98f05", objArray);
          return;
       }else {
          ckf.g(p0, "error");
          this.b();
          if ((ouwd = this.b.Z()) != null && (splashView = ouwd.getSplashView()) != null) {
             splashView.b(p0, null);
          }
          return;
       }
    }
    public final void b(){
       IpChange $ipChange = a9v.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("68761c72", objArray);
          return;
       }else if(this.b.Z() == null){
          return;
       }else if(this.b.Z().getSplashView() == null){
          this.b.Z().createSplashView(this.a);
       }
       return;
    }
    public void c(){
       IpChange $ipChange = a9v.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("896696a2", objArray);
          return;
       }else {
          tsm$a.b(this);
          return;
       }
    }
    public void showLoading(){
       uwd ouwd;
       ISplashView splashView;
       IpChange $ipChange = a9v.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("73936486", objArray);
          return;
       }else {
          this.b();
          if ((ouwd = this.b.Z()) != null && (splashView = ouwd.getSplashView()) != null) {
             splashView.showLoading();
          }
          return;
       }
    }
}
