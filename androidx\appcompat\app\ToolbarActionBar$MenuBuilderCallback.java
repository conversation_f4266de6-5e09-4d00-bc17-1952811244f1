package androidx.appcompat.app.ToolbarActionBar$MenuBuilderCallback;
import androidx.appcompat.view.menu.MenuBuilder$Callback;
import androidx.appcompat.app.ToolbarActionBar;
import java.lang.Object;
import androidx.appcompat.view.menu.MenuBuilder;
import android.view.MenuItem;
import androidx.appcompat.widget.DecorToolbar;
import android.view.Menu;
import android.view.Window$Callback;
import android.view.View;

public final class ToolbarActionBar$MenuBuilderCallback implements MenuBuilder$Callback	// class@000586 from classes.dex
{
    public final ToolbarActionBar this$0;

    public void ToolbarActionBar$MenuBuilderCallback(ToolbarActionBar p0){
       this.this$0 = p0;
       super();
    }
    public boolean onMenuItemSelected(MenuBuilder p0,MenuItem p1){
       return false;
    }
    public void onMenuModeChange(MenuBuilder p0){
       if (this.this$0.mDecorToolbar.isOverflowMenuShowing()) {
          this.this$0.mWindowCallback.onPanelClosed(108, p0);
       }else if(this.this$0.mWindowCallback.onPreparePanel(0, null, p0)){
          this.this$0.mWindowCallback.onMenuOpened(108, p0);
       }
       return;
    }
}
