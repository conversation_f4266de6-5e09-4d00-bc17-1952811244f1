package kotlinx.coroutines.selects.SelectKt;
import kotlinx.coroutines.selects.SelectKt$DUMMY_PROCESS_RESULT_FUNCTION$1;
import tb.u1r;
import java.lang.String;
import kotlinx.coroutines.selects.TrySelectDetailedResult;
import java.lang.IllegalStateException;
import java.lang.StringBuilder;
import java.lang.Object;
import tb.w1a;
import tb.q23;
import tb.g1a;
import tb.xhv;

public final class SelectKt	// class@0006b6 from classes11.dex
{
    public static final w1a a;
    public static final u1r b;
    public static final u1r c;
    public static final u1r d;
    public static final u1r e;
    public static final u1r f;

    static {
       SelectKt.a = SelectKt$DUMMY_PROCESS_RESULT_FUNCTION$1.INSTANCE;
       SelectKt.b = new u1r("STATE_REG");
       SelectKt.c = new u1r("STATE_COMPLETED");
       SelectKt.d = new u1r("STATE_CANCELLED");
       SelectKt.e = new u1r("NO_RESULT");
       SelectKt.f = new u1r("PARAM_CLAUSE_0");
    }
    public static final TrySelectDetailedResult a(int p0){
       TrySelectDetailedResult aLREADY_SELE;
       if (p0) {
          if (p0 != 1) {
             if (p0 != 2) {
                if (p0 == 3) {
                   aLREADY_SELE = TrySelectDetailedResult.ALREADY_SELECTED;
                }else {
                   throw new IllegalStateException("Unexpected internal result: "+p0.toString());
                }
             }else {
                aLREADY_SELE = TrySelectDetailedResult.CANCELLED;
             }
          }else {
             aLREADY_SELE = TrySelectDetailedResult.REREGISTER;
          }
       }else {
          aLREADY_SELE = TrySelectDetailedResult.SUCCESSFUL;
       }
       return aLREADY_SELE;
    }
    public static final TrySelectDetailedResult b(int p0){
       return SelectKt.a(p0);
    }
    public static final w1a c(){
       return SelectKt.a;
    }
    public static final u1r d(){
       return SelectKt.e;
    }
    public static final u1r e(){
       return SelectKt.d;
    }
    public static final u1r f(){
       return SelectKt.c;
    }
    public static final u1r g(){
       return SelectKt.b;
    }
    public static final boolean h(q23 p0,g1a p1){
       return SelectKt.j(p0, p1);
    }
    public static final u1r i(){
       return SelectKt.f;
    }
    public static final boolean j(q23 p0,g1a p1){
       if ((p1 = p0.q(xhv.INSTANCE, null, p1)) == null) {
          return false;
       }
       p0.p(p1);
       return true;
    }
}
