package kotlinx.coroutines.selects.SelectKt$DUMMY_PROCESS_RESULT_FUNCTION$1;
import tb.w1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import java.lang.Void;

public final class SelectKt$DUMMY_PROCESS_RESULT_FUNCTION$1 extends Lambda implements w1a	// class@0006b5 from classes11.dex
{
    public static final SelectKt$DUMMY_PROCESS_RESULT_FUNCTION$1 INSTANCE;

    static {
       SelectKt$DUMMY_PROCESS_RESULT_FUNCTION$1.INSTANCE = new SelectKt$DUMMY_PROCESS_RESULT_FUNCTION$1();
    }
    public void SelectKt$DUMMY_PROCESS_RESULT_FUNCTION$1(){
       super(3);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final Void invoke(Object p0,Object p1,Object p2){
       return null;
    }
}
