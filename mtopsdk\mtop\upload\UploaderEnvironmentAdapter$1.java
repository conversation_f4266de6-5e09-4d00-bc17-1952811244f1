package mtopsdk.mtop.upload.UploaderEnvironmentAdapter$1;
import mtopsdk.mtop.domain.EnvModeEnum;
import java.lang.Enum;

public class UploaderEnvironmentAdapter$1	// class@000809 from classes11.dex
{
    public static final int[] $SwitchMap$mtopsdk$mtop$domain$EnvModeEnum;

    static {
       int[] ointArray = new int[EnvModeEnum.values().length];
       try{
          UploaderEnvironmentAdapter$1.$SwitchMap$mtopsdk$mtop$domain$EnvModeEnum = ointArray;
          ointArray[EnvModeEnum.ONLINE.ordinal()] = 1;
          try{
             UploaderEnvironmentAdapter$1.$SwitchMap$mtopsdk$mtop$domain$EnvModeEnum[EnvModeEnum.PREPARE.ordinal()] = 2;
             try{
                UploaderEnvironmentAdapter$1.$SwitchMap$mtopsdk$mtop$domain$EnvModeEnum[EnvModeEnum.TEST.ordinal()] = 3;
                try{
                   UploaderEnvironmentAdapter$1.$SwitchMap$mtopsdk$mtop$domain$EnvModeEnum[EnvModeEnum.TEST_SANDBOX.ordinal()] = 4;
                }catch(java.lang.NoSuchFieldError e0){
                }
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
