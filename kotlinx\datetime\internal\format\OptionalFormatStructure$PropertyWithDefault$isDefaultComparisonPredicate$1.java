package kotlinx.datetime.internal.format.OptionalFormatStructure$PropertyWithDefault$isDefaultComparisonPredicate$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.Object;
import tb.lb7;
import java.lang.Class;
import java.lang.String;
import kotlin.jvm.internal.CallableReference;

public final class OptionalFormatStructure$PropertyWithDefault$isDefaultComparisonPredicate$1 extends FunctionReferenceImpl implements g1a	// class@0006f7 from classes11.dex
{

    public void OptionalFormatStructure$PropertyWithDefault$isDefaultComparisonPredicate$1(Object p0){
       super(1, p0, lb7.class, "getter", "getter\(Ljava/lang/Object;\)Ljava/lang/Object;", 0);
    }
    public final Object invoke(Object p0){
       return this.receiver.a(p0);
    }
}
