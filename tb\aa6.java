package tb.aa6;
import com.taobao.android.dinamicx.videoc.expose.impl.RecyclerViewZone$b;
import tb.t2o;
import tb.cxb;
import java.lang.String;
import java.lang.Object;
import tb.h4c;
import androidx.recyclerview.widget.RecyclerView;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import android.view.View;
import java.util.Map;
import java.util.Set;
import java.util.Iterator;
import tb.zg5;
import androidx.recyclerview.widget.RecyclerView$LayoutManager;
import java.lang.Throwable;
import tb.xv5;
import com.taobao.android.dinamicx.f;
import java.util.ArrayList;
import com.taobao.android.dinamicx.f$a;
import tb.ic5;
import java.lang.ref.WeakReference;
import tb.iew;
import java.util.Collection;
import java.util.List;
import java.util.Collections;
import java.lang.Number;

public class aa6 implements RecyclerViewZone$b	// class@00185c from classes5.dex
{
    public final cxb a;
    public final boolean b;
    public final boolean c;
    public final boolean d;
    public final boolean e;
    public int f;
    public int g;
    public final String h;
    public static IpChange $ipChange;
    public static final int ORIENTATION_IDLE;
    public static final int ORIENTATION_SCROLL_TO_BOTTOM;
    public static final int ORIENTATION_SCROLL_TO_TOP;

    static {
       t2o.a(0x1c5002ff);
       t2o.a(0x1c50033c);
    }
    public void aa6(cxb p0,String p1){
       super(p0, false, false, false, false, p1);
    }
    public void aa6(cxb p0,boolean p1,boolean p2,boolean p3,boolean p4,String p5){
       super();
       this.f = 0;
       this.g = 0;
       this.a = p0;
       this.b = p1;
       this.c = p2;
       this.d = p3;
       this.e = p4;
       this.h = p5;
    }
    public void a(h4c p0,String p1,RecyclerView p2,int p3,int p4){
       IpChange $ipChange = aa6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,new Integer(p3),new Integer(p4)};
          $ipChange.ipc$dispatch("b84f2e5e", objArray);
          return;
       }else {
          this.f = p3;
          this.g = p4;
          return;
       }
    }
    public void b(h4c p0,String p1,RecyclerView p2,View p3){
       int childAdapter;
       aa6 ta;
       int i = 0;
       IpChange $ipChange = aa6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("5376cb78", objArray);
          return;
       }else if((childAdapter = p2.getChildAdapterPosition(p3)) == -1){
          if (this.a != null) {
             Iterator iterator = p0.h().keySet().iterator();
             while (iterator.hasNext()) {
                Integer integer = iterator.next();
                View view = (zg5.T2())? p2.getLayoutManager().findViewByPosition(integer.intValue()): p2.getLayoutManager().findViewByPosition(integer.intValue());
                if (view != null && p2.getChildAdapterPosition(view) == integer.intValue()) {
                   p0.j(integer, new WeakReference(view));
                }
                p0.f(integer, p1, i);
             }
             this.a.c(p1);
             if (this.e != null) {
                this.h(p0, p1);
             }else {
                p0.c(p1);
             }
          }
       }else {
          p0.m(Integer.valueOf(childAdapter), p1);
          if (this.c != null) {
             p0.f(Integer.valueOf(childAdapter), p1, i);
             if ((ta = this.a) != null) {
                ta.a(new iew(childAdapter, -1, new WeakReference(p3)), p1, i);
             }
          }else {
             p0.e(Integer.valueOf(childAdapter), p1);
          }
       }
       return;
    }
    public void h(h4c p0,String p1){
       IpChange $ipChange = aa6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("64cbcc80", objArray);
          return;
       }else {
          ArrayList uArrayList = new ArrayList(p0.g().keySet());
          if (uArrayList.isEmpty()) {
             return;
          }
          Collections.sort(uArrayList);
          if (this.i() == -1) {
             Collections.reverse(uArrayList);
          }
          p0.k(p1, uArrayList);
          return;
       }
    }
    public int i(){
       aa6 tf;
       aa6 tg;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = aa6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("d817a207", objArray).intValue();
       }else if((tf = this.f) == null && (tg = this.g) != null){
          if (tg <= null) {
             i1 = -1;
          }
          return i1;
       }else if(this.g == null && tf != null){
          if (tf <= null) {
             i1 = -1;
          }
          return i1;
       }else {
          return i;
       }
    }
    public void j(h4c p0,String p1,RecyclerView p2,View p3){
       IpChange $ipChange = aa6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("15f2352a", objArray);
          return;
       }else {
          int childAdapter = p2.getChildAdapterPosition(p3);
          WeakReference weakReferenc = new WeakReference(p3);
          p0.j(Integer.valueOf(childAdapter), weakReferenc);
          if (this.c == null) {
             p0.l(Integer.valueOf(childAdapter), weakReferenc, p1);
          }
          return;
       }
    }
    public void k(h4c p0,String p1,RecyclerView p2,int p3){
       IpChange $ipChange = aa6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,new Integer(p3)};
          $ipChange.ipc$dispatch("198897ee", objArray);
          return;
       }else if(this.b != null){
          return;
       }else if(p3){
          return;
       }else if(this.d != null){
          if (this.e != null) {
             this.h(p0, p1);
          }else {
             p0.i(p1);
          }
       }else {
          p0.d();
       }
       return;
    }
}
