package kotlinx.coroutines.CoroutineContextKt$foldCopies$1;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import kotlin.coroutines.d;
import kotlin.coroutines.d$b;
import tb.st4;

public final class CoroutineContextKt$foldCopies$1 extends Lambda implements u1a	// class@00048d from classes11.dex
{
    public static final CoroutineContextKt$foldCopies$1 INSTANCE;

    static {
       CoroutineContextKt$foldCopies$1.INSTANCE = new CoroutineContextKt$foldCopies$1();
    }
    public void CoroutineContextKt$foldCopies$1(){
       super(2);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final d invoke(d p0,d$b p1){
       if (p1 instanceof st4) {
          return p0.plus(p1.e());
       }
       return p0.plus(p1);
    }
}
