package kotlinx.coroutines.JobSupport$a;
import kotlinx.coroutines.c;
import tb.ar4;
import kotlinx.coroutines.JobSupport;
import java.lang.String;
import kotlinx.coroutines.m;
import java.lang.Throwable;
import java.lang.Object;
import kotlinx.coroutines.JobSupport$c;
import tb.fa4;
import java.util.concurrent.CancellationException;

public final class JobSupport$a extends c	// class@0004a2 from classes11.dex
{
    private final JobSupport l;

    public void JobSupport$a(ar4 p0,JobSupport p1){
       super(p0, 1);
       this.l = p1;
    }
    public String Q(){
       return "AwaitContinuation";
    }
    public Throwable y(m p0){
       Throwable throwable;
       Object obj = this.l.v0();
       if (obj instanceof JobSupport$c && (throwable = obj.d()) != null) {
          return throwable;
       }
       if (obj instanceof fa4) {
          return obj.a;
       }
       return p0.u0();
    }
}
