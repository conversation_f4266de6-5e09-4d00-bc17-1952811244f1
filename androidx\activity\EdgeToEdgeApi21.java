package androidx.activity.EdgeToEdgeApi21;
import androidx.activity.EdgeToEdgeBase;
import androidx.activity.SystemBarStyle;
import android.view.Window;
import android.view.View;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.core.view.WindowCompat;

public final class EdgeToEdgeApi21 extends EdgeToEdgeBase	// class@000443 from classes.dex
{

    public void EdgeToEdgeApi21(){
       super();
    }
    public void setUp(SystemBarStyle p0,SystemBarStyle p1,Window p2,View p3,boolean p4,boolean p5){
       ckf.g(p0, "statusBarStyle");
       ckf.g(p1, "navigationBarStyle");
       ckf.g(p2, "window");
       ckf.g(p3, "view");
       WindowCompat.setDecorFitsSystemWindows(p2, false);
       p2.addFlags(0x4000000);
       p2.addFlags(0x8000000);
    }
}
