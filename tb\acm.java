package tb.acm;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import tb.ass;
import tb.j0a;
import android.content.Context;
import com.taobao.wireless.link.pop.PopMessageData;

public abstract class acm	// class@001ebd from classes10.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x40d00076);
    }
    public void acm(){
       super();
    }
    public static acm a(int p0){
       int i = 1;
       IpChange $ipChange = acm.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = new Integer(p0);
          return $ipChange.ipc$dispatch("11347233", objArray);
       }else if(i == p0){
          return ass.c();
       }else {
          return j0a.c();
       }
    }
    public abstract void b(Context p0,PopMessageData p1);
}
