package kotlinx.serialization.internal.ClassValueCache;
import tb.rb40;
import tb.g1a;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.h520;
import tb.wyf;
import tb.x530;
import java.lang.Class;
import tb.gyf;
import tb.g520;
import tb.xl30;
import java.lang.ref.SoftReference;
import kotlinx.serialization.internal.ClassValueCache$get$$inlined$getOrSet$1;
import tb.d1a;
import tb.m020;

public final class ClassValueCache implements rb40	// class@000737 from classes11.dex
{
    public final g1a a;
    public final h520 b;

    public void ClassValueCache(g1a p0){
       ckf.g(p0, "compute");
       super();
       this.a = p0;
       this.b = new h520();
    }
    public x530 a(wyf p0){
       m020 om020;
       ckf.g(p0, "key");
       Object obj = g520.a(this.b, gyf.b(p0));
       ckf.f(obj, "get\(...\)");
       if ((om020 = obj.a.get()) == null) {
          om020 = obj.a(new ClassValueCache$get$$inlined$getOrSet$1(this, p0));
       }
       return om020.a;
    }
    public final g1a b(){
       return this.a;
    }
}
