package tb.a4p;
import tb.t2o;
import java.lang.String;
import com.taobao.location.common.TBLocationDTO;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import tb.a4p$d;
import android.content.Context;
import java.lang.Boolean;
import android.app.Application;
import com.taobao.tao.Globals;
import tb.a4p$a;
import java.lang.Runnable;
import com.taobao.android.task.Coordinator;
import tb.o4p;
import tb.qzl;
import com.taobao.runtimepermission.a;
import com.alibaba.mtl.appmonitor.AppMonitor$Alarm;
import java.lang.StringBuilder;
import tb.c4p;
import android.util.Log;
import com.taobao.runtimepermission.a$a;
import tb.a4p$b;
import tb.gjb;
import com.taobao.taobao.R$string;
import com.alibaba.ability.localization.Localization;
import java.lang.System;
import com.taobao.location.common.TBLocationOption;
import com.taobao.location.common.TBLocationOption$TimeLimit;
import com.taobao.location.common.TBLocationOption$RegionType;
import com.taobao.location.client.TBLocationClient;
import tb.a4p$c;
import android.os.Looper;
import tb.unr;
import tb.a4p$e;

public class a4p	// class@001af1 from classes8.dex
{
    public static IpChange $ipChange;
    public static final String LOCATION_PERMISSION_BIZ_NAME;
    public static TBLocationDTO a;
    public static long b;
    public static final String[] c;
    public static Boolean d;

    static {
       t2o.a(0x3320003e);
       String[] stringArray = new String[]{"android.permission.ACCESS_FINE_LOCATION"};
       a4p.c = stringArray;
       a4p.d = null;
    }
    public static TBLocationDTO a(TBLocationDTO p0){
       IpChange $ipChange = a4p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("3289da3d", objArray);
       }else {
          a4p.a = p0;
          return p0;
       }
    }
    public static void b(a4p$d p0,Context p1,boolean p2){
       IpChange $ipChange = a4p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          $ipChange.ipc$dispatch("d62788ee", objArray);
          return;
       }else {
          a4p.g(p0, p1, p2);
          return;
       }
    }
    public static TBLocationDTO c(){
       IpChange $ipChange = a4p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("738c00aa", objArray);
       }else if(a4p.h(Globals.getApplication())){
          Coordinator.execute(new a4p$a());
          return a4p.a;
       }else {
          return null;
       }
    }
    public static boolean d(Context p0){
       qzl oqzl;
       int i1;
       int i = 1;
       IpChange $ipChange = a4p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("70a42a47", objArray).booleanValue();
       }else if(!o4p.t0()){
          return 0;
       }else if((oqzl = a.d(p0, "TB_SHOPPING_PROCESS", a4p.c)) == null){
          return 0;
       }else if(!(i1 = oqzl.b[0])){
          AppMonitor$Alarm.commitSuccess("tbsearch", "locationPermission");
          return i;
       }else {
          object oobject = oqzl.c[0];
          c4p.m("SearchLocationService", "checkBizPermission: denied with msg "+oobject);
          AppMonitor$Alarm.commitFail("tbsearch", "locationPermission", String.valueOf(i1), oobject);
          return 0;
       }
    }
    public static void e(a4p$d p0,Context p1,boolean p2){
       IpChange $ipChange = a4p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          $ipChange.ipc$dispatch("dafdcfbe", objArray);
          return;
       }else if(p1 == null){
          Log.e("SearchLocationService", "context is null");
          return;
       }else if(a4p.d(p1)){
          a4p.g(p0, p1, p2);
          return;
       }else {
          TBLocationDTO tBLocationDT = null;
          if (!p2) {
             if (p0 != null) {
                p0.a(tBLocationDT);
             }
             return;
          }else if(!o4p.L1()){
             if (p0 != null) {
                p0.a(tBLocationDT);
             }
             return;
          }else {
             a.c(p1, a4p.c).x(1).u(new a4p$b(p0, p1, p2)).t("TB_SHOPPING_PROCESS").B(1).y(0).w(Localization.q(R$string.taobao_app_1005_1_16678)).m();
             return;
          }
       }
    }
    public static void f(Context p0){
       IpChange $ipChange = a4p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("8bd14ee2", objArray);
          return;
       }else {
          int i = o4p.y(30) * 0xea60;
          c4p.m("SearchLocationService", "initiativeLocationIfOvertime: current interval is "+i);
          if (((System.currentTimeMillis() - a4p.b) - (long)i) < 0) {
             return;
          }
          a4p.e(null, p0, 0);
          return;
       }
    }
    public static void g(a4p$d p0,Context p1,boolean p2){
       IpChange $ipChange = a4p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          $ipChange.ipc$dispatch("c272921", objArray);
          return;
       }else if(!a4p.d(p1)){
          return;
       }else {
          a4p.b = System.currentTimeMillis();
          TBLocationOption tBLocationOp = new TBLocationOption();
          if (o4p.G1() && !p2) {
             tBLocationOp.setTimeLimit(TBLocationOption$TimeLimit.TWO_HOUR);
          }else {
             tBLocationOp.setTimeLimit(TBLocationOption$TimeLimit.NO_CACHE);
          }
          tBLocationOp.setRegionType(TBLocationOption$RegionType.DISTRICT);
          TBLocationClient.g(p1).h(tBLocationOp, new a4p$c(p0), Looper.getMainLooper());
          return;
       }
    }
    public static boolean h(Context p0){
       Boolean d;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a4p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("a17da9bc", objArray).booleanValue();
       }else if(a4p.d == null){
          a4p.d = Boolean.valueOf(a4p.d(p0));
       }else {
          Coordinator.execute(new a4p$e(p0));
       }
       if ((d = a4p.d) != null && d.booleanValue()) {
          i = true;
       }
       return i;
    }
}
