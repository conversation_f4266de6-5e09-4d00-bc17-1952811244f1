package mtopsdk.mtop.upload.util.FileUploadThreadPoolExecutorFactory;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.BlockingQueue;
import java.lang.Runnable;
import java.util.concurrent.Future;
import mtopsdk.mtop.upload.util.FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory;
import java.util.concurrent.AbstractExecutorService;
import java.lang.StringBuilder;
import java.lang.Throwable;
import mtopsdk.common.util.TBSdkLog;
import mtopsdk.mtop.upload.util.FileUploadSetting;

public class FileUploadThreadPoolExecutorFactory	// class@00081a from classes11.dex
{
    public static IpChange $ipChange;
    private static final int KEEP_ALIVE_TIME;
    private static final int REMOVE_TASKS_CORE_POOL_SIZE;
    private static final int REMOVE_TASKS_MAX_POOL_SIZE;
    private static final String TAG;
    private static int priority;
    private static ThreadPoolExecutor removeTasksExecutor;
    private static ThreadPoolExecutor uploadTasksExecutor;

    static {
       t2o.a(0x2590001c);
       FileUploadThreadPoolExecutorFactory.priority = 10;
    }
    public void FileUploadThreadPoolExecutorFactory(){
       super();
    }
    private static ThreadPoolExecutor createExecutor(int p0,int p1,int p2,int p3,ThreadFactory p4){
       LinkedBlockingQueue linkedBlocki;
       ThreadPoolExecutor p3;
       IpChange $ipChange = FileUploadThreadPoolExecutorFactory.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0),new Integer(p1),new Integer(p2),new Integer(p3),p4};
          return $ipChange.ipc$dispatch("d514e25d", objArray);
       }else if(p3 > 0){
          linkedBlocki = new LinkedBlockingQueue(p3);
       }else {
          linkedBlocki = new LinkedBlockingQueue();
       }
       LinkedBlockingQueue linkedBlocki1 = linkedBlocki;
       p3 = new ThreadPoolExecutor(p0, p1, (long)p2, TimeUnit.SECONDS, linkedBlocki1, p4);
       if (p2 > 0) {
          p3.allowCoreThreadTimeOut(1);
       }
       return p3;
    }
    public static void setUploadTasksThreadPoolExecutor(ThreadPoolExecutor p0){
       IpChange $ipChange = FileUploadThreadPoolExecutorFactory.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("97ec15a5", objArray);
          return;
       }else if(p0 != null){
          FileUploadThreadPoolExecutorFactory.uploadTasksExecutor = p0;
       }
       return;
    }
    public static Future submitRemoveTask(Runnable p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = FileUploadThreadPoolExecutorFactory.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("92791b7c", objArray);
       }else if(FileUploadThreadPoolExecutorFactory.removeTasksExecutor == null){
          _monitor_enter(FileUploadThreadPoolExecutorFactory.class);
          if (FileUploadThreadPoolExecutorFactory.removeTasksExecutor == null) {
             FileUploadThreadPoolExecutorFactory.removeTasksExecutor = FileUploadThreadPoolExecutorFactory.createExecutor(i1, i1, 10, i, new FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory(FileUploadThreadPoolExecutorFactory.priority, "RemoveTasks"));
          }
          _monitor_exit(FileUploadThreadPoolExecutorFactory.class);
       }
       Future uFuture = FileUploadThreadPoolExecutorFactory.removeTasksExecutor.submit(p0);
       return uFuture;
    }
    public static Future submitUploadTask(Runnable p0){
       int i = 0;
       IpChange $ipChange = FileUploadThreadPoolExecutorFactory.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("973795f9", objArray);
       }else if(FileUploadThreadPoolExecutorFactory.uploadTasksExecutor == null){
          _monitor_enter(FileUploadThreadPoolExecutorFactory.class);
          if (FileUploadThreadPoolExecutorFactory.uploadTasksExecutor == null) {
             FileUploadThreadPoolExecutorFactory.uploadTasksExecutor = FileUploadThreadPoolExecutorFactory.createExecutor(FileUploadSetting.getUploadThreadsNums(), FileUploadSetting.getUploadThreadsNums(), 10, i, new FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory(FileUploadThreadPoolExecutorFactory.priority, "UploadTasks"));
          }
          _monitor_exit(FileUploadThreadPoolExecutorFactory.class);
       }
       Future uFuture = FileUploadThreadPoolExecutorFactory.uploadTasksExecutor.submit(p0);
       return uFuture;
    }
}
