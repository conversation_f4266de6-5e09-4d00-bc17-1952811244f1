package androidx.activity.result.contract.ActivityResultContracts$OpenDocumentTree;
import androidx.activity.result.contract.ActivityResultContract;
import android.content.Context;
import android.net.Uri;
import android.content.Intent;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.os.Build$VERSION;
import android.os.Parcelable;
import androidx.activity.result.contract.ActivityResultContract$SynchronousResult;

public class ActivityResultContracts$OpenDocumentTree extends ActivityResultContract	// class@0004cc from classes.dex
{

    public void ActivityResultContracts$OpenDocumentTree(){
       super();
    }
    public Intent createIntent(Context p0,Uri p1){
       ckf.g(p0, "context");
       Intent intent = new Intent("android.intent.action.OPEN_DOCUMENT_TREE");
       if (Build$VERSION.SDK_INT >= 26 && p1 != null) {
          intent.putExtra("android.provider.extra.INITIAL_URI", p1);
       }
       return intent;
    }
    public Intent createIntent(Context p0,Object p1){
       return this.createIntent(p0, p1);
    }
    public final ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,Uri p1){
       ckf.g(p0, "context");
       return null;
    }
    public ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,Object p1){
       return this.getSynchronousResult(p0, p1);
    }
    public final Uri parseResult(int p0,Intent p1){
       Uri uri = null;
       if (p0 != -1) {
          Uri uri1 = uri;
       }
       if (p1 != null) {
          uri = p1.getData();
       }
       return uri;
    }
    public Object parseResult(int p0,Intent p1){
       return this.parseResult(p0, p1);
    }
}
