package mtopsdk.mtop.domain.BaseOutDo;
import mtopsdk.mtop.domain.IMTOPDataObject;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;
import java.util.Arrays;

public abstract class BaseOutDo implements IMTOPDataObject	// class@00247a from classes.dex
{
    private String api;
    private String[] ret;
    private String v;

    static {
       t2o.a(0x253000cc);
       t2o.a(0x253000cf);
    }
    public void BaseOutDo(){
       super();
    }
    public String getApi(){
       return this.api;
    }
    public abstract Object getData();
    public String[] getRet(){
       return this.ret;
    }
    public String getV(){
       return this.v;
    }
    public void setApi(String p0){
       this.api = p0;
    }
    public void setRet(String[] p0){
       this.ret = p0;
    }
    public void setV(String p0){
       this.v = p0;
    }
    public String toString(){
       return new StringBuilder(64)+"BaseOutDo [api="+this.api+", v="+this.v+", ret="+Arrays.toString(this.ret)+"]";
    }
}
