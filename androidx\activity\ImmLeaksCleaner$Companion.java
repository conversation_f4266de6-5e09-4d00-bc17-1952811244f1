package androidx.activity.ImmLeaksCleaner$Companion;
import java.lang.Object;
import tb.a07;
import androidx.activity.ImmLeaksCleaner$Cleaner;
import tb.njg;
import androidx.activity.ImmLeaksCleaner;

public final class ImmLeaksCleaner$Companion	// class@000451 from classes.dex
{

    private void ImmLeaksCleaner$Companion(){
       super();
    }
    public void ImmLeaksCleaner$Companion(a07 p0){
       super();
    }
    public final ImmLeaksCleaner$Cleaner getCleaner(){
       return ImmLeaksCleaner.access$getCleaner$delegate$cp().getValue();
    }
}
