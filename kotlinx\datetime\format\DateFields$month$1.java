package kotlinx.datetime.format.DateFields$month$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.jf20;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class DateFields$month$1 extends MutablePropertyReference1Impl	// class@0006d7 from classes11.dex
{
    public static final DateFields$month$1 INSTANCE;

    static {
       DateFields$month$1.INSTANCE = new DateFields$month$1();
    }
    public void DateFields$month$1(){
       super(jf20.class, "monthNumber", "getMonthNumber\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.x();
    }
    public void set(Object p0,Object p1){
       p0.l(p1);
    }
}
