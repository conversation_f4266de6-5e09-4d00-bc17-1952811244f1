package tb.ae;
import java.lang.Runnable;
import com.taobao.android.artry.arflow.ARTryJSFlowForMiniApp;
import tb.c6a;
import java.lang.Object;

public final class ae implements Runnable	// class@00187b from classes5.dex
{
    public final ARTryJSFlowForMiniApp a;
    public final c6a b;

    public void ae(ARTryJSFlowForMiniApp p0,c6a p1){
       super();
       this.a = p0;
       this.b = p1;
    }
    public final void run(){
       ARTryJSFlowForMiniApp.f(this.a, this.b);
    }
}
