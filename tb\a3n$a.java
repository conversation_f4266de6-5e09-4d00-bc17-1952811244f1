package tb.a3n$a;
import android.view.View$OnClickListener;
import android.content.Context;
import java.lang.Object;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.app.Activity;

public final class a3n$a implements View$OnClickListener	// class@001e96 from classes10.dex
{
    public final Context a;
    public static IpChange $ipChange;

    public void a3n$a(Context p0){
       this.a = p0;
       super();
    }
    public final void onClick(View p0){
       IpChange $ipChange = a3n$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8dfcefe2", objArray);
          return;
       }else {
          a3n$a ta = this.a;
          if (ta instanceof Activity) {
          }else {
             ta = null;
          }
          if (ta != null) {
             ta.finish();
          }
          return;
       }
    }
}
