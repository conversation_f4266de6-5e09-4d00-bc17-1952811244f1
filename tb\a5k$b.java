package tb.a5k$b;
import com.taobao.tao.flexbox.layoutmanager.core.n$h;
import tb.a5k;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import android.view.View;
import tb.nwv;

public class a5k$b implements n$h	// class@001761 from classes9.dex
{
    public final a5k a;
    public static IpChange $ipChange;

    public void a5k$b(a5k p0){
       super();
       this.a = p0;
    }
    public boolean a(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a5k$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("ec6a7c6a", objArray).booleanValue();
       }else if(nwv.a0(a5k.h(this.a)) && nwv.Z(a5k.i(this.a))){
          i = true;
       }
       return i;
    }
}
