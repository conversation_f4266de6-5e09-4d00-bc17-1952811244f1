package tb.aal$a;
import java.lang.Runnable;
import tb.aal;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.w8l;
import tb.djn;

public class aal$a implements Runnable	// class@001eb2 from classes10.dex
{
    public final aal a;
    public static IpChange $ipChange;

    public void aal$a(aal p0){
       this.a = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = aal$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          djn.e("calendar_clear_title", w8l.f);
          return;
       }
    }
}
