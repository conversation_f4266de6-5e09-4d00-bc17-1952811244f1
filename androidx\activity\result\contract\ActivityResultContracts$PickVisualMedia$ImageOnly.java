package androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$ImageOnly;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType;
import java.lang.Object;

public final class ActivityResultContracts$PickVisualMedia$ImageOnly implements ActivityResultContracts$PickVisualMedia$VisualMediaType	// class@0004d3 from classes.dex
{
    public static final ActivityResultContracts$PickVisualMedia$ImageOnly INSTANCE;

    static {
       ActivityResultContracts$PickVisualMedia$ImageOnly.INSTANCE = new ActivityResultContracts$PickVisualMedia$ImageOnly();
    }
    private void ActivityResultContracts$PickVisualMedia$ImageOnly(){
       super();
    }
}
