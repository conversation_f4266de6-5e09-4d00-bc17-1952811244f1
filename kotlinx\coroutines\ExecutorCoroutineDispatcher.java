package kotlinx.coroutines.ExecutorCoroutineDispatcher;
import java.io.Closeable;
import kotlinx.coroutines.CoroutineDispatcher;
import kotlinx.coroutines.ExecutorCoroutineDispatcher$Key;
import tb.a07;

public abstract class ExecutorCoroutineDispatcher extends CoroutineDispatcher implements Closeable	// class@00049c from classes11.dex
{
    public static final ExecutorCoroutineDispatcher$Key Key;

    static {
       ExecutorCoroutineDispatcher.Key = new ExecutorCoroutineDispatcher$Key(null);
    }
    public void ExecutorCoroutineDispatcher(){
       super();
    }
}
