package kotlinx.coroutines.selects.SelectImplementation$processResultAndInvokeBlockRecoveringException$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import kotlinx.coroutines.selects.SelectImplementation;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.selects.SelectImplementation$a;

public final class SelectImplementation$processResultAndInvokeBlockRecoveringException$1 extends ContinuationImpl	// class@0006b3 from classes11.dex
{
    public int label;
    public Object result;
    public final SelectImplementation this$0;

    public void SelectImplementation$processResultAndInvokeBlockRecoveringException$1(SelectImplementation p0,ar4 p1){
       this.this$0 = p0;
       super(p1);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return SelectImplementation.j(this.this$0, null, null, this);
    }
}
