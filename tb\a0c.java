package tb.a0c;
import tb.c0c;
import java.lang.String;
import java.lang.CharSequence;
import java.lang.RuntimeException;
import android.text.TextUtils;

public final class a0c	// class@001ac8 from classes8.dex
{

    static {
    }
    public static void a(c0c p0,String p1){
       p0.b(p1);
       if (!p1.contains("^")) {
          return;
       }
       throw new RuntimeException("The key cannot contain the special characters: ^");
    }
    public static void b(c0c p0,String p1){
       if (!TextUtils.isEmpty(p1)) {
          return;
       }
       throw new RuntimeException("The key is empty.");
    }
    public static String c(String p0){
       if (TextUtils.isEmpty(p0)) {
          return p0;
       }
       return p0.replace("&mksep", "^").replace("&mkequal", "=");
    }
    public static String d(String p0){
       if (TextUtils.isEmpty(p0)) {
          return p0;
       }
       return p0.replace("^", "&mksep").replace("=", "&mkequal");
    }
    public static String e(String p0){
       if (TextUtils.isEmpty(p0)) {
          return p0;
       }
       return p0.replace("=", "&mkequal");
    }
}
