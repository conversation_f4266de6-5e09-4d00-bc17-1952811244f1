package tb.a2d;
import tb.t2o;
import com.taobao.themis.utils.io.ByteArrayPool;
import java.lang.Object;
import java.io.Closeable;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Throwable;
import java.lang.Integer;
import java.io.InputStream;
import tb.bam;
import tb.a07;
import java.io.ByteArrayOutputStream;

public final class a2d	// class@001e60 from classes10.dex
{
    public static IpChange $ipChange;
    public static final a2d INSTANCE;
    public static final int IO_BUFFER_SIZE;
    public static final ByteArrayPool a;

    static {
       t2o.a(0x35200020);
       a2d.INSTANCE = new a2d();
       a2d.a = new ByteArrayPool(0x5000);
    }
    public void a2d(){
       super();
    }
    public static final void a(Closeable p0){
       IpChange $ipChange = a2d.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("e61d37bb", objArray);
          return;
       }else if(p0 != null){
          try{
             p0.close();
          }catch(java.io.IOException e4){
             e4.printStackTrace();
          }
       }
       return;
    }
    public static final byte[] b(int p0){
       IpChange $ipChange = a2d.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a2d.a.d(p0);
       }
       Object[] objArray = new Object[]{new Integer(p0)};
       return $ipChange.ipc$dispatch("a0197a7e", objArray);
    }
    public static final byte[] d(InputStream p0){
       int i1;
       int i = 0;
       IpChange $ipChange = a2d.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("2f0d8a3e", objArray);
       }else if(p0 == null){
          return null;
       }else {
          bam uobam = new bam(null, i, 3, null);
          byte[] uobyteArray = a2d.b(2048);
          try{
             while ((i1 = p0.read(uobyteArray, i, uobyteArray.length)) >= 0) {
                uobam.write(uobyteArray, i, i1);
             }
             a2d.e(uobyteArray);
             a2d.a(p0);
             return uobam.toByteArray();
          }catch(java.lang.Exception e0){
             e0.printStackTrace();
             a2d.e(uobyteArray);
             a2d.a(p0);
             return null;
          }
       }
    }
    public static final void e(byte[] p0){
       IpChange $ipChange = a2d.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("15524871", objArray);
          return;
       }else {
          a2d.a.e(p0);
          return;
       }
    }
    public final ByteArrayPool c(){
       IpChange $ipChange = a2d.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a2d.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f076f5af", objArray);
    }
}
