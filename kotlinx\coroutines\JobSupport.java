package kotlinx.coroutines.JobSupport;
import kotlinx.coroutines.m;
import tb.jr3;
import tb.vql;
import java.lang.Object;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import tb.suf;
import tb.k9p;
import tb.f5k;
import tb.ruf;
import kotlinx.coroutines.JobSupport$f;
import kotlinx.coroutines.internal.LockFreeLinkedListNode;
import kotlinx.coroutines.internal.LockFreeLinkedListNode$a;
import java.lang.Throwable;
import java.util.List;
import java.util.IdentityHashMap;
import java.util.Map;
import java.util.Set;
import java.util.Collections;
import tb.dv6;
import tb.rgq;
import java.util.Iterator;
import java.util.concurrent.CancellationException;
import tb.sm8;
import tb.yse;
import tb.ar4;
import kotlinx.coroutines.JobSupport$a;
import kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt;
import kotlinx.coroutines.c;
import tb.gbo;
import tb.g1a;
import tb.rr7;
import tb.q23;
import tb.s23;
import tb.dkf;
import tb.jv6;
import tb.hbo;
import tb.xhv;
import java.lang.Void;
import kotlinx.coroutines.JobSupport$c;
import tb.fa4;
import tb.a07;
import java.lang.IllegalStateException;
import java.lang.StringBuilder;
import tb.hr3;
import tb.o5k;
import tb.huf;
import tb.ukf;
import tb.vkf;
import tb.ir3;
import tb.ha4;
import kotlinx.coroutines.CompletionHandlerException;
import tb.ckf;
import kotlinx.coroutines.JobCancellationException;
import java.lang.UnsupportedOperationException;
import kotlinx.coroutines.JobSupport$d;
import tb.h30;
import tb.ic8;
import tb.wse;
import kotlinx.coroutines.JobSupport$e;
import java.lang.Iterable;
import kotlinx.coroutines.TimeoutCancellationException;
import kotlin.jvm.internal.Ref$ObjectRef;
import kotlinx.coroutines.JobSupport$b;
import kotlinx.coroutines.m$a;
import tb.ox10;
import tb.vu4;
import tb.ov6;
import tb.sbp;
import kotlinx.coroutines.JobSupport$children$1;
import tb.u1a;
import tb.wbp;
import kotlin.coroutines.d;
import tb.quf;
import kotlin.coroutines.d$c;
import kotlin.coroutines.d$b;
import tb.f9p;
import tb.g9p;
import kotlinx.coroutines.JobSupport$onAwaitInternal$1;
import tb.kqu;
import kotlinx.coroutines.JobSupport$onAwaitInternal$2;
import tb.w1a;
import tb.w820;
import tb.d9p;
import tb.e9p;
import kotlinx.coroutines.JobSupport$onJoin$1;
import tb.rxk;

public class JobSupport implements m, jr3, vql	// class@0004ac from classes11.dex
{
    private Object a;
    private Object b;
    private static final AtomicReferenceFieldUpdater c;
    private static final AtomicReferenceFieldUpdater d;

    static {
       JobSupport.c = AtomicReferenceFieldUpdater.newUpdater(JobSupport.class, Object.class, "a");
       JobSupport.d = AtomicReferenceFieldUpdater.newUpdater(JobSupport.class, Object.class, "b");
    }
    public void JobSupport(boolean p0){
       super();
       ic8 f = (p0)? suf.f: suf.e;
       this.a = f;
       return;
    }
    public static final void B(JobSupport p0,k9p p1,Object p2){
       p0.e1(p1, p2);
    }
    private final boolean D(Object p0,f5k p1,ruf p2){
       boolean b;
       JobSupport$f uof = new JobSupport$f(p2, this, p0);
       while (true) {
          int i = p1.j().o(p2, p1, uof);
          b = true;
          if (i != b) {
             if (i != 2) {
                continue ;
             }else {
                b = false;
                break ;
             }
          }else {
             break ;
          }
       }
       return b;
    }
    private final void E(Throwable p0,List p1){
       if (p1.size() <= 1) {
          return;
       }
       Set set = Collections.newSetFromMap(new IdentityHashMap(p1.size()));
       int i = (!dv6.c())? p0: rgq.l(p0);
       Iterator iterator = p1.iterator();
       while (iterator.hasNext()) {
          Throwable throwable = iterator.next();
          if (dv6.c()) {
             throwable = rgq.l(throwable);
          }
          if (throwable != p0 && (throwable != i && (!throwable instanceof CancellationException && set.add(throwable)))) {
             sm8.a(p0, throwable);
          }
       }
       return;
    }
    private final boolean H0(){
       while (true) {
          Object obj = this.v0();
          if (!obj instanceof yse) {
             return false;
          }
          if (this.h1(obj) >= 0) {
             break ;
          }
       }
       return true;
    }
    private final Object I(ar4 p0){
       Object obj;
       JobSupport$a uoa = new JobSupport$a(IntrinsicsKt__IntrinsicsJvmKt.c(p0), this);
       uoa.E();
       s23.a(uoa, this.D0(new gbo(uoa)));
       if ((obj = uoa.A()) == dkf.d()) {
          jv6.c(p0);
       }
       return obj;
    }
    private final Object I0(ar4 p0){
       Object obj;
       c uoc = new c(IntrinsicsKt__IntrinsicsJvmKt.c(p0), 1);
       uoc.E();
       s23.a(uoc, this.D0(new hbo(uoc)));
       if ((obj = uoc.A()) == dkf.d()) {
          jv6.c(p0);
       }
       if (obj == dkf.d()) {
          return obj;
       }else {
          return xhv.INSTANCE;
       }
    }
    private final Void K0(g1a p0){
       while (true) {
          p0.invoke(this.v0());
       }
    }
    private final Object N0(Object p0){
       Throwable throwable = null;
       Throwable throwable1 = throwable;
       while (true) {
          Object obj = this.v0();
          if (obj instanceof JobSupport$c) {
             _monitor_enter(obj);
             if (obj.g()) {
                _monitor_exit(obj);
                return suf.c;
             }else {
                boolean b = obj.e();
                if (p0 != null || !b) {
                   if (throwable1 == null) {
                      throwable1 = this.V(p0);
                   }
                   obj.a(throwable1);
                }
                p0 = obj.d();
                if (!b) {
                   throwable = p0;
                }
                _monitor_exit(obj);
                if (throwable != null) {
                   this.T0(obj.getList(), throwable);
                   break ;
                }
                break ;
             }
          }else if(obj instanceof yse){
             if (throwable1 == null) {
                throwable1 = this.V(p0);
             }
             Object obj1 = obj;
             if (obj1.isActive()) {
                if (this.n1(obj1, throwable1)) {
                   return suf.a;
                }
                continue ;
             }else if((obj1 = this.o1(obj, new fa4(throwable1, false, 2, throwable))) != suf.a){
                if (obj1 != suf.b) {
                   return obj1;
                }
             }else {
                throw new IllegalStateException("Cannot happen in "+obj.toString());
             }
          }else {
             return suf.c;
          }
       }
       return suf.a;
    }
    private final Object P(Object p0){
       Object obj;
       while (true) {
          obj = this.v0();
          if (!obj instanceof yse || (obj instanceof JobSupport$c || !obj.f())) {
             return suf.a;
          }
          if ((obj = this.o1(obj, new fa4(this.V(p0), false, 2, null))) != suf.b) {
             break ;
          }
       }
       return obj;
    }
    private final boolean Q(Throwable p0){
       hr3 ohr3;
       boolean b = true;
       if (this.G0()) {
          return b;
       }
       boolean b1 = p0 instanceof CancellationException;
       if ((ohr3 = this.t0()) == null || ohr3 == o5k.INSTANCE) {
          return b1;
       }
       if (!ohr3.d(p0) && !b1) {
          b = false;
       }
       return b;
    }
    private final ruf Q0(g1a p0,boolean p1){
       ruf oruf = null;
       if (p1) {
          if (p0 instanceof huf) {
             oruf = p0;
          }
          if (!oruf) {
             oruf = new ukf(p0);
          }
       }else if(p0 instanceof ruf){
          oruf = p0;
       }
       if (oruf != null) {
          String dEBUG_PROPER = dv6.DEBUG_PROPERTY_NAME;
       }else {
          oruf = new vkf(p0);
       }
       oruf.r(this);
       return oruf;
    }
    private final ir3 S0(LockFreeLinkedListNode p0){
       while (p0.k()) {
          p0 = p0.j();
       }
       while (true) {
          p0 = p0.i();
          if (!p0.k()) {
             if (p0 instanceof ir3) {
                break ;
             }else {
                if (p0 instanceof f5k) {
                   return null;
                }
                continue ;
             }
          }
       }
       return p0;
    }
    private final void T(yse p0,Object p1){
       hr3 ohr3;
       f5k list;
       if ((ohr3 = this.t0()) != null) {
          ohr3.dispose();
          this.g1(o5k.INSTANCE);
       }
       Throwable throwable = null;
       if (p1 instanceof fa4) {
       }else {
          p1 = throwable;
       }
       if (p1 != null) {
          throwable = p1.a;
       }
       if (p0 instanceof ruf) {
          p0.p(throwable);
       }else if((list = p0.getList()) != null){
          this.U0(list, throwable);
       }
       return;
    }
    private final void T0(f5k p0,Throwable p1){
       this.Y0(p1);
       LockFreeLinkedListNode lockFreeLink = p0.h();
       ckf.e(lockFreeLink, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeLinkedListNode{ kotlinx.coroutines.internal.LockFreeLinkedListKt.Node }");
       Throwable throwable = null;
       while (!ckf.b(lockFreeLink, p0)) {
          if (lockFreeLink instanceof huf) {
             lockFreeLink.p(p1);
          }
          lockFreeLink = lockFreeLink.i();
       }
       if (throwable != null) {
          this.x0(throwable);
       }
       this.Q(p1);
       return;
    }
    private final void U(JobSupport$c p0,ir3 p1,Object p2){
       if ((p1 = this.S0(p1)) != null && this.q1(p0, p1, p2)) {
          return;
       }
       this.G(this.Y(p0, p2));
       return;
    }
    private final void U0(f5k p0,Throwable p1){
       LockFreeLinkedListNode lockFreeLink = p0.h();
       ckf.e(lockFreeLink, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeLinkedListNode{ kotlinx.coroutines.internal.LockFreeLinkedListKt.Node }");
       Throwable throwable = null;
       while (!ckf.b(lockFreeLink, p0)) {
          if (lockFreeLink instanceof ruf) {
             lockFreeLink.p(p1);
          }
          lockFreeLink = lockFreeLink.i();
       }
       if (throwable != null) {
          this.x0(throwable);
       }
       return;
    }
    private final Throwable V(Object p0){
       int i = (p0 == null)? 1: p0 instanceof Throwable;
       if (i) {
          if (p0 == null) {
             p0 = new JobCancellationException(JobSupport.u(this), null, this);
          }
       }else {
          ckf.e(p0, "null cannot be cast to non-null type kotlinx.coroutines.ParentJob");
          p0 = p0.C0();
       }
       return p0;
    }
    private final void V0(f5k p0,Throwable p1){
       p1 = p0.h();
       ckf.e(p1, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeLinkedListNode{ kotlinx.coroutines.internal.LockFreeLinkedListKt.Node }");
       if (ckf.b(p1, p0)) {
          return;
       }
       ckf.m(3, "T");
       throw null;
    }
    private final Object W0(Object p0,Object p1){
       if (!p1 instanceof fa4) {
          return p1;
       }
       throw p1.a;
    }
    public static JobCancellationException X(JobSupport p0,String p1,Throwable p2,int p3,Object p4){
       if (p4 != null) {
          throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: defaultCancellationException");
       }
       if ((p3 & 0x01)) {
          p1 = null;
       }
       if ((p3 & 0x02)) {
          p2 = null;
       }
       if (p1 == null) {
          p1 = JobSupport.u(p0);
       }
       return new JobCancellationException(p1, p2, p0);
    }
    private final void X0(k9p p0,Object p1){
       while (true) {
          p1 = this.v0();
          if (!p1 instanceof yse) {
             if (!p1 instanceof fa4) {
                p1 = suf.b(p1);
                break ;
             }
             break ;
          }else {
             if (this.h1(p1) >= 0) {
                p0.d(this.D0(new JobSupport$d(this, p0)));
                return;
             }
          }
       }
       p0.b(p1);
       return;
    }
    private final Object Y(JobSupport$c p0,Object p1){
       Throwable throwable;
       a07 uoa071;
       a07 uoa07 = null;
       fa4 uofa4 = (p1 instanceof fa4)? p1: uoa07;
       uofa4 = (uofa4 != null)? uofa4.a: uoa07;
       _monitor_enter(p0);
       boolean b = p0.e();
       List list = p0.h(uofa4);
       if ((throwable = this.k0(p0, list)) != null) {
          this.E(throwable, list);
       }
       _monitor_exit(p0);
       if (throwable != null && throwable != uofa4) {
          p1 = new fa4(throwable, false, 2, uoa07);
       }
       if (throwable != null && (this.Q(throwable) && !this.w0(throwable))) {
          ckf.e(p1, "null cannot be cast to non-null type kotlinx.coroutines.CompletedExceptionally");
          p1.b();
       }
       if (!b) {
          this.Y0(throwable);
       }
       this.Z0(p1);
       h30.a(JobSupport.l(), this, p0, suf.a(p1));
       this.T(p0, p1);
       return p1;
    }
    private final ir3 a0(yse p0){
       f5k list;
       ir3 oir3 = null;
       int i = (p0 instanceof ir3)? p0: oir3;
       if (i == null) {
          if ((list = p0.getList()) != null) {
             oir3 = this.S0(list);
          }
       }else {
          oir3 = i;
       }
       return oir3;
    }
    private final void c1(ic8 p0){
       f5k uof5k = new f5k();
       if (!p0.isActive()) {
          uof5k = new wse(uof5k);
       }
       h30.a(JobSupport.l(), this, p0, uof5k);
       return;
    }
    private final Object d(){
       return this.b;
    }
    private final void d1(ruf p0){
       p0.c(new f5k());
       h30.a(JobSupport.l(), this, p0, p0.i());
    }
    private final void e1(k9p p0,Object p1){
       if (!this.H0()) {
          p0.b(xhv.INSTANCE);
          return;
       }else {
          p0.d(this.D0(new JobSupport$e(this, p0)));
          return;
       }
    }
    private final int h1(Object p0){
       int i = 0;
       if (p0 instanceof ic8) {
          if (p0.isActive()) {
             return i;
          }
          if (!h30.a(JobSupport.l(), this, p0, suf.f)) {
             return -1;
          }
          this.a1();
          return 1;
       }else if(p0 instanceof wse){
          if (!h30.a(JobSupport.l(), this, p0, p0.getList())) {
             return -1;
          }
          this.a1();
          return 1;
       }else {
          return i;
       }
    }
    private static final AtomicReferenceFieldUpdater i(){
       return JobSupport.d;
    }
    private final String i1(Object p0){
       String str = "Active";
       if (p0 instanceof JobSupport$c) {
          if (p0.e()) {
             str = "Cancelling";
          }else if(p0.f()){
             str = "Completing";
          }
       }else if(p0 instanceof yse){
          if (!p0.isActive()) {
             str = "New";
          }
       }else if(p0 instanceof fa4){
          str = "Cancelled";
       }else {
          str = "Completed";
       }
       return str;
    }
    private final Throwable j0(Object p0){
       Throwable throwable = null;
       if (p0 instanceof fa4) {
       }else {
          p0 = throwable;
       }
       if (p0 != null) {
          throwable = p0.a;
       }
       return throwable;
    }
    private final Object k(){
       return this.a;
    }
    private final Throwable k0(JobSupport$c p0,List p1){
       Throwable throwable1;
       Throwable throwable2;
       Object obj1;
       Throwable throwable = null;
       if (p1.isEmpty()) {
          if (p0.e()) {
             return new JobCancellationException(JobSupport.u(this), throwable, this);
          }else {
             return throwable;
          }
       }else {
          Iterable iterable = p1;
          Iterator iterator = iterable.iterator();
          while (true) {
             if (iterator.hasNext()) {
                throwable1 = iterator.next();
                if (throwable1 instanceof CancellationException) {
                   continue ;
                }
             }else {
                throwable1 = throwable;
             }
             if (throwable1 != null) {
                return throwable1;
             }else {
                throwable2 = p1.get(0);
                if (throwable2 instanceof TimeoutCancellationException) {
                   Iterator iterator1 = iterable.iterator();
                   while (true) {
                      if (iterator1.hasNext()) {
                         Object obj = iterator1.next();
                         if ((obj1 = obj) != throwable2 && obj1 instanceof TimeoutCancellationException) {
                            throwable = obj;
                         }else {
                            continue ;
                         }
                      }
                      if (throwable != null) {
                         return throwable;
                      }
                   }
                }
                break ;
             }
          }
          return throwable2;
       }
    }
    public static CancellationException k1(JobSupport p0,Throwable p1,String p2,int p3,Object p4){
       if (p4 != null) {
          throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: toCancellationException");
       }
       if ((p3 & 0x01)) {
          p2 = null;
       }
       return p0.j1(p1, p2);
    }
    private static final AtomicReferenceFieldUpdater l(){
       return JobSupport.c;
    }
    private final void m(Object p0,AtomicReferenceFieldUpdater p1,g1a p2){
       while (true) {
          p2.invoke(p1.get(p0));
       }
    }
    private final boolean m1(yse p0,Object p1){
       if (!h30.a(JobSupport.l(), this, p0, suf.a(p1))) {
          return false;
       }
       this.Y0(null);
       this.Z0(p1);
       this.T(p0, p1);
       return true;
    }
    private final void n(Object p0){
       this.b = p0;
    }
    private final boolean n1(yse p0,Throwable p1){
       f5k uof5k;
       if ((uof5k = this.s0(p0)) == null) {
          return false;
       }
       if (!h30.a(JobSupport.l(), this, p0, new JobSupport$c(uof5k, false, p1))) {
          return false;
       }
       this.T0(uof5k, p1);
       return true;
    }
    private final void o(Object p0){
       this.a = p0;
    }
    public static void o0(){
    }
    private final Object o1(Object p0,Object p1){
       if (!p0 instanceof yse) {
          return suf.a;
       }
       if (!p0 instanceof ic8 && (!p0 instanceof ruf || (p0 instanceof ir3 || p1 instanceof fa4))) {
          return this.p1(p0, p1);
       }
       if (this.m1(p0, p1)) {
          return p1;
       }
       return suf.b;
    }
    private final Object p1(yse p0,Object p1){
       f5k uof5k;
       ir3 oir3;
       if ((uof5k = this.s0(p0)) == null) {
          return suf.b;
       }
       Throwable throwable = null;
       JobSupport$c uoc = (p0 instanceof JobSupport$c)? p0: throwable;
       if (uoc == null) {
          uoc = new JobSupport$c(uof5k, false, throwable);
       }
       Ref$ObjectRef objectRef = new Ref$ObjectRef();
       _monitor_enter(uoc);
       if (uoc.f()) {
          _monitor_exit(uoc);
          return suf.a;
       }else {
          uoc.i(true);
          if (uoc != p0 && !h30.a(JobSupport.l(), this, p0, uoc)) {
             _monitor_exit(uoc);
             return suf.b;
          }else {
             boolean b = uoc.e();
             fa4 uofa4 = (p1 instanceof fa4)? p1: throwable;
             if (uofa4 != null) {
                uoc.a(uofa4.a);
             }
             Throwable throwable1 = uoc.d();
             if (!b) {
                throwable = throwable1;
             }
             objectRef.element = throwable;
             _monitor_exit(uoc);
             if (throwable != null) {
                this.T0(uof5k, throwable);
             }
             if ((oir3 = this.a0(p0)) != null && this.q1(uoc, oir3, p1)) {
                return suf.COMPLETING_WAITING_CHILDREN;
             }else {
                return this.Y(uoc, p1);
             }
          }
       }
    }
    private final boolean q1(JobSupport$c p0,ir3 p1,Object p2){
       while (true) {
          if (m$a.e(p1.h, false, false, new JobSupport$b(this, p0, p1, p2), 1, null) != o5k.INSTANCE) {
             return true;
          }
          if ((p1 = this.S0(p1)) == null) {
             break ;
          }
       }
       return false;
    }
    public static void r0(){
    }
    private final f5k s0(yse p0){
       f5k list;
       if ((list = p0.getList()) == null) {
          if (p0 instanceof ic8) {
             list = new f5k();
          }else if(p0 instanceof ruf){
             this.d1(p0);
             list = null;
          }else {
             throw new IllegalStateException("State should have list: "+p0.toString());
          }
       }
       return list;
    }
    public static final Object t(JobSupport p0,ar4 p1){
       return p0.I(p1);
    }
    public static final String u(JobSupport p0){
       return p0.R();
    }
    public static final void v(JobSupport p0,JobSupport$c p1,ir3 p2,Object p3){
       p0.U(p1, p2, p3);
    }
    public static final Object x(JobSupport p0,ar4 p1){
       return p0.I0(p1);
    }
    public static final Object y(JobSupport p0,Object p1,Object p2){
       p0.W0(p1, p2);
       return p2;
    }
    public static final void z(JobSupport p0,k9p p1,Object p2){
       p0.X0(p1, p2);
    }
    private final boolean z0(yse p0){
       boolean b = (p0 instanceof JobSupport$c && p0.e())? true: false;
       return b;
    }
    public final boolean A0(){
       return (this.v0() instanceof yse ^ 0x01);
    }
    public final hr3 B0(jr3 p0){
       rr7 orr7 = m$a.e(this, true, false, new ir3(p0), 2, null);
       ckf.e(orr7, "null cannot be cast to non-null type kotlinx.coroutines.ChildHandle");
       return orr7;
    }
    public CancellationException C0(){
       Throwable throwable;
       Object obj = this.v0();
       CancellationException uCancellatio = null;
       if (obj instanceof JobSupport$c) {
          throwable = obj.d();
       }else if(obj instanceof fa4){
          throwable = obj.a;
       }else if(!obj instanceof yse){
          throwable = uCancellatio;
       }else {
          throw new IllegalStateException("Cannot be cancelling child in this state: "+obj.toString());
       }
       if (throwable instanceof CancellationException) {
          uCancellatio = throwable;
       }
       if (uCancellatio == null) {
          uCancellatio = new JobCancellationException("Parent job is ".concat(this.i1(obj)), throwable, this);
       }
       return uCancellatio;
    }
    public final rr7 D0(g1a p0){
       return this.F(false, true, p0);
    }
    public final rr7 F(boolean p0,boolean p1,g1a p2){
       f5k obj1;
       o5k iNSTANCE;
       ruf oruf = this.Q0(p2, p0);
       while (true) {
          Object obj = this.v0();
          if (obj instanceof ic8) {
             obj1 = obj;
             if (obj1.isActive()) {
                if (h30.a(JobSupport.l(), this, obj, oruf)) {
                   return oruf;
                }
                continue ;
             }else {
                this.c1(obj1);
             }
          }else {
             Throwable throwable = null;
             if (obj instanceof yse) {
                if ((obj1 = obj.getList()) == null) {
                   ckf.e(obj, "null cannot be cast to non-null type kotlinx.coroutines.JobNode");
                   this.d1(obj);
                }else {
                   iNSTANCE = o5k.INSTANCE;
                   if (p0 && obj instanceof JobSupport$c) {
                      _monitor_enter(obj);
                      if ((throwable = obj.d()) == null || (p2 instanceof ir3 || obj.f())) {
                         if (!this.D(obj, obj1, oruf)) {
                            _monitor_exit(obj);
                         }else if(throwable == null){
                            _monitor_exit(obj);
                            return oruf;
                         }else {
                            iNSTANCE = oruf;
                         }
                      }
                      _monitor_exit(obj);
                   }
                   if (throwable != null) {
                      if (p1) {
                         p2.invoke(throwable);
                         break ;
                      }
                      break ;
                   }else if(this.D(obj, obj1, oruf)){
                      return oruf;
                   }
                }
             }else if(p1){
                if (obj instanceof fa4) {
                }else {
                   obj = throwable;
                }
                if (obj != null) {
                   throwable = obj.a;
                }
                p2.invoke(throwable);
             }
             return o5k.INSTANCE;
          }
       }
       return iNSTANCE;
    }
    public final boolean F0(){
       return this.v0() instanceof fa4;
    }
    public void G(Object p0){
    }
    public boolean G0(){
       return this instanceof ox10;
    }
    public final Object H(ar4 p0){
       fa4 obj;
       while (true) {
          obj = this.v0();
          if (!obj instanceof yse) {
             if (!obj instanceof fa4) {
                return suf.b(obj);
             }
             obj = obj.a;
             if (!dv6.c()) {
                throw obj;
             }
             if (p0 instanceof vu4) {
                throw rgq.a(obj, p0);
             }
             break ;
          }else {
             if (this.h1(obj) >= 0) {
                return this.I(p0);
             }
             continue ;
          }
       }
       throw obj;
    }
    public void J(){
       m$a.a(this);
    }
    public boolean K(Throwable p0){
       CancellationException uCancellatio = (p0 != null)? JobSupport.k1(this, p0, null, 1, null): new JobCancellationException(JobSupport.u(this), null, this);
       this.O(uCancellatio);
       return 1;
    }
    public final boolean M(Throwable p0){
       return this.N(p0);
    }
    public final boolean N(Object p0){
       u1r ou1r;
       u1r a = suf.a;
       boolean b = true;
       if (this.p0()) {
          if ((ou1r = this.P(p0)) == suf.COMPLETING_WAITING_CHILDREN) {
             return b;
          }
       }else {
          ou1r = a;
       }
       if (ou1r == a) {
          ou1r = this.N0(p0);
       }
       if (ou1r != a && ou1r != suf.COMPLETING_WAITING_CHILDREN) {
          if (ou1r == suf.c) {
             b = false;
          }else {
             this.G(ou1r);
          }
       }
       return b;
    }
    public void O(Throwable p0){
       this.N(p0);
    }
    public final boolean O0(Object p0){
       Object obj;
       while (true) {
          if ((obj = this.o1(this.v0(), p0)) == suf.a) {
             return false;
          }
          if (obj == suf.COMPLETING_WAITING_CHILDREN) {
             return true;
          }
          if (obj != suf.b) {
             break ;
          }
       }
       this.G(obj);
       return true;
    }
    public final Object P0(Object p0){
       Object obj;
       while (true) {
          if ((obj = this.o1(this.v0(), p0)) == suf.a) {
             throw new IllegalStateException("Job "+this+" is already complete or completing, but is being completed with "+p0, this.j0(p0));
          }
          if (obj != suf.b) {
             break ;
          }
       }
       return obj;
    }
    public String R(){
       return "Job was cancelled";
    }
    public String R0(){
       return ov6.a(this);
    }
    public boolean S(Throwable p0){
       boolean b = true;
       if (p0 instanceof CancellationException) {
          return b;
       }
       if (!this.N(p0) || !this.l0()) {
          b = false;
       }
       return b;
    }
    public final JobCancellationException W(String p0,Throwable p1){
       if (p0 == null) {
          p0 = JobSupport.u(this);
       }
       return new JobCancellationException(p0, p1, this);
    }
    public void Y0(Throwable p0){
    }
    public void Z0(Object p0){
    }
    public void a(CancellationException p0){
       JobCancellationException jobCancellat;
       if (p0 == null) {
          jobCancellat = new JobCancellationException(JobSupport.u(this), null, this);
       }
       this.O(jobCancellat);
       return;
    }
    public void a1(){
    }
    public final sbp b0(){
       return wbp.b(new JobSupport$children$1(this, null));
    }
    public m b1(m p0){
       return p0;
    }
    public boolean complete(Object p0){
       return this.O0(p0);
    }
    public final Object d0(){
       Object obj = this.v0();
       if (obj instanceof yse) {
          throw new IllegalStateException("This job has not completed yet");
       }
       if (!obj instanceof fa4) {
          return suf.b(obj);
       }
       throw obj.a;
    }
    public final Throwable e0(){
       Throwable obj = this.v0();
       if (obj instanceof JobSupport$c) {
          if ((obj = obj.d()) == null) {
             throw new IllegalStateException("Job is still new or active: "+this.toString());
          }
       }else if(!obj instanceof yse){
          fa4 a = (obj instanceof fa4)? obj.a: null;
       }else {
          throw new IllegalStateException("Job is still new or active: "+this.toString());
       }
       return obj;
    }
    public final Object f0(ar4 p0){
       if (!this.H0()) {
          quf.f(p0.getContext());
          return xhv.INSTANCE;
       }else if((p0 = this.I0(p0)) == dkf.d()){
          return p0;
       }else {
          return xhv.INSTANCE;
       }
    }
    public final void f1(ruf p0){
       while (true) {
          Object obj = this.v0();
          if (obj instanceof ruf) {
             if (obj != p0) {
                return;
             }else {
                if (h30.a(JobSupport.l(), this, obj, suf.f)) {
                   return;
                }
                continue ;
             }
          }else if(obj instanceof yse && obj.getList() != null){
             p0.l();
             break ;
          }
          break ;
       }
       return;
    }
    public Object fold(Object p0,u1a p1){
       return m$a.c(this, p0, p1);
    }
    public Object g(){
       return this.d0();
    }
    public final boolean g0(){
       Object obj = this.v0();
       boolean b = (obj instanceof fa4 && obj.a())? true: false;
       return b;
    }
    public final void g1(hr3 p0){
       JobSupport.i().set(this, p0);
    }
    public d$b get(d$c p0){
       return m$a.d(this, p0);
    }
    public final d$c getKey(){
       return m.Key;
    }
    public m getParent(){
       hr3 ohr3;
       m parent = ((ohr3 = this.t0()) != null)? ohr3.getParent(): null;
       return parent;
    }
    public final Throwable h0(){
       Object obj = this.v0();
       if (!obj instanceof yse) {
          return this.j0(obj);
       }
       throw new IllegalStateException("This job has not completed yet");
    }
    public boolean isActive(){
       Object obj = this.v0();
       boolean b = (obj instanceof yse && obj.isActive())? true: false;
       return b;
    }
    public final boolean isCancelled(){
       Object obj = this.v0();
       boolean b = (!obj instanceof fa4 && (!obj instanceof JobSupport$c && obj.e()))? false: true;
       return b;
    }
    public final CancellationException j1(Throwable p0,String p1){
       CancellationException uCancellatio = (p0 instanceof CancellationException)? p0: null;
       if (!uCancellatio) {
          if (p1 == null) {
             p1 = JobSupport.u(this);
          }
          uCancellatio = new JobCancellationException(p1, p0, this);
       }
       return uCancellatio;
    }
    public boolean l0(){
       return true;
    }
    public final String l1(){
       return this.R0()+'{'+this.i1(this.v0())+'}';
    }
    public final f9p m0(){
       JobSupport$onAwaitInternal$1 iNSTANCE = JobSupport$onAwaitInternal$1.INSTANCE;
       ckf.e(iNSTANCE, "null cannot be cast to non-null type kotlin.Function3<@[ParameterName\(name = \'clauseObject\'\)] kotlin.Any, @[ParameterName\(name = \'select\'\)] kotlinx.coroutines.selects.SelectInstance<*>, @[ParameterName\(name = \'param\'\)] kotlin.Any?, kotlin.Unit>{ kotlinx.coroutines.selects.SelectKt.RegistrationFunction }");
       kqu.e(iNSTANCE, 3);
       JobSupport$onAwaitInternal$2 iNSTANCE1 = JobSupport$onAwaitInternal$2.INSTANCE;
       ckf.e(iNSTANCE1, "null cannot be cast to non-null type kotlin.Function3<@[ParameterName\(name = \'clauseObject\'\)] kotlin.Any, @[ParameterName\(name = \'param\'\)] kotlin.Any?, @[ParameterName\(name = \'clauseResult\'\)] kotlin.Any?, kotlin.Any?>{ kotlinx.coroutines.selects.SelectKt.ProcessResultFunction }");
       kqu.e(iNSTANCE1, 3);
       g9p v7 = new g9p(this, iNSTANCE, iNSTANCE1, null, 8, null);
       return v7;
    }
    public d minusKey(d$c p0){
       return m$a.f(this, p0);
    }
    public final void n0(vql p0){
       this.N(p0);
    }
    public boolean p0(){
       return this instanceof w820;
    }
    public d plus(d p0){
       return m$a.g(this, p0);
    }
    public final d9p q0(){
       JobSupport$onJoin$1 iNSTANCE = JobSupport$onJoin$1.INSTANCE;
       ckf.e(iNSTANCE, "null cannot be cast to non-null type kotlin.Function3<@[ParameterName\(name = \'clauseObject\'\)] kotlin.Any, @[ParameterName\(name = \'select\'\)] kotlinx.coroutines.selects.SelectInstance<*>, @[ParameterName\(name = \'param\'\)] kotlin.Any?, kotlin.Unit>{ kotlinx.coroutines.selects.SelectKt.RegistrationFunction }");
       kqu.e(iNSTANCE, 3);
       JobSupport$onJoin$1 oonJoin$1 = iNSTANCE;
       e9p v6 = new e9p(this, oonJoin$1, null, 4, null);
       return v6;
    }
    public final boolean start(){
       int i;
       while (true) {
          if (!(i = this.h1(this.v0()))) {
             return false;
          }
          if (i != 1) {
             continue ;
          }else {
             break ;
          }
       }
       return true;
    }
    public final hr3 t0(){
       return JobSupport.i().get(this);
    }
    public String toString(){
       return this.l1()+'@'+ov6.b(this);
    }
    public final CancellationException u0(){
       CancellationException uCancellatio;
       Throwable obj = this.v0();
       String str = "Job is still new or active: ";
       if (obj instanceof JobSupport$c) {
          if ((obj = obj.d()) != null) {
             uCancellatio = this.j1(obj, ov6.a(this).concat(" is cancelling"));
          }else {
             throw new IllegalStateException(str+this.toString());
          }
       }else if(!obj instanceof yse){
          uCancellatio = (obj instanceof fa4)? JobSupport.k1(this, obj.a, null, 1, null): new JobCancellationException(ov6.a(this).concat(" has completed normally"), null, this);
       }else {
          throw new IllegalStateException(str+this.toString());
       }
       return uCancellatio;
    }
    public final Object v0(){
       Object obj;
       AtomicReferenceFieldUpdater uAtomicRefer = JobSupport.l();
       while (true) {
          obj = uAtomicRefer.get(this);
          if (!obj instanceof rxk) {
             break ;
          }else {
             obj.a(this);
          }
       }
       return obj;
    }
    public boolean w0(Throwable p0){
       return false;
    }
    public void x0(Throwable p0){
       throw p0;
    }
    public final void y0(m p0){
       if (p0 == null) {
          this.g1(o5k.INSTANCE);
          return;
       }else {
          p0.start();
          hr3 ohr3 = p0.B0(this);
          this.g1(ohr3);
          if (this.A0()) {
             ohr3.dispose();
             this.g1(o5k.INSTANCE);
          }
          return;
       }
    }
}
