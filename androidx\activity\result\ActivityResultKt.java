package androidx.activity.result.ActivityResultKt;
import androidx.activity.result.ActivityResult;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.content.Intent;

public final class ActivityResultKt	// class@0004b1 from classes.dex
{

    public static final int component1(ActivityResult p0){
       ckf.g(p0, "<this>");
       return p0.getResultCode();
    }
    public static final Intent component2(ActivityResult p0){
       ckf.g(p0, "<this>");
       return p0.getData();
    }
}
