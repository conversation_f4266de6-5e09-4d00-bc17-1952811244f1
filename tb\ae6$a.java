package tb.ae6$a;
import tb.qub;
import tb.t2o;
import java.lang.Object;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ae6;

public class ae6$a implements qub	// class@001b29 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x32900227);
       t2o.a(0x1c500477);
    }
    public void ae6$a(){
       super();
    }
    public DXWidgetNode build(Object p0){
       IpChange $ipChange = ae6$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new ae6();
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("966917b0", objArray);
    }
}
