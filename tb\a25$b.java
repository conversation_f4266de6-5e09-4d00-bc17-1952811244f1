package tb.a25$b;
import java.util.concurrent.ThreadFactory;
import java.lang.String;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicInteger;
import java.lang.Runnable;
import java.lang.Thread;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.a25$b$a;

public class a25$b implements ThreadFactory	// class@001841 from classes7.dex
{
    public final AtomicInteger a;
    public final int b;
    public final String c;
    public static IpChange $ipChange;

    public void a25$b(int p0,String p1){
       super();
       this.a = new AtomicInteger();
       this.b = p0;
       this.c = p1;
    }
    public Thread newThread(Runnable p0){
       IpChange $ipChange = a25$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d8079a58", objArray);
       }else {
          StringBuilder str = new StringBuilder(32)+"DownloadSDK ";
          str = (!TextUtils.isEmpty(this.c))? str+this.c+" ": str+"DefaultPool ";
          return new a25$b$a(this, p0, str+"Thread:"+this.a.getAndIncrement());
       }
    }
}
