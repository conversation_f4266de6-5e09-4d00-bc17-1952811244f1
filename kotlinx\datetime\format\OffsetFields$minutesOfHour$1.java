package kotlinx.datetime.format.OffsetFields$minutesOfHour$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.r150;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class OffsetFields$minutesOfHour$1 extends MutablePropertyReference1Impl	// class@0006e8 from classes11.dex
{
    public static final OffsetFields$minutesOfHour$1 INSTANCE;

    static {
       OffsetFields$minutesOfHour$1.INSTANCE = new OffsetFields$minutesOfHour$1();
    }
    public void OffsetFields$minutesOfHour$1(){
       super(r150.class, "minutesOfHour", "getMinutesOfHour\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.A();
    }
    public void set(Object p0,Object p1){
       p0.s(p1);
    }
}
