package kotlinx.coroutines.channels.BroadcastChannelImpl$registerSelectForSend$2;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.BroadcastChannelImpl;
import java.lang.Object;
import tb.k9p;
import tb.ar4;
import tb.uu4;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import kotlinx.coroutines.channels.ClosedSendChannelException;
import java.lang.Throwable;
import kotlinx.coroutines.channels.BufferedChannel;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.locks.Lock;
import tb.dv6;
import java.util.HashMap;
import tb.u1r;
import kotlinx.coroutines.channels.BufferedChannelKt;
import java.util.Map;
import tb.ckf;
import kotlinx.coroutines.selects.SelectImplementation;
import kotlinx.coroutines.selects.TrySelectDetailedResult;

public final class BroadcastChannelImpl$registerSelectForSend$2 extends SuspendLambda implements u1a	// class@0004b9 from classes11.dex
{
    public final Object $element;
    public final k9p $select;
    public int label;
    public final BroadcastChannelImpl this$0;

    public void BroadcastChannelImpl$registerSelectForSend$2(BroadcastChannelImpl p0,Object p1,k9p p2,ar4 p3){
       this.this$0 = p0;
       this.$element = p1;
       this.$select = p2;
       super(2, p3);
    }
    public final ar4 create(Object p0,ar4 p1){
       return new BroadcastChannelImpl$registerSelectForSend$2(this.this$0, this.$element, this.$select, p1);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(uu4 p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       BroadcastChannelImpl$registerSelectForSend$2 obj = dkf.d();
       BroadcastChannelImpl$registerSelectForSend$2 tlabel = this.label;
       int i = 1;
       if (tlabel != null) {
          if (tlabel == i) {
             b.b(p0);
          }else {
             throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
          }
       }else {
          b.b(p0);
          this.label = i;
          if (this.this$0.d(this.$element, this) == obj) {
             return obj;
          }
       }
       p0 = BroadcastChannelImpl.L1(this.this$0);
       obj = this.this$0;
       tlabel = this.$select;
       p0.lock();
       HashMap hashMap = BroadcastChannelImpl.M1(obj);
       xhv iNSTANCE = (i)? xhv.INSTANCE: BufferedChannelKt.z();
       hashMap.put(tlabel, iNSTANCE);
       ckf.e(tlabel, "null cannot be cast to non-null type kotlinx.coroutines.selects.SelectImplementation<*>");
       xhv iNSTANCE1 = xhv.INSTANCE;
       if (tlabel.y(obj, iNSTANCE1) != TrySelectDetailedResult.REREGISTER) {
          BroadcastChannelImpl.M1(obj).remove(tlabel);
       }
       p0.unlock();
       return iNSTANCE1;
    }
}
