package tb.a9l;
import tb.t2o;
import java.lang.Object;
import java.util.ArrayList;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a9l$a;
import java.lang.Boolean;
import tb.nim;
import android.content.Context;
import com.taobao.android.ab.api.ABGlobal;
import java.lang.Number;
import com.taobao.tbpoplayer.track.model.TrackConfig;
import com.taobao.tbpoplayer.track.model.TrackUTConfig;
import java.util.List;
import com.taobao.tbpoplayer.track.model.TrackAppMonitorConfig;
import android.content.SharedPreferences$Editor;
import tb.wdm;
import java.util.Collection;
import tb.lig;
import java.lang.Class;
import com.alibaba.fastjson.JSON;
import java.util.Map;
import tb.d0j;
import java.lang.StringBuilder;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Throwable;
import com.taobao.orange.OrangeConfig;
import java.lang.Long;
import com.alibaba.poplayer.trigger.BaseConfigItem;
import tb.auv;

public class a9l	// class@001eab from classes10.dex
{
    public List a;
    public List b;
    public List c;
    public TrackConfig d;
    public final AtomicBoolean e;
    public boolean f;
    public final AtomicBoolean g;
    public boolean h;
    public static IpChange $ipChange;
    public static final String SP_KEY_AUTO_SIZE_ASPECT;
    public static final String SP_KEY_CDN_FETCH_CDN_URL;
    public static final String SP_KEY_CDN_FETCH_PAGE_SWITCH_TIMES;
    public static final String SP_KEY_CONFIG_BY_CON;
    public static final String SP_KEY_CONFIG_LOCAL_OPT;
    public static final String SP_KEY_DAI_TRIGGER_ENABLE;
    public static final String SP_KEY_ENABLE_CLEAN_KEEP_DIRECTLY;
    public static final String SP_KEY_ENABLE_CONTEXT_WRAPPER;
    public static final String SP_KEY_ENABLE_LAUNCH_OPT;
    public static final String SP_KEY_ENABLE_LOCAL_CONFIG;
    public static final String SP_KEY_ENABLE_LOCAL_LM_CONFIG;
    public static final String SP_KEY_ENABLE_PRE_DEAL_GUARANTEE;
    public static final String SP_KEY_ENABLE_REMOVE_CONFIG_UPDATE_NOTIFY;
    public static final String SP_KEY_ENABLE_UCP_SYNC;
    public static final String SP_KEY_ENABLE_UCP_TRIGGER;
    public static final String SP_KEY_FATIGUE_FILTER_ENABLE;
    public static final String SP_KEY_FLASH_POP_ENABLE;
    public static final String SP_KEY_FLOW_INTERVENTION;
    public static final String SP_KEY_FORBID_H5_ACC;
    public static final String SP_KEY_INTERCEPT_HOVER;
    public static final String SP_KEY_INTERRUPT_DOWN_ACTION;
    public static final String SP_KEY_INVALID_ACTIVITY_CONFIG;
    public static final String SP_KEY_LM_CONFIG_ORANGE_VERSION;
    public static final String SP_KEY_MTOP_GROUP_ENABLE;
    public static final String SP_KEY_NATIVE_POP_ENABLE;
    public static final String SP_KEY_NATIVE_POP_GRADUAL_ENABLE;
    public static final String SP_KEY_NATIVE_POP_PROP_REPLACE_BF;
    public static final String SP_KEY_NATIVE_POP_RE_RENDER_ON_SCREEN_CHANGE;
    public static final String SP_KEY_NEW_NATIVE_NOTIFICATION_ENABLE;
    public static final String SP_KEY_OPT_GROUP_CHECK;
    public static final String SP_KEY_OPT_H5_SNAPSHOT;
    public static final String SP_KEY_OPT_NATIVE_SNAPSHOT;
    public static final String SP_KEY_OPT_THREAD;
    public static final String SP_KEY_OPT_WEBVIEW_REMOVE;
    public static final String SP_KEY_OPT_WEEX2_SNAPSHOT;
    public static final String SP_KEY_OPT_WEEX_SNAPSHOT;
    public static final String SP_KEY_PAGE_EVENT_ENABLE;
    public static final String SP_KEY_POP_CENTER_OUT_OF_TIME_HIGH;
    public static final String SP_KEY_POP_CENTER_OUT_OF_TIME_LOW;
    public static final String SP_KEY_POP_CENTER_OUT_OF_TIME_MIDDLE;
    public static final String SP_KEY_POP_DISPLAY_ENABLE;
    public static final String SP_KEY_POP_HUB_CODE_BLACK_LIST;
    public static final String SP_KEY_POP_HUB_CODE_WHITE_LIST;
    public static final String SP_KEY_POP_HUB_TRIGGER_MODE;
    public static final String SP_KEY_PRE_DEAL_TRIGGER_ENABLE;
    public static final String SP_KEY_RECORD_BUCKET_ENABLE;
    public static final String SP_KEY_REQUESTING_FILTER_ENABLE;
    public static final String SP_KEY_SCENE_FREQ;
    public static final String SP_KEY_SCREENSHOT_TLOG_ENABLE;
    public static final String SP_KEY_SEND_SCREEN_CHANGE_EVENT;
    public static final String SP_KEY_USE_NEW_ACTION_LINE;
    public static final String SP_KEY_USE_NEW_MAMA_TYPE;
    public static final String SP_KEY_WATER_MASK_BLACK_LIST_ENABLE;
    public static final String SP_KEY_WATER_MASK_CONFIG_INVALID_REQUEST_GAP;
    public static final String SP_KEY_WATER_MASK_DELAY_TIME;
    public static final String SP_KEY_WATER_MASK_ENABLE;
    public static final String SP_KEY_WATER_MASK_REQ_GAP;
    public static final String SP_KEY_WEEX2_SNAPSHOT_SCALE;
    public static final String SP_KEY_WEEX_INIT_WAIT_TIME;
    public static final String SP_KEY_WEEX_INIT_WAIT_TIME_ENABLE;
    public static final String SP_KEY_WEEX_MODULE_INTERCEPT_ENABLE;

    static {
       t2o.a(0x31a0003d);
    }
    public void a9l(){
       super();
       this.a = new ArrayList();
       this.b = new CopyOnWriteArrayList();
       this.c = new CopyOnWriteArrayList();
       this.e = new AtomicBoolean(false);
       this.f = false;
       this.g = new AtomicBoolean(false);
       this.h = true;
    }
    public static a9l w(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a9l$a.a();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("6eda4b5f", objArray);
    }
    public boolean A(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("ENABLE_CONTEXT_WRAPPER", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("e9f1609c", objArray).booleanValue();
    }
    public boolean B(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("FORBID_H5_ACC", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("7698e2be", objArray).booleanValue();
    }
    public boolean C(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("FATIGUE_FILTER_ENABLE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("f303f683", objArray).booleanValue();
    }
    public boolean D(){
       int i = 0;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("INTERCEPT_HOVER", i);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("47bc9bef", objArray).booleanValue();
    }
    public boolean E(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("ENABLE_LOCAL_CONFIG", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("211dd3f1", objArray).booleanValue();
    }
    public boolean F(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("ENABLE_LOCAL_LM_CONFIG", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("d943a732", objArray).booleanValue();
    }
    public boolean G(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.d("MTOP_GROUP_ENABLE");
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4b8717fb", objArray).booleanValue();
    }
    public boolean H(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("NEW_NATIVE_NOTIFICATION_ENABLE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("19677b06", objArray).booleanValue();
    }
    public boolean I(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("optWebviewRemove", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("130445bf", objArray).booleanValue();
    }
    public boolean J(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("PAGE_EVENT_ENABLE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("d6de53f2", objArray).booleanValue();
    }
    public boolean K(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("PRE_DEAL_TRIGGER_ENABLE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("72ea49c1", objArray).booleanValue();
    }
    public boolean L(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("RECORD_BUCKET_ENABLE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("e262ac8e", objArray).booleanValue();
    }
    public boolean M(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("ENABLE_REMOVE_CONFIG_UPDATE_NOTIFY", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("3c2b6f0", objArray).booleanValue();
    }
    public boolean N(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("REQUESTING_FILTER_ENABLE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("843b7003", objArray).booleanValue();
    }
    public boolean O(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("watermarkReportTLog", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("40f8cfa2", objArray).booleanValue();
    }
    public boolean P(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("USE_NEW_ACTION_LINE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("16d9e618", objArray).booleanValue();
    }
    public boolean Q(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("61320a52", objArray).booleanValue();
       }else if(this.g.compareAndSet(i, i1)){
          this.h = nim.e("watermarkBlackModeEnable", i1);
       }
       return this.h;
    }
    public boolean R(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("waterMaskEnable", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("f2f8007", objArray).booleanValue();
    }
    public boolean S(Context p0){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("3716ee0b", objArray).booleanValue();
       }else if(this.e.compareAndSet(0, i)){
          this.f = ABGlobal.isFeatureOpened(p0, "watermark_ahead_launch");
       }
       return this.f;
    }
    public boolean T(Context p0){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ABGlobal.isFeatureOpened(p0, "POPCallWeexInitOpt");
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("e2de2245", objArray).booleanValue();
    }
    public boolean U(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.d("WEEX_MODULE_INTERCEPT_ENABLE");
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2bc072cc", objArray).booleanValue();
    }
    public boolean V(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("OPT_H5_SNAPSHOT", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("1a98b34f", objArray).booleanValue();
    }
    public long W(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("watermarkDelayTime", 2000);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("b5f5f2c9", objArray).longValue();
    }
    public boolean X(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("OPT_NATIVE_SNAPSHOT", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("8c5b5619", objArray).booleanValue();
    }
    public boolean Y(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("OPT_WEEX2_SNAPSHOT", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("9f6a08cd", objArray).booleanValue();
    }
    public boolean Z(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("OPT_WEEX_SNAPSHOT", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("91edffc3", objArray).booleanValue();
    }
    public boolean a(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("CONFIG_BY_CON", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("738f98bf", objArray).booleanValue();
    }
    public boolean a0(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("NATIVE_POP_RE_RENDER_ON_SCREEN_CHANGE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("a69e36b7", objArray).booleanValue();
    }
    public boolean b(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("CONFIG_LOCAL_OPT", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("bcb84035", objArray).booleanValue();
    }
    public boolean b0(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("SEND_SCREEN_CHANGE_EVENT", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("7ee7e3a1", objArray).booleanValue();
    }
    public boolean c(){
       a9l td;
       TrackConfig appMonitor;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("5ce749dd", objArray).booleanValue();
       }else if((td = this.d) != null && ((appMonitor = td.AppMonitor) != null && appMonitor.enable != null)){
          i = true;
       }
       return i;
    }
    public void c0(List p0,boolean p1){
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("1c82768e", objArray);
          return;
       }else {
          this.c = p0;
          if (p1) {
             nim.r("POP_HUB_CODE_BLACK_LIST", p0);
          }
          return;
       }
    }
    public boolean d(String p0){
       a9l td;
       TrackConfig appMonitor;
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("df725b59", objArray).booleanValue();
       }else if((td = this.d) != null && ((appMonitor = td.AppMonitor) != null && appMonitor.getCategoryHit(p0, 0))){
          i = false;
       }
       return i;
    }
    public void d0(List p0,boolean p1){
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("cf3f5da4", objArray);
          return;
       }else {
          this.b = p0;
          if (p1) {
             nim.r("POP_HUB_CODE_WHITE_LIST", p0);
          }
          return;
       }
    }
    public String e(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.m("autoSizeAspect");
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("772ffdfb", objArray);
    }
    public void e0(List p0,boolean p1){
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("c569b2ba", objArray);
          return;
       }else {
          this.a = p0;
          if (p1) {
             nim.r("INVALID_ACTIVITY_CONFIG", p0);
          }
          return;
       }
    }
    public long f(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("CDN_FETCH_PAGE_SWITCH_TIMES", 5);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("81a0205b", objArray).longValue();
    }
    public SharedPreferences$Editor f0(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.s();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4a0c2a5f", objArray);
    }
    public String g(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.m("CDN_FETCH_CDN_URL");
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c232b834", objArray);
    }
    public void g0(String p0,boolean p1){
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("457d4e02", objArray);
          return;
       }else {
          nim.t(p0, p1);
          return;
       }
    }
    public List h(){
       Object[] objArray;
       a9l tc;
       List list;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("a48cae86", objArray);
       }else if((tc = this.c) != null && !tc.isEmpty()){
          objArray = new Object[i1];
          objArray[i] = this.c;
          wdm.d("OrangeConfigManager.getCodeBlackList.FromOrange.list=%s.", objArray);
          return this.c;
       }else {
          list = nim.n("POP_HUB_CODE_BLACK_LIST");
          objArray = new Object[i1];
          objArray[i] = list;
          wdm.d("OrangeConfigManager.getCodeBlackList.FromLocalSp.list=%s.", objArray);
          return new CopyOnWriteArrayList(list);
       }
    }
    public void h0(){
       a9l td;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("e4fa0472", objArray);
          return;
       }else {
          TrackConfig trackConfig = JSON.parseObject(lig.g().e("appMonitorConfig"), TrackConfig.class);
          this.d = trackConfig;
          if (trackConfig != null && (trackConfig = trackConfig.UserTrack) != null) {
             trackConfig.generateHitMap();
          }
          if ((td = this.d) != null && (trackConfig = td.AppMonitor) != null) {
             trackConfig.generateHitMap();
             d0j.d(this.d.AppMonitor.useConfigCheckFail);
             d0j.e(this.d.AppMonitor.onePopOnlyResult);
          }
          return;
       }
    }
    public List i(){
       Object[] objArray;
       a9l tb;
       List list;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("6e64631c", objArray);
       }else if((tb = this.b) != null && !tb.isEmpty()){
          objArray = new Object[i1];
          objArray[i] = this.b;
          wdm.d("OrangeConfigManager.getCodeWhiteList.FromOrange.list=%s.", objArray);
          return this.b;
       }else {
          list = nim.n("POP_HUB_CODE_WHITE_LIST");
          objArray = new Object[i1];
          objArray[i] = list;
          wdm.d("OrangeConfigManager.getCodeWhiteList.FromLocalSp.list=%s.", objArray);
          return new CopyOnWriteArrayList(list);
       }
    }
    public boolean i0(String p0,String p1,String p2,String p3){
       int b;
       String str = "PopConfigLocalManager.updateLocalConfigs.orangeVersionSame=";
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          return $ipChange.ipc$dispatch("17990663", objArray).booleanValue();
       }else {
          String str1 = nim.m("LM_CONFIG_ORANGE_VERSION");
          wdm.g("sdkLifeCycle", "", "PopConfigLocalManager.updateLocalConfigs.orangeVersion="+p3+".localOrangeVersion="+str1);
          if (!TextUtils.isEmpty(str1) && !TextUtils.isEmpty(p3)) {
             b = p3.equals(str1);
             wdm.g("sdkLifeCycle", "", str+b);
             if (b) {
                return 0;
             }
          }
          b = OrangeConfig.getInstance().getConfig(p0, p1, "");
          nim.v(p1, b);
          nim.v(p2, OrangeConfig.getInstance().getConfig(p0, p2, ""));
          String[] stringArray = b.split(",");
          int len = stringArray.length;
          for (b = 0; b < len; b = b + 1) {
             object oobject = stringArray[b];
             nim.v(oobject, OrangeConfig.getInstance().getConfig(p0, oobject, ""));
          }
          Object[] objArray1 = new Object[0];
          wdm.d("OrangeConfigManager.updateLMLocalConfigs.done", objArray1);
          return 1;
       }
    }
    public List j(){
       Object[] objArray;
       a9l ta;
       List list;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("1a4a6bb2", objArray);
       }else if((ta = this.a) != null && !ta.isEmpty()){
          objArray = new Object[i1];
          objArray[i] = this.a;
          wdm.d("OrangeConfigManager.getInValidActivities.FromOrange.list=%s.", objArray);
          return this.a;
       }else {
          list = nim.n("INVALID_ACTIVITY_CONFIG");
          objArray = new Object[i1];
          objArray[i] = list;
          wdm.d("OrangeConfigManager.getInValidActivities.FromLocalSp.list=%s.", objArray);
          return new CopyOnWriteArrayList(list);
       }
    }
    public void j0(String p0,long p1){
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("c6424fec", objArray);
          return;
       }else {
          nim.u(p0, p1);
          return;
       }
    }
    public String k(String p0){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.m(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("d59947cd", objArray);
    }
    public void k0(String p0,String p1){
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("63815c37", objArray);
          return;
       }else {
          nim.v(p0, p1);
          return;
       }
    }
    public long l(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("POP_CENTER_OUT_OF_TIME_HIGH", 500);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("72b77bff", objArray).longValue();
    }
    public boolean l0(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("USE_NEW_MAMA_TYPE", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("358c8a46", objArray).booleanValue();
    }
    public long m(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("POP_CENTER_OUT_OF_TIME_LOW", 1000);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("13dcbead", objArray).longValue();
    }
    public boolean m0(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          i = $ipChange.ipc$dispatch("88ec5e22", objArray).booleanValue();
       }
       return i;
    }
    public long n(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("POP_CENTER_OUT_OF_TIME_MIDDLE", 800);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ca2515f2", objArray).longValue();
    }
    public boolean n0(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("preDealGuarantee", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("695203d1", objArray).booleanValue();
    }
    public int o(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (int)nim.h("POP_HUB_TRIGGER_MODE", 0);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a4175f3b", objArray).intValue();
    }
    public boolean o0(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("ucpSync", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("4fc455c1", objArray).booleanValue();
    }
    public String p(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.m("sceneFreq");
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("74350da9", objArray);
    }
    public boolean p0(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("ucpTrigger", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("daa41d2e", objArray).booleanValue();
    }
    public boolean q(String p0,BaseConfigItem p1,boolean p2){
       a9l td;
       TrackConfig userTrack;
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Boolean(p2)};
          return $ipChange.ipc$dispatch("3b234189", objArray).booleanValue();
       }else if(p1 != null && (p1.forceUpdateUT != null && (p0.equals("webJSBridge") && (!p0.equals("weexJSBridge") && (!p0.equals("pageLifeCycle") && (!p0.equals("containerLifeCycle") && !auv.a().b(p0))))))){
          return i;
       }else if((td = this.d) != null && ((userTrack = td.UserTrack) != null && userTrack.getCategoryHit(p0, p2))){
          i = false;
       }
       return i;
    }
    public boolean r(String p0,BaseConfigItem p1){
       a9l td;
       TrackConfig userTrack;
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("95fb8191", objArray).booleanValue();
       }else if(p1 != null && (p1.forceUpdateUT != null && (p0.equals("webJSBridge") && (!p0.equals("weexJSBridge") && (!p0.equals("pageLifeCycle") && (!p0.equals("containerLifeCycle") && !auv.a().b(p0))))))){
          return i;
       }else if((td = this.d) != null && ((userTrack = td.UserTrack) != null && userTrack.enable != null)){
          i = false;
       }
       return i;
    }
    public long s(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("watermarkConfigInvalidRequestGap", 0x36ee80);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4e84c84a", objArray).longValue();
    }
    public long t(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("waterMaskRequestGap", 0x5265c00);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dbb087d5", objArray).longValue();
    }
    public long u(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("WEEX2_SNAPSHOT_SCALE", 25);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("975b4206", objArray).longValue();
    }
    public long v(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.h("WEEX_INIT_WAIT_TIME", 4000);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("72c27924", objArray).longValue();
    }
    public boolean x(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("INTERRUPT_DOWN_ACTION", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("aca35c92", objArray).booleanValue();
    }
    public boolean y(){
       int i = 1;
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.e("ENABLE_CLEAN_KEEP_DIRECTLY", i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("5129cae8", objArray).booleanValue();
    }
    public boolean z(){
       IpChange $ipChange = a9l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return nim.d("DAI_TRIGGER_ENABLE");
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4f620fb4", objArray).booleanValue();
    }
}
