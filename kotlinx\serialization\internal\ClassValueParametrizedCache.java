package kotlinx.serialization.internal.ClassValueParametrizedCache;
import tb.bv30;
import tb.u1a;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.h520;
import tb.wyf;
import java.util.List;
import java.lang.Class;
import tb.gyf;
import tb.g520;
import tb.xl30;
import java.lang.ref.SoftReference;
import kotlinx.serialization.internal.ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1;
import tb.d1a;
import tb.av30;
import java.lang.Iterable;
import java.util.ArrayList;
import tb.zz3;
import java.util.Iterator;
import tb.e1g;
import tb.z530;
import java.util.concurrent.ConcurrentHashMap;
import tb.x530;
import kotlin.Result;
import java.lang.Throwable;
import kotlin.b;

public final class ClassValueParametrizedCache implements bv30	// class@000739 from classes11.dex
{
    public final u1a a;
    public final h520 b;

    public void ClassValueParametrizedCache(u1a p0){
       ckf.g(p0, "compute");
       super();
       this.a = p0;
       this.b = new h520();
    }
    public Object a(wyf p0,List p1){
       av30 uoav30;
       Result result;
       ckf.g(p0, "key");
       ckf.g(p1, "types");
       Iterable obj = g520.a(this.b, gyf.b(p0));
       ckf.f(obj, "get\(...\)");
       if ((uoav30 = obj.a.get()) == null) {
          uoav30 = obj.a(new ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1());
       }
       obj = p1;
       ArrayList uArrayList = new ArrayList(zz3.q(obj, 10));
       Iterator iterator = obj.iterator();
       while (iterator.hasNext()) {
          uArrayList.add(new z530(iterator.next()));
       }
       ConcurrentHashMap uConcurrentH = av30.a(uoav30);
       if ((result = uConcurrentH.get(uArrayList)) == null) {
          p0 = Result.constructor-impl(this.a.invoke(p0, p1));
          Result result1 = Result.box-impl(p0);
          result = ((p1 = uConcurrentH.putIfAbsent(uArrayList, result1)) == null)? result1: p1;
       }
       ckf.f(result, "getOrPut\(...\)");
       return result.unbox-impl();
    }
}
