package tb.a6p;
import tb.t2o;
import java.lang.String;
import java.lang.Class;
import java.lang.Object;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.taobao.R$string;
import com.alibaba.ability.localization.Localization;
import java.lang.CharSequence;
import android.widget.Toast;
import com.taobao.tao.util.Constants;
import java.lang.Integer;
import android.app.Application;
import com.taobao.tao.Globals;
import com.taobao.uikit.extend.component.unify.Toast.TBToast;
import android.content.res.Resources;
import com.taobao.taobao.R$color;
import android.util.Log;
import android.text.TextUtils;

public class a6p	// class@001b05 from classes8.dex
{
    public static IpChange $ipChange;
    public static final String a;

    static {
       t2o.a(0x3320007c);
       a6p.a = a6p.class.getSimpleName();
    }
    public void a6p(){
       super();
    }
    public static void a(Context p0){
       IpChange $ipChange = a6p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("c33f534a", objArray);
          return;
       }else {
          Toast toast = Toast.makeText(p0, Localization.q(R$string.app_network_crash), 0);
          toast.setGravity(48, 0, (Constants.screen_height / 4));
          toast.show();
          return;
       }
    }
    public static void b(String p0){
       IpChange $ipChange = a6p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("3cfa744d", objArray);
          return;
       }else {
          a6p.c(p0, null, 0);
          return;
       }
    }
    public static void c(String p0,String p1,int p2){
       IpChange $ipChange = a6p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Integer(p2)};
          $ipChange.ipc$dispatch("7377090c", objArray);
          return;
       }else {
          TBToast tBToast = TBToast.makeText(Globals.getApplication(), p0);
          if (p2) {
             try{
                tBToast.setToastIcon(p2);
                tBToast.setToastIconColor(Globals.getApplication().getResources().getColor(R$color.tbsearch_toast_icon));
             }catch(java.lang.Exception e0){
                Log.e(a6p.a, "set icon error");
             }
          }
          if (!TextUtils.isEmpty(p1)) {
             tBToast.setText2(p1);
          }
          tBToast.setGravity(0x800003, e0, -500);
          tBToast.show();
          return;
       }
    }
}
