package tb.ac6;
import tb.fw5;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.dinamicx.expression.event.DXEvent;
import com.taobao.android.dinamicx.DXRuntimeContext;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.ArrayList;

public class ac6 extends fw5	// class@00186a from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1c50017c);
    }
    public void ac6(){
       super();
       this.d = "branch";
    }
    public static Object ipc$super(ac6 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/dinamicx/expression/DXSerialBlockNode");
    }
    public Object b(DXEvent p0,DXRuntimeContext p1){
       fw5 ta;
       Object obj;
       int i = 0;
       IpChange $ipChange = ac6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("1f85463f", objArray);
       }else if((ta = this.a) != null){
          int i1 = ta.size();
          ArrayList uArrayList = new ArrayList();
          while (i < i1) {
             if ((obj = this.a.get(i).b(p0, p1)) != null) {
                uArrayList.add(obj.toString());
             }
             i = i + 1;
          }
          return uArrayList;
       }else {
          return null;
       }
    }
}
