package tb.a1n$a;
import tb.t2o;
import java.lang.Object;
import java.util.ArrayList;
import java.util.List;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.Collection;
import tb.a1n$a$a;
import java.lang.Boolean;

public class a1n$a	// class@001e59 from classes10.dex
{
    public final Object a;
    public final List b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x31a0007b);
    }
    public void a1n$a(Object p0){
       super();
       this.a = p0;
       this.b = new ArrayList();
    }
    public void a(List p0){
       IpChange $ipChange = a1n$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fd2eaf81", objArray);
          return;
       }else if(p0 != null){
          this.b.addAll(p0);
       }
       return;
    }
    public void b(String p0,Object p1){
       IpChange $ipChange = a1n$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("8acedef9", objArray);
          return;
       }else {
          this.b.add(new a1n$a$a(p0, p1));
          return;
       }
    }
    public boolean c(){
       int i = 1;
       IpChange $ipChange = a1n$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (i ^ this.b.isEmpty());
       }
       Object[] objArray = new Object[i];
       objArray[0] = this;
       return $ipChange.ipc$dispatch("ad2927ee", objArray).booleanValue();
    }
}
