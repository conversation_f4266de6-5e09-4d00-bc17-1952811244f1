package androidx.appcompat.app.AlertController$2;
import androidx.core.widget.NestedScrollView$OnScrollChangeListener;
import androidx.appcompat.app.AlertController;
import android.view.View;
import java.lang.Object;
import androidx.core.widget.NestedScrollView;

public class AlertController$2 implements NestedScrollView$OnScrollChangeListener	// class@000543 from classes.dex
{
    public final AlertController this$0;
    public final View val$bottom;
    public final View val$top;

    public void AlertController$2(AlertController p0,View p1,View p2){
       this.this$0 = p0;
       this.val$top = p1;
       this.val$bottom = p2;
       super();
    }
    public void onScrollChange(NestedScrollView p0,int p1,int p2,int p3,int p4){
       AlertController.manageScrollIndicators(p0, this.val$top, this.val$bottom);
    }
}
