package tb.ad7$a;
import tb.l6j;
import tb.ad7;
import java.lang.Object;
import mtopsdk.mtop.domain.MtopResponse;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ad7$c;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.DescNativeController$b;
import anetwork.channel.Response;
import java.util.HashMap;
import tb.gf7;

public class ad7$a implements l6j	// class@001874 from classes5.dex
{
    public final ad7 a;
    public static IpChange $ipChange;

    public void ad7$a(ad7 p0){
       super();
       this.a = p0;
    }
    public void a(Object p0){
       this.d(p0);
    }
    public void d(MtopResponse p0){
       IpChange $ipChange = ad7$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("cb2ff8f7", objArray);
          return;
       }else {
          this.a.b.b(new ad7$c(p0));
          new HashMap().put("requestParams", this.a.a);
          return;
       }
    }
    public void e(gf7 p0){
       IpChange $ipChange = ad7$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fda5cda7", objArray);
          return;
       }else {
          this.a.d = p0;
          if (p0 != null && p0.a()) {
             ad7.a(this.a, p0.b);
          }
          this.a.b.c(p0);
          new HashMap().put("requestParams", this.a.a);
          return;
       }
    }
    public void onSuccess(Object p0){
       this.e(p0);
    }
}
