package androidx.appcompat.app.TwilightCalculator;
import java.lang.Object;
import java.lang.Math;

public class TwilightCalculator	// class@000589 from classes.dex
{
    public int state;
    public long sunrise;
    public long sunset;
    private static final float ALTIDUTE_CORRECTION_CIVIL_TWILIGHT = -0.104720;
    private static final float C1 = 0.033420;
    private static final float C2 = 0.000349;
    private static final float C3 = 0.000005;
    public static final int DAY = 0;
    private static final float DEGREES_TO_RADIANS = 0.017453;
    private static final float J0 = 0.000900;
    public static final int NIGHT = 1;
    private static final float OBLIQUITY = 0.409280;
    private static final long UTC_2000 = 0xdc6d62da00;
    private static TwilightCalculator sInstance;

    public void TwilightCalculator(){
       super();
    }
    public static TwilightCalculator getInstance(){
       if (TwilightCalculator.sInstance == null) {
          TwilightCalculator.sInstance = new TwilightCalculator();
       }
       return TwilightCalculator.sInstance;
    }
    public void calculateTwilight(long p0,double p1,double p2){
       TwilightCalculator twilightCalc = this;
       long l = 0xdc6d62da00;
       float f = (float)(p0 - l) / 86400000.00f;
       float f1 = (0.02f * f) + 6.24f;
       double d = (double)f1;
       double d1 = (((((Math.sin(d) * 0.03f) + d) + (Math.sin((double)(2.00f * f1)) * 0.00f)) + (Math.sin((double)(f1 * 3.00f)) * 0.00f)) + 1.80f) + 3.14f;
       double d2 = (- p2) / 360.00f;
       double d3 = (double)((float)Math.round(((double)(f - 0.00f) - d2)) + 0.00f) + d2;
       d3 = (d3 + (Math.sin(d) * 0.01f)) + (Math.sin((2.00f * d1)) * -0.01f);
       d = Math.asin((Math.sin(d1) * Math.sin(0.41f)));
       d2 = 0.02f * p1;
       d1 = (Math.sin(-0.10f) - (Math.sin(d2) * Math.sin(d))) / (Math.cos(d2) * Math.cos(d));
       long l1 = -1;
       if ((0x3ff0000000000000 - d1) >= 0) {
          twilightCalc.state = 1;
          twilightCalc.sunset = l1;
          twilightCalc.sunrise = l1;
          return;
       }else if((d1 - 0xbff0000000000000) <= 0){
          twilightCalc.state = 0;
          twilightCalc.sunset = l1;
          twilightCalc.sunrise = l1;
          return;
       }else {
          d = (double)(float)(Math.acos(d1) / 6.28f);
          twilightCalc.sunset = Math.round(((d3 + d) * 86400000.00f)) + l;
          long l2 = Math.round(((d3 - d) * 86400000.00f)) + l;
          twilightCalc.sunrise = l2;
          twilightCalc.state = ((l2 - p0) < 0 && (twilightCalc.sunset - p0) > 0)? 0: 1;
          return;
       }
    }
}
