package kotlinx.serialization.internal.TripleSerializer$descriptor$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.internal.TripleSerializer;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;
import tb.x530;
import kotlinx.serialization.descriptors.a;
import tb.qb40;
import java.util.List;

public final class TripleSerializer$descriptor$1 extends Lambda implements g1a	// class@000750 from classes11.dex
{
    public final TripleSerializer this$0;

    public void TripleSerializer$descriptor$1(TripleSerializer p0){
       this.this$0 = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       ckf.g(p0, "$this$buildClassSerialDescriptor");
       f520 uof520 = p0;
       f520.b(uof520, "first", TripleSerializer.c(this.this$0).getDescriptor(), null, false, 12, null);
       f520.b(uof520, "second", TripleSerializer.d(this.this$0).getDescriptor(), null, false, 12, null);
       f520.b(uof520, "third", TripleSerializer.e(this.this$0).getDescriptor(), null, false, 12, null);
    }
}
