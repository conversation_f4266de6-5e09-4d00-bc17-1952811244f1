package kotlinx.serialization.PolymorphicSerializer$descriptor$2$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.PolymorphicSerializer;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;
import tb.si40;
import tb.x530;
import tb.cz10;
import tb.fj40;
import kotlinx.serialization.descriptors.a;
import java.util.List;
import java.lang.StringBuilder;
import tb.wyf;
import tb.ob40$a;
import tb.ob40;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;

public final class PolymorphicSerializer$descriptor$2$1 extends Lambda implements g1a	// class@00070d from classes11.dex
{
    public final PolymorphicSerializer this$0;

    public void PolymorphicSerializer$descriptor$2$1(PolymorphicSerializer p0){
       this.this$0 = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       PolymorphicSerializer$descriptor$2$1 uodescriptor = this;
       Object obj = p0;
       ckf.g(obj, "$this$buildSerialDescriptor");
       f520.b(p0, "type", cz10.G(si40.INSTANCE).getDescriptor(), null, false, 12, null);
       a[] uoaArray = new a[0];
       f520.b(p0, "value", SerialDescriptorsKt.d("kotlinx.serialization.Polymorphic<"+uodescriptor.this$0.getBaseClass().getSimpleName()+'>', ob40$a.INSTANCE, uoaArray, null, 8, null), null, false, 12, null);
       obj.h(PolymorphicSerializer.c(uodescriptor.this$0));
    }
}
