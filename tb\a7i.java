package tb.a7i;
import tb.mk4;
import tb.k7i;
import tb.rxc;
import java.lang.Object;
import java.lang.Throwable;

public final class a7i implements mk4	// class@001ea5 from classes10.dex
{
    public final k7i a;
    public final rxc b;

    public void a7i(k7i p0,rxc p1){
       super();
       this.a = p0;
       this.b = p1;
    }
    public final void accept(Object p0){
       k7i.q(this.a, this.b, p0);
    }
}
