package kotlinx.datetime.format.TimeFields$hour$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.fw40;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class TimeFields$hour$1 extends MutablePropertyReference1Impl	// class@0006ef from classes11.dex
{
    public static final TimeFields$hour$1 INSTANCE;

    static {
       TimeFields$hour$1.INSTANCE = new TimeFields$hour$1();
    }
    public void TimeFields$hour$1(){
       super(fw40.class, "hour", "getHour\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.b();
    }
    public void set(Object p0,Object p1){
       p0.D(p1);
    }
}
