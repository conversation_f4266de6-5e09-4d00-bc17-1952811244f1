package kotlinx.coroutines.sync.SemaphoreKt$withPermit$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import tb.y9p;
import tb.d1a;
import kotlinx.coroutines.sync.SemaphoreKt;

public final class SemaphoreKt$withPermit$1 extends ContinuationImpl	// class@0006cf from classes11.dex
{
    public Object L$0;
    public Object L$1;
    public int label;
    public Object result;

    public void SemaphoreKt$withPermit$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return SemaphoreKt.k(null, null, this);
    }
}
