package androidx.activity.result.ActivityResultCallerLauncher;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContract;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.activity.result.ActivityResultCallerLauncher$resultContract$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import androidx.core.app.ActivityOptionsCompat;
import tb.xhv;

public final class ActivityResultCallerLauncher extends ActivityResultLauncher	// class@0004b0 from classes.dex
{
    private final ActivityResultContract callerContract;
    private final Object callerInput;
    private final ActivityResultContract contract;
    private final ActivityResultLauncher launcher;
    private final njg resultContract$delegate;

    public void ActivityResultCallerLauncher(ActivityResultLauncher p0,ActivityResultContract p1,Object p2){
       ckf.g(p0, "launcher");
       ckf.g(p1, "callerContract");
       super();
       this.launcher = p0;
       this.callerContract = p1;
       this.callerInput = p2;
       this.resultContract$delegate = a.b(new ActivityResultCallerLauncher$resultContract$2(this));
       this.contract = this.getResultContract();
    }
    private final ActivityResultContract getResultContract(){
       return this.resultContract$delegate.getValue();
    }
    public final ActivityResultContract getCallerContract(){
       return this.callerContract;
    }
    public final Object getCallerInput(){
       return this.callerInput;
    }
    public ActivityResultContract getContract(){
       return this.contract;
    }
    public void launch(Object p0,ActivityOptionsCompat p1){
       this.launch(p0, p1);
    }
    public void launch(xhv p0,ActivityOptionsCompat p1){
       ckf.g(p0, "input");
       this.launcher.launch(this.callerInput, p1);
    }
    public void unregister(){
       this.launcher.unregister();
    }
}
