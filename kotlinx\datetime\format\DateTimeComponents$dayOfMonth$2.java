package kotlinx.datetime.format.DateTimeComponents$dayOfMonth$2;
import kotlin.jvm.internal.MutablePropertyReference0Impl;
import java.lang.Object;
import tb.b230;
import java.lang.Class;
import java.lang.String;
import kotlin.jvm.internal.CallableReference;
import java.lang.Integer;

public final class DateTimeComponents$dayOfMonth$2 extends MutablePropertyReference0Impl	// class@0006d9 from classes11.dex
{

    public void DateTimeComponents$dayOfMonth$2(Object p0){
       super(p0, b230.class, "dayOfMonth", "getDayOfMonth\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(){
       return this.receiver.o();
    }
    public void set(Object p0){
       this.receiver.w(p0);
    }
}
