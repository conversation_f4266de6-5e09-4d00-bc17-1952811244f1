package tb.a74$a;
import com.taobao.android.behavir.util.b$a;
import tb.a74;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;

public class a74$a extends b$a	// class@001844 from classes5.dex
{
    public final boolean b;
    public final a74 c;
    public static IpChange $ipChange;

    public void a74$a(a74 p0,boolean p1){
       this.c = p0;
       this.b = p1;
       super();
    }
    public static Object ipc$super(a74$a p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/behavix/postureCenter/observer/CommonSensorObserver$1");
    }
    public void e(){
       IpChange $ipChange = a74$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("4134b145", objArray);
          return;
       }else {
          a74.b(this.c, this.b);
          return;
       }
    }
}
