package kotlinx.coroutines.channels.BufferedChannelKt;
import tb.zi3;
import kotlinx.coroutines.channels.BufferedChannel;
import java.lang.String;
import java.lang.Object;
import tb.o3r;
import tb.u1r;
import tb.q23;
import tb.g1a;
import tb.zyf;
import kotlinx.coroutines.channels.BufferedChannelKt$createSegmentFunction$1;

public final class BufferedChannelKt	// class@0004d1 from classes11.dex
{
    public static final u1r BUFFERED;
    public static final int SEGMENT_SIZE;
    public static final zi3 a;
    public static final int b;
    public static final u1r c;
    public static final u1r d;
    public static final u1r e;
    public static final u1r f;
    public static final u1r g;
    public static final u1r h;
    public static final u1r i;
    public static final u1r j;
    public static final u1r k;
    public static final u1r l;
    public static final u1r m;
    public static final u1r n;
    public static final u1r o;
    public static final u1r p;
    public static final u1r q;

    static {
       zi3 v6 = new zi3(-1, null, null, 0);
       BufferedChannelKt.a = v6;
       BufferedChannelKt.SEGMENT_SIZE = o3r.g("kotlinx.coroutines.bufferedChannel.segmentSize", 32, 0, 0, 12, null);
       BufferedChannelKt.b = o3r.g("kotlinx.coroutines.bufferedChannel.expandBufferCompletionWaitIterations", 0x2710, 0, 0, 12, null);
       BufferedChannelKt.BUFFERED = new u1r("BUFFERED");
       BufferedChannelKt.c = new u1r("SHOULD_BUFFER");
       BufferedChannelKt.d = new u1r("S_RESUMING_BY_RCV");
       BufferedChannelKt.e = new u1r("RESUMING_BY_EB");
       BufferedChannelKt.f = new u1r("POISONED");
       BufferedChannelKt.g = new u1r("DONE_RCV");
       BufferedChannelKt.h = new u1r("INTERRUPTED_SEND");
       BufferedChannelKt.i = new u1r("INTERRUPTED_RCV");
       BufferedChannelKt.j = new u1r("CHANNEL_CLOSED");
       BufferedChannelKt.k = new u1r("SUSPEND");
       BufferedChannelKt.l = new u1r("SUSPEND_NO_WAITER");
       BufferedChannelKt.m = new u1r("FAILED");
       BufferedChannelKt.n = new u1r("NO_RECEIVE_RESULT");
       BufferedChannelKt.o = new u1r("CLOSE_HANDLER_CLOSED");
       BufferedChannelKt.p = new u1r("CLOSE_HANDLER_INVOKED");
       BufferedChannelKt.q = new u1r("NO_CLOSE_CAUSE");
    }
    public static final long A(int p0){
       long l;
       if (p0) {
          l = (p0 != Integer.MAX_VALUE)? (long)p0: Long.MAX_VALUE;
       }else {
          l = 0;
       }
       return l;
    }
    public static final boolean B(q23 p0,Object p1,g1a p2){
       boolean b;
       if ((p1 = p0.q(p1, null, p2)) != null) {
          p0.p(p1);
          b = true;
       }else {
          b = false;
       }
       return b;
    }
    public static boolean C(q23 p0,Object p1,g1a p2,int p3,Object p4){
       if ((p3 & 0x02)) {
          p2 = null;
       }
       return BufferedChannelKt.B(p0, p1, p2);
    }
    public static final long a(long p0,boolean p1){
       return BufferedChannelKt.v(p0, p1);
    }
    public static final long b(long p0,int p1){
       return BufferedChannelKt.w(p0, p1);
    }
    public static final zi3 c(long p0,zi3 p1){
       return BufferedChannelKt.x(p0, p1);
    }
    public static final u1r d(){
       return BufferedChannelKt.o;
    }
    public static final u1r e(){
       return BufferedChannelKt.p;
    }
    public static final u1r f(){
       return BufferedChannelKt.g;
    }
    public static final int g(){
       return BufferedChannelKt.b;
    }
    public static final u1r h(){
       return BufferedChannelKt.m;
    }
    public static final u1r i(){
       return BufferedChannelKt.i;
    }
    public static final u1r j(){
       return BufferedChannelKt.h;
    }
    public static final u1r k(){
       return BufferedChannelKt.c;
    }
    public static final u1r l(){
       return BufferedChannelKt.q;
    }
    public static final u1r m(){
       return BufferedChannelKt.n;
    }
    public static final zi3 n(){
       return BufferedChannelKt.a;
    }
    public static final u1r o(){
       return BufferedChannelKt.f;
    }
    public static final u1r p(){
       return BufferedChannelKt.e;
    }
    public static final u1r q(){
       return BufferedChannelKt.d;
    }
    public static final u1r r(){
       return BufferedChannelKt.k;
    }
    public static final u1r s(){
       return BufferedChannelKt.l;
    }
    public static final long t(int p0){
       return BufferedChannelKt.A(p0);
    }
    public static final boolean u(q23 p0,Object p1,g1a p2){
       return BufferedChannelKt.B(p0, p1, p2);
    }
    public static final long v(long p0,boolean p1){
       long l = (p1)? 0x4000000000000000: 0;
       return (l + p0);
    }
    public static final long w(long p0,int p1){
       return (((long)p1 << 60) + p0);
    }
    public static final zi3 x(long p0,zi3 p1){
       zi3 v6 = new zi3(p0, p1, p1.u(), 0);
       return v6;
    }
    public static final zyf y(){
       return BufferedChannelKt$createSegmentFunction$1.INSTANCE;
    }
    public static final u1r z(){
       return BufferedChannelKt.j;
    }
}
