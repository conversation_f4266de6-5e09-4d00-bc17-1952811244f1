package tb.a34$c;
import tb.cc50;
import tb.a34;
import java.lang.Object;
import tb.ikl;
import com.taobao.tao.messagekit.core.model.Command;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class a34$c implements cc50	// class@00174d from classes9.dex
{
    public static IpChange $ipChange;

    public void a34$c(a34 p0){
       super();
    }
    public Command a(ikl p0){
       IpChange $ipChange = a34$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.a;
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("25f19f7c", objArray);
    }
    public Object apply(Object p0){
       return this.a(p0);
    }
}
