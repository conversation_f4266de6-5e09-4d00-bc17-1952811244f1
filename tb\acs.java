package tb.acs;
import com.taobao.themis.kernel.launcher.step.TMSBaseLaunchStep;
import tb.t2o;
import tb.bbs;
import tb.a9s;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.eas;
import com.android.alibaba.ip.runtime.IpChange;
import tb.nky;
import tb.ckf;
import tb.acs$b;
import tb.mwd$a;
import tb.nky$a;
import tb.lwd;

public class acs extends TMSBaseLaunchStep	// class@001ec1 from classes10.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x34c000a5);
    }
    public void acs(bbs p0,a9s p1){
       super(p0, p1);
    }
    public static Object ipc$super(acs p0,String p1,Object[] p2){
       if (p1.hashCode() != -790391893) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/themis/open/launch_step/TMSPackageStep");
       }
       super.i();
       return null;
    }
    public static final void k(acs p0,eas p1){
       IpChange $ipChange = acs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("573841e4", objArray);
          return;
       }else {
          p0.g(p1);
          return;
       }
    }
    public String c(){
       IpChange $ipChange = acs.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "Package";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7c09e698", objArray);
    }
    public void e(){
       IpChange $ipChange = acs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("1ef60dff", objArray);
       }
       return;
    }
    public void f(){
       IpChange $ipChange = acs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b1e2985e", objArray);
       }
       return;
    }
    public void h(){
       TMSBaseLaunchStep tb;
       IpChange $ipChange = acs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("67daaada", objArray);
          return;
       }else if((tb = this.b) != null && !tb.g0()){
          TMSBaseLaunchStep tb1 = this.b;
          ckf.f(tb1, "mInstance");
          TMSBaseLaunchStep tc = this.c;
          ckf.f(tc, "mLogTraceId");
          nky.Companion.c(tb1, tc, new acs$b(this));
       }
       return;
    }
    public void i(){
       TMSBaseLaunchStep tj;
       IpChange $ipChange = acs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d0e393ab", objArray);
          return;
       }else {
          super.i();
          if ((tj = this.j) != null) {
             tj.e();
          }
          return;
       }
    }
}
