package androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$2;
import tb.sp9;
import android.app.Activity;
import java.lang.Object;
import android.graphics.Rect;
import tb.ar4;
import androidx.activity.Api26Impl;
import tb.xhv;

public final class PipHintTrackerKt$trackPipAnimationHintView$2 implements sp9	// class@000466 from classes.dex
{
    public final Activity $this_trackPipAnimationHintView;

    public void PipHintTrackerKt$trackPipAnimationHintView$2(Activity p0){
       this.$this_trackPipAnimationHintView = p0;
       super();
    }
    public final Object emit(Rect p0,ar4 p1){
       Api26Impl.INSTANCE.setPipParamsSourceRectHint(this.$this_trackPipAnimationHintView, p0);
       return xhv.INSTANCE;
    }
    public Object emit(Object p0,ar4 p1){
       return this.emit(p0, p1);
    }
}
