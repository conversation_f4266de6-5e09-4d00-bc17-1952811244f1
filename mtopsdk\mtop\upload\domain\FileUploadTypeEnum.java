package mtopsdk.mtop.upload.domain.FileUploadTypeEnum;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class FileUploadTypeEnum extends Enum	// class@00080d from classes11.dex
{
    private String uploadType;
    private static final FileUploadTypeEnum[] $VALUES;
    public static IpChange $ipChange;
    public static final FileUploadTypeEnum NORMAL;
    public static final FileUploadTypeEnum RESUMABLE;

    static {
       FileUploadTypeEnum uFileUploadT = new FileUploadTypeEnum("RESUMABLE", 0, "resumable");
       FileUploadTypeEnum.RESUMABLE = uFileUploadT;
       FileUploadTypeEnum uFileUploadT1 = new FileUploadTypeEnum("NORMAL", 1, "normal");
       FileUploadTypeEnum.NORMAL = uFileUploadT1;
       FileUploadTypeEnum[] uFileUploadT2 = new FileUploadTypeEnum[]{uFileUploadT,uFileUploadT1};
       FileUploadTypeEnum.$VALUES = uFileUploadT2;
    }
    private void FileUploadTypeEnum(String p0,int p1,String p2){
       super(p0, p1);
       this.uploadType = p2;
    }
    public static Object ipc$super(FileUploadTypeEnum p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/upload/domain/FileUploadTypeEnum");
    }
    public static FileUploadTypeEnum valueOf(String p0){
       IpChange $ipChange = FileUploadTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(FileUploadTypeEnum.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("b31b1b2b", objArray);
    }
    public static FileUploadTypeEnum[] values(){
       IpChange $ipChange = FileUploadTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return FileUploadTypeEnum.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("771e69a", objArray);
    }
    public String getUploadType(){
       IpChange $ipChange = FileUploadTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.uploadType;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("35e0aac8", objArray);
    }
}
