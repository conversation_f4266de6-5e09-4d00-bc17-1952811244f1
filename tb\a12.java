package tb.a12;
import tb.vrd;
import tb.auc;
import tb.b12;
import tb.t2o;
import android.app.Activity;
import tb.ude;
import tb.acx;
import android.view.ViewGroup;
import tb.vfw;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.meta.data.MetaResult;
import androidx.viewpager.widget.ViewPager;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ddd;
import tb.sxh;
import tb.pib;
import java.lang.Boolean;
import java.lang.Float;
import android.view.View;
import tb.khq;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import java.lang.Integer;
import java.lang.Number;
import tb.kr1;
import tb.p9e;
import tb.qib;
import tb.yko;
import tb.abx;
import tb.tb4;
import tb.xua;
import tb.ay4;

public class a12 extends b12 implements vrd, auc	// class@001834 from classes7.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3db000dc);
       t2o.a(0x3db000df);
       t2o.a(0x3db00027);
       t2o.a(0x3db0018f);
    }
    public void a12(Activity p0,ude p1,acx p2,ViewGroup p3,vfw p4){
       super(p0, p1, p2, p3, p4);
    }
    public static Object ipc$super(a12 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/searchbaseframe/business/srp/tab/BaseSrpTabWidget");
    }
    public void D0(MetaResult p0,ViewPager p1){
       IpChange $ipChange = a12.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("5ae5d4ba", objArray);
          return;
       }else {
          this.u2().p(p1);
          return;
       }
    }
    public void K0(boolean p0,float p1){
       IpChange $ipChange = a12.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0),new Float(p1)};
          $ipChange.ipc$dispatch("224521ba", objArray);
       }
       return;
    }
    public void Z0(){
       IpChange $ipChange = a12.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("8a95530d", objArray);
          return;
       }else {
          this.getView().setVisibility(0);
          return;
       }
    }
    public void b(int p0){
       IpChange $ipChange = a12.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("d6833c7a", objArray);
       }
       return;
    }
    public int f(){
       int i = 0;
       IpChange $ipChange = a12.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("af5b5a8a", objArray).intValue();
       }
       return i;
    }
    public kr1 getBehavior(){
       IpChange $ipChange = a12.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9c30dbef", objArray);
    }
    public String getLogTag(){
       IpChange $ipChange = a12.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "BaseSrpTabWidget";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("55d7c1cd", objArray);
    }
    public void h0(){
       IpChange $ipChange = a12.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("726a97c8", objArray);
          return;
       }else {
          this.getView().setVisibility(8);
          return;
       }
    }
    public boolean p(){
       int i = 0;
       IpChange $ipChange = a12.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("9409b0bc", objArray).booleanValue();
       }
       return i;
    }
    public ddd q2(){
       return this.w2();
    }
    public p9e s2(){
       return this.x2();
    }
    public pib w2(){
       IpChange $ipChange = a12.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c().j().a().b.a(null);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f4612ae0", objArray);
    }
    public qib x2(){
       IpChange $ipChange = a12.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c().j().a().a.a(null);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c1082b80", objArray);
    }
}
