package androidx.activity.result.IntentSenderRequest;
import android.os.Parcelable;
import androidx.activity.result.IntentSenderRequest$Companion;
import tb.a07;
import androidx.activity.result.IntentSenderRequest$Companion$CREATOR$1;
import android.content.IntentSender;
import android.content.Intent;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.os.Parcel;
import java.lang.ClassLoader;
import java.lang.Class;

public final class IntentSenderRequest implements Parcelable	// class@0004c0 from classes.dex
{
    private final Intent fillInIntent;
    private final int flagsMask;
    private final int flagsValues;
    private final IntentSender intentSender;
    public static final Parcelable$Creator CREATOR;
    public static final IntentSenderRequest$Companion Companion;

    static {
       IntentSenderRequest.Companion = new IntentSenderRequest$Companion(null);
       IntentSenderRequest.CREATOR = new IntentSenderRequest$Companion$CREATOR$1();
    }
    public void IntentSenderRequest(IntentSender p0,Intent p1,int p2,int p3){
       ckf.g(p0, "intentSender");
       super();
       this.intentSender = p0;
       this.fillInIntent = p1;
       this.flagsMask = p2;
       this.flagsValues = p3;
    }
    public void IntentSenderRequest(IntentSender p0,Intent p1,int p2,int p3,int p4,a07 p5){
       if ((p4 & 0x02)) {
          p1 = null;
       }
       if ((p4 & 0x04)) {
          p2 = 0;
       }
       if ((p4 & 0x08)) {
          p3 = 0;
       }
       super(p0, p1, p2, p3);
       return;
    }
    public void IntentSenderRequest(Parcel p0){
       ckf.g(p0, "parcel");
       Parcelable parcelable = p0.readParcelable(IntentSender.class.getClassLoader());
       ckf.d(parcelable);
       super(parcelable, p0.readParcelable(Intent.class.getClassLoader()), p0.readInt(), p0.readInt());
    }
    public int describeContents(){
       return 0;
    }
    public final Intent getFillInIntent(){
       return this.fillInIntent;
    }
    public final int getFlagsMask(){
       return this.flagsMask;
    }
    public final int getFlagsValues(){
       return this.flagsValues;
    }
    public final IntentSender getIntentSender(){
       return this.intentSender;
    }
    public void writeToParcel(Parcel p0,int p1){
       ckf.g(p0, "dest");
       p0.writeParcelable(this.intentSender, p1);
       p0.writeParcelable(this.fillInIntent, p1);
       p0.writeInt(this.flagsMask);
       p0.writeInt(this.flagsValues);
    }
}
