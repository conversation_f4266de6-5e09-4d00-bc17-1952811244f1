package tb.a90;
import tb.t2o;
import tb.a90$a;
import tb.a07;
import java.lang.Object;
import com.taobao.themis.kernel.page.ITMSPage;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ckf;
import java.lang.Number;
import android.content.Context;
import android.view.View;
import java.lang.Integer;
import com.taobao.themis.kernel.container.Window$Theme;

public abstract class a90	// class@001ea9 from classes10.dex
{
    public int a;
    public ITMSPage b;
    public static IpChange $ipChange;
    public static final int CENTER;
    public static final a90$a Companion;
    public static final int LEFT;
    public static final int RIGHT;

    static {
       t2o.a(0x3490008f);
       a90.Companion = new a90$a(null);
    }
    public void a90(){
       super();
    }
    public void i(ITMSPage p0){
       IpChange $ipChange = a90.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1cee1ba6", objArray);
          return;
       }else {
          ckf.g(p0, "page");
          this.b = p0;
          return;
       }
    }
    public final int j(){
       IpChange $ipChange = a90.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5d66d425", objArray).intValue();
    }
    public final ITMSPage k(){
       IpChange $ipChange = a90.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("26554f74", objArray);
    }
    public abstract View l(Context p0);
    public void m(){
       IpChange $ipChange = a90.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a6532022", objArray);
       }
       return;
    }
    public void n(){
       IpChange $ipChange = a90.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("3b4c13c8", objArray);
       }
       return;
    }
    public void o(){
       IpChange $ipChange = a90.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("90d5bc03", objArray);
       }
       return;
    }
    public final void p(int p0){
       IpChange $ipChange = a90.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("e24a8b85", objArray);
          return;
       }else {
          this.a = p0;
          return;
       }
    }
    public void q(Window$Theme p0){
       IpChange $ipChange = a90.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f7f4570c", objArray);
          return;
       }else {
          ckf.g(p0, "style");
          return;
       }
    }
}
