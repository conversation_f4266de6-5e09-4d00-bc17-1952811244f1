package kotlinx.datetime.format.TimeFields$second$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.fw40;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class TimeFields$second$1 extends MutablePropertyReference1Impl	// class@0006f2 from classes11.dex
{
    public static final TimeFields$second$1 INSTANCE;

    static {
       TimeFields$second$1.INSTANCE = new TimeFields$second$1();
    }
    public void TimeFields$second$1(){
       super(fw40.class, "second", "getSecond\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.k();
    }
    public void set(Object p0,Object p1){
       p0.d(p1);
    }
}
