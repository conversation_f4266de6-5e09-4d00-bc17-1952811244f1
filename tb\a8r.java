package tb.a8r;
import tb.t2o;
import android.app.Activity;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import tb.d7r;
import android.content.Context;
import java.lang.Boolean;
import com.taobao.android.autosize.TBDeviceUtils;

public class a8r	// class@001853 from classes5.dex
{
    public static IpChange $ipChange;
    public static Boolean a;

    static {
       t2o.a(0x2cc000b9);
       a8r.a = null;
    }
    public static void a(Activity p0){
       IpChange $ipChange = a8r.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("4a4082ae", objArray);
          return;
       }else {
          d7r.a(p0);
          return;
       }
    }
    public static boolean b(Context p0){
       Boolean a;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a8r.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("fe5b6e48", objArray).booleanValue();
       }else if(a8r.a != null){
          boolean b = (!TBDeviceUtils.p(p0) && !TBDeviceUtils.P(p0))? false: true;
          a8r.a = Boolean.valueOf(b);
       }
       if ((a = a8r.a) != null && a.booleanValue()) {
          i = true;
       }
       return i;
    }
}
