package kotlinx.coroutines.sync.SemaphoreImpl$onCancellationRelease$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.coroutines.sync.SemaphoreImpl;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;

public final class SemaphoreImpl$onCancellationRelease$1 extends Lambda implements g1a	// class@0006cc from classes11.dex
{
    public final SemaphoreImpl this$0;

    public void SemaphoreImpl$onCancellationRelease$1(SemaphoreImpl p0){
       this.this$0 = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(Throwable p0){
       this.this$0.release();
    }
}
