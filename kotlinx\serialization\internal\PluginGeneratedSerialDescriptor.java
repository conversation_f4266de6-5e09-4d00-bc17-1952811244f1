package kotlinx.serialization.internal.PluginGeneratedSerialDescriptor;
import kotlinx.serialization.descriptors.a;
import tb.q020;
import java.lang.String;
import tb.eu20;
import java.lang.Object;
import tb.ckf;
import java.util.List;
import java.util.Map;
import kotlin.collections.a;
import kotlin.LazyThreadSafetyMode;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor$childSerializers$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor$typeParameterDescriptors$2;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor$_hashCode$2;
import tb.a07;
import java.lang.UnsupportedOperationException;
import java.util.Set;
import tb.ob40;
import kotlinx.serialization.descriptors.b$a;
import tb.x530;
import tb.qb40;
import tb.yz3;
import java.util.HashMap;
import java.lang.Integer;
import java.lang.Number;
import tb.aef;
import tb.hfn;
import java.lang.StringBuilder;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor$toString$1;
import java.lang.Iterable;
import java.lang.CharSequence;
import tb.g1a;
import tb.i04;

public class PluginGeneratedSerialDescriptor implements a, q020	// class@00074c from classes11.dex
{
    public final String a;
    public final eu20 b;
    public final int c;
    public int d;
    public final String[] e;
    public final List[] f;
    public final boolean[] g;
    public Map h;
    public final njg i;
    public final njg j;
    public final njg k;

    public void PluginGeneratedSerialDescriptor(String p0,eu20 p1,int p2){
       ckf.g(p0, "serialName");
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
       this.d = -1;
       String[] stringArray = new String[p2];
       for (int i = 0; i < p2; i++) {
          stringArray[i] = "[UNINITIALIZED]";
       }
       this.e = stringArray;
       PluginGeneratedSerialDescriptor tc = this.c;
       List[] listArray = new List[tc];
       this.f = listArray;
       boolean[] uobooleanArr = new boolean[tc];
       this.g = uobooleanArr;
       this.h = a.h();
       LazyThreadSafetyMode pUBLICATION = LazyThreadSafetyMode.PUBLICATION;
       this.i = a.a(pUBLICATION, new PluginGeneratedSerialDescriptor$childSerializers$2(this));
       this.j = a.a(pUBLICATION, new PluginGeneratedSerialDescriptor$typeParameterDescriptors$2(this));
       this.k = a.a(pUBLICATION, new PluginGeneratedSerialDescriptor$_hashCode$2(this));
       return;
    }
    public void PluginGeneratedSerialDescriptor(String p0,eu20 p1,int p2,int p3,a07 p4){
       if ((p3 & 0x02)) {
          p1 = null;
       }
       super(p0, p1, p2);
       return;
    }
    public static final eu20 i(PluginGeneratedSerialDescriptor p0){
       return p0.b;
    }
    public static void k(PluginGeneratedSerialDescriptor p0,String p1,boolean p2,int p3,Object p4){
       if (p4 != null) {
          throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: addElement");
       }
       if ((p3 & 0x02)) {
          p2 = false;
       }
       p0.j(p1, p2);
       return;
    }
    public Set a(){
       return this.h.keySet();
    }
    public boolean b(){
       return false;
    }
    public ob40 c(){
       return b$a.INSTANCE;
    }
    public final int d(){
       return this.c;
    }
    public String e(int p0){
       return this.e[p0];
    }
    public String f(){
       return this.a;
    }
    public a g(int p0){
       return this.m()[p0].getDescriptor();
    }
    public List getAnnotations(){
       return yz3.i();
    }
    public boolean h(int p0){
       return this.g[p0];
    }
    public int hashCode(){
       return this.o();
    }
    public final void j(String p0,boolean p1){
       ckf.g(p0, "name");
       int i = this.d + 1;
       this.d = i;
       this.e[i] = p0;
       p0[i] = p1;
       p0[i] = null;
       if (i == (this.c - 1)) {
          this.h = this.l();
       }
       return;
    }
    public final Map l(){
       HashMap hashMap = new HashMap();
       PluginGeneratedSerialDescriptor te = this.e;
       int len = te.length;
       for (int i = 0; i < len; i = i + 1) {
          hashMap.put(te[i], Integer.valueOf(i));
       }
       return hashMap;
    }
    public final x530[] m(){
       return this.i.getValue();
    }
    public final a[] n(){
       return this.j.getValue();
    }
    public final int o(){
       return this.k.getValue().intValue();
    }
    public String toString(){
       return i04.j0(hfn.n(0, this.c), ", ", this.f()+'(', "\)", 0, null, new PluginGeneratedSerialDescriptor$toString$1(this), 24, null);
    }
}
