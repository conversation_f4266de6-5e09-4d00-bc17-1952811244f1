package androidx.appcompat.app.AlertController$AlertParams;
import android.content.Context;
import java.lang.Object;
import java.lang.String;
import android.view.LayoutInflater;
import androidx.appcompat.app.AlertController;
import android.view.ViewGroup;
import android.view.View;
import androidx.appcompat.app.AlertController$RecycleListView;
import androidx.appcompat.app.AlertController$AlertParams$1;
import java.lang.CharSequence;
import androidx.appcompat.app.AlertController$AlertParams$2;
import android.database.Cursor;
import android.widget.SimpleCursorAdapter;
import androidx.appcompat.app.AlertController$CheckedItemAdapter;
import android.widget.ListView;
import androidx.appcompat.app.AlertController$AlertParams$OnPrepareListViewListener;
import androidx.appcompat.app.AlertController$AlertParams$3;
import android.widget.AdapterView$OnItemClickListener;
import android.widget.AdapterView;
import androidx.appcompat.app.AlertController$AlertParams$4;
import android.widget.AdapterView$OnItemSelectedListener;
import android.widget.AbsListView;
import android.graphics.drawable.Drawable;
import android.content.DialogInterface$OnClickListener;
import android.os.Message;

public class AlertController$AlertParams	// class@00054c from classes.dex
{
    public ListAdapter mAdapter;
    public boolean mCancelable;
    public int mCheckedItem;
    public boolean[] mCheckedItems;
    public final Context mContext;
    public Cursor mCursor;
    public View mCustomTitleView;
    public boolean mForceInverseBackground;
    public Drawable mIcon;
    public int mIconAttrId;
    public int mIconId;
    public final LayoutInflater mInflater;
    public String mIsCheckedColumn;
    public boolean mIsMultiChoice;
    public boolean mIsSingleChoice;
    public CharSequence[] mItems;
    public String mLabelColumn;
    public CharSequence mMessage;
    public Drawable mNegativeButtonIcon;
    public DialogInterface$OnClickListener mNegativeButtonListener;
    public CharSequence mNegativeButtonText;
    public Drawable mNeutralButtonIcon;
    public DialogInterface$OnClickListener mNeutralButtonListener;
    public CharSequence mNeutralButtonText;
    public DialogInterface$OnCancelListener mOnCancelListener;
    public DialogInterface$OnMultiChoiceClickListener mOnCheckboxClickListener;
    public DialogInterface$OnClickListener mOnClickListener;
    public DialogInterface$OnDismissListener mOnDismissListener;
    public AdapterView$OnItemSelectedListener mOnItemSelectedListener;
    public DialogInterface$OnKeyListener mOnKeyListener;
    public AlertController$AlertParams$OnPrepareListViewListener mOnPrepareListViewListener;
    public Drawable mPositiveButtonIcon;
    public DialogInterface$OnClickListener mPositiveButtonListener;
    public CharSequence mPositiveButtonText;
    public boolean mRecycleOnMeasure;
    public CharSequence mTitle;
    public View mView;
    public int mViewLayoutResId;
    public int mViewSpacingBottom;
    public int mViewSpacingLeft;
    public int mViewSpacingRight;
    public boolean mViewSpacingSpecified;
    public int mViewSpacingTop;

    public void AlertController$AlertParams(Context p0){
       super();
       this.mIconId = 0;
       this.mIconAttrId = 0;
       this.mViewSpacingSpecified = false;
       this.mCheckedItem = -1;
       this.mRecycleOnMeasure = true;
       this.mContext = p0;
       this.mCancelable = true;
       this.mInflater = p0.getSystemService("layout_inflater");
    }
    private void createListView(AlertController p0){
       AlertController$AlertParams$1 v8;
       AlertController$AlertParams tmOnPrepareL;
       AlertController mSingleChoic;
       AlertController$AlertParams tmAdapter;
       AlertController$RecycleListView recycleListV = this.mInflater.inflate(p0.mListLayout, null);
       if (this.mIsMultiChoice != null) {
          if (this.mCursor == null) {
             v8 = new AlertController$AlertParams$1(this, this.mContext, p0.mMultiChoiceItemLayout, 0x1020014, this.mItems, recycleListV);
          }else {
             AlertController$AlertParams$2 v81 = new AlertController$AlertParams$2(this, this.mContext, this.mCursor, false, recycleListV, p0);
          }
       }else if(this.mIsSingleChoice != null){
          mSingleChoic = p0.mSingleChoiceItemLayout;
       }else {
          mSingleChoic = p0.mListItemLayout;
       }
       AlertController uAlertContro = mSingleChoic;
       int[] ointArray = 0x1020014;
       if (this.mCursor != null) {
          String[] stringArray = new String[]{this.mLabelColumn};
          int[] ointArray1 = new int[]{ointArray};
          SimpleCursorAdapter v82 = new SimpleCursorAdapter(this.mContext, uAlertContro, this.mCursor, stringArray, ointArray1);
       }else if((tmAdapter = this.mAdapter) != null){
          tmAdapter = new AlertController$CheckedItemAdapter(this.mContext, uAlertContro, ointArray, this.mItems);
       }
       if ((tmOnPrepareL = this.mOnPrepareListViewListener) != null) {
          tmOnPrepareL.onPrepareListView(recycleListV);
       }
       p0.mAdapter = v8;
       p0.mCheckedItem = this.mCheckedItem;
       if (this.mOnClickListener != null) {
          recycleListV.setOnItemClickListener(new AlertController$AlertParams$3(this, p0));
       }else if(this.mOnCheckboxClickListener != null){
          recycleListV.setOnItemClickListener(new AlertController$AlertParams$4(this, recycleListV, p0));
       }
       if ((tmOnPrepareL = this.mOnItemSelectedListener) != null) {
          recycleListV.setOnItemSelectedListener(tmOnPrepareL);
       }
       if (this.mIsSingleChoice != null) {
          recycleListV.setChoiceMode(1);
       }else if(this.mIsMultiChoice != null){
          recycleListV.setChoiceMode(2);
       }
       p0.mListView = recycleListV;
       return;
    }
    public void apply(AlertController p0){
       AlertController$AlertParams tmCustomTitl;
       AlertController$AlertParams tmPositiveBu;
       AlertController$AlertParams tmNegativeBu;
       AlertController$AlertParams tmNeutralBut;
       if ((tmCustomTitl = this.mCustomTitleView) != null) {
          p0.setCustomTitle(tmCustomTitl);
       }else if((tmCustomTitl = this.mTitle) != null){
          p0.setTitle(tmCustomTitl);
       }
       if ((tmCustomTitl = this.mIcon) != null) {
          p0.setIcon(tmCustomTitl);
       }
       if ((tmCustomTitl = this.mIconId) != null) {
          p0.setIcon(tmCustomTitl);
       }
       if ((tmCustomTitl = this.mIconAttrId) != null) {
          p0.setIcon(p0.getIconAttributeResId(tmCustomTitl));
       }
       if ((tmCustomTitl = this.mMessage) != null) {
          p0.setMessage(tmCustomTitl);
       }
       if ((tmPositiveBu = this.mPositiveButtonText) != null || this.mPositiveButtonIcon != null) {
          p0.setButton(-1, tmPositiveBu, this.mPositiveButtonListener, null, this.mPositiveButtonIcon);
       }
       if ((tmNegativeBu = this.mNegativeButtonText) != null || this.mNegativeButtonIcon != null) {
          p0.setButton(-2, tmNegativeBu, this.mNegativeButtonListener, null, this.mNegativeButtonIcon);
       }
       if ((tmNeutralBut = this.mNeutralButtonText) != null || this.mNeutralButtonIcon != null) {
          p0.setButton(-3, tmNeutralBut, this.mNeutralButtonListener, null, this.mNeutralButtonIcon);
       }
       if (this.mItems != null || (this.mCursor != null || this.mAdapter != null)) {
          this.createListView(p0);
       }
       if ((tmNeutralBut = this.mView) != null) {
          if (this.mViewSpacingSpecified != null) {
             p0.setView(tmNeutralBut, this.mViewSpacingLeft, this.mViewSpacingTop, this.mViewSpacingRight, this.mViewSpacingBottom);
          }else {
             p0.setView(tmNeutralBut);
          }
       }else if((tmCustomTitl = this.mViewLayoutResId) != null){
          p0.setView(tmCustomTitl);
       }
       return;
    }
}
