package androidx.activity.SystemBarStyle$Companion;
import java.lang.Object;
import tb.a07;
import tb.g1a;
import androidx.activity.SystemBarStyle;
import androidx.activity.SystemBarStyle$Companion$auto$1;
import java.lang.String;
import tb.ckf;
import androidx.activity.SystemBarStyle$Companion$dark$1;
import androidx.activity.SystemBarStyle$Companion$light$1;

public final class SystemBarStyle$Companion	// class@00046e from classes.dex
{

    private void SystemBarStyle$Companion(){
       super();
    }
    public void SystemBarStyle$Companion(a07 p0){
       super();
    }
    public static SystemBarStyle auto$default(SystemBarStyle$Companion p0,int p1,int p2,g1a p3,int p4,Object p5){
       SystemBarStyle$Companion$auto$1 iNSTANCE;
       if ((p4 & 0x04)) {
          iNSTANCE = SystemBarStyle$Companion$auto$1.INSTANCE;
       }
       return p0.auto(p1, p2, iNSTANCE);
    }
    public final SystemBarStyle auto(int p0,int p1){
       return SystemBarStyle$Companion.auto$default(this, p0, p1, null, 4, null);
    }
    public final SystemBarStyle auto(int p0,int p1,g1a p2){
       ckf.g(p2, "detectDarkMode");
       SystemBarStyle v0 = new SystemBarStyle(p0, p1, 0, p2, null);
       return v0;
    }
    public final SystemBarStyle dark(int p0){
       SystemBarStyle v6 = new SystemBarStyle(p0, p0, 2, SystemBarStyle$Companion$dark$1.INSTANCE, null);
       return v6;
    }
    public final SystemBarStyle light(int p0,int p1){
       SystemBarStyle v6 = new SystemBarStyle(p0, p1, 1, SystemBarStyle$Companion$light$1.INSTANCE, null);
       return v6;
    }
}
