package tb.aaw;
import java.lang.Comparable;
import tb.t2o;
import com.taobao.infoflow.protocol.subservice.biz.IVideoPlayControllerService$c;
import tb.dcw;
import java.lang.Object;
import java.lang.System;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import java.lang.Integer;

public class aaw implements Comparable	// class@001b17 from classes8.dex
{
    private final int a;
    private final IVideoPlayControllerService$c b;
    private final dcw c;
    private int d;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1f600269);
    }
    public void aaw(IVideoPlayControllerService$c p0,dcw p1){
       super();
       this.b = p0;
       this.c = p1;
       this.a = System.identityHashCode(p0);
    }
    public int a(aaw p0){
       IpChange $ipChange = aaw.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (this.d - p0.d());
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("1e5fdd4", objArray).intValue();
    }
    public int b(){
       IpChange $ipChange = aaw.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1ddaaf0b", objArray).intValue();
    }
    public int compareTo(Object p0){
       return this.a(p0);
    }
    public int d(){
       IpChange $ipChange = aaw.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.d;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("49b31e94", objArray).intValue();
    }
    public IVideoPlayControllerService$c e(){
       IpChange $ipChange = aaw.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("64b6c2c1", objArray);
    }
    public dcw f(){
       IpChange $ipChange = aaw.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1eddf429", objArray);
    }
    public void g(int p0){
       IpChange $ipChange = aaw.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("7f878ef6", objArray);
          return;
       }else {
          this.d = p0;
          return;
       }
    }
}
