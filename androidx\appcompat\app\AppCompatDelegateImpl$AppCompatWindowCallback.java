package androidx.appcompat.app.AppCompatDelegateImpl$AppCompatWindowCallback;
import androidx.appcompat.view.WindowCallbackWrapper;
import androidx.appcompat.app.AppCompatDelegateImpl;
import android.view.Window$Callback;
import android.view.KeyEvent;
import android.view.Menu;
import androidx.appcompat.view.menu.MenuBuilder;
import android.view.View;
import androidx.appcompat.app.AppCompatDelegateImpl$ActionBarMenuCallback;
import java.util.List;
import androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState;
import android.view.ActionMode$Callback;
import android.view.ActionMode;
import android.os.Build$VERSION;
import androidx.appcompat.view.SupportActionModeWrapper$CallbackWrapper;
import android.content.Context;
import androidx.appcompat.view.ActionMode$Callback;
import androidx.appcompat.view.ActionMode;

public class AppCompatDelegateImpl$AppCompatWindowCallback extends WindowCallbackWrapper	// class@00056e from classes.dex
{
    private AppCompatDelegateImpl$ActionBarMenuCallback mActionBarCallback;
    private boolean mDispatchKeyEventBypassEnabled;
    private boolean mOnContentChangedBypassEnabled;
    private boolean mOnPanelClosedBypassEnabled;
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$AppCompatWindowCallback(AppCompatDelegateImpl p0,Window$Callback p1){
       this.this$0 = p0;
       super(p1);
    }
    public boolean bypassDispatchKeyEvent(Window$Callback p0,KeyEvent p1){
       this.mDispatchKeyEventBypassEnabled = true;
       this.mDispatchKeyEventBypassEnabled = false;
       return p0.dispatchKeyEvent(p1);
    }
    public void bypassOnContentChanged(Window$Callback p0){
       this.mOnContentChangedBypassEnabled = true;
       p0.onContentChanged();
       this.mOnContentChangedBypassEnabled = false;
    }
    public void bypassOnPanelClosed(Window$Callback p0,int p1,Menu p2){
       this.mOnPanelClosedBypassEnabled = true;
       p0.onPanelClosed(p1, p2);
       this.mOnPanelClosedBypassEnabled = false;
    }
    public boolean dispatchKeyEvent(KeyEvent p0){
       if (this.mDispatchKeyEventBypassEnabled != null) {
          return this.getWrapped().dispatchKeyEvent(p0);
       }
       boolean b = (!this.this$0.dispatchKeyEvent(p0) && !super.dispatchKeyEvent(p0))? false: true;
       return b;
    }
    public boolean dispatchKeyShortcutEvent(KeyEvent p0){
       boolean b = (!super.dispatchKeyShortcutEvent(p0) && !this.this$0.onKeyShortcut(p0.getKeyCode(), p0))? false: true;
       return b;
    }
    public void onContentChanged(){
       if (this.mOnContentChangedBypassEnabled != null) {
          this.getWrapped().onContentChanged();
       }
       return;
    }
    public boolean onCreatePanelMenu(int p0,Menu p1){
       if (!p0 && !p1 instanceof MenuBuilder) {
          return false;
       }
       return super.onCreatePanelMenu(p0, p1);
    }
    public View onCreatePanelView(int p0){
       AppCompatDelegateImpl$AppCompatWindowCallback tmActionBarC;
       View view;
       if ((tmActionBarC = this.mActionBarCallback) != null && (view = tmActionBarC.onCreatePanelView(p0)) != null) {
          return view;
       }
       return super.onCreatePanelView(p0);
    }
    public boolean onMenuOpened(int p0,Menu p1){
       super.onMenuOpened(p0, p1);
       this.this$0.onMenuOpened(p0);
       return true;
    }
    public void onPanelClosed(int p0,Menu p1){
       if (this.mOnPanelClosedBypassEnabled != null) {
          this.getWrapped().onPanelClosed(p0, p1);
          return;
       }else {
          super.onPanelClosed(p0, p1);
          this.this$0.onPanelClosed(p0);
          return;
       }
    }
    public boolean onPreparePanel(int p0,View p1,Menu p2){
       AppCompatDelegateImpl$AppCompatWindowCallback tmActionBarC;
       MenuBuilder menuBuilder = (p2 instanceof MenuBuilder)? p2: null;
       if (!p0 && !menuBuilder) {
          return false;
       }else {
          boolean b = true;
          if (menuBuilder) {
             menuBuilder.setOverrideVisibleItems(b);
          }
          if ((tmActionBarC = this.mActionBarCallback) == null || !tmActionBarC.onPreparePanel(p0)) {
             b = false;
          }
          if (!b) {
             b = super.onPreparePanel(p0, p1, p2);
          }
          if (menuBuilder != null) {
             menuBuilder.setOverrideVisibleItems(false);
          }
          return b;
       }
    }
    public void onProvideKeyboardShortcuts(List p0,Menu p1,int p2){
       AppCompatDelegateImpl$PanelFeatureState panelState;
       if ((panelState = this.this$0.getPanelState(0, true)) != null && (panelState = panelState.menu) != null) {
          super.onProvideKeyboardShortcuts(p0, panelState, p2);
       }else {
          super.onProvideKeyboardShortcuts(p0, p1, p2);
       }
       return;
    }
    public ActionMode onWindowStartingActionMode(ActionMode$Callback p0){
       if (Build$VERSION.SDK_INT >= 23) {
          return null;
       }
       if (this.this$0.isHandleNativeActionModesEnabled()) {
          return this.startAsSupportActionMode(p0);
       }
       return super.onWindowStartingActionMode(p0);
    }
    public ActionMode onWindowStartingActionMode(ActionMode$Callback p0,int p1){
       if (this.this$0.isHandleNativeActionModesEnabled() && !p1) {
          return this.startAsSupportActionMode(p0);
       }
       return super.onWindowStartingActionMode(p0, p1);
    }
    public void setActionBarCallback(AppCompatDelegateImpl$ActionBarMenuCallback p0){
       this.mActionBarCallback = p0;
    }
    public final ActionMode startAsSupportActionMode(ActionMode$Callback p0){
       ActionMode uActionMode;
       SupportActionModeWrapper$CallbackWrapper uCallbackWra = new SupportActionModeWrapper$CallbackWrapper(this.this$0.mContext, p0);
       if ((uActionMode = this.this$0.startSupportActionMode(uCallbackWra)) != null) {
          return uCallbackWra.getActionModeWrapper(uActionMode);
       }
       return null;
    }
}
