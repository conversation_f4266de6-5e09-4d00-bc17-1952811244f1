package androidx.activity.PipHintTrackerKt;
import android.view.View;
import android.graphics.Rect;
import android.app.Activity;
import tb.ar4;
import java.lang.Object;
import androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1;
import tb.u1a;
import tb.qp9;
import tb.yp9;
import androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$2;
import kotlinx.coroutines.flow.internal.ChannelFlow;
import tb.sp9;
import tb.dkf;
import tb.xhv;

public final class PipHintTrackerKt	// class@00046a from classes.dex
{

    public static final Rect access$trackPipAnimationHintView$positionInWindow(View p0){
       return PipHintTrackerKt.trackPipAnimationHintView$positionInWindow(p0);
    }
    public static final Object trackPipAnimationHintView(Activity p0,View p1,ar4 p2){
       if ((p0 = yp9.d(new PipHintTrackerKt$trackPipAnimationHintView$flow$1(p1, null)).a(new PipHintTrackerKt$trackPipAnimationHintView$2(p0), p2)) == dkf.d()) {
          return p0;
       }
       return xhv.INSTANCE;
    }
    private static final Rect trackPipAnimationHintView$positionInWindow(View p0){
       Rect rect = new Rect();
       p0.getGlobalVisibleRect(rect);
       return rect;
    }
}
