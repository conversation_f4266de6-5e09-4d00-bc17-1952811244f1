package kotlinx.coroutines.AwaitKt$joinAll$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.m;
import kotlinx.coroutines.AwaitKt;

public final class AwaitKt$joinAll$1 extends ContinuationImpl	// class@000489 from classes11.dex
{
    public int I$0;
    public int I$1;
    public Object L$0;
    public int label;
    public Object result;

    public void AwaitKt$joinAll$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return AwaitKt.b(null, this);
    }
}
