package tb.abm;
import tb.mgb;
import tb.t2o;
import tb.e3b;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.fve;
import tb.f3b;

public class abm implements mgb	// class@001b1b from classes8.dex
{
    public final e3b a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1f6001ca);
       t2o.a(0x1f30002f);
    }
    public void abm(e3b p0){
       super();
       this.a = p0;
    }
    public void onAppToBackground(){
       abm ta;
       IpChange $ipChange = abm.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("938dadf2", objArray);
          return;
       }else {
          String[] stringArray = new String[]{"onAppToBackground"};
          fve.e("PopAppLifecycleListener", stringArray);
          if ((ta = this.a) != null) {
             ta.j().f();
          }
          return;
       }
    }
    public void onAppToFront(){
       abm ta;
       IpChange $ipChange = abm.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("c0c238d3", objArray);
          return;
       }else {
          String[] stringArray = new String[]{"onAppToFront"};
          fve.e("PopAppLifecycleListener", stringArray);
          if ((ta = this.a) != null) {
             ta.j().g();
          }
          return;
       }
    }
}
