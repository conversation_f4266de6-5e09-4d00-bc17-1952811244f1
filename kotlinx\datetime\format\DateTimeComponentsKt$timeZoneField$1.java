package kotlinx.datetime.format.DateTimeComponentsKt$timeZoneField$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.tf20;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;

public final class DateTimeComponentsKt$timeZoneField$1 extends MutablePropertyReference1Impl	// class@0006e2 from classes11.dex
{
    public static final DateTimeComponentsKt$timeZoneField$1 INSTANCE;

    static {
       DateTimeComponentsKt$timeZoneField$1.INSTANCE = new DateTimeComponentsKt$timeZoneField$1();
    }
    public void DateTimeComponentsKt$timeZoneField$1(){
       super(tf20.class, "timeZoneId", "getTimeZoneId\(\)Ljava/lang/String;", 0);
    }
    public Object get(Object p0){
       return p0.E();
    }
    public void set(Object p0,Object p1){
       p0.F(p1);
    }
}
