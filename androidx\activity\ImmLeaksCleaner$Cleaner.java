package androidx.activity.ImmLeaksCleaner$Cleaner;
import java.lang.Object;
import tb.a07;
import android.view.inputmethod.InputMethodManager;
import android.view.View;

public abstract class ImmLeaksCleaner$Cleaner	// class@00044f from classes.dex
{

    private void ImmLeaksCleaner$Cleaner(){
       super();
    }
    public void ImmLeaksCleaner$Cleaner(a07 p0){
       super();
    }
    public abstract boolean clearNextServedView(InputMethodManager p0);
    public abstract Object getLock(InputMethodManager p0);
    public abstract View getServedView(InputMethodManager p0);
}
