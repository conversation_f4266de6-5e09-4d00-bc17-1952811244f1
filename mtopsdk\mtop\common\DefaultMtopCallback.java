package mtopsdk.mtop.common.DefaultMtopCallback;
import mtopsdk.mtop.common.MtopCallback$MtopProgressListener;
import mtopsdk.mtop.common.MtopCallback$MtopFinishListener;
import mtopsdk.mtop.common.MtopCallback$MtopHeaderListener;
import tb.t2o;
import java.lang.Object;
import mtopsdk.mtop.common.MtopProgressEvent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import java.lang.StringBuilder;
import mtopsdk.mtop.common.MtopFinishEvent;
import mtopsdk.mtop.domain.MtopResponse;
import mtopsdk.mtop.common.MtopHeaderEvent;

public class DefaultMtopCallback implements MtopCallback$MtopProgressListener, MtopCallback$MtopFinishListener, MtopCallback$MtopHeaderListener	// class@00079e from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x253000b1);
       t2o.a(0x253000b8);
       t2o.a(0x253000b6);
       t2o.a(0x253000b7);
    }
    public void DefaultMtopCallback(){
       super();
    }
    public void onDataReceived(MtopProgressEvent p0,Object p1){
       IpChange $ipChange = DefaultMtopCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4ea8f321", objArray);
          return;
       }else if(p0 != null && TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)){
          TBSdkLog.d("mtopsdk.DefaultMtopCallback", p0.seqNo, "[onDataReceived]"+p0.toString());
       }
       return;
    }
    public void onFinished(MtopFinishEvent p0,Object p1){
       IpChange $ipChange = DefaultMtopCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("732e17e0", objArray);
          return;
       }else if(p0 != null && (p0.getMtopResponse() != null && TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable))){
          TBSdkLog.d("mtopsdk.DefaultMtopCallback", p0.seqNo, "[onFinished]"+p0.getMtopResponse().toString());
       }
       return;
    }
    public void onHeader(MtopHeaderEvent p0,Object p1){
       IpChange $ipChange = DefaultMtopCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("34d28e9f", objArray);
          return;
       }else if(p0 != null && TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)){
          TBSdkLog.d("mtopsdk.DefaultMtopCallback", p0.seqNo, "[onHeader]"+p0.toString());
       }
       return;
    }
}
