package androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.app.AppCompatDelegate$SerialExecutor;
import androidx.appcompat.app.AppCompatDelegate$ThreadPerTaskExecutor;
import java.util.concurrent.Executor;
import androidx.collection.ArraySet;
import java.lang.Object;
import android.content.Context;
import java.lang.ref.WeakReference;
import java.util.Iterator;
import java.lang.ref.Reference;
import android.app.Activity;
import androidx.appcompat.app.AppCompatCallback;
import androidx.appcompat.app.AppCompatDelegateImpl;
import android.app.Dialog;
import android.view.Window;
import androidx.core.os.LocaleListCompat;
import android.os.Build$VERSION;
import android.os.LocaleList;
import androidx.appcompat.app.AppCompatDelegate$Api33Impl;
import java.lang.String;
import android.content.pm.ServiceInfo;
import androidx.appcompat.app.AppLocalesMetadataHolderService;
import android.os.Bundle;
import java.lang.Boolean;
import androidx.appcompat.widget.VectorEnabledTintResources;
import java.util.Objects;
import androidx.appcompat.app.AppCompatDelegate$Api24Impl;
import android.content.ComponentName;
import android.content.pm.PackageManager;
import androidx.core.app.AppLocalesStorageHelper;
import tb.e31;
import java.lang.Runnable;
import android.view.View;
import android.view.ViewGroup$LayoutParams;
import tb.f31;
import tb.acq;
import android.util.AttributeSet;
import androidx.appcompat.app.ActionBarDrawerToggle$Delegate;
import android.view.MenuInflater;
import androidx.appcompat.app.ActionBar;
import android.content.res.Configuration;
import android.window.OnBackInvokedDispatcher;
import androidx.appcompat.widget.Toolbar;
import java.lang.CharSequence;
import androidx.appcompat.view.ActionMode$Callback;
import androidx.appcompat.view.ActionMode;

public abstract class AppCompatDelegate	// class@00055c from classes.dex
{
    public static final String APP_LOCALES_META_DATA_HOLDER_SERVICE_NAME = "androidx.appcompat.app.AppLocalesMetadataHolderService";
    public static final boolean DEBUG = false;
    public static final int FEATURE_ACTION_MODE_OVERLAY;
    public static final int FEATURE_SUPPORT_ACTION_BAR;
    public static final int FEATURE_SUPPORT_ACTION_BAR_OVERLAY;
    public static final int MODE_NIGHT_AUTO;
    public static final int MODE_NIGHT_AUTO_BATTERY;
    public static final int MODE_NIGHT_AUTO_TIME;
    public static final int MODE_NIGHT_FOLLOW_SYSTEM;
    public static final int MODE_NIGHT_NO;
    public static final int MODE_NIGHT_UNSPECIFIED;
    public static final int MODE_NIGHT_YES;
    public static final String TAG;
    private static final ArraySet sActivityDelegates;
    private static final Object sActivityDelegatesLock;
    private static final Object sAppLocalesStorageSyncLock;
    private static int sDefaultNightMode;
    private static Boolean sIsAutoStoreLocalesOptedIn;
    private static boolean sIsFrameworkSyncChecked;
    private static LocaleListCompat sRequestedAppLocales;
    public static AppCompatDelegate$SerialExecutor sSerialExecutorForLocalesStorage;
    private static LocaleListCompat sStoredAppLocales;

    static {
       AppCompatDelegate.sSerialExecutorForLocalesStorage = new AppCompatDelegate$SerialExecutor(new AppCompatDelegate$ThreadPerTaskExecutor());
       AppCompatDelegate.sDefaultNightMode = -100;
       AppCompatDelegate.sRequestedAppLocales = null;
       AppCompatDelegate.sStoredAppLocales = null;
       AppCompatDelegate.sIsAutoStoreLocalesOptedIn = null;
       AppCompatDelegate.sIsFrameworkSyncChecked = false;
       AppCompatDelegate.sActivityDelegates = new ArraySet();
       AppCompatDelegate.sActivityDelegatesLock = new Object();
       AppCompatDelegate.sAppLocalesStorageSyncLock = new Object();
    }
    public void AppCompatDelegate(){
       super();
    }
    public static void a(Context p0){
       AppCompatDelegate.lambda$syncRequestedAndStoredLocales$1(p0);
    }
    public static void addActiveDelegate(AppCompatDelegate p0){
       Object sActivityDel = AppCompatDelegate.sActivityDelegatesLock;
       _monitor_enter(sActivityDel);
       AppCompatDelegate.removeDelegateFromActives(p0);
       AppCompatDelegate.sActivityDelegates.add(new WeakReference(p0));
       _monitor_exit(sActivityDel);
    }
    private static void applyDayNightToActiveDelegates(){
       AppCompatDelegate uAppCompatDe;
       Object sActivityDel = AppCompatDelegate.sActivityDelegatesLock;
       _monitor_enter(sActivityDel);
       Iterator iterator = AppCompatDelegate.sActivityDelegates.iterator();
       while (iterator.hasNext()) {
          if ((uAppCompatDe = iterator.next().get()) != null) {
             uAppCompatDe.applyDayNight();
          }
       }
       _monitor_exit(sActivityDel);
       return;
    }
    private static void applyLocalesToActiveDelegates(){
       AppCompatDelegate uAppCompatDe;
       Iterator iterator = AppCompatDelegate.sActivityDelegates.iterator();
       while (iterator.hasNext()) {
          if ((uAppCompatDe = iterator.next().get()) != null) {
             uAppCompatDe.applyAppLocales();
          }
       }
       return;
    }
    public static void b(Context p0){
       AppCompatDelegate.lambda$asyncExecuteSyncRequestedAndStoredLocales$0(p0);
    }
    public static AppCompatDelegate create(Activity p0,AppCompatCallback p1){
       return new AppCompatDelegateImpl(p0, p1);
    }
    public static AppCompatDelegate create(Dialog p0,AppCompatCallback p1){
       return new AppCompatDelegateImpl(p0, p1);
    }
    public static AppCompatDelegate create(Context p0,Activity p1,AppCompatCallback p2){
       return new AppCompatDelegateImpl(p0, p1, p2);
    }
    public static AppCompatDelegate create(Context p0,Window p1,AppCompatCallback p2){
       return new AppCompatDelegateImpl(p0, p1, p2);
    }
    public static LocaleListCompat getApplicationLocales(){
       LocaleListCompat localeManage;
       if (Build$VERSION.SDK_INT >= 33) {
          if ((localeManage = AppCompatDelegate.getLocaleManagerForApplication()) != null) {
             return LocaleListCompat.wrap(AppCompatDelegate$Api33Impl.localeManagerGetApplicationLocales(localeManage));
          }
       }else if((localeManage = AppCompatDelegate.sRequestedAppLocales) != null){
          return localeManage;
       }
       return LocaleListCompat.getEmptyLocaleList();
    }
    public static int getDefaultNightMode(){
       return AppCompatDelegate.sDefaultNightMode;
    }
    public static Object getLocaleManagerForApplication(){
       AppCompatDelegate uAppCompatDe;
       Context contextForDe;
       Iterator iterator = AppCompatDelegate.sActivityDelegates.iterator();
       while (true) {
          if (!iterator.hasNext()) {
             return null;
          }
          if ((uAppCompatDe = iterator.next().get()) != null && (contextForDe = uAppCompatDe.getContextForDelegate()) != null) {
             break ;
          }
       }
       return contextForDe.getSystemService("locale");
    }
    public static LocaleListCompat getRequestedAppLocales(){
       return AppCompatDelegate.sRequestedAppLocales;
    }
    public static LocaleListCompat getStoredAppLocales(){
       return AppCompatDelegate.sStoredAppLocales;
    }
    public static boolean isAutoStorageOptedIn(Context p0){
       ServiceInfo metaData;
       try{
          if (AppCompatDelegate.sIsAutoStoreLocalesOptedIn == null && (metaData = AppLocalesMetadataHolderService.getServiceInfo(p0).metaData) != null) {
             AppCompatDelegate.sIsAutoStoreLocalesOptedIn = Boolean.valueOf(metaData.getBoolean("autoStoreLocales"));
          }
       }catch(android.content.pm.PackageManager$NameNotFoundException e0){
          AppCompatDelegate.sIsAutoStoreLocalesOptedIn = Boolean.FALSE;
       }
       return AppCompatDelegate.sIsAutoStoreLocalesOptedIn.booleanValue();
    }
    public static boolean isCompatVectorFromResourcesEnabled(){
       return VectorEnabledTintResources.isCompatVectorFromResourcesEnabled();
    }
    private static void lambda$asyncExecuteSyncRequestedAndStoredLocales$0(Context p0){
       AppCompatDelegate.syncRequestedAndStoredLocales(p0);
    }
    private static void lambda$syncRequestedAndStoredLocales$1(Context p0){
       AppCompatDelegate.syncLocalesToFramework(p0);
       AppCompatDelegate.sIsFrameworkSyncChecked = true;
    }
    public static void removeActivityDelegate(AppCompatDelegate p0){
       Object sActivityDel = AppCompatDelegate.sActivityDelegatesLock;
       _monitor_enter(sActivityDel);
       AppCompatDelegate.removeDelegateFromActives(p0);
       _monitor_exit(sActivityDel);
    }
    private static void removeDelegateFromActives(AppCompatDelegate p0){
       AppCompatDelegate uAppCompatDe;
       Object sActivityDel = AppCompatDelegate.sActivityDelegatesLock;
       _monitor_enter(sActivityDel);
       Iterator iterator = AppCompatDelegate.sActivityDelegates.iterator();
       while (iterator.hasNext()) {
          if ((uAppCompatDe = iterator.next().get()) != p0 && uAppCompatDe != null) {
             continue ;
          }else {
             iterator.remove();
          }
       }
       _monitor_exit(sActivityDel);
       return;
    }
    public static void resetStaticRequestedAndStoredLocales(){
       AppCompatDelegate.sRequestedAppLocales = null;
       AppCompatDelegate.sStoredAppLocales = null;
    }
    public static void setApplicationLocales(LocaleListCompat p0){
       Object localeManage;
       Objects.requireNonNull(p0);
       if (Build$VERSION.SDK_INT >= 33) {
          if ((localeManage = AppCompatDelegate.getLocaleManagerForApplication()) != null) {
             AppCompatDelegate$Api33Impl.localeManagerSetApplicationLocales(localeManage, AppCompatDelegate$Api24Impl.localeListForLanguageTags(p0.toLanguageTags()));
          }
       }else if(!p0.equals(AppCompatDelegate.sRequestedAppLocales)){
          localeManage = AppCompatDelegate.sActivityDelegatesLock;
          _monitor_enter(localeManage);
          AppCompatDelegate.sRequestedAppLocales = p0;
          AppCompatDelegate.applyLocalesToActiveDelegates();
          _monitor_exit(localeManage);
       }
       return;
    }
    public static void setCompatVectorFromResourcesEnabled(boolean p0){
       VectorEnabledTintResources.setCompatVectorFromResourcesEnabled(p0);
    }
    public static void setDefaultNightMode(int p0){
       if (p0 == -1 || (!p0 || (p0 == 1 || (p0 == 2 || (p0 == 3 && AppCompatDelegate.sDefaultNightMode != p0))))) {
          AppCompatDelegate.sDefaultNightMode = p0;
          AppCompatDelegate.applyDayNightToActiveDelegates();
       }
       return;
    }
    public static void setIsAutoStoreLocalesOptedIn(boolean p0){
       AppCompatDelegate.sIsAutoStoreLocalesOptedIn = Boolean.valueOf(p0);
    }
    public static void syncLocalesToFramework(Context p0){
       Object systemServic;
       if (Build$VERSION.SDK_INT >= 33) {
          ComponentName uComponentNa = new ComponentName(p0, "androidx.appcompat.app.AppLocalesMetadataHolderService");
          if (p0.getPackageManager().getComponentEnabledSetting(uComponentNa) != 1) {
             if (AppCompatDelegate.getApplicationLocales().isEmpty()) {
                String str = AppLocalesStorageHelper.readLocales(p0);
                if ((systemServic = p0.getSystemService("locale")) != null) {
                   AppCompatDelegate$Api33Impl.localeManagerSetApplicationLocales(systemServic, AppCompatDelegate$Api24Impl.localeListForLanguageTags(str));
                }
             }
             p0.getPackageManager().setComponentEnabledSetting(uComponentNa, 1, 1);
          }
       }
       return;
    }
    public static void syncRequestedAndStoredLocales(Context p0){
       LocaleListCompat sRequestedAp;
       if (!AppCompatDelegate.isAutoStorageOptedIn(p0)) {
          return;
       }
       if (Build$VERSION.SDK_INT >= 33) {
          if (!AppCompatDelegate.sIsFrameworkSyncChecked) {
             AppCompatDelegate.sSerialExecutorForLocalesStorage.execute(new e31(p0));
          }
       }else {
          Object sAppLocalesS = AppCompatDelegate.sAppLocalesStorageSyncLock;
          _monitor_enter(sAppLocalesS);
          if ((sRequestedAp = AppCompatDelegate.sRequestedAppLocales) == null) {
             if (AppCompatDelegate.sStoredAppLocales == null) {
                AppCompatDelegate.sStoredAppLocales = LocaleListCompat.forLanguageTags(AppLocalesStorageHelper.readLocales(p0));
             }
             if (AppCompatDelegate.sStoredAppLocales.isEmpty()) {
                _monitor_exit(sAppLocalesS);
                return;
             }else {
                AppCompatDelegate.sRequestedAppLocales = AppCompatDelegate.sStoredAppLocales;
             }
          }else if(!sRequestedAp.equals(AppCompatDelegate.sStoredAppLocales)){
             sRequestedAp = AppCompatDelegate.sRequestedAppLocales;
             AppCompatDelegate.sStoredAppLocales = sRequestedAp;
             AppLocalesStorageHelper.persistLocales(p0, sRequestedAp.toLanguageTags());
          }
          _monitor_exit(sAppLocalesS);
       }
       return;
    }
    public abstract void addContentView(View p0,ViewGroup$LayoutParams p1);
    public boolean applyAppLocales(){
       return false;
    }
    public abstract boolean applyDayNight();
    public void asyncExecuteSyncRequestedAndStoredLocales(Context p0){
       AppCompatDelegate.sSerialExecutorForLocalesStorage.execute(new f31(p0));
    }
    public void attachBaseContext(Context p0){
       acq.B(p0);
    }
    public Context attachBaseContext2(Context p0){
       this.attachBaseContext(p0);
       return p0;
    }
    public abstract View createView(View p0,String p1,Context p2,AttributeSet p3);
    public abstract View findViewById(int p0);
    public Context getContextForDelegate(){
       return null;
    }
    public abstract ActionBarDrawerToggle$Delegate getDrawerToggleDelegate();
    public int getLocalNightMode(){
       return -100;
    }
    public abstract MenuInflater getMenuInflater();
    public abstract ActionBar getSupportActionBar();
    public abstract boolean hasWindowFeature(int p0);
    public abstract void installViewFactory();
    public abstract void invalidateOptionsMenu();
    public abstract boolean isHandleNativeActionModesEnabled();
    public abstract void onConfigurationChanged(Configuration p0);
    public abstract void onCreate(Bundle p0);
    public abstract void onDestroy();
    public abstract void onPostCreate(Bundle p0);
    public abstract void onPostResume();
    public abstract void onSaveInstanceState(Bundle p0);
    public abstract void onStart();
    public abstract void onStop();
    public abstract boolean requestWindowFeature(int p0);
    public abstract void setContentView(int p0);
    public abstract void setContentView(View p0);
    public abstract void setContentView(View p0,ViewGroup$LayoutParams p1);
    public abstract void setHandleNativeActionModesEnabled(boolean p0);
    public abstract void setLocalNightMode(int p0);
    public void setOnBackInvokedDispatcher(OnBackInvokedDispatcher p0){
    }
    public abstract void setSupportActionBar(Toolbar p0);
    public void setTheme(int p0){
    }
    public abstract void setTitle(CharSequence p0);
    public abstract ActionMode startSupportActionMode(ActionMode$Callback p0);
}
