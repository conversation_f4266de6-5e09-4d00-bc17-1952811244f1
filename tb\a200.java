package tb.a200;
import tb.v300;
import tb.t2o;
import tb.bbs;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import java.util.ArrayList;
import com.android.alibaba.ip.runtime.IpChange;
import tb.v300$a;
import java.util.Map;
import tb.xhv;
import kotlin.Pair;
import tb.v300$b;
import tb.v300$b$a;
import tb.bbs$d;
import tb.eas;
import com.taobao.themis.kernel.TMSContainerType;
import java.util.Iterator;
import java.lang.Iterable;

public final class a200 implements v300	// class@001e5e from classes10.dex
{
    public final bbs a;
    public final List b;
    public bbs$d c;
    public boolean d;
    public final Object e;
    public static IpChange $ipChange;

    static {
       t2o.a(0x349000ae);
       t2o.a(0x349000b9);
    }
    public void a200(bbs p0){
       ckf.g(p0, "instance");
       super();
       this.a = p0;
       this.b = new ArrayList();
       this.e = new Object();
    }
    public void N(bbs p0){
       IpChange $ipChange = a200.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3dc73485", objArray);
          return;
       }else {
          v300$a.a(this, p0);
          return;
       }
    }
    public void a(String p0,Map p1){
       IpChange $ipChange = a200.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("77b7f3ff", objArray);
          return;
       }else {
          ckf.g(p0, "type");
          a200 te = this.e;
          _monitor_enter(te);
          if (this.d != null) {
             this.b(p0, p1);
             xhv iNSTANCE = xhv.INSTANCE;
          }else {
             this.b.add(new Pair(v300$b.e(p0), p1));
          }
          _monitor_exit(te);
          return;
       }
    }
    public final void b(String p0,Map p1){
       a200 tc;
       a200 tc1;
       IpChange $ipChange = a200.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("a24c8fe8", objArray);
          return;
       }else {
          v300$b$a companion = v300$b.Companion;
          if (v300$b.h(p0, companion.a())) {
             if ((tc = this.c) != null) {
                tc.b();
             }
          }else if(v300$b.h(p0, companion.c())){
             if ((tc = this.c) != null) {
                tc.onRenderReady();
             }
          }else if(v300$b.h(p0, companion.d())){
             if ((tc = this.c) != null) {
                tc.a();
             }
          }else if(v300$b.h(p0, companion.b())){
             eas uoeas = null;
             int i = (p1 == null)? uoeas: p1.get("tmsError");
             if (i instanceof eas) {
                uoeas = i;
             }
             if (uoeas == null) {
                return;
             }else if((tc1 = this.c) == null){
                tc1.c(uoeas);
             }
          }
          return;
       }
    }
    public void c(){
       IpChange $ipChange = a200.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("896696a2", objArray);
          return;
       }else {
          v300$a.b(this);
          return;
       }
    }
    public void c(bbs$d p0){
       int i = 1;
       IpChange $ipChange = a200.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5f4c1041", objArray);
          return;
       }else {
          a200 te = this.e;
          _monitor_enter(te);
          if (this.a.O() != TMSContainerType.EMBEDDED) {
             _monitor_exit(te);
             return;
          }else {
             this.c = p0;
             this.d = i;
             Iterator iterator = this.b.iterator();
             while (iterator.hasNext()) {
                Pair pair = iterator.next();
                this.b(pair.getFirst().k(), pair.getSecond());
             }
             this.b.clear();
             _monitor_exit(te);
             return;
          }
       }
    }
}
