package kotlinx.coroutines.CoroutineDispatcher;
import kotlin.coroutines.c;
import kotlin.coroutines.a;
import kotlinx.coroutines.CoroutineDispatcher$Key;
import tb.a07;
import kotlin.coroutines.d$c;
import kotlin.coroutines.d;
import java.lang.Runnable;
import kotlin.coroutines.d$b;
import kotlin.coroutines.c$a;
import tb.ar4;
import tb.uq7;
import tb.apg;
import tb.zog;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import java.lang.StringBuilder;
import tb.ov6;

public abstract class CoroutineDispatcher extends a implements c	// class@000493 from classes11.dex
{
    public static final CoroutineDispatcher$Key Key;

    static {
       CoroutineDispatcher.Key = new CoroutineDispatcher$Key(null);
    }
    public void CoroutineDispatcher(){
       super(c.Key);
    }
    public abstract void dispatch(d p0,Runnable p1);
    public void dispatchYield(d p0,Runnable p1){
       this.dispatch(p0, p1);
    }
    public d$b get(d$c p0){
       return c$a.a(this, p0);
    }
    public final ar4 interceptContinuation(ar4 p0){
       return new uq7(this, p0);
    }
    public boolean isDispatchNeeded(d p0){
       return true;
    }
    public CoroutineDispatcher limitedParallelism(int p0){
       apg.a(p0);
       return new zog(this, p0);
    }
    public d minusKey(d$c p0){
       return c$a.b(this, p0);
    }
    public final CoroutineDispatcher plus(CoroutineDispatcher p0){
       return p0;
    }
    public final void releaseInterceptedContinuation(ar4 p0){
       ckf.e(p0, "null cannot be cast to non-null type kotlinx.coroutines.internal.DispatchedContinuation<*>");
       p0.v();
    }
    public String toString(){
       return ov6.a(this)+'@'+ov6.b(this);
    }
}
