package androidx.activity.OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.activity.Cancellable;
import androidx.activity.OnBackPressedDispatcher;
import androidx.lifecycle.Lifecycle;
import androidx.activity.OnBackPressedCallback;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Lifecycle$Event;

public final class OnBackPressedDispatcher$LifecycleOnBackPressedCancellable implements LifecycleEventObserver, Cancellable	// class@00045e from classes.dex
{
    private Cancellable currentCancellable;
    private final Lifecycle lifecycle;
    private final OnBackPressedCallback onBackPressedCallback;
    public final OnBackPressedDispatcher this$0;

    public void OnBackPressedDispatcher$LifecycleOnBackPressedCancellable(OnBackPressedDispatcher p0,Lifecycle p1,OnBackPressedCallback p2){
       ckf.g(p1, "lifecycle");
       ckf.g(p2, "onBackPressedCallback");
       this.this$0 = p0;
       super();
       this.lifecycle = p1;
       this.onBackPressedCallback = p2;
       p1.addObserver(this);
    }
    public void cancel(){
       OnBackPressedDispatcher$LifecycleOnBackPressedCancellable tcurrentCanc;
       this.lifecycle.removeObserver(this);
       this.onBackPressedCallback.removeCancellable(this);
       if ((tcurrentCanc = this.currentCancellable) != null) {
          tcurrentCanc.cancel();
       }
       this.currentCancellable = null;
       return;
    }
    public void onStateChanged(LifecycleOwner p0,Lifecycle$Event p1){
       OnBackPressedDispatcher$LifecycleOnBackPressedCancellable tcurrentCanc;
       ckf.g(p0, "source");
       ckf.g(p1, "event");
       if (p1 == Lifecycle$Event.ON_START) {
          this.currentCancellable = this.this$0.addCancellableCallback$activity_release(this.onBackPressedCallback);
       }else if(p1 == Lifecycle$Event.ON_STOP){
          if ((tcurrentCanc = this.currentCancellable) != null) {
             tcurrentCanc.cancel();
          }
       }else if(p1 == Lifecycle$Event.ON_DESTROY){
          this.cancel();
       }
       return;
    }
}
