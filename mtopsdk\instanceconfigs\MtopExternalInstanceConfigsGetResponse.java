package mtopsdk.instanceconfigs.MtopExternalInstanceConfigsGetResponse;
import mtopsdk.mtop.domain.BaseOutDo;
import tb.t2o;
import java.lang.Object;
import mtopsdk.instanceconfigs.MtopExternalInstanceConfigsData;

public class MtopExternalInstanceConfigsGetResponse extends BaseOutDo	// class@000780 from classes11.dex
{
    private MtopExternalInstanceConfigsData data;

    static {
       t2o.a(0x25300090);
    }
    public void MtopExternalInstanceConfigsGetResponse(){
       super();
    }
    public Object getData(){
       return this.getData();
    }
    public MtopExternalInstanceConfigsData getData(){
       return this.data;
    }
    public void setData(MtopExternalInstanceConfigsData p0){
       this.data = p0;
    }
}
