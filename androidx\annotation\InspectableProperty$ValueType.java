package androidx.annotation.InspectableProperty$ValueType;
import java.lang.Enum;
import java.lang.String;
import tb.fg8;
import kotlin.enums.a;
import java.lang.Class;
import java.lang.Object;

public final class InspectableProperty$ValueType extends Enum	// class@000504 from classes.dex
{
    private static final fg8 $ENTRIES;
    private static final InspectableProperty$ValueType[] $VALUES;
    public static final InspectableProperty$ValueType COLOR;
    public static final InspectableProperty$ValueType GRAVITY;
    public static final InspectableProperty$ValueType INFERRED;
    public static final InspectableProperty$ValueType INT_ENUM;
    public static final InspectableProperty$ValueType INT_FLAG;
    public static final InspectableProperty$ValueType NONE;
    public static final InspectableProperty$ValueType RESOURCE_ID;

    private static final InspectableProperty$ValueType[] $values(){
       InspectableProperty$ValueType[] valueTypeArr = new InspectableProperty$ValueType[]{InspectableProperty$ValueType.NONE,InspectableProperty$ValueType.INFERRED,InspectableProperty$ValueType.INT_ENUM,InspectableProperty$ValueType.INT_FLAG,InspectableProperty$ValueType.COLOR,InspectableProperty$ValueType.GRAVITY,InspectableProperty$ValueType.RESOURCE_ID};
       return valueTypeArr;
    }
    static {
       InspectableProperty$ValueType.NONE = new InspectableProperty$ValueType("NONE", 0);
       InspectableProperty$ValueType.INFERRED = new InspectableProperty$ValueType("INFERRED", 1);
       InspectableProperty$ValueType.INT_ENUM = new InspectableProperty$ValueType("INT_ENUM", 2);
       InspectableProperty$ValueType.INT_FLAG = new InspectableProperty$ValueType("INT_FLAG", 3);
       InspectableProperty$ValueType.COLOR = new InspectableProperty$ValueType("COLOR", 4);
       InspectableProperty$ValueType.GRAVITY = new InspectableProperty$ValueType("GRAVITY", 5);
       InspectableProperty$ValueType.RESOURCE_ID = new InspectableProperty$ValueType("RESOURCE_ID", 6);
       InspectableProperty$ValueType[] valueTypeArr = InspectableProperty$ValueType.$values();
       InspectableProperty$ValueType.$VALUES = valueTypeArr;
       InspectableProperty$ValueType.$ENTRIES = a.a(valueTypeArr);
    }
    private void InspectableProperty$ValueType(String p0,int p1){
       super(p0, p1);
    }
    public static fg8 getEntries(){
       return InspectableProperty$ValueType.$ENTRIES;
    }
    public static InspectableProperty$ValueType valueOf(String p0){
       return Enum.valueOf(InspectableProperty$ValueType.class, p0);
    }
    public static InspectableProperty$ValueType[] values(){
       return InspectableProperty$ValueType.$VALUES.clone();
    }
}
