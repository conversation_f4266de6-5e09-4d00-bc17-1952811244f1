package androidx.activity.BackEventCompat;
import androidx.activity.BackEventCompat$Companion;
import tb.a07;
import java.lang.Object;
import android.window.BackEvent;
import java.lang.String;
import tb.ckf;
import androidx.activity.Api34Impl;
import android.os.Build$VERSION;
import java.lang.UnsupportedOperationException;
import java.lang.StringBuilder;

public final class BackEventCompat	// class@000433 from classes.dex
{
    private final float progress;
    private final int swipeEdge;
    private final float touchX;
    private final float touchY;
    public static final BackEventCompat$Companion Companion;
    public static final int EDGE_LEFT;
    public static final int EDGE_RIGHT;

    static {
       BackEventCompat.Companion = new BackEventCompat$Companion(null);
    }
    public void BackEventCompat(float p0,float p1,float p2,int p3){
       super();
       this.touchX = p0;
       this.touchY = p1;
       this.progress = p2;
       this.swipeEdge = p3;
    }
    public void BackEventCompat(BackEvent p0){
       ckf.g(p0, "backEvent");
       Api34Impl iNSTANCE = Api34Impl.INSTANCE;
       super(iNSTANCE.touchX(p0), iNSTANCE.touchY(p0), iNSTANCE.progress(p0), iNSTANCE.swipeEdge(p0));
    }
    public final float getProgress(){
       return this.progress;
    }
    public final int getSwipeEdge(){
       return this.swipeEdge;
    }
    public final float getTouchX(){
       return this.touchX;
    }
    public final float getTouchY(){
       return this.touchY;
    }
    public final BackEvent toBackEvent(){
       if (Build$VERSION.SDK_INT >= 34) {
          return Api34Impl.INSTANCE.createOnBackEvent(this.touchX, this.touchY, this.progress, this.swipeEdge);
       }
       throw new UnsupportedOperationException("This method is only supported on API level 34+");
    }
    public String toString(){
       return "BackEventCompat{touchX="+this.touchX+", touchY="+this.touchY+", progress="+this.progress+", swipeEdge="+this.swipeEdge+'}';
    }
}
