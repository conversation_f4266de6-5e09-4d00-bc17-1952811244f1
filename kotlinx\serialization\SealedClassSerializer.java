package kotlinx.serialization.SealedClassSerializer;
import tb.b63;
import java.lang.String;
import tb.wyf;
import tb.x530;
import java.lang.Object;
import tb.ckf;
import java.util.List;
import tb.yz3;
import kotlin.LazyThreadSafetyMode;
import kotlinx.serialization.SealedClassSerializer$descriptor$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import tb.ic1;
import java.lang.Iterable;
import java.util.Map;
import kotlin.collections.a;
import java.util.Set;
import kotlinx.serialization.SealedClassSerializer$a;
import java.util.LinkedHashMap;
import java.util.Iterator;
import java.util.Map$Entry;
import java.lang.IllegalStateException;
import java.lang.StringBuilder;
import tb.v3i;
import java.lang.IllegalArgumentException;
import java.lang.annotation.Annotation;
import tb.hc1;
import tb.gn20;
import tb.qb40;
import java.lang.Class;
import tb.dun;
import tb.ga20;
import tb.qh20;
import kotlinx.serialization.descriptors.a;

public final class SealedClassSerializer extends b63	// class@000715 from classes11.dex
{
    private List _annotations;
    private final wyf baseClass;
    private final Map class2Serializer;
    private final njg descriptor$delegate;
    private final Map serialName2Serializer;

    public void SealedClassSerializer(String p0,wyf p1,wyf[] p2,x530[] p3){
       Object obj1;
       Object obj2;
       ckf.g(p0, "serialName");
       ckf.g(p1, "baseClass");
       ckf.g(p2, "subclasses");
       ckf.g(p3, "subclassSerializers");
       super();
       this.baseClass = p1;
       this._annotations = yz3.i();
       this.descriptor$delegate = a.a(LazyThreadSafetyMode.PUBLICATION, new SealedClassSerializer$descriptor$2(p0, this));
       if (p2.length != p3.length) {
          throw new IllegalArgumentException("All subclasses of sealed class "+this.getBaseClass().getSimpleName()+" should be marked @Serializable");
       }
       Map map = a.p(ic1.a1(p2, p3));
       this.class2Serializer = map;
       SealedClassSerializer$a uoa = new SealedClassSerializer$a(map.entrySet());
       LinkedHashMap linkedHashMa = new LinkedHashMap();
       Iterator iterator = uoa.b();
       while (true) {
          if (iterator.hasNext()) {
             p3 = iterator.next();
             Object obj = uoa.a(p3);
             if ((obj1 = linkedHashMa.get(obj)) == null) {
                linkedHashMa.containsKey(obj);
             }
             obj2 = obj;
             if (obj1 == null) {
                linkedHashMa.put(obj, p3);
             }else {
                break ;
             }
          }else {
             LinkedHashMap linkedHashMa1 = new LinkedHashMap(v3i.e(linkedHashMa.size()));
             Iterator iterator1 = linkedHashMa.entrySet().iterator();
             while (iterator1.hasNext()) {
                Map$Entry uEntry = iterator1.next();
                p3 = uEntry.getKey();
                linkedHashMa1.put(p3, uEntry.getValue().getValue());
             }
             this.serialName2Serializer = linkedHashMa1;
             return;
          }
       }
       throw new IllegalStateException("Multiple sealed subclasses of \'"+this.getBaseClass()+"\' have the same serial name \'"+obj2+"\': \'"+obj1.getKey()+"\', \'"+p3.getKey()+'''.toString());
    }
    public void SealedClassSerializer(String p0,wyf p1,wyf[] p2,x530[] p3,Annotation[] p4){
       ckf.g(p0, "serialName");
       ckf.g(p1, "baseClass");
       ckf.g(p2, "subclasses");
       ckf.g(p3, "subclassSerializers");
       ckf.g(p4, "classAnnotations");
       super(p0, p1, p2, p3);
       this._annotations = hc1.c(p4);
    }
    public static final Map access$getSerialName2Serializer$p(SealedClassSerializer p0){
       return p0.serialName2Serializer;
    }
    public static final List access$get_annotations$p(SealedClassSerializer p0){
       return p0._annotations;
    }
    public qb40 findPolymorphicSerializerOrNull(gn20 p0,Object p1){
       x530 ox530;
       ckf.g(p0, "encoder");
       ckf.g(p1, "value");
       if ((ox530 = this.class2Serializer.get(dun.b(p1.getClass()))) == null) {
          ox530 = super.findPolymorphicSerializerOrNull(p0, p1);
       }
       if (ox530 == null) {
          ox530 = null;
       }
       return ox530;
    }
    public qh20 findPolymorphicSerializerOrNull(ga20 p0,String p1){
       x530 ox530;
       ckf.g(p0, "decoder");
       if ((ox530 = this.serialName2Serializer.get(p1)) == null) {
          ox530 = super.findPolymorphicSerializerOrNull(p0, p1);
       }
       return ox530;
    }
    public wyf getBaseClass(){
       return this.baseClass;
    }
    public a getDescriptor(){
       return this.descriptor$delegate.getValue();
    }
}
