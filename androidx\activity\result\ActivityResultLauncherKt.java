package androidx.activity.result.ActivityResultLauncherKt;
import androidx.activity.result.ActivityResultLauncher;
import androidx.core.app.ActivityOptionsCompat;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.xhv;

public final class ActivityResultLauncherKt	// class@0004b3 from classes.dex
{

    public static final void launch(ActivityResultLauncher p0,ActivityOptionsCompat p1){
       ckf.g(p0, "<this>");
       p0.launch(null, p1);
    }
    public static void launch$default(ActivityResultLauncher p0,ActivityOptionsCompat p1,int p2,Object p3){
       if ((p2 & 0x01)) {
          p1 = null;
       }
       ActivityResultLauncherKt.launch(p0, p1);
       return;
    }
    public static final void launchUnit(ActivityResultLauncher p0,ActivityOptionsCompat p1){
       ckf.g(p0, "<this>");
       p0.launch(xhv.INSTANCE, p1);
    }
    public static void launchUnit$default(ActivityResultLauncher p0,ActivityOptionsCompat p1,int p2,Object p3){
       if ((p2 & 0x01)) {
          p1 = null;
       }
       ActivityResultLauncherKt.launchUnit(p0, p1);
       return;
    }
}
