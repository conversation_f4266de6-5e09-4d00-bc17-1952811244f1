package androidx.activity.ImmLeaksCleaner$FailedInitialization;
import androidx.activity.ImmLeaksCleaner$Cleaner;
import tb.a07;
import android.view.inputmethod.InputMethodManager;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.view.View;

public final class ImmLeaksCleaner$FailedInitialization extends ImmLeaksCleaner$Cleaner	// class@000452 from classes.dex
{
    public static final ImmLeaksCleaner$FailedInitialization INSTANCE;

    static {
       ImmLeaksCleaner$FailedInitialization.INSTANCE = new ImmLeaksCleaner$FailedInitialization();
    }
    private void ImmLeaksCleaner$FailedInitialization(){
       super(null);
    }
    public boolean clearNextServedView(InputMethodManager p0){
       ckf.g(p0, "<this>");
       return false;
    }
    public Object getLock(InputMethodManager p0){
       ckf.g(p0, "<this>");
       return null;
    }
    public View getServedView(InputMethodManager p0){
       ckf.g(p0, "<this>");
       return null;
    }
}
