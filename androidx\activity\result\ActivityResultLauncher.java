package androidx.activity.result.ActivityResultLauncher;
import java.lang.Object;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.core.app.ActivityOptionsCompat;

public abstract class ActivityResultLauncher	// class@0004b2 from classes.dex
{

    public void ActivityResultLauncher(){
       super();
    }
    public abstract ActivityResultContract getContract();
    public void launch(Object p0){
       this.launch(p0, null);
    }
    public abstract void launch(Object p0,ActivityOptionsCompat p1);
    public abstract void unregister();
}
