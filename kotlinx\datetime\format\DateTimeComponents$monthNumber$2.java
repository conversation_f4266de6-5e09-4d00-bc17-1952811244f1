package kotlinx.datetime.format.DateTimeComponents$monthNumber$2;
import kotlin.jvm.internal.MutablePropertyReference0Impl;
import java.lang.Object;
import tb.b230;
import java.lang.Class;
import java.lang.String;
import kotlin.jvm.internal.CallableReference;
import java.lang.Integer;

public final class DateTimeComponents$monthNumber$2 extends MutablePropertyReference0Impl	// class@0006dd from classes11.dex
{

    public void DateTimeComponents$monthNumber$2(Object p0){
       super(p0, b230.class, "monthNumber", "getMonthNumber\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(){
       return this.receiver.x();
    }
    public void set(Object p0){
       this.receiver.l(p0);
    }
}
