package tb.a4a;
import tb.pr6;
import tb.t2o;
import android.content.Context;
import tb.ze7;
import java.lang.Object;
import com.alibaba.fastjson.JSONObject;
import tb.wua;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.android.detail.ttdetail.utils.DynamicMergeUtils;
import tb.vbl;
import tb.bq6;
import com.taobao.android.detail.ttdetail.utils.DataUtils;
import java.lang.StringBuilder;
import java.lang.Throwable;
import android.util.Log;
import tb.tgh;
import com.taobao.android.detail.ttdetail.handler.AbilityCenter;
import tb.qdb;

public class a4a implements pr6	// class@001833 from classes5.dex
{
    public final ze7 a;
    public static IpChange $ipChange;
    public static final String PARSER_ID;

    static {
       t2o.a(0x38e001fd);
       t2o.a(0x38e001fb);
    }
    public void a4a(Context p0,ze7 p1){
       super();
       this.a = p1;
    }
    public Object a(JSONObject p0){
       return this.b(p0);
    }
    public wua b(JSONObject p0){
       IpChange $ipChange = a4a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c(DynamicMergeUtils.h(p0));
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("163c9229", objArray);
    }
    public final wua c(JSONObject p0){
       IpChange $ipChange = a4a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("b03364ec", objArray);
       }else {
          wua owua = null;
          if (p0 == null) {
             return owua;
          }
          if ((p0 = p0.getJSONObject("model")) == null) {
             return owua;
          }
          if ((p0 = p0.getJSONObject("headerPic")) == null) {
             return owua;
          }
          if (vbl.i0() && DataUtils.b0(this.a.e())) {
             DataUtils.a(p0);
          }
          return new wua(p0, this.a.b().d());
       }
    }
}
