package androidx.appcompat.app.WindowDecorActionBar;
import androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback;
import androidx.appcompat.app.ActionBar;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.app.Activity;
import java.util.ArrayList;
import androidx.appcompat.app.WindowDecorActionBar$1;
import androidx.appcompat.app.WindowDecorActionBar$2;
import androidx.appcompat.app.WindowDecorActionBar$3;
import android.view.Window;
import android.view.View;
import android.app.Dialog;
import androidx.appcompat.app.ActionBar$Tab;
import androidx.appcompat.widget.ScrollingTabContainerView;
import androidx.appcompat.app.WindowDecorActionBar$TabImpl;
import androidx.appcompat.app.ActionBar$TabListener;
import java.lang.Object;
import java.lang.IllegalStateException;
import java.lang.String;
import android.content.Context;
import androidx.appcompat.widget.DecorToolbar;
import androidx.core.view.ViewCompat;
import androidx.appcompat.widget.ActionBarContainer;
import androidx.appcompat.widget.Toolbar;
import java.lang.Class;
import androidx.appcompat.widget.ActionBarOverlayLayout;
import com.taobao.taobao.R$id;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.appcompat.view.ActionBarPolicy;
import com.taobao.taobao.R$styleable;
import com.taobao.taobao.R$attr;
import android.util.AttributeSet;
import android.content.res.TypedArray;
import androidx.appcompat.app.ActionBar$OnMenuVisibilityListener;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.appcompat.view.ViewPropertyAnimatorCompatSet;
import androidx.appcompat.view.ActionMode;
import androidx.appcompat.view.ActionMode$Callback;
import androidx.core.view.ViewPropertyAnimatorUpdateListener;
import android.view.animation.Interpolator;
import androidx.core.view.ViewPropertyAnimatorListener;
import java.lang.CharSequence;
import android.util.TypedValue;
import android.content.res.Resources$Theme;
import android.view.ContextThemeWrapper;
import android.content.res.Configuration;
import android.view.KeyEvent;
import android.view.Menu;
import androidx.appcompat.app.WindowDecorActionBar$ActionModeImpl;
import android.view.KeyCharacterMap;
import java.lang.Math;
import android.view.ViewGroup;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import androidx.appcompat.app.ActionBar$LayoutParams;
import android.view.ViewGroup$LayoutParams;
import android.widget.SpinnerAdapter;
import androidx.appcompat.app.ActionBar$OnNavigationListener;
import androidx.appcompat.app.NavItemSelectedListener;
import android.widget.AdapterView$OnItemSelectedListener;

public class WindowDecorActionBar extends ActionBar implements ActionBarOverlayLayout$ActionBarVisibilityCallback	// class@000591 from classes.dex
{
    public WindowDecorActionBar$ActionModeImpl mActionMode;
    private Activity mActivity;
    public ActionBarContainer mContainerView;
    public boolean mContentAnimations;
    public View mContentView;
    public Context mContext;
    public ActionBarContextView mContextView;
    private int mCurWindowVisibility;
    public ViewPropertyAnimatorCompatSet mCurrentShowAnim;
    public DecorToolbar mDecorToolbar;
    public ActionMode mDeferredDestroyActionMode;
    public ActionMode$Callback mDeferredModeDestroyCallback;
    private boolean mDisplayHomeAsUpSet;
    private boolean mHasEmbeddedTabs;
    public boolean mHiddenByApp;
    public boolean mHiddenBySystem;
    public final ViewPropertyAnimatorListener mHideListener;
    public boolean mHideOnContentScroll;
    private boolean mLastMenuVisibility;
    private ArrayList mMenuVisibilityListeners;
    private boolean mNowShowing;
    public ActionBarOverlayLayout mOverlayLayout;
    private int mSavedTabPosition;
    private WindowDecorActionBar$TabImpl mSelectedTab;
    private boolean mShowHideAnimationEnabled;
    public final ViewPropertyAnimatorListener mShowListener;
    private boolean mShowingForMode;
    public ScrollingTabContainerView mTabScrollView;
    private ArrayList mTabs;
    private Context mThemedContext;
    public final ViewPropertyAnimatorUpdateListener mUpdateListener;
    private static final long FADE_IN_DURATION_MS = 0xc8;
    private static final long FADE_OUT_DURATION_MS = 0x64;
    private static final int INVALID_POSITION = 255;
    private static final String TAG = "WindowDecorActionBar";
    private static final Interpolator sHideInterpolator;
    private static final Interpolator sShowInterpolator;

    static {
       WindowDecorActionBar.sHideInterpolator = new AccelerateInterpolator();
       WindowDecorActionBar.sShowInterpolator = new DecelerateInterpolator();
    }
    public void WindowDecorActionBar(Activity p0,boolean p1){
       super();
       this.mTabs = new ArrayList();
       this.mSavedTabPosition = -1;
       this.mMenuVisibilityListeners = new ArrayList();
       this.mCurWindowVisibility = 0;
       this.mContentAnimations = true;
       this.mNowShowing = true;
       this.mHideListener = new WindowDecorActionBar$1(this);
       this.mShowListener = new WindowDecorActionBar$2(this);
       this.mUpdateListener = new WindowDecorActionBar$3(this);
       this.mActivity = p0;
       View decorView = p0.getWindow().getDecorView();
       this.init(decorView);
       if (!p1) {
          this.mContentView = decorView.findViewById(0x1020002);
       }
       return;
    }
    public void WindowDecorActionBar(Dialog p0){
       super();
       this.mTabs = new ArrayList();
       this.mSavedTabPosition = -1;
       this.mMenuVisibilityListeners = new ArrayList();
       this.mCurWindowVisibility = 0;
       this.mContentAnimations = true;
       this.mNowShowing = true;
       this.mHideListener = new WindowDecorActionBar$1(this);
       this.mShowListener = new WindowDecorActionBar$2(this);
       this.mUpdateListener = new WindowDecorActionBar$3(this);
       this.init(p0.getWindow().getDecorView());
    }
    public void WindowDecorActionBar(View p0){
       super();
       this.mTabs = new ArrayList();
       this.mSavedTabPosition = -1;
       this.mMenuVisibilityListeners = new ArrayList();
       this.mCurWindowVisibility = 0;
       this.mContentAnimations = true;
       this.mNowShowing = true;
       this.mHideListener = new WindowDecorActionBar$1(this);
       this.mShowListener = new WindowDecorActionBar$2(this);
       this.mUpdateListener = new WindowDecorActionBar$3(this);
       this.init(p0);
    }
    public static boolean checkShowingFlags(boolean p0,boolean p1,boolean p2){
       if (p2) {
          return true;
       }
       if (!p0 && !p1) {
          return true;
       }
       return false;
    }
    private void cleanupTabs(){
       WindowDecorActionBar tmTabScrollV;
       if (this.mSelectedTab != null) {
          this.selectTab(null);
       }
       this.mTabs.clear();
       if ((tmTabScrollV = this.mTabScrollView) != null) {
          tmTabScrollV.removeAllTabs();
       }
       this.mSavedTabPosition = -1;
       return;
    }
    private void configureTab(ActionBar$Tab p0,int p1){
       if (p0.getCallback() == null) {
          throw new IllegalStateException("Action Bar Tab must have a Callback");
       }
       p0.setPosition(p1);
       this.mTabs.add(p1, p0);
       int i = this.mTabs.size();
       while ((p1++) < i) {
          this.mTabs.get(p1).setPosition(p1);
       }
       return;
    }
    private void ensureTabsExist(){
       WindowDecorActionBar tmOverlayLay;
       if (this.mTabScrollView != null) {
          return;
       }
       ScrollingTabContainerView scrollingTab = new ScrollingTabContainerView(this.mContext);
       if (this.mHasEmbeddedTabs != null) {
          scrollingTab.setVisibility(0);
          this.mDecorToolbar.setEmbeddedTabView(scrollingTab);
       }else if(this.getNavigationMode() == 2){
          scrollingTab.setVisibility(0);
          if ((tmOverlayLay = this.mOverlayLayout) != null) {
             ViewCompat.requestApplyInsets(tmOverlayLay);
          }
       }else {
          scrollingTab.setVisibility(8);
       }
       this.mContainerView.setTabContainer(scrollingTab);
       this.mTabScrollView = scrollingTab;
       return;
    }
    private DecorToolbar getDecorToolbar(View p0){
       if (p0 instanceof DecorToolbar) {
          return p0;
       }
       if (p0 instanceof Toolbar) {
          return p0.getWrapper();
       }
       String simpleName = (p0 != null)? p0.getClass().getSimpleName(): "null";
       throw new IllegalStateException("Can\'t make a decor toolbar out of ".concat(simpleName));
    }
    private void hideForActionMode(){
       WindowDecorActionBar tmOverlayLay;
       if (this.mShowingForMode != null) {
          this.mShowingForMode = false;
          if ((tmOverlayLay = this.mOverlayLayout) != null) {
             tmOverlayLay.setShowingForActionMode(false);
          }
          this.updateVisibility(false);
       }
       return;
    }
    private void init(View p0){
       WindowDecorActionBar tmDecorToolb;
       ActionBarOverlayLayout uActionBarOv = p0.findViewById(R$id.decor_content_parent);
       this.mOverlayLayout = uActionBarOv;
       if (uActionBarOv != null) {
          uActionBarOv.setActionBarVisibilityCallback(this);
       }
       this.mDecorToolbar = this.getDecorToolbar(p0.findViewById(R$id.action_bar));
       this.mContextView = p0.findViewById(R$id.action_context_bar);
       ActionBarContainer uActionBarCo = p0.findViewById(R$id.action_bar_container);
       this.mContainerView = uActionBarCo;
       if ((tmDecorToolb = this.mDecorToolbar) != null && (this.mContextView != null && uActionBarCo != null)) {
          this.mContext = tmDecorToolb.getContext();
          int b = true;
          uActionBarCo = ((this.mDecorToolbar.getDisplayOptions() & 0x04))? 1: 0;
          if (uActionBarCo) {
             this.mDisplayHomeAsUpSet = b;
          }
          ActionBarPolicy uActionBarPo = ActionBarPolicy.get(this.mContext);
          boolean b1 = (!uActionBarPo.enableHomeButtonByDefault() && !uActionBarCo)? false: true;
          this.setHomeButtonEnabled(b1);
          this.setHasEmbeddedTabs(uActionBarPo.hasEmbeddedTabs());
          TypedArray typedArray = this.mContext.obtainStyledAttributes(null, R$styleable.ActionBar, R$attr.actionBarStyle, 0);
          if (typedArray.getBoolean(R$styleable.ActionBar_hideOnContentScroll, 0)) {
             this.setHideOnContentScrollEnabled(b);
          }
          if (b = typedArray.getDimensionPixelSize(R$styleable.ActionBar_elevation, 0)) {
             this.setElevation((float)b);
          }
          typedArray.recycle();
          return;
       }else {
          throw new IllegalStateException(this.getClass().getSimpleName().concat(" can only be used with a compatible window decor layout"));
       }
    }
    private void setHasEmbeddedTabs(boolean p0){
       WindowDecorActionBar tmTabScrollV;
       this.mHasEmbeddedTabs = p0;
       ScrollingTabContainerView scrollingTab = null;
       if (!p0) {
          this.mDecorToolbar.setEmbeddedTabView(scrollingTab);
          this.mContainerView.setTabContainer(this.mTabScrollView);
       }else {
          this.mContainerView.setTabContainer(scrollingTab);
          this.mDecorToolbar.setEmbeddedTabView(this.mTabScrollView);
       }
       boolean b = true;
       int i = (this.getNavigationMode() == 2)? 1: 0;
       if ((tmTabScrollV = this.mTabScrollView) != null) {
          if (i) {
             tmTabScrollV.setVisibility(0);
             if ((tmTabScrollV = this.mOverlayLayout) != null) {
                ViewCompat.requestApplyInsets(tmTabScrollV);
             }
          }else {
             tmTabScrollV.setVisibility(8);
          }
       }
       tmTabScrollV = this.mDecorToolbar;
       boolean b1 = (this.mHasEmbeddedTabs == null && i)? true: false;
       tmTabScrollV.setCollapsible(b1);
       tmTabScrollV = this.mOverlayLayout;
       if (this.mHasEmbeddedTabs != null || !i) {
          b = false;
       }
       tmTabScrollV.setHasNonEmbeddedTabs(b);
       return;
    }
    private boolean shouldAnimateContextView(){
       return this.mContainerView.isLaidOut();
    }
    private void showForActionMode(){
       WindowDecorActionBar tmOverlayLay;
       if (this.mShowingForMode == null) {
          boolean b = true;
          this.mShowingForMode = b;
          if ((tmOverlayLay = this.mOverlayLayout) != null) {
             tmOverlayLay.setShowingForActionMode(b);
          }
          this.updateVisibility(false);
       }
       return;
    }
    private void updateVisibility(boolean p0){
       if (WindowDecorActionBar.checkShowingFlags(this.mHiddenByApp, this.mHiddenBySystem, this.mShowingForMode)) {
          if (this.mNowShowing == null) {
             this.mNowShowing = true;
             this.doShow(p0);
          }
       }else if(this.mNowShowing != null){
          this.mNowShowing = false;
          this.doHide(p0);
       }
       return;
    }
    public void addOnMenuVisibilityListener(ActionBar$OnMenuVisibilityListener p0){
       this.mMenuVisibilityListeners.add(p0);
    }
    public void addTab(ActionBar$Tab p0){
       this.addTab(p0, this.mTabs.isEmpty());
    }
    public void addTab(ActionBar$Tab p0,int p1){
       this.addTab(p0, p1, this.mTabs.isEmpty());
    }
    public void addTab(ActionBar$Tab p0,int p1,boolean p2){
       this.ensureTabsExist();
       this.mTabScrollView.addTab(p0, p1, p2);
       this.configureTab(p0, p1);
       if (p2) {
          this.selectTab(p0);
       }
       return;
    }
    public void addTab(ActionBar$Tab p0,boolean p1){
       this.ensureTabsExist();
       this.mTabScrollView.addTab(p0, p1);
       this.configureTab(p0, this.mTabs.size());
       if (p1) {
          this.selectTab(p0);
       }
       return;
    }
    public void animateToMode(boolean p0){
       ViewPropertyAnimatorCompat viewProperty;
       ViewPropertyAnimatorCompat viewProperty1;
       if (p0) {
          this.showForActionMode();
       }else {
          this.hideForActionMode();
       }
       int i = 8;
       if (this.shouldAnimateContextView()) {
          if (p0) {
             viewProperty = this.mDecorToolbar.setupAnimatorToVisibility(4, 100);
             viewProperty1 = this.mContextView.setupAnimatorToVisibility(0, 200);
          }else {
             viewProperty1 = this.mDecorToolbar.setupAnimatorToVisibility(0, 200);
             viewProperty = this.mContextView.setupAnimatorToVisibility(i, 100);
          }
          ViewPropertyAnimatorCompatSet viewProperty2 = new ViewPropertyAnimatorCompatSet();
          viewProperty2.playSequentially(viewProperty, viewProperty1);
          viewProperty2.start();
       }else if(p0){
          this.mDecorToolbar.setVisibility(4);
          this.mContextView.setVisibility(0);
       }else {
          this.mDecorToolbar.setVisibility(0);
          this.mContextView.setVisibility(i);
       }
       return;
    }
    public boolean collapseActionView(){
       WindowDecorActionBar tmDecorToolb;
       if ((tmDecorToolb = this.mDecorToolbar) == null || !tmDecorToolb.hasExpandedActionView()) {
          return false;
       }
       this.mDecorToolbar.collapseActionView();
       return true;
    }
    public void completeDeferredDestroyActionMode(){
       WindowDecorActionBar tmDeferredMo;
       if ((tmDeferredMo = this.mDeferredModeDestroyCallback) != null) {
          tmDeferredMo.onDestroyActionMode(this.mDeferredDestroyActionMode);
          this.mDeferredDestroyActionMode = null;
          this.mDeferredModeDestroyCallback = null;
       }
       return;
    }
    public void dispatchMenuVisibilityChanged(boolean p0){
       if (p0 == this.mLastMenuVisibility) {
          return;
       }
       this.mLastMenuVisibility = p0;
       int i = this.mMenuVisibilityListeners.size();
       for (int i1 = 0; i1 < i; i1 = i1 + 1) {
          this.mMenuVisibilityListeners.get(i1).onMenuVisibilityChanged(p0);
       }
       return;
    }
    public void doHide(boolean p0){
       WindowDecorActionBar tmCurrentSho;
       WindowDecorActionBar tmContentVie;
       if ((tmCurrentSho = this.mCurrentShowAnim) != null) {
          tmCurrentSho.cancel();
       }
       if (this.mCurWindowVisibility == null && (this.mShowHideAnimationEnabled != null && !p0)) {
          this.mContainerView.setAlpha(1.00f);
          boolean b = true;
          this.mContainerView.setTransitioning(b);
          ViewPropertyAnimatorCompatSet viewProperty = new ViewPropertyAnimatorCompatSet();
          float f = (float)(- this.mContainerView.getHeight());
          if (p0) {
             int[] ointArray = new int[]{null,null};
             this.mContainerView.getLocationInWindow(ointArray);
             f = f - (float)ointArray[b];
          }
          ViewPropertyAnimatorCompat viewProperty1 = ViewCompat.animate(this.mContainerView).translationY(f);
          viewProperty1.setUpdateListener(this.mUpdateListener);
          viewProperty.play(viewProperty1);
          if (this.mContentAnimations != null && (tmContentVie = this.mContentView) != null) {
             viewProperty.play(ViewCompat.animate(tmContentVie).translationY(f));
          }
          viewProperty.setInterpolator(WindowDecorActionBar.sHideInterpolator);
          viewProperty.setDuration(250);
          viewProperty.setListener(this.mHideListener);
          this.mCurrentShowAnim = viewProperty;
          viewProperty.start();
       }else {
          this.mHideListener.onAnimationEnd(null);
       }
       return;
    }
    public void doShow(boolean p0){
       WindowDecorActionBar tmCurrentSho;
       WindowDecorActionBar tmContentVie;
       WindowDecorActionBar tmOverlayLay;
       if ((tmCurrentSho = this.mCurrentShowAnim) != null) {
          tmCurrentSho.cancel();
       }
       int i = 0;
       this.mContainerView.setVisibility(i);
       if (this.mCurWindowVisibility == null && (this.mShowHideAnimationEnabled != null && !p0)) {
          this.mContainerView.setTranslationY(0);
          float f = (float)(- this.mContainerView.getHeight());
          if (p0) {
             int[] ointArray = new int[]{i,i};
             this.mContainerView.getLocationInWindow(ointArray);
             f = f - (float)ointArray[1];
          }
          this.mContainerView.setTranslationY(f);
          ViewPropertyAnimatorCompatSet viewProperty = new ViewPropertyAnimatorCompatSet();
          ViewPropertyAnimatorCompat viewProperty1 = ViewCompat.animate(this.mContainerView).translationY(0);
          viewProperty1.setUpdateListener(this.mUpdateListener);
          viewProperty.play(viewProperty1);
          if (this.mContentAnimations != null && (tmContentVie = this.mContentView) != null) {
             tmContentVie.setTranslationY(f);
             viewProperty.play(ViewCompat.animate(this.mContentView).translationY(0));
          }
          viewProperty.setInterpolator(WindowDecorActionBar.sShowInterpolator);
          viewProperty.setDuration(250);
          viewProperty.setListener(this.mShowListener);
          this.mCurrentShowAnim = viewProperty;
          viewProperty.start();
       }else {
          this.mContainerView.setAlpha(1.00f);
          this.mContainerView.setTranslationY(0);
          if (this.mContentAnimations != null && (tmOverlayLay = this.mContentView) != null) {
             tmOverlayLay.setTranslationY(0);
          }
          this.mShowListener.onAnimationEnd(null);
       }
       if ((tmOverlayLay = this.mOverlayLayout) != null) {
          ViewCompat.requestApplyInsets(tmOverlayLay);
       }
       return;
    }
    public void enableContentAnimations(boolean p0){
       this.mContentAnimations = p0;
    }
    public View getCustomView(){
       return this.mDecorToolbar.getCustomView();
    }
    public int getDisplayOptions(){
       return this.mDecorToolbar.getDisplayOptions();
    }
    public float getElevation(){
       return ViewCompat.getElevation(this.mContainerView);
    }
    public int getHeight(){
       return this.mContainerView.getHeight();
    }
    public int getHideOffset(){
       return this.mOverlayLayout.getActionBarHideOffset();
    }
    public int getNavigationItemCount(){
       int navigationMo;
       if ((navigationMo = this.mDecorToolbar.getNavigationMode()) == 1) {
          return this.mDecorToolbar.getDropdownItemCount();
       }
       if (navigationMo != 2) {
          return 0;
       }
       return this.mTabs.size();
    }
    public int getNavigationMode(){
       return this.mDecorToolbar.getNavigationMode();
    }
    public int getSelectedNavigationIndex(){
       int navigationMo;
       WindowDecorActionBar tmSelectedTa;
       if ((navigationMo = this.mDecorToolbar.getNavigationMode()) == 1) {
          return this.mDecorToolbar.getDropdownSelectedPosition();
       }
       int i = -1;
       if (navigationMo != 2) {
          return i;
       }
       if ((tmSelectedTa = this.mSelectedTab) != null) {
          i = tmSelectedTa.getPosition();
       }
       return i;
    }
    public ActionBar$Tab getSelectedTab(){
       return this.mSelectedTab;
    }
    public CharSequence getSubtitle(){
       return this.mDecorToolbar.getSubtitle();
    }
    public ActionBar$Tab getTabAt(int p0){
       return this.mTabs.get(p0);
    }
    public int getTabCount(){
       return this.mTabs.size();
    }
    public Context getThemedContext(){
       if (this.mThemedContext == null) {
          TypedValue typedValue = new TypedValue();
          this.mContext.getTheme().resolveAttribute(R$attr.actionBarWidgetTheme, typedValue, true);
          this.mThemedContext = ((typedValue = typedValue.resourceId) != null)? new ContextThemeWrapper(this.mContext, typedValue): this.mContext;
       }
       return this.mThemedContext;
    }
    public CharSequence getTitle(){
       return this.mDecorToolbar.getTitle();
    }
    public boolean hasIcon(){
       return this.mDecorToolbar.hasIcon();
    }
    public boolean hasLogo(){
       return this.mDecorToolbar.hasLogo();
    }
    public void hide(){
       if (this.mHiddenByApp == null) {
          this.mHiddenByApp = true;
          this.updateVisibility(false);
       }
       return;
    }
    public void hideForSystem(){
       if (this.mHiddenBySystem == null) {
          this.mHiddenBySystem = true;
          this.updateVisibility(true);
       }
       return;
    }
    public boolean isHideOnContentScrollEnabled(){
       return this.mOverlayLayout.isHideOnContentScrollEnabled();
    }
    public boolean isShowing(){
       boolean height = this.getHeight();
       height = (this.mNowShowing != null && (!height && this.getHideOffset() >= height))? true: false;
       return height;
    }
    public boolean isTitleTruncated(){
       WindowDecorActionBar tmDecorToolb;
       boolean b = ((tmDecorToolb = this.mDecorToolbar) != null && tmDecorToolb.isTitleTruncated())? true: false;
       return b;
    }
    public ActionBar$Tab newTab(){
       return new WindowDecorActionBar$TabImpl(this);
    }
    public void onConfigurationChanged(Configuration p0){
       this.setHasEmbeddedTabs(ActionBarPolicy.get(this.mContext).hasEmbeddedTabs());
    }
    public void onContentScrollStarted(){
       WindowDecorActionBar tmCurrentSho;
       if ((tmCurrentSho = this.mCurrentShowAnim) != null) {
          tmCurrentSho.cancel();
          this.mCurrentShowAnim = null;
       }
       return;
    }
    public void onContentScrollStopped(){
    }
    public boolean onKeyShortcut(int p0,KeyEvent p1){
       WindowDecorActionBar tmActionMode;
       Menu menu;
       if ((tmActionMode = this.mActionMode) == null) {
          return false;
       }
       if ((menu = tmActionMode.getMenu()) == null) {
          return false;
       }
       int deviceId = (p1 != null)? p1.getDeviceId(): -1;
       boolean b = true;
       if (KeyCharacterMap.load(deviceId).getKeyboardType() == b) {
          b = false;
       }
       menu.setQwertyMode(b);
       return menu.performShortcut(p0, p1, false);
    }
    public void onWindowVisibilityChanged(int p0){
       this.mCurWindowVisibility = p0;
    }
    public void removeAllTabs(){
       this.cleanupTabs();
    }
    public void removeOnMenuVisibilityListener(ActionBar$OnMenuVisibilityListener p0){
       this.mMenuVisibilityListeners.remove(p0);
    }
    public void removeTab(ActionBar$Tab p0){
       this.removeTabAt(p0.getPosition());
    }
    public void removeTabAt(int p0){
       WindowDecorActionBar tmSelectedTa;
       WindowDecorActionBar$TabImpl tabImpl;
       ActionBar$Tab tab;
       if (this.mTabScrollView == null) {
          return;
       }
       int position = ((tmSelectedTa = this.mSelectedTab) != null)? tmSelectedTa.getPosition(): this.mSavedTabPosition;
       this.mTabScrollView.removeTabAt(p0);
       if ((tabImpl = this.mTabs.remove(p0)) != null) {
          tabImpl.setPosition(-1);
       }
       int i = this.mTabs.size();
       for (int i1 = p0; i1 < i; i1 = i1 + 1) {
          this.mTabs.get(i1).setPosition(i1);
       }
       if (position == p0) {
          if (this.mTabs.isEmpty()) {
             tab = null;
          }else {
             p0--;
             tab = this.mTabs.get(Math.max(0, p0));
          }
          this.selectTab(tab);
       }
       return;
    }
    public boolean requestFocus(){
       ViewGroup viewGroup;
       if ((viewGroup = this.mDecorToolbar.getViewGroup()) == null || viewGroup.hasFocus()) {
          return false;
       }
       viewGroup.requestFocus();
       return true;
    }
    public void selectTab(ActionBar$Tab p0){
       FragmentTransaction uFragmentTra;
       WindowDecorActionBar tmSelectedTa;
       int i = -1;
       if (this.getNavigationMode() != 2) {
          if (p0 != null) {
             i = p0.getPosition();
          }
          this.mSavedTabPosition = i;
          return;
       }else if(this.mActivity instanceof FragmentActivity && !this.mDecorToolbar.getViewGroup().isInEditMode()){
          uFragmentTra = this.mActivity.getSupportFragmentManager().beginTransaction().disallowAddToBackStack();
       }else {
          uFragmentTra = null;
       }
       if ((tmSelectedTa = this.mSelectedTab) == p0) {
          if (tmSelectedTa != null) {
             tmSelectedTa.getCallback().onTabReselected(this.mSelectedTab, uFragmentTra);
             this.mTabScrollView.animateToTab(p0.getPosition());
          }
       }else {
          tmSelectedTa = this.mTabScrollView;
          if (p0 != null) {
             i = p0.getPosition();
          }
          tmSelectedTa.setTabSelected(i);
          if ((tmSelectedTa = this.mSelectedTab) != null) {
             tmSelectedTa.getCallback().onTabUnselected(this.mSelectedTab, uFragmentTra);
          }
          this.mSelectedTab = p0;
          if (p0 != null) {
             p0.getCallback().onTabSelected(this.mSelectedTab, uFragmentTra);
          }
       }
       if (uFragmentTra != null && !uFragmentTra.isEmpty()) {
          uFragmentTra.commit();
       }
       return;
    }
    public void setBackgroundDrawable(Drawable p0){
       this.mContainerView.setPrimaryBackground(p0);
    }
    public void setCustomView(int p0){
       this.setCustomView(LayoutInflater.from(this.getThemedContext()).inflate(p0, this.mDecorToolbar.getViewGroup(), false));
    }
    public void setCustomView(View p0){
       this.mDecorToolbar.setCustomView(p0);
    }
    public void setCustomView(View p0,ActionBar$LayoutParams p1){
       p0.setLayoutParams(p1);
       this.mDecorToolbar.setCustomView(p0);
    }
    public void setDefaultDisplayHomeAsUpEnabled(boolean p0){
       if (this.mDisplayHomeAsUpSet == null) {
          this.setDisplayHomeAsUpEnabled(p0);
       }
       return;
    }
    public void setDisplayHomeAsUpEnabled(boolean p0){
       int i = (p0)? 4: 0;
       this.setDisplayOptions(i, 4);
       return;
    }
    public void setDisplayOptions(int p0){
       if ((p0 & 0x04)) {
          this.mDisplayHomeAsUpSet = true;
       }
       this.mDecorToolbar.setDisplayOptions(p0);
       return;
    }
    public void setDisplayOptions(int p0,int p1){
       int displayOptio = this.mDecorToolbar.getDisplayOptions();
       if ((p1 & 0x04)) {
          this.mDisplayHomeAsUpSet = true;
       }
       this.mDecorToolbar.setDisplayOptions(((p0 & p1) | ((~ p1) & displayOptio)));
       return;
    }
    public void setDisplayShowCustomEnabled(boolean p0){
       int i = (p0)? 16: 0;
       this.setDisplayOptions(i, 16);
       return;
    }
    public void setDisplayShowHomeEnabled(boolean p0){
       int i = (p0)? 2: 0;
       this.setDisplayOptions(i, 2);
       return;
    }
    public void setDisplayShowTitleEnabled(boolean p0){
       int i = (p0)? 8: 0;
       this.setDisplayOptions(i, 8);
       return;
    }
    public void setDisplayUseLogoEnabled(boolean p0){
       this.setDisplayOptions(p0, 1);
    }
    public void setElevation(float p0){
       ViewCompat.setElevation(this.mContainerView, p0);
    }
    public void setHideOffset(int p0){
       if (p0 && !this.mOverlayLayout.isInOverlayMode()) {
          throw new IllegalStateException("Action bar must be in overlay mode \(Window.FEATURE_OVERLAY_ACTION_BAR\) to set a non-zero hide offset");
       }
       this.mOverlayLayout.setActionBarHideOffset(p0);
       return;
    }
    public void setHideOnContentScrollEnabled(boolean p0){
       if (p0 && !this.mOverlayLayout.isInOverlayMode()) {
          throw new IllegalStateException("Action bar must be in overlay mode \(Window.FEATURE_OVERLAY_ACTION_BAR\) to enable hide on content scroll");
       }
       this.mHideOnContentScroll = p0;
       this.mOverlayLayout.setHideOnContentScrollEnabled(p0);
       return;
    }
    public void setHomeActionContentDescription(int p0){
       this.mDecorToolbar.setNavigationContentDescription(p0);
    }
    public void setHomeActionContentDescription(CharSequence p0){
       this.mDecorToolbar.setNavigationContentDescription(p0);
    }
    public void setHomeAsUpIndicator(int p0){
       this.mDecorToolbar.setNavigationIcon(p0);
    }
    public void setHomeAsUpIndicator(Drawable p0){
       this.mDecorToolbar.setNavigationIcon(p0);
    }
    public void setHomeButtonEnabled(boolean p0){
       this.mDecorToolbar.setHomeButtonEnabled(p0);
    }
    public void setIcon(int p0){
       this.mDecorToolbar.setIcon(p0);
    }
    public void setIcon(Drawable p0){
       this.mDecorToolbar.setIcon(p0);
    }
    public void setListNavigationCallbacks(SpinnerAdapter p0,ActionBar$OnNavigationListener p1){
       this.mDecorToolbar.setDropdownParams(p0, new NavItemSelectedListener(p1));
    }
    public void setLogo(int p0){
       this.mDecorToolbar.setLogo(p0);
    }
    public void setLogo(Drawable p0){
       this.mDecorToolbar.setLogo(p0);
    }
    public void setNavigationMode(int p0){
       boolean navigationMo;
       WindowDecorActionBar tmOverlayLay;
       WindowDecorActionBar tmDecorToolb;
       if ((navigationMo = this.mDecorToolbar.getNavigationMode()) == 2) {
          this.mSavedTabPosition = this.getSelectedNavigationIndex();
          this.selectTab(null);
          this.mTabScrollView.setVisibility(8);
       }
       if (navigationMo != p0 && (this.mHasEmbeddedTabs == null && (tmOverlayLay = this.mOverlayLayout) != null)) {
          ViewCompat.requestApplyInsets(tmOverlayLay);
       }
       this.mDecorToolbar.setNavigationMode(p0);
       navigationMo = false;
       if (p0 == 2) {
          this.ensureTabsExist();
          this.mTabScrollView.setVisibility(navigationMo);
          tmDecorToolb = this.mSavedTabPosition;
          int i = -1;
          if (tmDecorToolb != i) {
             this.setSelectedNavigationItem(tmDecorToolb);
             this.mSavedTabPosition = i;
          }
       }
       tmDecorToolb = this.mDecorToolbar;
       boolean b = (p0 == 2 && this.mHasEmbeddedTabs == null)? true: false;
       tmDecorToolb.setCollapsible(b);
       tmDecorToolb = this.mOverlayLayout;
       if (p0 == 2 && this.mHasEmbeddedTabs == null) {
          navigationMo = true;
       }
       tmDecorToolb.setHasNonEmbeddedTabs(navigationMo);
       return;
    }
    public void setSelectedNavigationItem(int p0){
       int navigationMo;
       if ((navigationMo = this.mDecorToolbar.getNavigationMode()) != 1) {
          if (navigationMo == 2) {
             this.selectTab(this.mTabs.get(p0));
          }else {
             throw new IllegalStateException("setSelectedNavigationIndex not valid for current navigation mode");
          }
       }else {
          this.mDecorToolbar.setDropdownSelectedPosition(p0);
       }
       return;
    }
    public void setShowHideAnimationEnabled(boolean p0){
       WindowDecorActionBar tmCurrentSho;
       this.mShowHideAnimationEnabled = p0;
       if (!p0 && (tmCurrentSho = this.mCurrentShowAnim) != null) {
          tmCurrentSho.cancel();
       }
       return;
    }
    public void setSplitBackgroundDrawable(Drawable p0){
    }
    public void setStackedBackgroundDrawable(Drawable p0){
       this.mContainerView.setStackedBackground(p0);
    }
    public void setSubtitle(int p0){
       this.setSubtitle(this.mContext.getString(p0));
    }
    public void setSubtitle(CharSequence p0){
       this.mDecorToolbar.setSubtitle(p0);
    }
    public void setTitle(int p0){
       this.setTitle(this.mContext.getString(p0));
    }
    public void setTitle(CharSequence p0){
       this.mDecorToolbar.setTitle(p0);
    }
    public void setWindowTitle(CharSequence p0){
       this.mDecorToolbar.setWindowTitle(p0);
    }
    public void show(){
       if (this.mHiddenByApp != null) {
          this.mHiddenByApp = false;
          this.updateVisibility(false);
       }
       return;
    }
    public void showForSystem(){
       if (this.mHiddenBySystem != null) {
          this.mHiddenBySystem = false;
          this.updateVisibility(true);
       }
       return;
    }
    public ActionMode startActionMode(ActionMode$Callback p0){
       WindowDecorActionBar tmActionMode;
       if ((tmActionMode = this.mActionMode) != null) {
          tmActionMode.finish();
       }
       this.mOverlayLayout.setHideOnContentScrollEnabled(false);
       this.mContextView.killMode();
       WindowDecorActionBar$ActionModeImpl uActionModeI = new WindowDecorActionBar$ActionModeImpl(this, this.mContextView.getContext(), p0);
       if (uActionModeI.dispatchOnCreate()) {
          this.mActionMode = uActionModeI;
          uActionModeI.invalidate();
          this.mContextView.initForMode(uActionModeI);
          this.animateToMode(true);
          return uActionModeI;
       }else {
          return null;
       }
    }
}
