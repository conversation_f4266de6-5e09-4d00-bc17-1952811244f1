package tb.a410;
import tb.t2o;
import com.taobao.mytaobao.pagev2.dataservice.model.BasementDataMode;
import tb.z310;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import java.util.List;
import java.lang.Iterable;
import java.util.ArrayList;
import tb.zz3;
import java.util.Iterator;
import com.taobao.mytaobao.pagev2.dataservice.model.BasementChildDataMode;
import tb.r310;
import com.alibaba.fastjson.JSONObject;
import tb.yz3;

public final class a410	// class@001ae5 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2ef001ab);
    }
    public static final z310 a(BasementDataMode p0){
       List dataList;
       ArrayList uArrayList;
       String type;
       IpChange $ipChange = a410.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("3683d73a", objArray);
       }else {
          ckf.h(p0, "$this$toViewPagerUIState");
          if ((dataList = p0.getDataList()) != null) {
             uArrayList = new ArrayList(zz3.q(dataList, 10));
             Iterator iterator = dataList.iterator();
             while (iterator.hasNext()) {
                BasementChildDataMode uBasementChi = iterator.next();
                String id = uBasementChi.getId();
                String str = "";
                if (id == null) {
                   id = str;
                }
                if ((type = uBasementChi.getType()) != null) {
                   str = type;
                }
                uArrayList.add(new r310(id, str, uBasementChi.getWeexOriginData()));
             }
          }else {
             uArrayList = yz3.i();
          }
          return new z310(uArrayList);
       }
    }
}
