package tb.a7n;
import tb.t2o;
import java.io.File;
import java.lang.String;
import android.content.SharedPreferences;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import android.content.Context;
import com.taobao.orange.OrangeConfig;
import tb.a7n$a;
import tb.obk;
import java.lang.Boolean;

public class a7n	// class@00184d from classes5.dex
{
    public static IpChange $ipChange;
    public static final String KEY_SWITCH;
    public static final String KEY_TEST_LOCAL_SWITCH;
    public static final String PATH_LOCAL_SWITCH_FOLDER;
    public static final String SP_NAMESPACE;
    public static SharedPreferences a;

    static {
       t2o.a(0x27e00007);
       new File("/data/local/tmp/.tbquality/.test_switch_on").exists();
    }
    public static SharedPreferences a(){
       IpChange $ipChange = a7n.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a7n.a;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("1f071a8b", objArray);
    }
    public static void b(Context p0){
       IpChange $ipChange = a7n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("609fd211", objArray);
          return;
       }else {
          a7n.a = p0.getSharedPreferences("tb_quality_android", 0);
          String[] stringArray = new String[]{"tb_quality_android"};
          OrangeConfig.getInstance().registerListener(stringArray, new a7n$a(), 0);
          return;
       }
    }
    public static boolean c(){
       SharedPreferences a;
       IpChange $ipChange = a7n.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("5c904294", objArray).booleanValue();
       }else if((a = a7n.a) == null){
          return i;
       }else {
          return Boolean.parseBoolean(a.getString("switch_on", "true"));
       }
    }
}
