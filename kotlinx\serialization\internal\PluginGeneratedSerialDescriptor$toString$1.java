package kotlinx.serialization.internal.PluginGeneratedSerialDescriptor$toString$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor;
import java.lang.CharSequence;
import java.lang.StringBuilder;
import java.lang.String;
import kotlinx.serialization.descriptors.a;
import java.lang.Object;
import java.lang.Number;

public final class PluginGeneratedSerialDescriptor$toString$1 extends Lambda implements g1a	// class@00074a from classes11.dex
{
    public final PluginGeneratedSerialDescriptor this$0;

    public void PluginGeneratedSerialDescriptor$toString$1(PluginGeneratedSerialDescriptor p0){
       this.this$0 = p0;
       super(1);
    }
    public final CharSequence invoke(int p0){
       return this.this$0.e(p0)+": "+this.this$0.g(p0).f();
    }
    public Object invoke(Object p0){
       return this.invoke(p0.intValue());
    }
}
