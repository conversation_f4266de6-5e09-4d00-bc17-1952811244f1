package kotlinx.coroutines.TimeoutCancellationException;
import tb.tt4;
import java.util.concurrent.CancellationException;
import java.lang.String;
import kotlinx.coroutines.m;
import java.lang.Throwable;

public final class TimeoutCancellationException extends CancellationException implements tt4	// class@0004ae from classes11.dex
{
    public final m coroutine;

    public void TimeoutCancellationException(String p0){
       super(p0, null);
    }
    public void TimeoutCancellationException(String p0,m p1){
       super(p0);
       this.coroutine = p1;
    }
    public Throwable createCopy(){
       return this.createCopy();
    }
    public TimeoutCancellationException createCopy(){
       String message;
       if ((message = this.getMessage()) == null) {
          message = "";
       }
       TimeoutCancellationException timeoutCance = new TimeoutCancellationException(message, this.coroutine);
       timeoutCance.initCause(this);
       return timeoutCance;
    }
}
