package androidx.appcompat.app.AppCompatDelegateImpl$Api21Impl;
import java.lang.Object;
import android.os.PowerManager;
import java.util.Locale;
import java.lang.String;

public class AppCompatDelegateImpl$Api21Impl	// class@00056a from classes.dex
{

    private void AppCompatDelegateImpl$Api21Impl(){
       super();
    }
    public static boolean isPowerSaveMode(PowerManager p0){
       return p0.isPowerSaveMode();
    }
    public static String toLanguageTag(Locale p0){
       return p0.toLanguageTag();
    }
}
