package androidx.activity.EdgeToEdgeApi29;
import androidx.activity.EdgeToEdgeApi28;
import androidx.activity.SystemBarStyle;
import android.view.Window;
import android.view.View;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.core.view.WindowCompat;
import tb.b78;
import tb.c78;
import androidx.core.view.WindowInsetsControllerCompat;

public class EdgeToEdgeApi29 extends EdgeToEdgeApi28	// class@000447 from classes.dex
{

    public void EdgeToEdgeApi29(){
       super();
    }
    public void setUp(SystemBarStyle p0,SystemBarStyle p1,Window p2,View p3,boolean p4,boolean p5){
       ckf.g(p0, "statusBarStyle");
       ckf.g(p1, "navigationBarStyle");
       ckf.g(p2, "window");
       ckf.g(p3, "view");
       boolean b = false;
       WindowCompat.setDecorFitsSystemWindows(p2, b);
       p2.setStatusBarColor(p0.getScrimWithEnforcedContrast$activity_release(p4));
       p2.setNavigationBarColor(p1.getScrimWithEnforcedContrast$activity_release(p5));
       b78.a(p2, b);
       int i = 1;
       if (!p1.getNightMode$activity_release()) {
          b = true;
       }
       c78.a(p2, b);
       WindowInsetsControllerCompat windowInsets = new WindowInsetsControllerCompat(p2, p3);
       windowInsets.setAppearanceLightStatusBars((p4 ^ 0x01));
       windowInsets.setAppearanceLightNavigationBars((i ^ p5));
       return;
    }
}
