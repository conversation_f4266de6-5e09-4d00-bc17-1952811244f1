package tb.a52;
import tb.m8;
import tb.t2o;
import tb.a52$a;
import tb.a07;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.n8;
import tb.k8;
import tb.u8;
import tb.c8;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import com.alibaba.fastjson.JSONObject;
import java.util.Set;
import java.lang.Iterable;
import java.util.Iterator;
import java.util.Map$Entry;
import tb.h4g;
import tb.f8;

public final class a52 extends m8	// class@00183b from classes5.dex
{
    public static IpChange $ipChange;
    public static final String BATCHCHAINSTORAGESET;
    public static final a52$a Companion;

    static {
       t2o.a(0x15d00037);
       a52.Companion = new a52$a(null);
    }
    public void a52(){
       super();
    }
    public static Object ipc$super(a52 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/abilitykit/ability/BatchChainStorageSetAbility");
    }
    public c8 f(n8 p0,k8 p1,u8 p2){
       JSONObject jSONObject;
       Set set;
       IpChange $ipChange = a52.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("40f5a125", objArray);
       }else {
          ckf.g(p0, "abilityData");
          ckf.g(p1, "akCtx");
          ckf.g(p2, "akiAbilityCallback");
          if ((jSONObject = p0.h().getJSONObject("pairs")) != null && (set = jSONObject.entrySet()) != null) {
             Iterator iterator = set.iterator();
             while (iterator.hasNext()) {
                Map$Entry uEntry = iterator.next();
                Object key = uEntry.getKey();
                ckf.f(key, "it.key");
                h4g.c(key, p1.b(), uEntry.getValue());
             }
          }
          return new f8();
       }
    }
}
