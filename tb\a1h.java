package tb.a1h;
import java.lang.Object;
import tb.nbl;
import java.util.HashMap;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import java.util.Set;
import java.util.Iterator;
import java.lang.Integer;
import java.lang.StringBuilder;
import tb.k6s;
import tb.v2s;
import tb.u9b;
import tb.prq;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.Arrays;

public class a1h	// class@00173f from classes9.dex
{
    public final int a;
    public final HashMap b;
    public final boolean c;
    public final int d;
    public final int e;
    public final String f;
    public final String g;
    public final boolean h;
    public final boolean i;
    public final boolean j;
    public final boolean k;
    public final int l;
    public final boolean m;
    public final int n;
    public final int o;
    public final String[] p;
    public final boolean q;
    public final boolean r;
    public final int s;
    public static IpChange $ipChange;

    public void a1h(){
       super();
       this.r = false;
       this.a = nbl.j();
       HashMap hashMap = new HashMap();
       try{
          this.b = hashMap;
          JSONObject jSONObject = JSON.parseObject(nbl.e());
          Iterator iterator = jSONObject.keySet().iterator();
          while (iterator.hasNext()) {
             String str = iterator.next();
             Integer integer = Integer.valueOf(str);
             this.b.put(integer, jSONObject.getInteger(str));
          }
       }catch(java.lang.Exception e0){
       }
       this.c = nbl.u();
       this.d = nbl.a();
       this.e = nbl.b();
       this.f = nbl.c();
       this.g = nbl.d();
       this.h = nbl.o();
       this.i = nbl.q();
       this.l = nbl.t();
       this.j = nbl.r();
       this.k = nbl.m();
       this.m = nbl.v();
       this.n = nbl.n();
       this.o = nbl.k();
       this.p = nbl.l().split(";");
       this.s = nbl.s();
       k6s.a("a1h", "LiveMessageConfig[init]:"+this.toString());
       this.q = (!nbl.f())? prq.a(v2s.o().c().b("taolive", "liveMsgUsecdn", "enable", "true")): true;
       this.r = nbl.i();
       return;
    }
    public String toString(){
       IpChange $ipChange = a1h.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "LiveMessageConfig{deduplicationSize="+this.a+", defaultColorRate="+this.b+", useCdnFetchMSG="+this.c+", cdnFetchMSGInterval="+this.d+", cdnFetchMSGIntervalMax="+this.e+", cdnFetchMSGURL=\'"+this.f+"\', isAddDeviceIdCdnFetchMSG="+this.h+", isNeedCDNMessageGet="+this.i+", timeoutCDNMessageGet="+this.l+", useHeartbeat="+this.m+", heartbeatInterval="+this.n+", heartFetchStatusInterval="+this.o+", heartbeatCommonExtraParams="+Arrays.toString(this.p)+", isLiveMessageLongPullDisable="+this.j+", timeoutCDNLongPull="+this.s+", cdnLongFetchMSGURL="+this.g+", isHeartbeatDisable="+this.k+'}';
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
