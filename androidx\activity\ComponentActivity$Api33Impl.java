package androidx.activity.ComponentActivity$Api33Impl;
import java.lang.Object;
import android.app.Activity;
import android.window.OnBackInvokedDispatcher;
import java.lang.String;
import tb.ckf;

public final class ComponentActivity$Api33Impl	// class@000436 from classes.dex
{
    public static final ComponentActivity$Api33Impl INSTANCE;

    static {
       ComponentActivity$Api33Impl.INSTANCE = new ComponentActivity$Api33Impl();
    }
    private void ComponentActivity$Api33Impl(){
       super();
    }
    public final OnBackInvokedDispatcher getOnBackInvokedDispatcher(Activity p0){
       ckf.g(p0, "activity");
       OnBackInvokedDispatcher onBackInvoke = p0.getOnBackInvokedDispatcher();
       ckf.f(onBackInvoke, "activity.getOnBackInvokedDispatcher\(\)");
       return onBackInvoke;
    }
}
