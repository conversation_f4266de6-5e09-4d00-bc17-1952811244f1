package kotlinx.coroutines.channels.b;
import tb.ozm;
import tb.hl2;
import kotlinx.coroutines.a;
import kotlin.coroutines.d;
import kotlinx.coroutines.m;
import kotlin.coroutines.d$c;
import kotlin.coroutines.d$b;
import kotlinx.coroutines.JobSupport;
import java.lang.Throwable;
import kotlinx.coroutines.JobCancellationException;
import java.lang.String;
import java.lang.Object;
import java.util.concurrent.CancellationException;
import tb.h9p;
import kotlinx.coroutines.channels.i;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import tb.g1a;
import tb.tu4;
import tb.xhv;
import kotlinx.coroutines.channels.i$a;

public class b extends a implements ozm, hl2	// class@000515 from classes11.dex
{
    private final hl2 f;

    public void b(d p0,hl2 p1,boolean p2){
       super(p0, false, p2);
       this.f = p1;
       this.y0(p0.get(m.Key));
    }
    public final boolean K(Throwable p0){
       JobCancellationException jobCancellat;
       if (p0 == null) {
          jobCancellat = new JobCancellationException(JobSupport.u(this), null, this);
       }
       this.O(jobCancellat);
       return true;
    }
    public void O(Throwable p0){
       CancellationException uCancellatio = JobSupport.k1(this, p0, null, 1, null);
       this.f.a(uCancellatio);
       this.M(uCancellatio);
    }
    public final void a(CancellationException p0){
       JobCancellationException jobCancellat;
       if (p0 == null) {
          jobCancellat = new JobCancellationException(JobSupport.u(this), null, this);
       }
       this.O(jobCancellat);
       return;
    }
    public h9p b(){
       return this.f.b();
    }
    public ReceiveChannel c(){
       return this.f.c();
    }
    public Object d(Object p0,ar4 p1){
       return this.f.d(p0, p1);
    }
    public boolean f(){
       return this.f.f();
    }
    public boolean isActive(){
       return super.isActive();
    }
    public void k(g1a p0){
       this.f.k(p0);
    }
    public Object m(Object p0){
       return this.f.m(p0);
    }
    public boolean offer(Object p0){
       return this.f.offer(p0);
    }
    public boolean s(Throwable p0){
       this.start();
       return this.f.s(p0);
    }
    public void t1(Throwable p0,boolean p1){
       if (!this.f.s(p0) && !p1) {
          tu4.a(this.getContext(), p0);
       }
       return;
    }
    public void u1(Object p0){
       this.x1(p0);
    }
    public i w(){
       return this;
    }
    public final hl2 w1(){
       return this.f;
    }
    public void x1(xhv p0){
       i$a.a(this.f, null, 1, null);
    }
}
