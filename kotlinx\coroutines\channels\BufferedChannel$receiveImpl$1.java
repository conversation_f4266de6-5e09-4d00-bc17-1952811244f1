package kotlinx.coroutines.channels.BufferedChannel$receiveImpl$1;
import tb.w1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.zi3;
import java.lang.Number;
import java.lang.Void;
import java.lang.IllegalStateException;
import java.lang.String;

public final class BufferedChannel$receiveImpl$1 extends Lambda implements w1a	// class@0004cd from classes11.dex
{
    public static final BufferedChannel$receiveImpl$1 INSTANCE;

    static {
       BufferedChannel$receiveImpl$1.INSTANCE = new BufferedChannel$receiveImpl$1();
    }
    public void BufferedChannel$receiveImpl$1(){
       super(3);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1.intValue(), p2.longValue());
    }
    public final Void invoke(zi3 p0,int p1,long p2){
       throw new IllegalStateException("unexpected");
    }
}
