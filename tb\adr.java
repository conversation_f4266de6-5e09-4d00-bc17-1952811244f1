package tb.adr;
import tb.xnj;
import tb.t2o;
import java.lang.Object;
import androidx.fragment.app.FragmentManager;
import android.content.Intent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import com.taobao.tao.tbmainfragment.ISupportFragment;
import tb.qxq;
import com.taobao.tao.tbmainfragment.TBMainFragment;
import androidx.fragment.app.Fragment;
import java.lang.Class;
import android.net.Uri;
import tb.hnv;
import android.os.Bundle;
import android.os.BaseBundle;
import android.content.Context;
import tb.nx9;
import tb.zmj;
import com.taobao.tao.tbmainfragment.SupportActivity;
import tb.bew;
import androidx.fragment.app.FragmentActivity;
import java.lang.StringBuilder;
import com.taobao.tao.util.TBMainLog;
import com.alibaba.mtl.appmonitor.AppMonitor$Counter;
import tb.hx9;

public class adr implements xnj	// class@001788 from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x30c0008f);
       t2o.a(0x24900063);
    }
    public void adr(){
       super();
    }
    public final boolean a(FragmentManager p0,Intent p1){
       Fragment currentFragm;
       Uri data;
       int i = 1;
       IpChange $ipChange = adr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("fe180d13", objArray).booleanValue();
       }else {
          ISupportFragment iSupportFrag = qxq.h(p0);
          if (!iSupportFrag instanceof TBMainFragment) {
             return i;
          }
          if ((currentFragm = iSupportFrag.getCurrentFragment()) != null && ("com.taobao.tao.homepage.HomepageFragment".equals(currentFragm.getClass().getName()) && (data = p1.getData()) != null)) {
             return hnv.b(data.toString());
          }
          return 0;
       }
    }
    public final Bundle b(Intent p0){
       Bundle extras;
       IpChange $ipChange = adr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("2776af8b", objArray);
       }else if((extras = p0.getExtras()) == null){
          return null;
       }else {
          extras = extras.get("fragmentArgs");
          if (extras instanceof Bundle) {
             return extras;
          }
          return null;
       }
    }
    public final Bundle c(Intent p0){
       Bundle extras;
       IpChange $ipChange = adr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("32e674f0", objArray);
       }else if((extras = p0.getExtras()) == null){
          return null;
       }else {
          extras = extras.get("fragmentBundle");
          if (extras instanceof Bundle) {
             return extras;
          }
          return null;
       }
    }
    public final String d(Intent p0){
       Bundle uBundle;
       IpChange $ipChange = adr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f6b7e60b", objArray);
       }else if((uBundle = this.c(p0)) == null){
          return null;
       }else {
          uBundle = uBundle.get("fragmentName");
          if (uBundle instanceof String) {
             return uBundle;
          }
          return null;
       }
    }
    public final boolean e(Intent p0){
       Bundle uBundle;
       int i = 0;
       IpChange $ipChange = adr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d0d474f", objArray).booleanValue();
       }else if((uBundle = this.c(p0)) == null){
          return i;
       }else {
          uBundle = uBundle.get("jumpByFragment");
          if (uBundle instanceof String) {
             return "true".equals(uBundle);
          }
          return i;
       }
    }
    public final boolean f(Intent p0,Context p1){
       Bundle uBundle;
       int i = 0;
       IpChange $ipChange = adr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("eccba701", objArray).booleanValue();
       }else if((uBundle = this.c(p0)) == null){
          return i;
       }else {
          uBundle = uBundle.get("subBizType");
          if (!uBundle instanceof String) {
             return i;
          }
          if (nx9.r(p1) && "newDetail_immersive".equals(uBundle)) {
             return 1;
          }
          return nx9.n(p1, uBundle);
       }
    }
    public String name(){
       IpChange $ipChange = adr.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "TBFragmentHookProcessor";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("aa84494e", objArray);
    }
    public boolean process(Intent p0,zmj p1){
       String str;
       int i = 2;
       int i1 = 3;
       IpChange $ipChange = adr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[i] = p1;
          return $ipChange.ipc$dispatch("b3ebca67", objArray).booleanValue();
       }else if(!this.e(p0)){
          return 1;
       }else if(!nx9.q(p1.d()) && !nx9.r(p1.d())){
          return 1;
       }else if(!this.f(p0, p1.d())){
          return 1;
       }else if((str = this.d(p0)) == null){
          return 1;
       }else {
          Context uContext = p1.d();
          if (!uContext instanceof SupportActivity) {
             if (uContext instanceof bew) {
                uContext = uContext.b();
                if (uContext instanceof SupportActivity) {
                label_0064 :
                   Context uContext1 = uContext;
                   FragmentManager supportFragm = uContext1.getSupportFragmentManager();
                   int i2 = qxq.f(supportFragm);
                   TBMainLog.tlog("TBFragmentHookProcessor", "fragment count is "+i2);
                   if (nx9.x(uContext)) {
                      if (i2 >= i1) {
                         AppMonitor$Counter.commit("tbMainFragment", "threeFloorsCount", 1.00f);
                         return 1;
                      }
                   }else if(i2 >= i){
                      return 1;
                   }
                   if (!this.a(supportFragm, p0)) {
                      return 1;
                   }else if(hx9.b(uContext1, p0, str, this.b(p0))){
                      AppMonitor$Counter.commit("tbMainFragment", "jumpToFragment", 1.00f);
                      p1.B(1, "TBFragmentHookProcessor");
                      p1.A(1, "TBFragmentHookProcessor");
                   }
                   return 1;
                }
             }
             return 1;
          }else {
             goto label_0064 ;
          }
       }
    }
    public boolean skip(){
       int i = 0;
       IpChange $ipChange = adr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("7fce928a", objArray).booleanValue();
       }
       return i;
    }
}
