package kotlinx.datetime.format.LocalDateFormatKt$toKotlinCode$2;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import tb.pw40;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import tb.ckf;

public final class LocalDateFormatKt$toKotlinCode$2 extends FunctionReferenceImpl implements g1a	// class@0006e6 from classes11.dex
{
    public static final LocalDateFormatKt$toKotlinCode$2 INSTANCE;

    static {
       LocalDateFormatKt$toKotlinCode$2.INSTANCE = new LocalDateFormatKt$toKotlinCode$2();
    }
    public void LocalDateFormatKt$toKotlinCode$2(){
       super(1, pw40.class, "toKotlinCode", "toKotlinCode\(Ljava/lang/String;\)Ljava/lang/String;", 1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final String invoke(String p0){
       ckf.g(p0, "p0");
       return pw40.a(p0);
    }
}
