package androidx.appcompat.app.AppCompatDelegateImpl$AutoTimeNightModeManager;
import androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager;
import androidx.appcompat.app.AppCompatDelegateImpl;
import androidx.appcompat.app.TwilightManager;
import android.content.IntentFilter;
import java.lang.String;

public class AppCompatDelegateImpl$AutoTimeNightModeManager extends AppCompatDelegateImpl$AutoNightModeManager	// class@000572 from classes.dex
{
    private final TwilightManager mTwilightManager;
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$AutoTimeNightModeManager(AppCompatDelegateImpl p0,TwilightManager p1){
       this.this$0 = p0;
       super(p0);
       this.mTwilightManager = p1;
    }
    public IntentFilter createIntentFilterForBroadcastReceiver(){
       IntentFilter intentFilter = new IntentFilter();
       intentFilter.addAction("android.intent.action.TIME_SET");
       intentFilter.addAction("android.intent.action.TIMEZONE_CHANGED");
       intentFilter.addAction("android.intent.action.TIME_TICK");
       return intentFilter;
    }
    public int getApplyableNightMode(){
       int i = (this.mTwilightManager.isNight())? 2: 1;
       return i;
    }
    public void onChange(){
       this.this$0.applyDayNight();
    }
}
