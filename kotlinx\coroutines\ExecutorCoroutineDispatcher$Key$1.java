package kotlinx.coroutines.ExecutorCoroutineDispatcher$Key$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import kotlin.coroutines.d$b;
import kotlinx.coroutines.ExecutorCoroutineDispatcher;

public final class ExecutorCoroutineDispatcher$Key$1 extends Lambda implements g1a	// class@00049a from classes11.dex
{
    public static final ExecutorCoroutineDispatcher$Key$1 INSTANCE;

    static {
       ExecutorCoroutineDispatcher$Key$1.INSTANCE = new ExecutorCoroutineDispatcher$Key$1();
    }
    public void ExecutorCoroutineDispatcher$Key$1(){
       super(1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final ExecutorCoroutineDispatcher invoke(d$b p0){
       if (p0 instanceof ExecutorCoroutineDispatcher) {
       }else {
          p0 = null;
       }
       return p0;
    }
}
