package tb.a0k;
import tb.t2o;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.Boolean;
import com.alibaba.mtl.appmonitor.AppMonitor$Alarm;
import android.app.Activity;

public class a0k	// class@001832 from classes7.dex
{
    public static IpChange $ipChange;
    public static final String DOUBLE_11_CATEGORY_ID;
    public static final String DOUBLE_12_CATEGORY_ID;

    static {
       t2o.a(0x2cb00061);
    }
    public static boolean a(Context p0){
       int i = 1;
       IpChange $ipChange = a0k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("7f0ad042", objArray).booleanValue();
       }else {
          AppMonitor$Alarm.commitFail("CodeTrack-favorite", "offline", "com.taobao.favorites.utils.NewFavoriteUtils", "public static boolean checkActivityDestroy\(Context context\)", "20180112");
          if (p0 == null || !p0 instanceof Activity) {
             return i;
          }
          if (p0.isFinishing()) {
             return i;
          }
          if (p0.isDestroyed()) {
             return i;
          }
          return 0;
       }
    }
}
