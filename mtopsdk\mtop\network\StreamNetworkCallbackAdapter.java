package mtopsdk.mtop.network.StreamNetworkCallbackAdapter;
import tb.htj;
import tb.t2o;
import tb.w4j;
import java.lang.Object;
import mtopsdk.mtop.global.MtopConfig;
import mtopsdk.mtop.intf.Mtop;
import mtopsdk.mtop.common.MtopCallback$MtopStreamListener;
import tb.o9o;
import mtopsdk.mtop.domain.MtopResponse;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.mtop.domain.MtopRequest;
import java.util.Map;
import mtopsdk.mtop.util.MtopStatistics;
import tb.jpq;
import com.alibaba.fastjson.JSONObject;
import tb.q9o;
import java.lang.Throwable;
import mtopsdk.common.util.TBSdkLog;
import tb.zu2;
import tb.o9o$b;
import tb.h3o;
import java.lang.Exception;
import mtopsdk.mtop.common.MtopNetworkProp;
import mtopsdk.mtop.network.StreamNetworkCallbackAdapter$2;
import android.os.Handler;
import java.lang.Runnable;
import tb.ui9;
import mtopsdk.mtop.network.StreamNetworkCallbackAdapter$1;

public class StreamNetworkCallbackAdapter implements htj	// class@0007f0 from classes11.dex
{
    public hi9 filterManager;
    public final w4j mtopContext;
    public MtopCallback$MtopStreamListener streamListener;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x25300112);
       t2o.a(0x25300142);
    }
    public void StreamNetworkCallbackAdapter(w4j p0){
       w4j a;
       super();
       this.mtopContext = p0;
       if (p0 != null) {
          if ((a = p0.a) != null) {
             this.filterManager = a.getMtopConfig().filterManager;
          }
          p0 = p0.e;
          if (p0 instanceof MtopCallback$MtopStreamListener) {
             this.streamListener = p0;
          }
       }
       return;
    }
    public static MtopResponse access$000(StreamNetworkCallbackAdapter p0,o9o p1){
       IpChange $ipChange = StreamNetworkCallbackAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.buildMtopResponse(p1);
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("2489444b", objArray);
    }
    private MtopResponse buildMtopResponse(o9o p0){
       o9o i;
       IpChange $ipChange = StreamNetworkCallbackAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("816f7fc6", objArray);
       }else {
          MtopResponse mtopResponse = new MtopResponse(this.mtopContext.b.getApiName(), this.mtopContext.b.getVersion(), null, null);
          mtopResponse.setResponseCode(p0.b);
          mtopResponse.setHeaderFields(p0.d);
          mtopResponse.setMtopStat(this.mtopContext.g);
          mtopResponse.setStreamModeData(p0.h);
          mtopResponse.setSupportStreamJson(this.mtopContext.q);
          if ((i = p0.i) != null) {
             mtopResponse.setOriginFastJsonObject(i);
          }
          if ((i = p0.g) != null) {
             mtopResponse.setBytedata(i);
          }else if((p0 = p0.e) != null){
             try{
                mtopResponse.setBytedata(p0.c());
             }catch(java.io.IOException e5){
                TBSdkLog.e("mtopsdk.StreamNetworkCallbackAdapter", this.mtopContext.h, "call getBytes of response.body\(\) error.", e5);
             }
          }
          return mtopResponse;
       }
    }
    public void onCancel(zu2 p0){
       IpChange $ipChange = StreamNetworkCallbackAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("52b9b7f3", objArray);
          return;
       }else {
          o9o oo9o = new o9o$b().l(p0.request()).g(-8).e();
          this.onFinish(oo9o, oo9o.a.r);
          return;
       }
    }
    public void onFailure(zu2 p0,Exception p1){
       IpChange $ipChange = StreamNetworkCallbackAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("2b8b04c1", objArray);
          return;
       }else {
          o9o oo9o = new o9o$b().l(p0.request()).g(-7).i(p1.getMessage()).e();
          this.onFinish(oo9o, oo9o.a.r);
          return;
       }
    }
    public void onFinish(o9o p0,Object p1){
       int i = 1;
       IpChange $ipChange = StreamNetworkCallbackAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("48a238d6", objArray);
          return;
       }else {
          w4j g = this.mtopContext.g;
          g.streamRequest = i;
          g.netSendEndTime = g.currentTimeMillis();
          this.mtopContext.d.reqContext = p1;
          StreamNetworkCallbackAdapter tmtopContext = this.mtopContext;
          ui9.e(tmtopContext.d.handler, new StreamNetworkCallbackAdapter$2(this, p0), tmtopContext.h.hashCode());
          return;
       }
    }
    public void onHeader(){
       IpChange $ipChange = StreamNetworkCallbackAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("3ce9b7d3", objArray);
          return;
       }else {
          w4j g = this.mtopContext.g;
          g.receivedResponseCodeTime = g.currentTimeMillis();
          return;
       }
    }
    public void onReceiveData(o9o p0){
       IpChange $ipChange = StreamNetworkCallbackAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7818be7e", objArray);
          return;
       }else {
          StreamNetworkCallbackAdapter tmtopContext = this.mtopContext;
          ui9.e(tmtopContext.d.handler, new StreamNetworkCallbackAdapter$1(this, p0), tmtopContext.h.hashCode());
          return;
       }
    }
    public void onResponse(zu2 p0,o9o p1){
       IpChange $ipChange = StreamNetworkCallbackAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("ae7ed339", objArray);
          return;
       }else {
          this.onFinish(p1, p1.a.r);
          return;
       }
    }
}
