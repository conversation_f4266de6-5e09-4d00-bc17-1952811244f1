package androidx.activity.result.IntentSenderRequest$Builder;
import android.app.PendingIntent;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.content.IntentSender;
import androidx.activity.result.IntentSenderRequest;
import android.content.Intent;

public final class IntentSenderRequest$Builder	// class@0004bd from classes.dex
{
    private Intent fillInIntent;
    private int flagsMask;
    private int flagsValues;
    private final IntentSender intentSender;

    public void IntentSenderRequest$Builder(PendingIntent p0){
       ckf.g(p0, "pendingIntent");
       IntentSender intentSender = p0.getIntentSender();
       ckf.f(intentSender, "pendingIntent.intentSender");
       super(intentSender);
    }
    public void IntentSenderRequest$Builder(IntentSender p0){
       ckf.g(p0, "intentSender");
       super();
       this.intentSender = p0;
    }
    public final IntentSenderRequest build(){
       return new IntentSenderRequest(this.intentSender, this.fillInIntent, this.flagsMask, this.flagsValues);
    }
    public final IntentSenderRequest$Builder setFillInIntent(Intent p0){
       this.fillInIntent = p0;
       return this;
    }
    public final IntentSenderRequest$Builder setFlags(int p0,int p1){
       this.flagsValues = p0;
       this.flagsMask = p1;
       return this;
    }
}
