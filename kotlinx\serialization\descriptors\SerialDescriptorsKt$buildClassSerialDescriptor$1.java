package kotlinx.serialization.descriptors.SerialDescriptorsKt$buildClassSerialDescriptor$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;

public final class SerialDescriptorsKt$buildClassSerialDescriptor$1 extends Lambda implements g1a	// class@00072c from classes11.dex
{
    public static final SerialDescriptorsKt$buildClassSerialDescriptor$1 INSTANCE;

    static {
       SerialDescriptorsKt$buildClassSerialDescriptor$1.INSTANCE = new SerialDescriptorsKt$buildClassSerialDescriptor$1();
    }
    public void SerialDescriptorsKt$buildClassSerialDescriptor$1(){
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       ckf.g(p0, "$this$null");
    }
}
