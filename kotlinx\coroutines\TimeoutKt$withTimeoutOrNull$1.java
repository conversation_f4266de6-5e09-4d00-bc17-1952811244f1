package kotlinx.coroutines.TimeoutKt$withTimeoutOrNull$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import tb.u1a;
import kotlinx.coroutines.TimeoutKt;

public final class TimeoutKt$withTimeoutOrNull$1 extends ContinuationImpl	// class@0004af from classes11.dex
{
    public long J$0;
    public Object L$0;
    public Object L$1;
    public int label;
    public Object result;

    public void TimeoutKt$withTimeoutOrNull$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return TimeoutKt.c(0, null, this);
    }
}
