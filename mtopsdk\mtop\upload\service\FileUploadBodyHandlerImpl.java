package mtopsdk.mtop.upload.service.FileUploadBodyHandlerImpl;
import anetwork.channel.IBodyHandler;
import tb.t2o;
import java.io.File;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import java.lang.Number;
import java.io.RandomAccessFile;
import java.lang.Throwable;
import mtopsdk.common.util.TBSdkLog;

public class FileUploadBodyHandlerImpl implements IBodyHandler	// class@000813 from classes11.dex
{
    private File file;
    private boolean isCompleted;
    private long offset;
    private long patchSize;
    private int postedLength;
    private RandomAccessFile raf;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x25900017);
       t2o.a(0x264001d4);
    }
    public void FileUploadBodyHandlerImpl(File p0,long p1,long p2){
       super();
       this.isCompleted = false;
       this.postedLength = 0;
       this.raf = null;
       this.file = p0;
       this.offset = p1;
       this.patchSize = p2;
    }
    public boolean isCompleted(){
       IpChange $ipChange = FileUploadBodyHandlerImpl.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.isCompleted;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8c6bb44c", objArray).booleanValue();
    }
    public int read(byte[] p0){
       FileUploadBodyHandlerImpl traf;
       int i2;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = FileUploadBodyHandlerImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("9ed24497", objArray).intValue();
       }else if(p0 != null && (p0.length && this.file != null)){
          if (((long)this.postedLength - this.patchSize) >= 0) {
             this.isCompleted = i;
             return i1;
          }else if(this.raf == null){
             this.raf = new RandomAccessFile(this.file, "r");
          label_004f :
             long l = this.raf.length();
             FileUploadBodyHandlerImpl toffset = this.offset;
             if ((toffset - l) < 0 && ((long)this.postedLength - l) < 0) {
                this.raf.seek(toffset);
                if ((i2 = this.raf.read(p0)) != -1) {
                   toffset = this.postedLength;
                   FileUploadBodyHandlerImpl tpatchSize = this.patchSize;
                   if (((long)(toffset + i2) - tpatchSize) > 0) {
                      i2 = (int)(tpatchSize - (long)toffset);
                   }
                   i1 = i2;
                   int i3 = toffset + i1;
                   this.postedLength = i3;
                   long l1 = this.offset + (long)i1;
                   this.offset = l1;
                   if (((long)i3 - tpatchSize) >= 0 || (l1 - l) >= 0) {
                      this.isCompleted = i;
                   }
                }
                if ((traf = this.raf) != null && this.isCompleted != null) {
                   traf.close();
                }
             }else {
                this.isCompleted = i;
                if ((traf = this.raf) != null) {
                   try{
                      traf.close();
                   }catch(java.io.IOException e14){
                      TBSdkLog.e("mtopsdk.FileUploadBodyHandlerImpl", "close RandomAccessFile error", e14);
                   }
                }
                return i1;
             }
          }else {
             goto label_004f ;
          }
       }else {
          TBSdkLog.e("mtopsdk.FileUploadBodyHandlerImpl", "[read\(byte[] buffer\)]parameter buffer or file is null");
          this.isCompleted = i;
          return i1;
       }
    }
}
