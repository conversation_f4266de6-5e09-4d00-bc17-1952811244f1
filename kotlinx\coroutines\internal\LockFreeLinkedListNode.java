package kotlinx.coroutines.internal.LockFreeLinkedListNode;
import java.lang.Object;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import tb.h30;
import tb.rxk;
import tb.vzn;
import tb.ckf;
import kotlinx.coroutines.internal.LockFreeLinkedListNode$a;
import tb.tg1;
import java.lang.StringBuilder;
import kotlinx.coroutines.internal.LockFreeLinkedListNode$toString$1;
import tb.ov6;

public class LockFreeLinkedListNode	// class@00068f from classes11.dex
{
    public Object a;
    public Object b;
    public Object c;
    public static final AtomicReferenceFieldUpdater d;
    public static final AtomicReferenceFieldUpdater e;
    public static final AtomicReferenceFieldUpdater f;

    static {
       LockFreeLinkedListNode.d = AtomicReferenceFieldUpdater.newUpdater(LockFreeLinkedListNode.class, Object.class, "a");
       LockFreeLinkedListNode.e = AtomicReferenceFieldUpdater.newUpdater(LockFreeLinkedListNode.class, Object.class, "b");
       LockFreeLinkedListNode.f = AtomicReferenceFieldUpdater.newUpdater(LockFreeLinkedListNode.class, Object.class, "c");
    }
    public void LockFreeLinkedListNode(){
       super();
       this.a = this;
       this.b = this;
    }
    public static final void a(LockFreeLinkedListNode p0,LockFreeLinkedListNode p1){
       p0.g(p1);
    }
    public static final AtomicReferenceFieldUpdater b(){
       return LockFreeLinkedListNode.d();
    }
    public static final AtomicReferenceFieldUpdater d(){
       return LockFreeLinkedListNode.d;
    }
    public static final AtomicReferenceFieldUpdater p(){
       return LockFreeLinkedListNode.e;
    }
    public static final AtomicReferenceFieldUpdater r(){
       return LockFreeLinkedListNode.f;
    }
    public final boolean c(LockFreeLinkedListNode p0){
       LockFreeLinkedListNode.p().set(p0, this);
       LockFreeLinkedListNode.d().set(p0, this);
       while (true) {
          if (this.h() != this) {
             return false;
          }
          if (h30.a(LockFreeLinkedListNode.d(), this, this, p0)) {
             break ;
          }
       }
       p0.g(this);
       return true;
    }
    public final LockFreeLinkedListNode e(rxk p0){
       Object obj;
       while (true) {
          LockFreeLinkedListNode lockFreeLink = LockFreeLinkedListNode.p().get(this);
          LockFreeLinkedListNode lockFreeLink1 = null;
          LockFreeLinkedListNode lockFreeLink2 = lockFreeLink;
          while (true) {
             LockFreeLinkedListNode lockFreeLink3 = lockFreeLink1;
             break ;
          }
          while (true) {
             if ((obj = LockFreeLinkedListNode.d().get(lockFreeLink2)) == this) {
                if (lockFreeLink == lockFreeLink2) {
                   return lockFreeLink2;
                }
                if (h30.a(LockFreeLinkedListNode.p(), this, lockFreeLink, lockFreeLink2)) {
                   return lockFreeLink2;
                }
                continue ;
             }else if(this.k()){
                return lockFreeLink1;
             }else if(obj == p0){
                break ;
             }else if(obj instanceof rxk){
                obj.a(lockFreeLink2);
                continue ;
             }else if(obj instanceof vzn){
                if (lockFreeLink3 != null) {
                   if (!h30.a(LockFreeLinkedListNode.d(), lockFreeLink3, lockFreeLink2, obj.a)) {
                      continue ;
                   }else {
                      lockFreeLink2 = lockFreeLink3;
                   }
                }else {
                   lockFreeLink2 = LockFreeLinkedListNode.p().get(lockFreeLink2);
                }
             }else {
                ckf.e(obj, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeLinkedListNode{ kotlinx.coroutines.internal.LockFreeLinkedListKt.Node }");
                lockFreeLink3 = lockFreeLink2;
                lockFreeLink2 = obj;
             }
          }
          return lockFreeLink2;
       }
    }
    public final LockFreeLinkedListNode f(LockFreeLinkedListNode p0){
       while (p0.k()) {
          p0 = LockFreeLinkedListNode.p().get(p0);
       }
       return p0;
    }
    public final void g(LockFreeLinkedListNode p0){
       AtomicReferenceFieldUpdater uAtomicRefer = LockFreeLinkedListNode.p();
       while (true) {
          LockFreeLinkedListNode lockFreeLink = uAtomicRefer.get(p0);
          if (this.h() != p0) {
             return;
          }
          if (h30.a(LockFreeLinkedListNode.p(), p0, lockFreeLink, this)) {
             if (this.k()) {
                p0.e(null);
                break ;
             }
             break ;
          }
       }
       return;
    }
    public final Object h(){
       Object obj;
       AtomicReferenceFieldUpdater uAtomicRefer = LockFreeLinkedListNode.d();
       while (true) {
          obj = uAtomicRefer.get(this);
          if (!obj instanceof rxk) {
             break ;
          }else {
             obj.a(this);
          }
       }
       return obj;
    }
    public final LockFreeLinkedListNode i(){
       Object obj = this.h();
       vzn obj1 = (obj instanceof vzn)? obj: null;
       if (obj1 == null || (obj1 = obj1.a) == null) {
          ckf.e(obj, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeLinkedListNode{ kotlinx.coroutines.internal.LockFreeLinkedListKt.Node }");
          obj1 = obj;
       }
       return obj1;
    }
    public final LockFreeLinkedListNode j(){
       LockFreeLinkedListNode lockFreeLink;
       if ((lockFreeLink = this.e(null)) == null) {
          lockFreeLink = this.f(LockFreeLinkedListNode.p().get(this));
       }
       return lockFreeLink;
    }
    public boolean k(){
       return this.h() instanceof vzn;
    }
    public boolean l(){
       boolean b = (this.m() == null)? true: false;
       return b;
    }
    public final LockFreeLinkedListNode m(){
       Object obj1;
       while (true) {
          Object obj = this.h();
          if (obj instanceof vzn) {
             return obj.a;
          }
          if (obj == this) {
             return obj;
          }
          ckf.e(obj, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeLinkedListNode{ kotlinx.coroutines.internal.LockFreeLinkedListKt.Node }");
          obj1 = obj;
          if (h30.a(LockFreeLinkedListNode.d(), this, obj, obj1.n())) {
             break ;
          }
       }
       obj1.e(null);
       return null;
    }
    public final vzn n(){
       vzn ovzn;
       if ((ovzn = LockFreeLinkedListNode.r().get(this)) == null) {
          ovzn = new vzn(this);
          LockFreeLinkedListNode.r().set(this, ovzn);
       }
       return ovzn;
    }
    public final int o(LockFreeLinkedListNode p0,LockFreeLinkedListNode p1,LockFreeLinkedListNode$a p2){
       LockFreeLinkedListNode.p().set(p0, this);
       LockFreeLinkedListNode.d().set(p0, p1);
       p2.d = p1;
       if (!h30.a(LockFreeLinkedListNode.d(), this, p1, p2)) {
          return 0;
       }
       int i = (p2.a(this) == null)? 1: 2;
       return i;
    }
    public String toString(){
       return new LockFreeLinkedListNode$toString$1(this)+'@'+ov6.b(this);
    }
}
