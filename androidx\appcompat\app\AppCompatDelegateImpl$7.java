package androidx.appcompat.app.AppCompatDelegateImpl$7;
import androidx.core.view.ViewPropertyAnimatorListenerAdapter;
import androidx.appcompat.app.AppCompatDelegateImpl;
import android.view.View;
import androidx.core.view.ViewPropertyAnimatorListener;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.appcompat.widget.ActionBarContextView;
import android.view.ViewParent;
import androidx.core.view.ViewCompat;

public class AppCompatDelegateImpl$7 extends ViewPropertyAnimatorListenerAdapter	// class@000564 from classes.dex
{
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$7(AppCompatDelegateImpl p0){
       this.this$0 = p0;
       super();
    }
    public void onAnimationEnd(View p0){
       this.this$0.mActionModeView.setAlpha(1.00f);
       this.this$0.mFadeAnim.setListener(null);
       p0.mFadeAnim = null;
    }
    public void onAnimationStart(View p0){
       this.this$0.mActionModeView.setVisibility(0);
       if (this.this$0.mActionModeView.getParent() instanceof View) {
          ViewCompat.requestApplyInsets(this.this$0.mActionModeView.getParent());
       }
       return;
    }
}
