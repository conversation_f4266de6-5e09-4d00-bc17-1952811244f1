package kotlinx.coroutines.CoroutineStart$a;
import kotlinx.coroutines.CoroutineStart;
import java.lang.Enum;

public final class CoroutineStart$a	// class@000494 from classes11.dex
{
    public static final int[] $EnumSwitchMapping$0;

    static {
       int len = CoroutineStart.values().length;
       try{
          int[] ointArray = new int[len];
          ointArray[CoroutineStart.DEFAULT.ordinal()] = 1;
          try{
             e0[CoroutineStart.ATOMIC.ordinal()] = 2;
             try{
                e0[CoroutineStart.UNDISPATCHED.ordinal()] = 3;
                try{
                   e0[CoroutineStart.LAZY.ordinal()] = 4;
                   CoroutineStart$a.$EnumSwitchMapping$0 = e0;
                }catch(java.lang.NoSuchFieldError e0){
                }
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
