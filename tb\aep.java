package tb.aep;
import tb.md7;
import tb.t2o;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.structure.ComponentModel;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import tb.es7;
import com.alibaba.fastjson.JSONObject;

public class aep extends md7	// class@00187e from classes5.dex
{
    public String k;
    public String l;
    public String m;
    public String n;
    public String o;
    public static IpChange $ipChange;

    static {
       t2o.a(0x38e0061f);
    }
    public void aep(ComponentModel p0){
       super(p0);
    }
    public static Object ipc$super(aep p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/detail/ttdetail/skeleton/desc/natives/viewmodel/ServiceViewModel");
    }
    public boolean g(){
       int i = 0;
       IpChange $ipChange = aep.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("ea845f58", objArray).booleanValue();
       }
       return i;
    }
    public es7 i(){
       IpChange $ipChange = aep.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9f63107b", objArray);
    }
    public void j(JSONObject p0){
       IpChange $ipChange = aep.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a37e7e28", objArray);
          return;
       }else {
          this.k = p0.getString("iconUrl");
          this.l = p0.getString("title");
          this.m = p0.getString("subTitle");
          this.n = p0.getString("tips");
          this.o = p0.getString("jumpUrl");
          return;
       }
    }
}
