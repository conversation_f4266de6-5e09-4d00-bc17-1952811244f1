package tb.a76;
import tb.w76;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.svb;
import com.taobao.android.dinamicx.DXRuntimeContext;
import com.android.alibaba.ip.runtime.IpChange;
import tb.zg5;
import tb.ex5;
import android.view.View;
import tb.z76;
import android.util.SparseArray;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import java.util.List;
import java.util.ArrayList;
import java.lang.Integer;
import java.lang.Class;
import tb.kl6;

public class a76 extends w76	// class@001849 from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1c50026e);
    }
    public void a76(){
       super();
    }
    public static Object ipc$super(a76 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/dinamicx/pipeline/opt/DXOptSimplePipelineDiff");
    }
    public void a(svb p0,svb p1,DXRuntimeContext p2){
       DXWidgetNode uDXWidgetNod;
       int autoId;
       List list;
       ArrayList uArrayList;
       List list1;
       int i = 0;
       IpChange $ipChange = a76.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("2f392b7d", objArray);
          return;
       }else {
          zg5.p4();
          if (p0 == null && p1 == null) {
             return;
          }
          ex5 uoex5 = p1;
          if (p1 == null) {
             return;
          }
          if (p0 == null) {
             z76.b(p2, uoex5.z());
             return;
          }else {
             int i1 = uoex5.j();
             int i2 = p0.j();
             if (!i1 && !i2) {
                return;
             }
             if (!i1 && i2 > 0) {
                return;
             }
             if (i1 > 0 && !i2) {
                z76.a(uoex5.z(), p2);
                return;
             }else {
                SparseArray sparseArray = new SparseArray();
                int i3 = 0;
                while (i3 < p0.j()) {
                   if ((uDXWidgetNod = p0.h(i3).u()) != null) {
                      autoId = uDXWidgetNod.getAutoId();
                      if ((list = sparseArray.get(autoId)) == null) {
                         uArrayList = new ArrayList();
                         uArrayList.add(Integer.valueOf(i3));
                         sparseArray.put(autoId, uArrayList);
                      }else {
                         list.add(Integer.valueOf(i3));
                      }
                   }else if(zg5.q5()){
                      autoId = p0.h(i3).t();
                      if ((list = sparseArray.get(autoId)) == null) {
                         uArrayList = new ArrayList();
                         uArrayList.add(Integer.valueOf(i3));
                         sparseArray.put(autoId, uArrayList);
                      }else {
                         list.add(Integer.valueOf(i3));
                      }
                   }
                   i3 = i3 + 1;
                }
                int[] ointArray = new int[i2];
                for (autoId = 0; autoId < i2; autoId = autoId + 1) {
                   ointArray[autoId] = -1;
                }
                autoId = 0;
                while (autoId < uoex5.j()) {
                   ex5 uoex51 = uoex5.h(autoId);
                   if (uoex51.B().getSourceWidget() != null && sparseArray.size()) {
                      if ((list1 = sparseArray.get(uoex51.u().getAutoId())) != null && !list1.isEmpty()) {
                         ointArray[list1.get(i).intValue()] = autoId;
                         list1.remove(i);
                      }else {
                         z76.b(p2, uoex51.z());
                      }
                   }else if(zg5.q5() && (uoex51.t() >= 0 && sparseArray.size())){
                      if ((list1 = sparseArray.get(uoex51.t())) != null && !list1.isEmpty()) {
                         ointArray[list1.get(i).intValue()] = autoId;
                         list1.remove(i);
                      }else {
                         z76.b(p2, uoex51.z());
                      }
                   }else {
                      z76.b(p2, uoex51.z());
                   }
                   autoId = autoId + 1;
                }
                while (i < i2) {
                   i1 = ointArray[i];
                   ex5 uoex52 = p0.h(i);
                   if (i1 != -1) {
                      ex5 uoex53 = uoex5.h(i1);
                      if (uoex52 != null && uoex53.m() != uoex52.m()) {
                         z76.b(p2, uoex53.z());
                      }else {
                         uoex52.U(uoex53.z());
                         if (uoex52.z() != null) {
                            kl6.e(uoex52.z(), uoex52);
                         }
                         if (uoex52.j() > 0 || uoex53.j() > 0) {
                            this.a(uoex52, uoex53, p2);
                         }
                      }
                   }
                   i = i + 1;
                }
                return;
             }
          }
       }
    }
}
