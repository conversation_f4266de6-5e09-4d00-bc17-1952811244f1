package androidx.activity.result.PickVisualMediaRequest;
import java.lang.Object;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$ImageAndVideo;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType;
import java.lang.String;
import tb.ckf;

public final class PickVisualMediaRequest	// class@0004c2 from classes.dex
{
    private ActivityResultContracts$PickVisualMedia$VisualMediaType mediaType;

    public void PickVisualMediaRequest(){
       super();
       this.mediaType = ActivityResultContracts$PickVisualMedia$ImageAndVideo.INSTANCE;
    }
    public final ActivityResultContracts$PickVisualMedia$VisualMediaType getMediaType(){
       return this.mediaType;
    }
    public final void setMediaType$activity_release(ActivityResultContracts$PickVisualMedia$VisualMediaType p0){
       ckf.g(p0, "<set-?>");
       this.mediaType = p0;
    }
}
