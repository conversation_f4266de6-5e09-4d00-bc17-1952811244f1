package me.leolin.shortcutbadger.impl.XiaomiHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import java.lang.Class;
import java.lang.reflect.Field;
import java.lang.reflect.AccessibleObject;
import java.lang.Integer;
import android.content.Intent;
import java.lang.StringBuilder;
import tb.ol2;
import android.os.Build;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.app.NotificationManager;
import android.app.Notification$Builder;
import java.lang.CharSequence;
import android.app.Notification;
import java.lang.reflect.Method;
import me.leolin.shortcutbadger.ShortcutBadgeException;
import java.lang.Exception;

public class XiaomiHomeBadger implements po1	// class@00076a from classes11.dex
{
    public ResolveInfo a;
    public static final String EXTRA_UPDATE_APP_COMPONENT_NAME = "android.intent.extra.update_application_component_name";
    public static final String EXTRA_UPDATE_APP_MSG_TEXT = "android.intent.extra.update_application_message_text";
    public static final String INTENT_ACTION = "android.intent.action.APPLICATION_MESSAGE_UPDATE";

    public void XiaomiHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"com.miui.miuilite","com.miui.home","com.miui.miuihome","com.miui.miuihome2","com.miui.mihome","com.miui.mihome2","com.i.miui.launcher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       Intent obj;
       Field declaredFiel;
       String str1;
       String str = "";
       try{
          obj = Class.forName("android.app.MiuiNotification").newInstance();
          declaredFiel = obj.getClass().getDeclaredField("messageCount");
          declaredFiel.setAccessible(true);
          if (!p2) {
             str1 = str;
          label_0023 :
             declaredFiel.set(obj, String.valueOf(str1));
          }else {
             str1 = Integer.valueOf(p2);
             goto label_0023 ;
          }
       }catch(java.lang.Exception e0){
          obj = new Intent("android.intent.action.APPLICATION_MESSAGE_UPDATE");
          obj.putExtra("android.intent.extra.update_application_component_name", p1.getPackageName()+"/"+p1.getClassName());
          if (p2) {
             Integer integer = Integer.valueOf(p2);
          }
          obj.putExtra("android.intent.extra.update_application_message_text", String.valueOf(e0));
          if (ol2.a(p0, obj)) {
             p0.sendBroadcast(obj);
          }
       }catch(java.lang.Exception e0){
          declaredFiel.set(obj, Integer.valueOf(p2));
       }
       if (Build.MANUFACTURER.equalsIgnoreCase("Xiaomi")) {
          this.c(p0, p2);
       }
       return;
    }
    public final void c(Context p0,int p1){
       Notification notification;
       int i = 1;
       if (this.a == null) {
          Intent intent = new Intent("android.intent.action.MAIN");
          intent.addCategory("android.intent.category.HOME");
          this.a = p0.getPackageManager().resolveActivity(intent, 0x10000);
       }
       if (this.a != null) {
          NotificationManager systemServic = p0.getSystemService("notification");
          notification = new Notification$Builder(p0).setContentTitle("").setContentText("").setSmallIcon(this.a.getIconResource()).build();
          try{
             Object obj = notification.getClass().getDeclaredField("extraNotification").get(notification);
             Class[] uClassArray = new Class[i];
             uClassArray[0] = Integer.TYPE;
             Object[] objArray = new Object[i];
             objArray[0] = Integer.valueOf(p1);
             obj.getClass().getDeclaredMethod("setMessageCount", uClassArray).invoke(obj, objArray);
             systemServic.notify(0, notification);
          }catch(java.lang.Exception e9){
             throw new ShortcutBadgeException("not able to set badge", e9);
          }
       }
       return;
    }
}
