package tb.a6i;
import tb.av;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.util.List;
import tb.bn7;
import com.android.alibaba.ip.runtime.IpChange;
import tb.rak;
import java.lang.Boolean;

public class a6i extends av	// class@001841 from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1c50003d);
    }
    public void a6i(){
       super();
    }
    public static Object ipc$super(a6i p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/dinamic/expressionv2/ExepressionEvaluation/Match");
    }
    public Object a(List p0,bn7 p1){
       IpChange $ipChange = a6i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("ab0b6f88", objArray);
       }else if(p0 != null && (p0.size() == 2 && Boolean.valueOf(rak.b(p0.get(0).toString())).booleanValue())){
          return p0.get(1);
       }else {
          return null;
       }
    }
}
