package androidx.appcompat.app.AppCompatDelegateImpl$Api33Impl;
import java.lang.Object;
import android.app.Activity;
import android.window.OnBackInvokedDispatcher;
import tb.t31;
import androidx.appcompat.app.AppCompatDelegateImpl;
import android.window.OnBackInvokedCallback;
import java.util.Objects;
import tb.nl00;
import tb.r31;
import tb.ml00;
import tb.q31;
import tb.s31;

public class AppCompatDelegateImpl$Api33Impl	// class@00056d from classes.dex
{

    private void AppCompatDelegateImpl$Api33Impl(){
       super();
    }
    public static OnBackInvokedDispatcher getOnBackInvokedDispatcher(Activity p0){
       return t31.a(p0);
    }
    public static OnBackInvokedCallback registerOnBackPressedCallback(Object p0,AppCompatDelegateImpl p1){
       Objects.requireNonNull(p1);
       nl00 onl00 = new nl00(p1);
       ml00.a(r31.a(p0), 0xf4240, onl00);
       return onl00;
    }
    public static void unregisterOnBackInvokedCallback(Object p0,Object p1){
       s31.a(r31.a(p0), q31.a(p1));
    }
}
