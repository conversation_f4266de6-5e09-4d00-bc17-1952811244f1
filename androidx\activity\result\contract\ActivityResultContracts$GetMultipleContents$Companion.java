package androidx.activity.result.contract.ActivityResultContracts$GetMultipleContents$Companion;
import java.lang.Object;
import tb.a07;
import android.content.Intent;
import java.util.List;
import java.lang.String;
import tb.ckf;
import java.util.LinkedHashSet;
import android.net.Uri;
import java.util.AbstractCollection;
import android.content.ClipData;
import tb.yz3;
import android.content.ClipData$Item;
import java.util.ArrayList;
import java.util.Collection;

public final class ActivityResultContracts$GetMultipleContents$Companion	// class@0004c9 from classes.dex
{

    private void ActivityResultContracts$GetMultipleContents$Companion(){
       super();
    }
    public void ActivityResultContracts$GetMultipleContents$Companion(a07 p0){
       super();
    }
    public final List getClipDataUris$activity_release(Intent p0){
       Uri data;
       ClipData clipData;
       Uri uri;
       ckf.g(p0, "<this>");
       LinkedHashSet linkedHashSe = new LinkedHashSet();
       if ((data = p0.getData()) != null) {
          linkedHashSe.add(data);
       }
       if ((clipData = p0.getClipData()) == null && linkedHashSe.isEmpty()) {
          return yz3.i();
       }else if(clipData != null){
          int itemCount = clipData.getItemCount();
          int i = 0;
          while (i < itemCount) {
             if ((uri = clipData.getItemAt(i).getUri()) != null) {
                linkedHashSe.add(uri);
             }
             i = i + 1;
          }
       }
       return new ArrayList(linkedHashSe);
    }
}
