package mtopsdk.mtop.domain.ApiTypeEnum;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class ApiTypeEnum extends Enum	// class@0007b4 from classes11.dex
{
    private String apiType;
    private static final ApiTypeEnum[] $VALUES;
    public static IpChange $ipChange;
    public static final ApiTypeEnum ISV_OPEN_API;

    static {
       ApiTypeEnum uApiTypeEnum = new ApiTypeEnum("ISV_OPEN_API", 0, "isv_open_api");
       ApiTypeEnum.ISV_OPEN_API = uApiTypeEnum;
       ApiTypeEnum[] uApiTypeEnum1 = new ApiTypeEnum[]{uApiTypeEnum};
       ApiTypeEnum.$VALUES = uApiTypeEnum1;
    }
    private void ApiTypeEnum(String p0,int p1,String p2){
       super(p0, p1);
       this.apiType = p2;
    }
    public static Object ipc$super(ApiTypeEnum p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/domain/ApiTypeEnum");
    }
    public static ApiTypeEnum valueOf(String p0){
       IpChange $ipChange = ApiTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(ApiTypeEnum.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("ee9069b6", objArray);
    }
    public static ApiTypeEnum[] values(){
       IpChange $ipChange = ApiTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ApiTypeEnum.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("91355365", objArray);
    }
    public String getApiType(){
       IpChange $ipChange = ApiTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.apiType;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1cffc57b", objArray);
    }
}
