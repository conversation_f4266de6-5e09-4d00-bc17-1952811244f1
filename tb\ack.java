package tb.ack;
import tb.vuo;
import tb.t2o;
import tb.bck;
import tb.paq;
import java.lang.Object;
import java.lang.ThreadLocal;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class ack implements vuo	// class@001b1d from classes8.dex
{
    public final bck a;
    public final paq b;
    public final ack c;
    public static IpChange $ipChange;

    static {
       t2o.a(0x14900022);
       t2o.a(0x14900003);
    }
    public void ack(bck p0,paq p1){
       super();
       this.a = p0;
       this.b = p1;
       this.c = p0.a.get();
       p0.a.set(this);
    }
    public void close(){
       IpChange $ipChange = ack.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("e32ba67f", objArray);
          return;
       }else if(this.a.a.get() != this){
          return;
       }else {
          this.a.a.set(this.c);
          return;
       }
    }
    public paq e(){
       IpChange $ipChange = ack.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c844e39", objArray);
    }
}
