package tb.a4o;
import tb.t2o;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.a4o$b;
import com.taobao.android.detail.ttdetail.request.MtopInfo;
import java.lang.Boolean;
import java.lang.Number;
import java.lang.Integer;
import tb.a4o$a;
import tb.owc;

public class a4o	// class@001838 from classes5.dex
{
    public final String a;
    public static IpChange $ipChange;
    public static final Map b;
    public static final Map c;
    public static final ReentrantLock d;
    public static final Map e;

    static {
       t2o.a(0x38e00497);
       a4o.b = new ConcurrentHashMap();
       a4o.c = new ConcurrentHashMap();
       a4o.e = new ConcurrentHashMap();
       a4o.d = new ReentrantLock();
    }
    public void a4o(String p0){
       super();
       this.a = p0;
    }
    public static void c(String p0){
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("c0a1b9e1", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          return;
       }else {
          a4o.c.remove(p0);
          a4o.b.remove(p0);
          a4o.e.remove(p0);
          return;
       }
    }
    public static void d(String p0){
       Object obj;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("15ddfb9", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          return;
       }else {
          Map b = a4o.b;
          if ((obj = b.get(p0)) == null || !obj instanceof a4o$b) {
             b.put(p0, new a4o$b());
          }
          return;
       }
    }
    public static String f(String p0){
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("1b18725b", objArray);
       }else if(p0 == null){
          return null;
       }else {
          return a4o.c.get(p0);
       }
    }
    public static boolean i(MtopInfo p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("7a613492", objArray).booleanValue();
       }else if(p0 == null){
          return i;
       }else if(p0.b() == 3){
          i = true;
       }
       return i;
    }
    public static boolean j(MtopInfo p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("39ec94f8", objArray).booleanValue();
       }else if(p0.b() == i1){
          i = true;
       }
       return i;
    }
    public static boolean k(MtopInfo p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("a895cdb7", objArray).booleanValue();
       }else if(p0 == null){
          return i;
       }else if(p0.b() == 2){
          i = true;
       }
       return i;
    }
    public static void n(String p0,String p1){
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("12dd9fe3", objArray);
          return;
       }else if(p0 != null && p1 != null){
          a4o.c.put(p0, p1);
       }
       return;
    }
    public static void o(String p0){
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("35c0d9a5", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          return;
       }else {
          a4o.e.remove(p0);
          return;
       }
    }
    public static int p(String p0){
       Integer integer;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("b2875bef", objArray).intValue();
       }else if(TextUtils.isEmpty(p0)){
          return 0;
       }else if((integer = a4o.e.get(p0)) == null){
          return 0;
       }else {
          return integer.intValue();
       }
    }
    public static void q(String p0,int p1){
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,new Integer(p1)};
          $ipChange.ipc$dispatch("5a79012e", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          return;
       }else {
          a4o.e.put(p0, Integer.valueOf(p1));
          return;
       }
    }
    public boolean a(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("ab1d87b6", objArray).booleanValue();
       }else if(!TextUtils.isEmpty(this.a) && a4o.b.get(this.a) != null){
          i = true;
       }
       return i;
    }
    public void b(){
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("51c437d7", objArray);
          return;
       }else {
          a4o.b.remove(this.a);
          return;
       }
    }
    public a4o$a e(){
       a4o$b uob;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("bf5553b", objArray);
       }else {
          ReentrantLock d = a4o.d;
          d.lock();
          a4o$a uoa = ((uob = this.g()) == null)? null: uob.b();
          d.unlock();
          return uoa;
       }
    }
    public a4o$b g(){
       IpChange $ipChange = a4o.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a4o.b.get(this.a);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9c78dbbf", objArray);
    }
    public owc h(){
       a4o$b uob;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("75ca5557", objArray);
       }else {
          ReentrantLock d = a4o.d;
          d.lock();
          owc oowc = ((uob = this.g()) == null)? null: uob.a();
          d.unlock();
          return oowc;
       }
    }
    public void l(a4o$a p0){
       a4o$b uob;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("adb18f96", objArray);
          return;
       }else {
          ReentrantLock d = a4o.d;
          d.lock();
          if ((uob = a4o.b.get(this.a)) == null) {
             d.unlock();
             return;
          }else {
             uob.d(p0);
             d.unlock();
             return;
          }
       }
    }
    public void m(owc p0){
       a4o$b uob;
       IpChange $ipChange = a4o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("189c08e2", objArray);
          return;
       }else {
          ReentrantLock d = a4o.d;
          d.lock();
          if ((uob = a4o.b.get(this.a)) == null) {
             d.unlock();
             return;
          }else {
             uob.c(p0);
             d.unlock();
             return;
          }
       }
    }
}
