package tb.ad7$c;
import anetwork.channel.Response;
import tb.t2o;
import mtopsdk.mtop.domain.MtopResponse;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.Map;
import java.lang.Throwable;
import anetwork.channel.statist.StatisticData;
import java.lang.Number;

public class ad7$c implements Response	// class@001876 from classes5.dex
{
    public final MtopResponse a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x38e005ca);
       t2o.a(0x264001e2);
    }
    public void ad7$c(MtopResponse p0){
       super();
       this.a = p0;
    }
    public byte[] getBytedata(){
       IpChange $ipChange = ad7$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a.getBytedata();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6e9b5c0e", objArray);
    }
    public Map getConnHeadFields(){
       IpChange $ipChange = ad7$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a.getHeaderFields();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("3de6d7b7", objArray);
    }
    public String getDesc(){
       IpChange $ipChange = ad7$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a.getRetMsg();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f24b4252", objArray);
    }
    public Throwable getError(){
       IpChange $ipChange = ad7$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4db6723e", objArray);
    }
    public StatisticData getStatisticData(){
       IpChange $ipChange = ad7$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("396a8f27", objArray);
    }
    public int getStatusCode(){
       IpChange $ipChange = ad7$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a.getResponseCode();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("eae362ef", objArray).intValue();
    }
    public String toString(){
       IpChange $ipChange = ad7$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a.toString();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
