package kotlinx.coroutines.DelayKt;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.DelayKt$awaitCancellation$1;
import tb.dkf;
import java.lang.IllegalStateException;
import java.lang.String;
import kotlin.b;
import kotlinx.coroutines.c;
import kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt;
import tb.jv6;
import kotlin.KotlinNothingValueException;
import tb.xhv;
import kotlin.coroutines.d;
import kotlinx.coroutines.h;
import tb.q23;
import kotlin.coroutines.c;
import kotlin.coroutines.d$c;
import kotlin.coroutines.d$b;
import tb.t07;
import tb.s08;
import kotlin.time.DurationUnit;
import tb.w08;
import kotlin.NoWhenBranchMatchedException;

public final class DelayKt	// class@000498 from classes11.dex
{

    public static final Object a(ar4 p0){
       DelayKt$awaitCancellation$1 uoawaitCance;
       DelayKt$awaitCancellation$1 label1;
       if (p0 instanceof DelayKt$awaitCancellation$1) {
          uoawaitCance = p0;
          DelayKt$awaitCancellation$1 label = uoawaitCance.label;
          int i = Integer.MIN_VALUE;
          if ((label & i)) {
             uoawaitCance.label = label - i;
          label_0018 :
             DelayKt$awaitCancellation$1 result = uoawaitCance.result;
             Object obj = dkf.d();
             if ((label1 = uoawaitCance.label) != null) {
                if (label1 != 1) {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }else {
                   b.b(result);
                }
             }else {
                b.b(result);
                uoawaitCance.label = 1;
                c uoc = new c(IntrinsicsKt__IntrinsicsJvmKt.c(uoawaitCance), 1);
                uoc.E();
                if ((uoc = uoc.A()) == dkf.d()) {
                   jv6.c(uoawaitCance);
                }
                if (uoc == obj) {
                   return obj;
                }
             }
             throw new KotlinNothingValueException();
          }
       }
       uoawaitCance = new DelayKt$awaitCancellation$1(p0);
       goto label_0018 ;
    }
    public static final Object b(long p0,ar4 p1){
       Object obj;
       if ((p0) <= 0) {
          return xhv.INSTANCE;
       }
       c uoc = new c(IntrinsicsKt__IntrinsicsJvmKt.c(p1), 1);
       uoc.E();
       if ((p0 - Long.MAX_VALUE) < 0) {
          DelayKt.c(uoc.getContext()).c(p0, uoc);
       }
       if ((obj = uoc.A()) == dkf.d()) {
          jv6.c(p1);
       }
       if (obj == dkf.d()) {
          return obj;
       }else {
          return xhv.INSTANCE;
       }
    }
    public static final h c(d p0){
       d$b uob = p0.get(c.Key);
       if (uob instanceof h) {
       }else {
          uob = null;
       }
       if (uob == null) {
          uob = t07.a();
       }
       return uob;
    }
    public static final long d(long p0){
       boolean b;
       if ((b = s08.U(p0)) == true) {
          p0 = s08.v(s08.W(p0, w08.p(0xf423f, DurationUnit.NANOSECONDS)));
       }else if(!b){
          p0 = 0;
       }else {
          throw new NoWhenBranchMatchedException();
       }
       return p0;
    }
}
