package mtopsdk.instanceconfigs.a;
import tb.t2o;
import java.util.HashMap;
import java.lang.Object;
import android.content.Context;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.mtop.global.MtopConfig;
import mtopsdk.mtop.domain.MtopResponse;
import java.lang.Integer;
import mtopsdk.mtop.util.MtopStatistics;
import mtopsdk.mtop.domain.EnvModeEnum;
import mtopsdk.mtop.global.MtopConfig$MtopDomain;
import mtopsdk.common.util.StringUtils;
import java.lang.System;
import mtopsdk.instanceconfigs.MtopExternalInstanceConfigsData$a;
import mtopsdk.instanceconfigs.MtopExternalInstanceConfigsGetResponse;
import java.lang.Class;
import com.alibaba.fastjson.JSON;
import mtopsdk.mtop.domain.BaseOutDo;
import mtopsdk.instanceconfigs.MtopExternalInstanceConfigsData;
import java.util.List;
import java.util.Iterator;
import java.lang.CharSequence;
import android.text.TextUtils;
import mtopsdk.common.util.ConfigStoreManager;
import java.lang.StringBuilder;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import mtopsdk.instanceconfigs.MtopExternalInstanceConfigsGetRequest;
import mtopsdk.mtop.intf.Mtop;
import mtopsdk.mtop.domain.IMTOPDataObject;
import mtopsdk.mtop.intf.MtopBuilder;
import java.nio.charset.StandardCharsets;
import java.nio.charset.Charset;
import java.lang.Throwable;
import java.lang.Long;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.FutureTask;
import mtopsdk.instanceconfigs.a$a;
import java.util.concurrent.Callable;
import mtopsdk.instanceconfigs.a$b;
import java.lang.Runnable;
import mtopsdk.mtop.util.MtopSDKThreadPoolExecutorFactory;

public class a	// class@000783 from classes11.dex
{
    public static IpChange $ipChange;
    public static final Map a;
    public static final Map b;
    public static a c;

    static {
       t2o.a(0x2530008a);
       a.a = new HashMap();
       a.b = new HashMap();
    }
    public void a(){
       super();
    }
    public static String a(a p0,Context p1,String p2,String p3){
       IpChange $ipChange = a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.f(p1, p2, p3);
       }
       Object[] objArray = new Object[]{p0,p1,p2,p3};
       return $ipChange.ipc$dispatch("bde9e089", objArray);
    }
    public static a c(){
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("d0d531d2", objArray);
       }else if(a.c == null){
          a uoa = a.class;
          _monitor_enter(uoa);
          if (a.c == null) {
             a.c = new a();
          }
          _monitor_exit(uoa);
       }
       return a.c;
    }
    public final void b(MtopConfig p0,String p1,int p2,MtopResponse p3,String p4){
       MtopStatistics mtopStat;
       int i = 1;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),p3,p4};
          $ipChange.ipc$dispatch("9ca203a9", objArray);
          return;
       }else if((mtopStat = p3.getMtopStat()) == null){
          return;
       }else {
          mtopStat.configReqDomain = p0.mtopDomain.getDomain(p0.envMode);
          mtopStat.instanceId = "INNER";
          mtopStat.accountSite = p1;
          mtopStat.configRequestType = StringUtils.isBlank(p1) ^ i;
          mtopStat.configReqStartTime = (long)p2;
          mtopStat.configReqFinishTime = System.currentTimeMillis();
          if (!p3.isApiSuccess()) {
             i = -1;
          }else if(StringUtils.isBlank(p4)){
             i = 0;
          }
          mtopStat.configReturnType = i;
          mtopStat.commitCustomStatData(0, mtopStat);
          return;
       }
    }
    public final MtopExternalInstanceConfigsData$a d(String p0,String p1){
       BaseOutDo uBaseOutDo;
       MtopExternalInstanceConfigsData data;
       MtopExternalInstanceConfigsData externalInst;
       MtopExternalInstanceConfigsData$a uoa;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("740faec5", objArray);
       }else {
          CharSequence uCharSequenc = null;
          if (StringUtils.isNotBlank(p0) && ((uBaseOutDo = JSON.parseObject(p0, MtopExternalInstanceConfigsGetResponse.class)) != null && ((data = uBaseOutDo.getData()) != null && ((externalInst = data.externalInstanceConfigs) != null && externalInst.size() > 0)))) {
             Iterator iterator = data.externalInstanceConfigs.iterator();
             while (iterator.hasNext()) {
                if ((uoa = iterator.next()) != null && TextUtils.equals(uCharSequenc, p1)) {
                   uCharSequenc = uoa;
                   break ;
                }
             }
          }
          return uCharSequenc;
       }
    }
    public MtopExternalInstanceConfigsData$a e(Context p0,String p1,String p2){
       Map a;
       MtopExternalInstanceConfigsData$a uoa;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("274d0004", objArray);
       }else {
          a = a.a;
          if ((uoa = a.get(p2)) != null) {
             return uoa;
          }
          uoa = null;
          if (p0 == null) {
             return uoa;
          }
          MtopExternalInstanceConfigsData$a uoa1 = this.d(ConfigStoreManager.getInstance().getConfigItem(p0, "MtopConfigStore", "MTOPSDK_INSTANCE_CONFIG_STORE"+p1, "instance_config"), p2);
          if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)) {
             TBSdkLog.i("mtopsdk.InstanceConfigsManager", "[getLocalInstanceConfig]get instanceId from store accountSite="+p2+"; instanceId="+uoa);
          }
          if (uoa1 != null) {
             a.put(p2, uoa1);
          }
          return uoa1;
       }
    }
    public final String f(Context p0,String p1,String p2){
       byte[] bytedata;
       object oobject = p1;
       object oobject1 = p2;
       String str = "MTOPSDK_INSTANCE_CONFIG_STORE";
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,oobject,oobject1};
          return $ipChange.ipc$dispatch("3d5b75e", objArray);
       }else {
          String str1 = "mtopsdk.InstanceConfigsManager";
          if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)) {
             TBSdkLog.i(str1, "[getRemoteInstanceConfig] called!accountSite="+oobject1);
          }
          int i = (int)System.currentTimeMillis();
          Context uContext = null;
          Mtop mtop = Mtop.instance("INNER", uContext);
          MtopResponse mtopResponse = mtop.build(new MtopExternalInstanceConfigsGetRequest(), uContext).setBizId(4099).syncRequest();
          String str2 = "";
          if (mtopResponse.isApiSuccess()) {
             if ((bytedata = mtopResponse.getBytedata()) != null && bytedata.length) {
                String str3 = new String(bytedata, StandardCharsets.UTF_8);
                if (p0 == null) {
                   return uContext;
                }else {
                   ConfigStoreManager.getInstance().saveConfigItem(p0, "MtopConfigStore", str+oobject, "instance_config", str3);
                   str2 = str3;
                }
             }else {
                TBSdkLog.e(str1, "jsonData is blank");
                return uContext;
             }
          }
          this.b(mtop.getMtopConfig(), p2, i, mtopResponse, str2);
          return str2;
       }
    }
    public MtopExternalInstanceConfigsData$a g(Context p0,String p1){
       IpChange $ipChange = a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h(p0, p1, "", 5000);
       }
       Object[] objArray = new Object[]{this,p0,p1};
       return $ipChange.ipc$dispatch("de6a0955", objArray);
    }
    public MtopExternalInstanceConfigsData$a h(Context p0,String p1,String p2,long p3){
       Future uFuture;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,new Long(p3)};
          return $ipChange.ipc$dispatch("f481724b", objArray);
       }else if(StringUtils.isBlank(p1)){
          return null;
       }else {
          Map b = a.b;
          if ((uFuture = b.get(p1)) != null && !uFuture.isDone()) {
             String str = ((p3) > 0)? uFuture.get(p3, TimeUnit.MILLISECONDS): uFuture.get();
             return this.d(str, p2);
          }else {
             FutureTask uFutureTask = new FutureTask(new a$a(this, p0, p1, p2));
             MtopSDKThreadPoolExecutorFactory.submit(new a$b(this, uFutureTask));
             b.put(p1, uFutureTask);
             if ((p3) > 0) {
                return this.d(uFutureTask.get(p3, TimeUnit.MILLISECONDS), p2);
             }
             return this.d(uFutureTask.get(), p2);
          }
       }
    }
}
