package kotlinx.coroutines.selects.SelectImplementation;
import tb.c9p;
import tb.l9p;
import tb.g23;
import java.lang.Object;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import kotlin.coroutines.d;
import tb.u1r;
import kotlinx.coroutines.selects.SelectKt;
import java.util.ArrayList;
import tb.ar4;
import kotlinx.coroutines.selects.SelectImplementation$a;
import java.lang.UnsupportedOperationException;
import kotlinx.coroutines.c;
import kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt;
import tb.h30;
import tb.g1a;
import java.util.List;
import java.lang.Iterable;
import java.util.Iterator;
import tb.xhv;
import tb.k9p;
import tb.dkf;
import tb.jv6;
import java.lang.IllegalStateException;
import java.lang.StringBuilder;
import tb.d9p;
import tb.j9p;
import tb.w1a;
import tb.v8p;
import tb.rr7;
import java.lang.Throwable;
import tb.ckf;
import java.util.Collection;
import tb.dv6;
import kotlinx.coroutines.selects.SelectImplementation$doSelectSuspend$1;
import kotlin.b;
import tb.f9p;
import tb.u1a;
import kotlinx.coroutines.selects.SelectImplementation$processResultAndInvokeBlockRecoveringException$1;
import tb.vu4;
import tb.rgq;
import kotlinx.coroutines.selects.TrySelectDetailedResult;
import tb.q23;
import tb.xz3;
import tb.i04;

public class SelectImplementation extends g23 implements c9p, l9p	// class@0006b4 from classes11.dex
{
    public final d a;
    public Object b;
    public List c;
    public Object d;
    public int e;
    public Object f;
    public static final AtomicReferenceFieldUpdater g;

    static {
       SelectImplementation.g = AtomicReferenceFieldUpdater.newUpdater(SelectImplementation.class, Object.class, "b");
    }
    public void SelectImplementation(d p0){
       super();
       this.a = p0;
       this.b = SelectKt.g();
       this.c = new ArrayList(2);
       this.e = -1;
       this.f = SelectKt.d();
    }
    public static final AtomicReferenceFieldUpdater B(){
       return SelectImplementation.g;
    }
    public static final Object g(SelectImplementation p0,ar4 p1){
       return p0.q(p1);
    }
    public static final Object h(SelectImplementation p0){
       return p0.f;
    }
    public static final AtomicReferenceFieldUpdater i(){
       return SelectImplementation.B();
    }
    public static final Object j(SelectImplementation p0,SelectImplementation$a p1,Object p2,ar4 p3){
       return p0.u(p1, p2, p3);
    }
    public static final void k(SelectImplementation p0,Object p1){
       p0.x(p1);
    }
    public static Object p(SelectImplementation p0,ar4 p1){
       if (p0.t()) {
          return p0.n(p1);
       }
       return p0.q(p1);
    }
    public static void w(SelectImplementation p0,SelectImplementation$a p1,boolean p2,int p3,Object p4){
       if (p4 != null) {
          throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: register");
       }
       if ((p3 & 0x01)) {
          p2 = false;
       }
       p0.v(p1, p2);
       return;
    }
    public final Object A(ar4 p0){
       Iterator obj;
       Object obj1;
       c uoc = new c(IntrinsicsKt__IntrinsicsJvmKt.c(p0), 1);
       uoc.E();
       AtomicReferenceFieldUpdater uAtomicRefer = SelectImplementation.i();
       while (true) {
          if ((obj = uAtomicRefer.get(this)) == SelectKt.g()) {
             if (h30.a(SelectImplementation.i(), this, obj, uoc)) {
                uoc.h(this);
             }
          }else if(obj instanceof List){
             if (h30.a(SelectImplementation.i(), this, obj, SelectKt.g())) {
                obj = obj.iterator();
                while (obj.hasNext()) {
                   SelectImplementation.k(this, obj.next());
                }
             }
          }else if(obj instanceof SelectImplementation$a){
             uoc.l(xhv.INSTANCE, obj.a(this, SelectImplementation.h(this)));
          }else {
             break ;
          }
          if ((obj1 = uoc.A()) == dkf.d()) {
             jv6.c(p0);
          }
          if (obj1 == dkf.d()) {
             return obj1;
          }else {
             return xhv.INSTANCE;
          }
       }
       throw new IllegalStateException("unexpected state: "+obj.toString());
    }
    public void a(d9p p0,g1a p1){
       SelectImplementation$a v8 = new SelectImplementation$a(this, p0.c(), p0.b(), p0.d(), SelectKt.i(), p1, p0.a());
       SelectImplementation.w(this, v8, false, 1, null);
    }
    public void b(Object p0){
       this.f = p0;
    }
    public void c(v8p p0,int p1){
       this.d = p0;
       this.e = p1;
    }
    public void d(rr7 p0){
       this.d = p0;
    }
    public boolean e(Object p0,Object p1){
       boolean b = (!this.z(p0, p1))? true: false;
       return b;
    }
    public void f(Throwable p0){
       Object obj;
       SelectImplementation tc;
       AtomicReferenceFieldUpdater uAtomicRefer = SelectImplementation.B();
       while (true) {
          if ((obj = uAtomicRefer.get(this)) == SelectKt.f()) {
             return;
          }
          if (h30.a(uAtomicRefer, this, obj, SelectKt.e())) {
             if ((tc = this.c) == null) {
                return;
             }
             Iterator iterator = tc.iterator();
             while (iterator.hasNext()) {
                iterator.next().b();
             }
             this.f = SelectKt.d();
             this.c = null;
             return;
          }
       }
    }
    public d getContext(){
       return this.a;
    }
    public Object invoke(Object p0){
       this.f(p0);
       return xhv.INSTANCE;
    }
    public final void l(Object p0){
       SelectImplementation tc = this.c;
       ckf.d(tc);
       if (!tc instanceof Collection || !tc.isEmpty()) {
          Iterator iterator = tc.iterator();
          while (iterator.hasNext()) {
             if (iterator.next().a != p0) {
             }else {
                throw new IllegalStateException("Cannot use select clauses on the same object: "+p0.toString());
             }
          }
       }
       return;
    }
    public final void m(SelectImplementation$a p0){
       SelectImplementation tc;
       SelectImplementation$a uoa;
       if ((tc = this.c) == null) {
          return;
       }
       Iterator iterator = tc.iterator();
       while (iterator.hasNext()) {
          if ((uoa = iterator.next()) != p0) {
             uoa.b();
          }
       }
       SelectImplementation.B().set(this, SelectKt.f());
       this.f = SelectKt.d();
       this.c = null;
       return;
    }
    public final Object n(ar4 p0){
       Object obj = SelectImplementation.B().get(this);
       ckf.e(obj, "null cannot be cast to non-null type kotlinx.coroutines.selects.SelectImplementation.ClauseData<R of kotlinx.coroutines.selects.SelectImplementation>");
       SelectImplementation tf = this.f;
       this.m(obj);
       if (!dv6.c()) {
          return obj.c(obj.d(tf), p0);
       }
       return this.u(obj, tf, p0);
    }
    public Object o(ar4 p0){
       return SelectImplementation.p(this, p0);
    }
    public final Object q(ar4 p0){
       SelectImplementation$doSelectSuspend$1 uodoSelectSu;
       SelectImplementation$doSelectSuspend$1 label1;
       if (p0 instanceof SelectImplementation$doSelectSuspend$1) {
          uodoSelectSu = p0;
          SelectImplementation$doSelectSuspend$1 label = uodoSelectSu.label;
          int i = Integer.MIN_VALUE;
          if ((label & i)) {
             uodoSelectSu.label = label - i;
          label_0018 :
             SelectImplementation$doSelectSuspend$1 result = uodoSelectSu.result;
             Object obj = dkf.d();
             if ((label1 = uodoSelectSu.label) != null) {
                if (label1 != 1) {
                   if (label1 == 2) {
                      b.b(result);
                   label_0057 :
                      return result;
                   }else {
                      throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                   }
                }else {
                   label1 = uodoSelectSu.L$0;
                   b.b(result);
                }
             }else {
                b.b(result);
                uodoSelectSu.L$0 = this;
                uodoSelectSu.label = 1;
                if (this.A(uodoSelectSu) == obj) {
                   return obj;
                }else {
                   SelectImplementation selectImplem = this;
                }
             }
             uodoSelectSu.L$0 = 0;
             uodoSelectSu.label = 2;
             if ((result = label1.n(uodoSelectSu)) == obj) {
                return obj;
             }else {
                goto label_0057 ;
             }
          }
       }
       uodoSelectSu = new SelectImplementation$doSelectSuspend$1(this, p0);
       goto label_0018 ;
    }
    public final SelectImplementation$a r(Object p0){
       SelectImplementation tc = this.c;
       SelectImplementation$a uoa = null;
       if (tc == null) {
          return uoa;
       }
       Iterator iterator = tc.iterator();
       while (true) {
          if (iterator.hasNext()) {
             Object obj = iterator.next();
             if (obj.a == p0) {
                uoa = obj;
             }
          }
          if (uoa != null) {
             break ;
          }else {
             throw new IllegalStateException("Clause with object "+p0+" is not found".toString());
          }
       }
       return uoa;
    }
    public void s(f9p p0,u1a p1){
       SelectImplementation$a v8 = new SelectImplementation$a(this, p0.c(), p0.b(), p0.d(), null, p1, p0.a());
       SelectImplementation.w(this, v8, false, 1, null);
    }
    public final boolean t(){
       return SelectImplementation.B().get(this) instanceof SelectImplementation$a;
    }
    public final Object u(SelectImplementation$a p0,Object p1,ar4 p2){
       SelectImplementation$processResultAndInvokeBlockRecoveringException$1 oprocessResu;
       SelectImplementation$processResultAndInvokeBlockRecoveringException$1 label1;
       if (p2 instanceof SelectImplementation$processResultAndInvokeBlockRecoveringException$1) {
          oprocessResu = p2;
          SelectImplementation$processResultAndInvokeBlockRecoveringException$1 label = oprocessResu.label;
          int i = Integer.MIN_VALUE;
          if ((label & i)) {
             oprocessResu.label = label - i;
          label_0018 :
             SelectImplementation$processResultAndInvokeBlockRecoveringException$1 result = oprocessResu.result;
             Object obj = dkf.d();
             if ((label1 = oprocessResu.label) != null) {
                if (label1 == 1) {
                   b.b(result);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                oprocessResu.label = 1;
                if ((result = p0.c(p0.d(p1), oprocessResu)) == obj) {
                   return obj;
                }
             }
             return result;
          }
       }
       oprocessResu = new SelectImplementation$processResultAndInvokeBlockRecoveringException$1(this, p2);
       goto label_0018 ;
    }
    public final void v(SelectImplementation$a p0,boolean p1){
       if (SelectImplementation.B().get(this) instanceof SelectImplementation$a) {
          return;
       }
       if (!p1) {
          this.l(p0.a);
       }
       if (p0.e(this)) {
          if (!p1) {
             SelectImplementation tc = this.c;
             ckf.d(tc);
             tc.add(p0);
          }
          p0.g = this.d;
          p0.h = this.e;
          this.d = null;
          this.e = -1;
       }else {
          SelectImplementation.B().set(this, p0);
       }
       return;
    }
    public final void x(Object p0){
       p0 = this.r(p0);
       ckf.d(p0);
       p0.g = null;
       p0.h = -1;
       this.v(p0, true);
    }
    public final TrySelectDetailedResult y(Object p0,Object p1){
       return SelectKt.b(this.z(p0, p1));
    }
    public final int z(Object p0,Object p1){
       SelectImplementation$a uoa;
       while (true) {
          Object obj = SelectImplementation.B().get(this);
          int i = 2;
          if (obj instanceof q23) {
             if ((uoa = this.r(p0)) == null) {
                continue ;
             }else {
                g1a og1a = uoa.a(this, p1);
                if (h30.a(SelectImplementation.B(), this, obj, uoa)) {
                   this.f = p1;
                   if (SelectKt.h(obj, og1a)) {
                      break ;
                   }else {
                      this.f = SelectKt.d();
                      return i;
                   }
                }
             }
          }else {
             int i1 = 1;
             uoa = (ckf.b(obj, SelectKt.f()))? 1: obj instanceof SelectImplementation$a;
             if (uoa) {
                return 3;
             }else if(ckf.b(obj, SelectKt.e())){
                return i;
             }else if(ckf.b(obj, SelectKt.g())){
                if (h30.a(SelectImplementation.B(), this, obj, xz3.e(p0))) {
                   return i1;
                }
             }else if(obj instanceof List){
                if (h30.a(SelectImplementation.B(), this, obj, i04.p0(obj, p0))) {
                   return i1;
                }
             }else {
                throw new IllegalStateException("Unexpected state: "+obj.toString());
             }
          }
       }
       return 0;
    }
}
