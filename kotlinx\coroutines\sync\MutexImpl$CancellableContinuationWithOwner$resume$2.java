package kotlinx.coroutines.sync.MutexImpl$CancellableContinuationWithOwner$resume$2;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.coroutines.sync.MutexImpl;
import kotlinx.coroutines.sync.MutexImpl$CancellableContinuationWithOwner;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;

public final class MutexImpl$CancellableContinuationWithOwner$resume$2 extends Lambda implements g1a	// class@0006c0 from classes11.dex
{
    public final MutexImpl this$0;
    public final MutexImpl$CancellableContinuationWithOwner this$1;

    public void MutexImpl$CancellableContinuationWithOwner$resume$2(MutexImpl p0,MutexImpl$CancellableContinuationWithOwner p1){
       this.this$0 = p0;
       this.this$1 = p1;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(Throwable p0){
       this.this$0.unlock(this.this$1.b);
    }
}
