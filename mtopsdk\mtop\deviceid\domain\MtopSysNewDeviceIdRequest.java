package mtopsdk.mtop.deviceid.domain.MtopSysNewDeviceIdRequest;
import mtopsdk.mtop.domain.IMTOPDataObject;
import tb.t2o;
import java.lang.Object;

public class MtopSysNewDeviceIdRequest implements IMTOPDataObject	// class@0007b1 from classes11.dex
{
    public String API_NAME;
    public boolean NEED_ECODE;
    public boolean NEED_SESSION;
    public String VERSION;
    public String c0;
    public String c1;
    public String c2;
    public String c3;
    public String c4;
    public String c5;
    public String c6;
    public String device_global_id;
    public boolean new_device;

    static {
       t2o.a(0x253000c8);
       t2o.a(0x253000cf);
    }
    public void MtopSysNewDeviceIdRequest(){
       super();
       this.API_NAME = "mtop.sys.newDeviceId";
       this.VERSION = "4.0";
       this.NEED_ECODE = false;
       this.NEED_SESSION = false;
       this.new_device = false;
       this.c6 = null;
       this.c5 = null;
       this.c4 = null;
       this.c3 = null;
       this.c1 = null;
       this.c2 = null;
       this.device_global_id = null;
       this.c0 = null;
    }
}
