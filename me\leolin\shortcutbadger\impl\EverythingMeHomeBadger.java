package me.leolin.shortcutbadger.impl.EverythingMeHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.content.ContentValues;
import java.lang.Integer;
import android.content.ContentResolver;
import android.net.Uri;

public class EverythingMeHomeBadger implements po1	// class@000760 from classes11.dex
{

    public void EverythingMeHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"me.everything.launcher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       ContentValues uContentValu = new ContentValues();
       uContentValu.put("package_name", p1.getPackageName());
       uContentValu.put("activity_name", p1.getClassName());
       uContentValu.put("count", Integer.valueOf(p2));
       p0.getContentResolver().insert(Uri.parse("content://me.everything.badger/apps"), uContentValu);
    }
}
