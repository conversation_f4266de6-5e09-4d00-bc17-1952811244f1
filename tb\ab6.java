package tb.ab6;
import tb.uu;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.dinamicx.DXRuntimeContext;
import tb.uw5;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import com.taobao.android.dinamicx.expression.expr_v2.DXExprFunctionError;
import java.lang.Throwable;

public class ab6 extends uu	// class@001864 from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1c5005d1);
    }
    public void ab6(){
       super();
    }
    public static Object ipc$super(ab6 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/dinamicx_v4/expr/fuction/string/DXReplaceRangeFunction");
    }
    public uw5 execute(DXRuntimeContext p0,uw5 p1,int p2,uw5[] p3,Map p4){
       IpChange $ipChange = ab6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),p3,p4};
          return $ipChange.ipc$dispatch("5d66b176", objArray);
       }else if(p3 != null && (p3.length == 3 && p1 != null)){
          try{
             String str = p1.toString();
             int i = p3[0].s();
             p2 = p3[1].s();
             String str1 = p3[2].w();
             if (p2 >= i) {
                return uw5.W(str.substring(0, i)+str1+str.substring(p2));
             }
             throw new DXExprFunctionError("End index \($endIndex\) is less than start index \($startIndex\).");
          }catch(java.lang.Exception e7){
             throw new DXExprFunctionError(e7);
          }
       }else {
          return uw5.V();
       }
    }
    public String getDxFunctionName(){
       IpChange $ipChange = ab6.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "replaceRange";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("bc5916ec", objArray);
    }
}
