package kotlinx.coroutines.AwaitKt$joinAll$3;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import java.util.Collection;
import kotlinx.coroutines.AwaitKt;

public final class AwaitKt$joinAll$3 extends ContinuationImpl	// class@00048a from classes11.dex
{
    public Object L$0;
    public int label;
    public Object result;

    public void AwaitKt$joinAll$3(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return AwaitKt.a(null, this);
    }
}
