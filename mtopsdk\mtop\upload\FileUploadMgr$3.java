package mtopsdk.mtop.upload.FileUploadMgr$3;
import java.lang.Runnable;
import mtopsdk.mtop.upload.FileUploadMgr;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.concurrent.ConcurrentHashMap;
import mtopsdk.common.util.TBSdkLog;

public class FileUploadMgr$3 implements Runnable	// class@000803 from classes11.dex
{
    public final FileUploadMgr this$0;
    public final UploadFileInfo val$fileInfo;
    public static IpChange $ipChange;

    public void FileUploadMgr$3(FileUploadMgr p0,UploadFileInfo p1){
       this.this$0 = p0;
       this.val$fileInfo = p1;
       super();
    }
    public void run(){
       FileUploadMgr$3 tval$fileInf;
       IpChange $ipChange = FileUploadMgr$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if((tval$fileInf = this.val$fileInfo) != null && tval$fileInf.isValid()){
          FileUploadMgr.access$100(this.this$0).remove(this.val$fileInfo);
          return;
       }else {
          TBSdkLog.e("mtopsdk.FileUploadMgr", "remove upload task failed,fileInfo is invalid");
          return;
       }
    }
}
