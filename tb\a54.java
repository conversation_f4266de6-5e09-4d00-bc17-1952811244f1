package tb.a54;
import tb.su;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.searchbaseframe.datasource.impl.BaseSearchResult;
import com.alibaba.fastjson.JSONObject;
import tb.yko;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.search.sf.datasource.CommonSearchResult;
import tb.t64;
import java.util.List;
import com.alibaba.fastjson.JSONArray;
import tb.c4p;
import com.taobao.android.searchbaseframe.datasource.result.AbsSearchResult;
import tb.kxi;
import com.taobao.android.searchbaseframe.datasource.impl.BaseTypedBean;
import com.taobao.android.searchbaseframe.parse.TypedBean;
import java.util.Set;
import java.util.Iterator;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.vg3;
import com.taobao.android.searchbaseframe.datasource.impl.cell.BaseCellBean;
import com.taobao.android.searchbaseframe.datasource.impl.bean.ResultLayoutInfoBean;
import com.taobao.android.searchbaseframe.datasource.impl.bean.ResultMainInfoBean;
import com.taobao.search.mmd.datasource.bean.SearchBarBean;
import java.util.ArrayList;
import com.taobao.android.searchbaseframe.datasource.impl.bean.TabBean;
import java.util.Map;
import tb.qit;

public class a54 extends su	// class@00175d from classes9.dex
{
    public static IpChange $ipChange;
    public static final String CONVERTER_NAME;

    static {
       t2o.a(0x332004a3);
    }
    public void a54(){
       super();
    }
    public static Object ipc$super(a54 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/search/sf/datasource/converter/CommonApiConverter");
    }
    public void a(BaseSearchResult p0,JSONObject p1,yko p2){
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("337b45c", objArray);
          return;
       }else {
          JSONObject jSONObject = p1.getJSONObject("nxData");
          this.g(p0, jSONObject);
          if (p0 instanceof CommonSearchResult) {
             t64.INSTANCE.b(p0, p1);
          }
          this.e(p0, jSONObject);
          this.i(p0, p1);
          this.d(p0, jSONObject);
          this.f(p0, jSONObject);
          this.h(p0, jSONObject);
          this.c(p0, jSONObject);
          return;
       }
    }
    public final void b(BaseSearchResult p0,List p1,JSONArray p2){
       BaseTypedBean uBaseTypedBe;
       int i = 0;
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("5baeb21e", objArray);
          return;
       }else if(p2 == null){
          c4p.m("CommonApiConverter", "fillLayoutArray:layoutArray is null");
          return;
       }else {
          while (i < p2.size()) {
             if ((uBaseTypedBe = p0.c().n().d(p2.getJSONObject(i), p0, null)) == null) {
                c4p.n("CommonApiConverter", "fillLayoutArray: no parser");
             }else {
                p1.add(uBaseTypedBe.type);
                p0.addMod(uBaseTypedBe.type, uBaseTypedBe);
             }
             i = i + 1;
          }
          return;
       }
    }
    public final void c(BaseSearchResult p0,JSONObject p1){
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("e615a33e", objArray);
          return;
       }else if((p1 = p1.getJSONObject("extMods")) == null){
          return;
       }else {
          Iterator iterator = p1.keySet().iterator();
          while (iterator.hasNext()) {
             String str = iterator.next();
             if (TextUtils.isEmpty(str)) {
                continue ;
             }else {
                String str1 = p1.getString(str);
                if (TextUtils.isEmpty(str1)) {
                   continue ;
                }else {
                   p0.addExtMod(str, str1);
                }
             }
          }
          return;
       }
    }
    public final void d(BaseSearchResult p0,JSONObject p1){
       JSONArray jSONArray;
       JSONObject jSONObject;
       BaseCellBean uBaseCellBea;
       int i = 0;
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("d9a7994c", objArray);
          return;
       }else if((jSONArray = p1.getJSONArray("listItems")) != null){
          int i1 = jSONArray.size();
          while (i < i1) {
             if ((jSONObject = jSONArray.getJSONObject(i)) != null && (uBaseCellBea = p0.c().b().b(jSONObject, p0, null)) != null) {
                p0.addCell(uBaseCellBea);
             }
             i = i + 1;
          }
       }
       return;
    }
    public final void e(BaseSearchResult p0,JSONObject p1){
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7bff490a", objArray);
          return;
       }else {
          ResultLayoutInfoBean resultLayout = new ResultLayoutInfoBean();
          this.b(p0, resultLayout.foldHeaders, p1.getJSONArray("foldHeader"));
          this.b(p0, resultLayout.halfStickyHeaders, p1.getJSONArray("halfStickyHeader"));
          this.b(p0, resultLayout.stickyHeaders, p1.getJSONArray("stickyHeader"));
          this.b(p0, resultLayout.listHeaders, p1.getJSONArray("listHeader"));
          p0.layoutInfo = resultLayout;
          return;
       }
    }
    public final void f(BaseSearchResult p0,JSONObject p1){
       SearchBarBean searchBarBea;
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("31de6a97", objArray);
          return;
       }else if(!p0 instanceof CommonSearchResult){
          return;
       }else if((p1 = p1.getJSONObject("searchBar")) == null){
          searchBarBea = SearchBarBean.createDefault();
       }else {
          SearchBarBean searchBarBea1 = new SearchBarBean();
          searchBarBea1.type = p1.getString("searchType");
          searchBarBea1.text = p1.getString("text");
          searchBarBea = searchBarBea1;
       }
       p0.setSearchBarInfo(searchBarBea);
       return;
    }
    public final void g(BaseSearchResult p0,JSONObject p1){
       ResultMainInfoBean resultMainIn;
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("b0ee304", objArray);
          return;
       }else if((p1 = p1.getJSONObject("pageInfo")) == null){
          resultMainIn = ResultMainInfoBean.createDefault();
       }else {
          ResultMainInfoBean resultMainIn1 = new ResultMainInfoBean();
          ResultMainInfoBean.parseBaseInfo(resultMainIn1, p1);
          resultMainIn = resultMainIn1;
       }
       p0.setMainInfo(resultMainIn);
       return;
    }
    public final void h(BaseSearchResult p0,JSONObject p1){
       ArrayList uArrayList;
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("3dd6d050", objArray);
          return;
       }else if((uArrayList = TabBean.parseBean(p1)) != null && uArrayList.size()){
          uArrayList = TabBean.createDefaultTabs();
       }
       p0.setTabs(uArrayList);
       return;
    }
    public final void i(BaseSearchResult p0,JSONObject p1){
       IpChange $ipChange = a54.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("c2a18ccc", objArray);
          return;
       }else {
          p0.setTemplates(qit.b(p1.getJSONArray("templates"), p0.c()));
          return;
       }
    }
}
