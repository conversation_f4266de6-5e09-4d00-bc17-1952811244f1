package kotlinx.coroutines.channels.BroadcastKt$broadcast$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;
import tb.bj3;

public final class BroadcastKt$broadcast$1 extends Lambda implements g1a	// class@0004bc from classes11.dex
{
    public final ReceiveChannel $this_broadcast;

    public void BroadcastKt$broadcast$1(ReceiveChannel p0){
       this.$this_broadcast = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(Throwable p0){
       bj3.b(this.$this_broadcast, p0);
    }
}
