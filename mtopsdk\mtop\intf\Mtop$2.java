package mtopsdk.mtop.intf.Mtop$2;
import java.lang.Runnable;
import mtopsdk.mtop.intf.Mtop;
import mtopsdk.mtop.domain.EnvModeEnum;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.mtop.global.MtopConfig;
import java.lang.StringBuilder;
import mtopsdk.common.util.TBSdkLog;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.mtop.global.init.IMtopInitTask;
import mtopsdk.mtop.global.init.IExtendMtopInitTask;
import java.lang.Throwable;

public class Mtop$2 implements Runnable	// class@0007cf from classes11.dex
{
    public final Mtop this$0;
    public final EnvModeEnum val$envMode;
    public static IpChange $ipChange;

    public void Mtop$2(Mtop p0,EnvModeEnum p1){
       this.this$0 = p0;
       this.val$envMode = p1;
       super();
    }
    public void run(){
       int i = 0;
       IpChange $ipChange = Mtop$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.this$0.checkMtopSDKInit();
          if (this.this$0.mtopConfig.envMode == this.val$envMode) {
             TBSdkLog.i("mtopsdk.Mtop", Mtop.access$200(this.this$0)+" [switchEnvMode] Current EnvMode matches target EnvMode,envMode="+this.val$envMode);
             return;
          }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)){
             TBSdkLog.i("mtopsdk.Mtop", Mtop.access$200(this.this$0)+" [switchEnvMode]MtopSDK switchEnvMode start");
          }
          Mtop$2 tthis$0 = this.this$0;
          tthis$0.mtopConfig.envMode = this.val$envMode;
          try{
             tthis$0.updateAppKeyIndex();
             if (EnvModeEnum.ONLINE == this.val$envMode) {
                TBSdkLog.setPrintLog(i);
             }
             Mtop$2 tthis$01 = this.this$0;
             tthis$01.initTask.executeCoreTask(tthis$01.mtopConfig);
             tthis$01 = this.this$0;
             Mtop initTask = tthis$01.initTask;
             if (initTask instanceof IExtendMtopInitTask) {
                initTask.executeSignTask(tthis$01.mtopConfig);
             }
             tthis$01 = this.this$0;
             tthis$01.initTask.executeExtraTask(tthis$01.mtopConfig);
          }catch(java.lang.Exception e0){
             e0.printStackTrace();
          }
          if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)) {
             TBSdkLog.i("mtopsdk.Mtop", Mtop.access$200(this.this$0)+" [switchEnvMode]MtopSDK switchEnvMode end. envMode ="+this.val$envMode);
          }
          return;
       }
    }
}
