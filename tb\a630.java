package tb.a630;
import tb.jd20;
import tb.t2o;
import tb.a630$a;
import tb.a07;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.Boolean;
import java.util.Map;

public final class a630 implements jd20	// class@001afd from classes8.dex
{
    public static IpChange $ipChange;
    public static final a630$a Companion;

    static {
       t2o.a(0x3ea00004);
       t2o.a(0x3ea00003);
       t2o.a(0x3e700009);
       a630.Companion = new a630$a(null);
    }
    public static final boolean a(a630 p0){
       IpChange $ipChange = a630.$ipChange;
       if (!$ipChange instanceof IpChange) {
          throw false;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("ac2266c9", objArray).booleanValue();
    }
    public static final String b(a630 p0){
       IpChange $ipChange = a630.$ipChange;
       if (!$ipChange instanceof IpChange) {
          throw null;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("e0d11530", objArray);
    }
    public static final Map c(a630 p0){
       IpChange $ipChange = a630.$ipChange;
       if (!$ipChange instanceof IpChange) {
          throw null;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("be0ec89d", objArray);
    }
}
