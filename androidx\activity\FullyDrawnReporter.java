package androidx.activity.FullyDrawnReporter;
import java.util.concurrent.Executor;
import tb.d1a;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import java.util.ArrayList;
import tb.x0a;
import java.lang.Runnable;
import tb.xhv;
import java.util.Collection;
import java.lang.Iterable;
import java.util.Iterator;
import java.util.List;

public final class FullyDrawnReporter	// class@00044b from classes.dex
{
    private final Executor executor;
    private final Object lock;
    private final List onReportCallbacks;
    private final d1a reportFullyDrawn;
    private boolean reportPosted;
    private final Runnable reportRunnable;
    private boolean reportedFullyDrawn;
    private int reporterCount;

    public void FullyDrawnReporter(Executor p0,d1a p1){
       ckf.g(p0, "executor");
       ckf.g(p1, "reportFullyDrawn");
       super();
       this.executor = p0;
       this.reportFullyDrawn = p1;
       this.lock = new Object();
       this.onReportCallbacks = new ArrayList();
       this.reportRunnable = new x0a(this);
    }
    public static void a(FullyDrawnReporter p0){
       FullyDrawnReporter.reportRunnable$lambda$2(p0);
    }
    private final void postWhenReportersAreDone(){
       if (this.reportPosted == null && this.reporterCount == null) {
          this.reportPosted = true;
          this.executor.execute(this.reportRunnable);
       }
       return;
    }
    private static final void reportRunnable$lambda$2(FullyDrawnReporter p0){
       ckf.g(p0, "this$0");
       FullyDrawnReporter lock = p0.lock;
       _monitor_enter(lock);
       p0.reportPosted = false;
       if (p0.reporterCount == null && p0.reportedFullyDrawn == null) {
          p0.reportFullyDrawn.invoke();
          p0.fullyDrawnReported();
       }
       _monitor_exit(lock);
       return;
    }
    public final void addOnReportDrawnListener(d1a p0){
       int i;
       ckf.g(p0, "callback");
       FullyDrawnReporter tlock = this.lock;
       _monitor_enter(tlock);
       if (this.reportedFullyDrawn != null) {
          i = 1;
       }else {
          this.onReportCallbacks.add(p0);
          i = 0;
       }
       _monitor_exit(tlock);
       if (i) {
          p0.invoke();
       }
       return;
    }
    public final void addReporter(){
       FullyDrawnReporter tlock = this.lock;
       _monitor_enter(tlock);
       if (this.reportedFullyDrawn == null) {
          this.reporterCount = this.reporterCount + 1;
       }
       _monitor_exit(tlock);
       return;
    }
    public final void fullyDrawnReported(){
       FullyDrawnReporter tlock = this.lock;
       _monitor_enter(tlock);
       this.reportedFullyDrawn = true;
       Iterator iterator = this.onReportCallbacks.iterator();
       while (iterator.hasNext()) {
          iterator.next().invoke();
       }
       this.onReportCallbacks.clear();
       _monitor_exit(tlock);
       return;
    }
    public final boolean isFullyDrawnReported(){
       FullyDrawnReporter tlock = this.lock;
       _monitor_enter(tlock);
       _monitor_exit(tlock);
       return this.reportedFullyDrawn;
    }
    public final void removeOnReportDrawnListener(d1a p0){
       ckf.g(p0, "callback");
       FullyDrawnReporter tlock = this.lock;
       _monitor_enter(tlock);
       this.onReportCallbacks.remove(p0);
       _monitor_exit(tlock);
    }
    public final void removeReporter(){
       FullyDrawnReporter treporterCou;
       FullyDrawnReporter tlock = this.lock;
       _monitor_enter(tlock);
       if (this.reportedFullyDrawn == null && (treporterCou = this.reporterCount) > null) {
          this.reporterCount = treporterCou - 1;
          this.postWhenReportersAreDone();
       }
       _monitor_exit(tlock);
       return;
    }
}
