package tb.aa5;
import tb.upc;
import tb.t2o;
import java.lang.Object;
import android.content.Context;
import tb.spc;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.search.musie.video.a;

public class aa5 implements upc	// class@00177a from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x332001f4);
       t2o.a(0x3d500006);
    }
    public void aa5(){
       super();
    }
    public spc a(Context p0){
       IpChange $ipChange = aa5.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new a(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("2be7285", objArray);
    }
}
