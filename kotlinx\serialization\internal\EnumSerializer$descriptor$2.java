package kotlinx.serialization.internal.EnumSerializer$descriptor$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.internal.EnumSerializer;
import java.lang.String;
import java.lang.Object;
import kotlinx.serialization.descriptors.a;

public final class EnumSerializer$descriptor$2 extends Lambda implements d1a	// class@00073e from classes11.dex
{
    public final String $serialName;
    public final EnumSerializer this$0;

    public void EnumSerializer$descriptor$2(EnumSerializer p0,String p1){
       this.this$0 = p0;
       this.$serialName = p1;
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final a invoke(){
       EnumSerializer.d(this.this$0);
       return EnumSerializer.c(this.this$0, this.$serialName);
    }
}
