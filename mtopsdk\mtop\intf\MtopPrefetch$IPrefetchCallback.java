package mtopsdk.mtop.intf.MtopPrefetch$IPrefetchCallback;
import java.lang.String;
import java.util.HashMap;

public interface abstract MtopPrefetch$IPrefetchCallback	// class@0007e1 from classes11.dex
{
    public static final String DATA_API = "data_api";
    public static final String DATA_COST_TIME = "data_cost_time";
    public static final String DATA_KEY = "data_key";
    public static final String DATA_REQ_PARAM = "data_req_param";
    public static final String DATA_SEQ = "data_seq";
    public static final String DATA_VERSION = "data_version";

    void onPrefetch(String p0,HashMap p1);
}
