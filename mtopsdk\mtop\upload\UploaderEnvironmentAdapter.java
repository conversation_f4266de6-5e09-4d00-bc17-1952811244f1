package mtopsdk.mtop.upload.UploaderEnvironmentAdapter;
import tb.smv;
import tb.t2o;
import android.content.Context;
import mtopsdk.mtop.intf.Mtop;
import mtopsdk.mtop.global.MtopConfig;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;
import mtopsdk.mtop.global.SDKConfig;
import mtopsdk.mtop.domain.EnvModeEnum;
import mtopsdk.mtop.upload.UploaderEnvironmentAdapter$1;
import java.lang.Enum;

public class UploaderEnvironmentAdapter extends smv	// class@00080a from classes11.dex
{
    private Mtop mtopInstance;
    public static IpChange $ipChange;

    static {
       t2o.a(0x2590000d);
    }
    public void UploaderEnvironmentAdapter(Context p0){
       super(p0);
       Mtop mtop = Mtop.instance(null);
       this.mtopInstance = mtop;
       this.setAuthCode(mtop.getMtopConfig().authCode);
    }
    public static Object ipc$super(UploaderEnvironmentAdapter p0,String p1,Object[] p2){
       if (p1.hashCode() == 0x487b46d7) {
          return new Integer(super.getEnvironment());
       }
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/upload/UploaderEnvironmentAdapter");
    }
    public String getAppKey(){
       IpChange $ipChange = UploaderEnvironmentAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mtopInstance.getMtopConfig().appKey;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("49079005", objArray);
    }
    public String getAppVersion(){
       IpChange $ipChange = UploaderEnvironmentAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mtopInstance.getMtopConfig().appVersion;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("65f009ac", objArray);
    }
    public int getEnvironment(){
       EnvModeEnum globalEnvMod;
       int i2;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = UploaderEnvironmentAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("487b46d7", objArray).intValue();
       }else if((globalEnvMod = SDKConfig.getInstance().getGlobalEnvMode()) != null){
          if ((i2 = UploaderEnvironmentAdapter$1.$SwitchMap$mtopsdk$mtop$domain$EnvModeEnum[globalEnvMod.ordinal()]) != i1) {
             i = 2;
             if (i2 != i) {
                if (i2 == 3 || i2 == 4) {
                   return i;
                }
             }else {
                return i1;
             }
          }else {
             return i;
          }
       }
       return super.getEnvironment();
    }
    public String getUserId(){
       IpChange $ipChange = UploaderEnvironmentAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mtopInstance.getUserId();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("58ad3b3d", objArray);
    }
}
