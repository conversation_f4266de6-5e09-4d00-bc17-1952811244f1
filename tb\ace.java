package tb.ace;
import tb.ycd;
import tb.add;
import tb.cce;
import java.lang.String;
import tb.ace$a;
import tb.ace$b;
import android.taobao.windvane.extra.uc.pool.PreCreateInfo;
import tb.w9o;

public interface abstract ace	// class@002570 from classes.dex
{

    void addPrerenderAttachEventListener(ycd p0);
    void addPrerenderSuccessEventListener(add p0);
    void addWebViewPageModel(cce p0);
    String getCustomMegaBizId();
    String getCustomMegaNamespace();
    boolean getEnableAsyncJSAPIChannel();
    boolean getEnableNetworkTracing();
    String getMegaBizId();
    ace$a getMegaHandler();
    ace$b getMegaUserDataMapAdapter();
    PreCreateInfo getPreCreateInfo();
    String getRealUrl();
    cce getWebViewPageModel();
    boolean isClientPrerender();
    boolean isHitSnapshot();
    boolean isThemis();
    void notifyPrerenderSuccess();
    void removePrerenderAttachEventListener();
    void setCustomMegaBizId(String p0);
    void setCustomMegaNamespace(String p0);
    void setEnableAsyncJSAPIChannel(boolean p0);
    void setEnableNetworkTracing(boolean p0);
    void setHitSnapshot(boolean p0);
    void setIsClientPrerender(boolean p0);
    void setMainFrameResponseInfo(w9o p0);
    void setMegaHandler(ace$a p0);
    void setMegaUserDataMapAdapter(ace$b p0);
    void setPreCreateInfo(PreCreateInfo p0);
    void setRealUrl(String p0);
    void setThemis(boolean p0);
}
