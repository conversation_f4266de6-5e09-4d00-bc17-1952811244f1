package kotlinx.coroutines.sync.MutexImpl$onLock$2;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.sync.MutexImpl;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;

public final class MutexImpl$onLock$2 extends FunctionReferenceImpl implements w1a	// class@0006c5 from classes11.dex
{
    public static final MutexImpl$onLock$2 INSTANCE;

    static {
       MutexImpl$onLock$2.INSTANCE = new MutexImpl$onLock$2();
    }
    public void MutexImpl$onLock$2(){
       super(3, MutexImpl.class, "onLockProcessResult", "onLockProcessResult\(Ljava/lang/Object;Ljava/lang/Object;\)Ljava/lang/Object;", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final Object invoke(MutexImpl p0,Object p1,Object p2){
       p0.u(p1, p2);
       return p0;
    }
}
