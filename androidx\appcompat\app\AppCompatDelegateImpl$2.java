package androidx.appcompat.app.AppCompatDelegateImpl$2;
import java.lang.Runnable;
import androidx.appcompat.app.AppCompatDelegateImpl;
import java.lang.Object;

public class AppCompatDelegateImpl$2 implements Runnable	// class@00055e from classes.dex
{
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$2(AppCompatDelegateImpl p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       AppCompatDelegateImpl$2 tthis$0 = this.this$0;
       if ((tthis$0.mInvalidatePanelMenuFeatures & 0x01)) {
          tthis$0.doInvalidatePanelMenu(0);
       }
       tthis$0 = this.this$0;
       if ((tthis$0.mInvalidatePanelMenuFeatures & 0x1000)) {
          tthis$0.doInvalidatePanelMenu(108);
       }
       tthis$0 = this.this$0;
       tthis$0.mInvalidatePanelMenuPosted = false;
       tthis$0.mInvalidatePanelMenuFeatures = 0;
       return;
    }
}
