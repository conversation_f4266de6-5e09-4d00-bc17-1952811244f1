package androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState$SavedState;
import android.os.Parcelable;
import androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState$SavedState$1;
import java.lang.Object;
import android.os.Parcel;
import java.lang.ClassLoader;
import android.os.Bundle;

public class AppCompatDelegateImpl$PanelFeatureState$SavedState implements Parcelable	// class@000575 from classes.dex
{
    public int featureId;
    public boolean isOpen;
    public Bundle menuState;
    public static final Parcelable$Creator CREATOR;

    static {
       AppCompatDelegateImpl$PanelFeatureState$SavedState.CREATOR = new AppCompatDelegateImpl$PanelFeatureState$SavedState$1();
    }
    public void AppCompatDelegateImpl$PanelFeatureState$SavedState(){
       super();
    }
    public static AppCompatDelegateImpl$PanelFeatureState$SavedState readFromParcel(Parcel p0,ClassLoader p1){
       AppCompatDelegateImpl$PanelFeatureState$SavedState panelFeature = new AppCompatDelegateImpl$PanelFeatureState$SavedState();
       panelFeature.featureId = p0.readInt();
       boolean b = true;
       if (p0.readInt() != b) {
          b = false;
       }
       panelFeature.isOpen = b;
       if (b) {
          panelFeature.menuState = p0.readBundle(p1);
       }
       return panelFeature;
    }
    public int describeContents(){
       return 0;
    }
    public void writeToParcel(Parcel p0,int p1){
       p0.writeInt(this.featureId);
       p0.writeInt(this.isOpen);
       if (this.isOpen != null) {
          p0.writeBundle(this.menuState);
       }
       return;
    }
}
