package kotlinx.coroutines.sync.SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1;
import tb.u1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.sync.SemaphoreKt;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Number;
import tb.z9p;

public final class SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1 extends FunctionReferenceImpl implements u1a	// class@0006cd from classes11.dex
{
    public static final SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1 INSTANCE;

    static {
       SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1.INSTANCE = new SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1();
    }
    public void SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1(){
       super(2, SemaphoreKt.class, "createSegment", "createSegment\(JLkotlinx/coroutines/sync/SemaphoreSegment;\)Lkotlinx/coroutines/sync/SemaphoreSegment;", 1);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0.longValue(), p1);
    }
    public final z9p invoke(long p0,z9p p1){
       return SemaphoreKt.c(p0, p1);
    }
}
