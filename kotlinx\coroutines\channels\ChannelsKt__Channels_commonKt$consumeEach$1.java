package kotlinx.coroutines.channels.ChannelsKt__Channels_commonKt$consumeEach$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.g1a;
import kotlinx.coroutines.channels.ChannelsKt__Channels_commonKt;

public final class ChannelsKt__Channels_commonKt$consumeEach$1 extends ContinuationImpl	// class@0004d7 from classes11.dex
{
    public Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;
    public Object result;

    public void ChannelsKt__Channels_commonKt$consumeEach$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return ChannelsKt__Channels_commonKt.b(null, null, this);
    }
}
