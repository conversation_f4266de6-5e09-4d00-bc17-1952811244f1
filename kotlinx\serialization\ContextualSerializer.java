package kotlinx.serialization.ContextualSerializer;
import tb.x530;
import tb.wyf;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.yy30;
import java.util.List;
import tb.hc1;
import tb.ob40$a;
import kotlinx.serialization.descriptors.a;
import kotlinx.serialization.ContextualSerializer$descriptor$1;
import tb.ob40;
import tb.g1a;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;
import tb.sb20;
import tb.tb40;
import java.lang.Void;
import tb.wy30;
import tb.hg20;
import tb.qh20;
import tb.gn20;
import tb.qb40;

public final class ContextualSerializer implements x530	// class@000703 from classes11.dex
{
    private final a descriptor;
    private final x530 fallbackSerializer;
    private final wyf serializableClass;
    private final List typeArgumentsSerializers;

    public void ContextualSerializer(wyf p0){
       ckf.g(p0, "serializableClass");
       super(p0, null, yy30.EMPTY_SERIALIZER_ARRAY);
    }
    public void ContextualSerializer(wyf p0,x530 p1,x530[] p2){
       ckf.g(p0, "serializableClass");
       ckf.g(p2, "typeArgumentsSerializers");
       super();
       this.serializableClass = p0;
       this.fallbackSerializer = p1;
       this.typeArgumentsSerializers = hc1.c(p2);
       a[] uoaArray = new a[0];
       this.descriptor = sb20.b(SerialDescriptorsKt.c("kotlinx.serialization.ContextualSerializer", ob40$a.INSTANCE, uoaArray, new ContextualSerializer$descriptor$1(this)), p0);
    }
    public static final x530 access$getFallbackSerializer$p(ContextualSerializer p0){
       return p0.fallbackSerializer;
    }
    private final x530 serializer(tb40 p0){
       x530 ox530;
       if ((ox530 = p0.a(this.serializableClass, this.typeArgumentsSerializers)) != null || (ox530 = this.fallbackSerializer) != null) {
          return ox530;
       }
       wy30.f(this.serializableClass);
       throw null;
    }
    public Object deserialize(hg20 p0){
       ckf.g(p0, "decoder");
       return p0.e(this.serializer(p0.a()));
    }
    public a getDescriptor(){
       return this.descriptor;
    }
    public void serialize(gn20 p0,Object p1){
       ckf.g(p0, "encoder");
       ckf.g(p1, "value");
       p0.e(this.serializer(p0.a()), p1);
    }
}
