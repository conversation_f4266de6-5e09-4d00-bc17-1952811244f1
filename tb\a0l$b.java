package tb.a0l$b;
import tb.t2o;
import com.taobao.kmp.nexus.arch.openArch.service.message.logger.OpenArchMessageCheckResult;
import java.lang.Enum;

public final class a0l$b	// class@001acc from classes8.dex
{
    public static final int[] $EnumSwitchMapping$0;

    static {
       t2o.a(0x3f1002f9);
       int len = OpenArchMessageCheckResult.values().length;
       try{
          int[] ointArray = new int[len];
          ointArray[OpenArchMessageCheckResult.MessageCheckResultTopicNotMatch.ordinal()] = 1;
          try{
             e0[OpenArchMessageCheckResult.MessageCheckResultMsgIDDuplicated.ordinal()] = 2;
             try{
                e0[OpenArchMessageCheckResult.MessageCheckResultPassed.ordinal()] = 3;
                try{
                   e0[OpenArchMessageCheckResult.MessageCheckResultMsgDataEmpty.ordinal()] = 4;
                   a0l$b.$EnumSwitchMapping$0 = e0;
                }catch(java.lang.NoSuchFieldError e0){
                }
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
