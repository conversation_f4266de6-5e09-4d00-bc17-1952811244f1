package kotlinx.datetime.format.DayOfWeekNames$toString$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.String;
import java.lang.Class;
import java.lang.Object;
import tb.ckf;

public final class DayOfWeekNames$toString$1 extends FunctionReferenceImpl implements g1a	// class@0006e3 from classes11.dex
{
    public static final DayOfWeekNames$toString$1 INSTANCE;

    static {
       DayOfWeekNames$toString$1.INSTANCE = new DayOfWeekNames$toString$1();
    }
    public void DayOfWeekNames$toString$1(){
       super(1, String.class, "toString", "toString\(\)Ljava/lang/String;", 0);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final String invoke(String p0){
       ckf.g(p0, "p0");
       return p0;
    }
}
