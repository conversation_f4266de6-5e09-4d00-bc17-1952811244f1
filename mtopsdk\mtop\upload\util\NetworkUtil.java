package mtopsdk.mtop.upload.util.NetworkUtil;
import tb.t2o;
import java.lang.Object;
import java.util.Map;
import java.util.List;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.ArrayList;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import mtopsdk.common.util.StringUtils;
import anetwork.channel.entity.BasicHeader;
import anetwork.channel.entity.StringParam;

public class NetworkUtil	// class@00081c from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x25900020);
    }
    public void NetworkUtil(){
       super();
    }
    public static List createHttpHeaders(Map p0){
       Map$Entry uEntry;
       int i = 1;
       IpChange $ipChange = NetworkUtil.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("4c6f512b", objArray);
       }else if(p0 != null && p0.size() >= i){
          ArrayList uArrayList = new ArrayList();
          Iterator iterator = p0.entrySet().iterator();
          while (iterator.hasNext()) {
             if ((uEntry = iterator.next()) != null && StringUtils.isNotBlank(uEntry.getKey())) {
                uArrayList.add(new BasicHeader(uEntry.getKey(), uEntry.getValue()));
             }
          }
          return uArrayList;
       }else {
          return null;
       }
    }
    public static List createHttpParams(Map p0){
       int i = 1;
       IpChange $ipChange = NetworkUtil.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("bf2dc233", objArray);
       }else if(p0 != null && p0.size() >= i){
          ArrayList uArrayList = new ArrayList();
          Iterator iterator = p0.entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             String key = uEntry.getKey();
             uArrayList.add(new StringParam(key, uEntry.getValue()));
          }
          return uArrayList;
       }else {
          return null;
       }
    }
}
