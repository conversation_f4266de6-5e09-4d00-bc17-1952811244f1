package tb.a3h$e0;
import java.lang.Runnable;
import tb.a3h;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.a3h$s0;

public class a3h$e0 implements Runnable	// class@001e6c from classes10.dex
{
    public final String a;
    public final a3h b;
    public static IpChange $ipChange;

    public void a3h$e0(a3h p0,String p1){
       this.b = p0;
       this.a = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$e0.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.C(this.b) != null){
          a3h.C(this.b).k(this.a);
       }
       return;
    }
}
