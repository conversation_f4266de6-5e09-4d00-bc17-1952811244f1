package kotlinx.coroutines.l;
import tb.sk8;
import java.lang.Thread;
import kotlinx.coroutines.k$c;
import kotlinx.coroutines.g;
import kotlinx.coroutines.k;
import java.util.concurrent.locks.LockSupport;

public abstract class l extends sk8	// class@00069e from classes11.dex
{

    public void l(){
       super();
    }
    public abstract Thread a1();
    public void b1(long p0,k$c p1){
       g.INSTANCE.n1(p0, p1);
    }
    public final void c1(){
       Thread thread = this.a1();
       if (Thread.currentThread() != thread) {
          LockSupport.unpark(thread);
       }
       return;
    }
}
