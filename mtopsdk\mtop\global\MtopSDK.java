package mtopsdk.mtop.global.MtopSDK;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.content.Context;
import mtopsdk.mtop.intf.Mtop;
import java.lang.Boolean;

public class MtopSDK	// class@0007c8 from classes11.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000e0);
    }
    public void MtopSDK(){
       super();
    }
    public static void checkMtopSDKInit(){
       IpChange $ipChange = MtopSDK.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("fd87b2e1", objArray);
          return;
       }else {
          Mtop.instance(null).checkMtopSDKInit();
          return;
       }
    }
    public static void setLogSwitch(boolean p0){
       IpChange $ipChange = MtopSDK.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Boolean(p0)};
          $ipChange.ipc$dispatch("62e8af97", objArray);
          return;
       }else {
          Mtop.instance(null).logSwitch(p0);
          return;
       }
    }
}
