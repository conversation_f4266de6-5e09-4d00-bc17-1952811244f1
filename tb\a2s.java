package tb.a2s;
import tb.gi6;
import tb.t2o;
import java.lang.String;
import tb.k06;
import com.taobao.taobao.R$id;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import java.lang.Boolean;
import java.lang.Number;
import android.content.Context;
import android.view.View;
import android.widget.TextView;
import android.text.Editable;
import java.lang.Long;
import tb.a2s$a;
import android.widget.EditText;
import android.graphics.drawable.Drawable;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Integer;
import com.taobao.android.dinamicx.expression.event.DXTextInputEvent;
import com.taobao.android.dinamicx.expression.event.DXEvent;
import tb.xn7;

public class a2s extends gi6	// class@00182a from classes5.dex
{
    public String C0;
    public String a;
    public String b;
    public String c;
    public String d;
    public int e;
    public int f;
    public int g;
    public static IpChange $ipChange;
    public static final long D0;
    public static final long DX_WIDGET_ID;
    public static final int ID_KEY_BOARD;
    public static final int ID_MAX_LENGTH;
    public static final int ID_PLACE_HOLDER;
    public static final int ID_PLACE_HOLDER_COLOR;
    public static final int ID_TV_TEXT;
    public static final String INPUT_TYPE_DIALOG;
    public static final String INPUT_TYPE_INPUT;
    public static final String WIDGET_NAME;
    public static final int h;
    public static final long i;
    public static final long j;
    public static final long k;
    public static final long l;
    public static final long m;
    public static final long n;
    public static final long o;

    static {
       t2o.a(0x13c00029);
       a2s.DX_WIDGET_ID = k06.a("tdTextInput");
       a2s.ID_TV_TEXT = R$id.trade_id_text;
       a2s.ID_KEY_BOARD = R$id.trade_id_key_board;
       a2s.ID_MAX_LENGTH = R$id.trade_id_max_length;
       a2s.ID_PLACE_HOLDER = R$id.trade_id_place_holder;
       a2s.ID_PLACE_HOLDER_COLOR = R$id.trade_id_place_holder_color;
       a2s.h = R$id.trade_id_text_watcher;
       a2s.i = k06.a("placeholder");
       a2s.j = k06.a("inputType");
       a2s.k = k06.a("textUnit");
       a2s.D0 = k06.a("inputTitle");
       a2s.l = k06.a("placeholderColor");
       a2s.m = k06.a("keyboard");
       a2s.n = k06.a("maxLength");
       a2s.o = k06.a("onFinish");
    }
    public void a2s(){
       super();
    }
    public static String a(a2s p0){
       IpChange $ipChange = a2s.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.C0;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("a00db64b", objArray);
    }
    public static Object ipc$super(a2s p0,String p1,Object[] p2){
       int i = 1;
       int i1 = 0;
       switch (p1.hashCode()){
           case 0x9625fd55:
             super.onBindEvent(p2[i1], p2[i], p2[2].longValue());
             return null;
           case 0xd3e0d496:
             super.onSetIntAttribute(p2[i1].longValue(), p2[i].intValue());
             return null;
           case 0xd451fa5d:
             super.onBeforeMeasure(p2[i1]);
             return null;
           case 0xede516ab:
             super.onRenderView(p2[i1], p2[i]);
             return null;
           case 0x42764d9f:
             super.onSetStringAttribute(p2[i1].longValue(), p2[i]);
             return null;
           case 0x7e58628a:
             super.onClone(p2[i1], p2[i].booleanValue());
             return null;
           default:
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/buy/dinamicX/widget/TDTextInputWidgetNode");
       }
    }
    public static int m0(){
       IpChange $ipChange = a2s.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a2s.h;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("5b88b92d", objArray).intValue();
    }
    public static void n0(a2s p0,Editable p1){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("14aebb43", objArray);
          return;
       }else {
          p0.w(p1);
          return;
       }
    }
    public DXWidgetNode build(Object p0){
       IpChange $ipChange = a2s.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new a2s();
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("966917b0", objArray);
    }
    public void onBeforeMeasure(TextView p0){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d451fa5d", objArray);
          return;
       }else {
          super.onBeforeMeasure(p0);
          this.x(p0);
          return;
       }
    }
    public void onBindEvent(Context p0,View p1,long p2){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Long(p2)};
          $ipChange.ipc$dispatch("9625fd55", objArray);
          return;
       }else {
          super.onBindEvent(p0, p1, p2);
          if (!(a2s.o - p2)) {
             new a2s$a(this, p0, this.c).d(p1);
          }
          return;
       }
    }
    public void onClone(DXWidgetNode p0,boolean p1){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("7e58628a", objArray);
          return;
       }else {
          super.onClone(p0, p1);
          if (!p0 instanceof a2s) {
             return;
          }
          this.a = p0.a;
          this.d = p0.d;
          this.b = p0.b;
          this.C0 = p0.C0;
          this.e = p0.e;
          this.f = p0.f;
          this.g = p0.g;
          this.c = p0.c;
          return;
       }
    }
    public View onCreateView(Context p0){
       int i = 0;
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("93d55e23", objArray);
       }else {
          EditText uEditText = new EditText(p0);
          uEditText.setPadding(i, i, i, i);
          uEditText.setBackgroundDrawable(null);
          return uEditText;
       }
    }
    public void onRenderView(Context p0,View p1){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("ede516ab", objArray);
          return;
       }else if(!TextUtils.isEmpty(this.d)){
          this.setText(this.a+this.d);
       }
       super.onRenderView(p0, p1);
       this.x(p1);
       return;
    }
    public void onSetIntAttribute(long p0,int p1){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0),new Integer(p1)};
          $ipChange.ipc$dispatch("d3e0d496", objArray);
          return;
       }else {
          super.onSetIntAttribute(p0, p1);
          if (!(a2s.l - p0)) {
             this.e = p1;
          }else if(!(a2s.m - p0)){
             this.f = p1;
          }else if(!(a2s.n - p0)){
             this.g = p1;
          }
          return;
       }
    }
    public void onSetStringAttribute(long p0,String p1){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0),p1};
          $ipChange.ipc$dispatch("42764d9f", objArray);
          return;
       }else {
          super.onSetStringAttribute(p0, p1);
          if (!(0x8e396ac59 - p0)) {
             this.a = p1;
          }else if(!(a2s.i - p0)){
             this.b = p1;
          }else if(!(a2s.j - p0)){
             this.c = p1;
          }else if(!(a2s.k - p0)){
             this.d = p1;
          }else if(!(a2s.D0 - p0)){
             this.C0 = p1;
          }
          return;
       }
    }
    public final void v(EditText p0,boolean p1){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("d1e8ba", objArray);
          return;
       }else if(p0 != null){
          p0.setFocusable(p1);
          p0.setFocusableInTouchMode(p1);
       }
       return;
    }
    public final void w(Editable p0){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3f13dcf9", objArray);
          return;
       }else {
          DXTextInputEvent uDXTextInput = new DXTextInputEvent(a2s.o);
          uDXTextInput.setText(p0);
          this.postEvent(uDXTextInput);
          return;
       }
    }
    public final void x(View p0){
       IpChange $ipChange = a2s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("567e6447", objArray);
          return;
       }else if(p0 instanceof EditText){
          p0.setTag(a2s.ID_TV_TEXT, this.a);
          EditText uEditText = p0;
          xn7.b(uEditText, this.e);
          p0.setTag(a2s.ID_PLACE_HOLDER_COLOR, Integer.valueOf(this.e));
          xn7.c(uEditText, this.f);
          p0.setTag(a2s.ID_KEY_BOARD, Integer.valueOf(this.f));
          xn7.d(uEditText, this.g);
          p0.setTag(a2s.ID_MAX_LENGTH, Integer.valueOf(this.g));
          if (!TextUtils.isEmpty(this.b)) {
             xn7.a(uEditText, this.b);
             p0.setTag(a2s.ID_PLACE_HOLDER, this.b);
          }
          if (!TextUtils.isEmpty(this.c)) {
             this.v(uEditText, "input".equalsIgnoreCase(this.c));
          }
       }
       return;
    }
}
