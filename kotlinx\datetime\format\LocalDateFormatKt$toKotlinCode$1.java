package kotlinx.datetime.format.LocalDateFormatKt$toKotlinCode$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import tb.pw40;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import tb.ckf;

public final class LocalDateFormatKt$toKotlinCode$1 extends FunctionReferenceImpl implements g1a	// class@0006e5 from classes11.dex
{
    public static final LocalDateFormatKt$toKotlinCode$1 INSTANCE;

    static {
       LocalDateFormatKt$toKotlinCode$1.INSTANCE = new LocalDateFormatKt$toKotlinCode$1();
    }
    public void LocalDateFormatKt$toKotlinCode$1(){
       super(1, pw40.class, "toKotlinCode", "toKotlinCode\(Ljava/lang/String;\)Ljava/lang/String;", 1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final String invoke(String p0){
       ckf.g(p0, "p0");
       return pw40.a(p0);
    }
}
