package kotlinx.serialization.internal.EnumDescriptor;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor;
import java.lang.String;
import java.lang.Object;
import tb.ckf;
import tb.eu20;
import tb.a07;
import tb.ob40$b;
import kotlinx.serialization.internal.EnumDescriptor$elementDescriptors$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import tb.ob40;
import kotlinx.serialization.descriptors.a;
import java.util.Set;
import tb.wy30;
import java.lang.Iterable;
import tb.nb40;
import tb.nb40$d;
import java.util.Iterator;
import tb.nb40$b;
import java.lang.StringBuilder;
import java.lang.CharSequence;
import tb.g1a;
import tb.i04;

public final class EnumDescriptor extends PluginGeneratedSerialDescriptor	// class@00073d from classes11.dex
{
    public final ob40 l;
    public final njg m;

    public void EnumDescriptor(String p0,int p1){
       ckf.g(p0, "name");
       super(p0, null, p1, 2, null);
       this.l = ob40$b.INSTANCE;
       this.m = a.b(new EnumDescriptor$elementDescriptors$2(p1, p0, this));
    }
    public ob40 c(){
       return this.l;
    }
    public boolean equals(Object p0){
       if (this == p0) {
          return true;
       }
       if (p0 == null) {
          return false;
       }
       if (!p0 instanceof a) {
          return false;
       }
       if (p0.c() != ob40$b.INSTANCE) {
          return false;
       }
       if (!ckf.b(this.f(), p0.f())) {
          return false;
       }
       if (!ckf.b(wy30.a(this), wy30.a(p0))) {
          return false;
       }
       return true;
    }
    public a g(int p0){
       return this.p()[p0];
    }
    public int hashCode(){
       int i = this.f().hashCode();
       Iterator iterator = nb40.b(this).iterator();
       int i1 = 1;
       while (true) {
          Iterator iterator1 = iterator;
          if (iterator1.hasNext()) {
             Object obj = iterator1.next();
             i1 = i1 * 31;
             int i2 = (obj != null)? obj.hashCode(): 0;
             i1 = i1 + i2;
          }else {
             break ;
          }
       }
       return ((i * 31) + i1);
    }
    public final a[] p(){
       return this.m.getValue();
    }
    public String toString(){
       return i04.j0(nb40.b(this), ", ", this.f()+'(', "\)", 0, null, null, 56, null);
    }
}
