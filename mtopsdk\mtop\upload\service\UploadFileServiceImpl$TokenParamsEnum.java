package mtopsdk.mtop.upload.service.UploadFileServiceImpl$TokenParamsEnum;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class UploadFileServiceImpl$TokenParamsEnum extends Enum	// class@000815 from classes11.dex
{
    private String key;
    private static final UploadFileServiceImpl$TokenParamsEnum[] $VALUES;
    public static IpChange $ipChange;
    public static final UploadFileServiceImpl$TokenParamsEnum APPKEY;
    public static final UploadFileServiceImpl$TokenParamsEnum BIZ_CODE;
    public static final UploadFileServiceImpl$TokenParamsEnum FILE_ID;
    public static final UploadFileServiceImpl$TokenParamsEnum FILE_NAME;
    public static final UploadFileServiceImpl$TokenParamsEnum FILE_SIZE;
    public static final UploadFileServiceImpl$TokenParamsEnum SEGMENT_SIZE;
    public static final UploadFileServiceImpl$TokenParamsEnum TIMESTAMP;
    public static final UploadFileServiceImpl$TokenParamsEnum USERID;
    public static final UploadFileServiceImpl$TokenParamsEnum UTDID;
    public static final UploadFileServiceImpl$TokenParamsEnum VERSION;

    static {
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE = new UploadFileServiceImpl$TokenParamsEnum("VERSION", 0, "version");
       UploadFileServiceImpl$TokenParamsEnum.VERSION = tokenParamsE;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE1 = new UploadFileServiceImpl$TokenParamsEnum("BIZ_CODE", 1, "bizcode");
       UploadFileServiceImpl$TokenParamsEnum.BIZ_CODE = tokenParamsE1;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE2 = new UploadFileServiceImpl$TokenParamsEnum("APPKEY", 2, "appkey");
       UploadFileServiceImpl$TokenParamsEnum.APPKEY = tokenParamsE2;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE3 = new UploadFileServiceImpl$TokenParamsEnum("TIMESTAMP", 3, "t");
       UploadFileServiceImpl$TokenParamsEnum.TIMESTAMP = tokenParamsE3;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE4 = new UploadFileServiceImpl$TokenParamsEnum("UTDID", 4, "utdid");
       UploadFileServiceImpl$TokenParamsEnum.UTDID = tokenParamsE4;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE5 = new UploadFileServiceImpl$TokenParamsEnum("USERID", 5, "userid");
       UploadFileServiceImpl$TokenParamsEnum.USERID = tokenParamsE5;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE6 = new UploadFileServiceImpl$TokenParamsEnum("FILE_ID", 6, "fileid");
       UploadFileServiceImpl$TokenParamsEnum.FILE_ID = tokenParamsE6;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE7 = new UploadFileServiceImpl$TokenParamsEnum("FILE_NAME", 7, "filename");
       UploadFileServiceImpl$TokenParamsEnum.FILE_NAME = tokenParamsE7;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE8 = new UploadFileServiceImpl$TokenParamsEnum("FILE_SIZE", 8, "filesize");
       UploadFileServiceImpl$TokenParamsEnum.FILE_SIZE = tokenParamsE8;
       UploadFileServiceImpl$TokenParamsEnum tokenParamsE9 = new UploadFileServiceImpl$TokenParamsEnum("SEGMENT_SIZE", 9, "segmentsize");
       UploadFileServiceImpl$TokenParamsEnum.SEGMENT_SIZE = tokenParamsE9;
       UploadFileServiceImpl$TokenParamsEnum[] tokenParamsE10 = new UploadFileServiceImpl$TokenParamsEnum[10];
       tokenParamsE10[0] = tokenParamsE;
       tokenParamsE10[1] = tokenParamsE1;
       tokenParamsE10[2] = tokenParamsE2;
       tokenParamsE10[3] = tokenParamsE3;
       tokenParamsE10[4] = tokenParamsE4;
       tokenParamsE10[5] = tokenParamsE5;
       tokenParamsE10[6] = tokenParamsE6;
       tokenParamsE10[7] = tokenParamsE7;
       tokenParamsE10[8] = tokenParamsE8;
       tokenParamsE10[9] = tokenParamsE9;
       UploadFileServiceImpl$TokenParamsEnum.$VALUES = tokenParamsE10;
    }
    private void UploadFileServiceImpl$TokenParamsEnum(String p0,int p1,String p2){
       super(p0, p1);
       this.key = p2;
    }
    public static Object ipc$super(UploadFileServiceImpl$TokenParamsEnum p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/upload/service/UploadFileServiceImpl$TokenParamsEnum");
    }
    public static UploadFileServiceImpl$TokenParamsEnum valueOf(String p0){
       IpChange $ipChange = UploadFileServiceImpl$TokenParamsEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(UploadFileServiceImpl$TokenParamsEnum.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("74351e14", objArray);
    }
    public static UploadFileServiceImpl$TokenParamsEnum[] values(){
       IpChange $ipChange = UploadFileServiceImpl$TokenParamsEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return UploadFileServiceImpl$TokenParamsEnum.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("28adc03", objArray);
    }
    public String getKey(){
       IpChange $ipChange = UploadFileServiceImpl$TokenParamsEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.key;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("16c52370", objArray);
    }
}
