package tb.a4j$b;
import tb.z7v$c;
import tb.a4j;
import android.app.Activity;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.content.Context;
import com.taobao.android.dinamicx.s;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import com.taobao.android.dinamicx.DinamicXEngine;
import tb.ih4;
import tb.k3j;
import tb.dvb;

public final class a4j$b extends z7v$c	// class@001ae8 from classes8.dex
{
    public final a4j a;
    public final Activity b;
    public static IpChange $ipChange;

    public void a4j$b(a4j p0,Activity p1){
       this.a = p0;
       this.b = p1;
       super();
    }
    public static Object ipc$super(a4j$b p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/mytaobao/ultron/MtbUltronHelper$createUltronInstance$1");
    }
    public void b(Context p0,s p1){
       IpChange $ipChange = a4j$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("c0a5ce96", objArray);
          return;
       }else {
          ckf.h(p1, "dinamicXEngineRouter");
          a4j.b(this.a, p1.k());
          DinamicXEngine uDinamicXEng = a4j.a(this.a);
          void ovoid = null;
          if (uDinamicXEng != null) {
             uDinamicXEng.l1(this.b);
             if (ih4.a("compatDarkMode", 1)) {
                if ((uDinamicXEng = a4j.a(this.a)) != null) {
                   uDinamicXEng.O0(new k3j());
                }else {
                   ckf.s();
                   throw ovoid;
                }
             }
             return;
          }else {
             ckf.s();
             throw ovoid;
          }
       }
    }
}
