package androidx.activity.OnBackPressedDispatcher$addCancellableCallback$1;
import tb.d1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.Object;
import androidx.activity.OnBackPressedDispatcher;
import java.lang.Class;
import java.lang.String;
import tb.xhv;
import kotlin.jvm.internal.CallableReference;

public final class OnBackPressedDispatcher$addCancellableCallback$1 extends FunctionReferenceImpl implements d1a	// class@000461 from classes.dex
{

    public void OnBackPressedDispatcher$addCancellableCallback$1(Object p0){
       super(0, p0, OnBackPressedDispatcher.class, "updateEnabledCallbacks", "updateEnabledCallbacks\(\)V", 0);
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       OnBackPressedDispatcher.access$updateEnabledCallbacks(this.receiver);
    }
}
