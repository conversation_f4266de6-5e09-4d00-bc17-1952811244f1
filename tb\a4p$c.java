package tb.a4p$c;
import tb.unr;
import tb.a4p$d;
import java.lang.Object;
import com.taobao.location.common.TBLocationDTO;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a4p;

public class a4p$c implements unr	// class@001aee from classes8.dex
{
    public final a4p$d a;
    public static IpChange $ipChange;

    public void a4p$c(a4p$d p0){
       super();
       this.a = p0;
    }
    public void onLocationChanged(TBLocationDTO p0){
       a4p$c ta;
       IpChange $ipChange = a4p$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("240b6877", objArray);
          return;
       }else if(p0 != null && p0.isNavSuccess()){
          a4p.a(p0);
       }
       if ((ta = this.a) != null) {
          ta.a(p0);
       }
       return;
    }
}
