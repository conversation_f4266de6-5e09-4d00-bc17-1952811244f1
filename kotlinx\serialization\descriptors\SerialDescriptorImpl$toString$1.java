package kotlinx.serialization.descriptors.SerialDescriptorImpl$toString$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.descriptors.SerialDescriptorImpl;
import java.lang.CharSequence;
import java.lang.StringBuilder;
import java.lang.String;
import kotlinx.serialization.descriptors.a;
import java.lang.Object;
import java.lang.Number;

public final class SerialDescriptorImpl$toString$1 extends Lambda implements g1a	// class@00072a from classes11.dex
{
    public final SerialDescriptorImpl this$0;

    public void SerialDescriptorImpl$toString$1(SerialDescriptorImpl p0){
       this.this$0 = p0;
       super(1);
    }
    public final CharSequence invoke(int p0){
       return this.this$0.e(p0)+": "+this.this$0.g(p0).f();
    }
    public Object invoke(Object p0){
       return this.invoke(p0.intValue());
    }
}
