package kotlinx.coroutines.channels.BufferedChannel$onReceive$2;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.channels.BufferedChannel;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;

public final class BufferedChannel$onReceive$2 extends FunctionReferenceImpl implements w1a	// class@0004c2 from classes11.dex
{
    public static final BufferedChannel$onReceive$2 INSTANCE;

    static {
       BufferedChannel$onReceive$2.INSTANCE = new BufferedChannel$onReceive$2();
    }
    public void BufferedChannel$onReceive$2(){
       super(3, BufferedChannel.class, "processResultSelectReceive", "processResultSelectReceive\(Ljava/lang/Object;Ljava/lang/Object;\)Ljava/lang/Object;", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final Object invoke(BufferedChannel p0,Object p1,Object p2){
       BufferedChannel.B(p0, p1, p2);
       return p2;
    }
}
