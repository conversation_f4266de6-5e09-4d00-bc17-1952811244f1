package tb.a2p$b;
import tb.wnd;
import java.lang.Object;
import android.app.Activity;
import tb.ude;
import com.taobao.search.searchdoor.sf.widgets.SearchDoorContext;
import android.view.ViewGroup;
import tb.vfw;
import com.taobao.search.searchdoor.sf.widgets.activate.ActivateWidget;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ji3;
import com.taobao.search.searchdoor.sf.widgets.searchbar.SearchBarWidget;
import com.alibaba.ability.localization.Localization;
import com.taobao.search.searchdoor.sf.widgets.searchbar.I18nSearchBarWidget;
import com.taobao.search.searchdoor.sf.widgets.searchbar.PopupSearchBarWidget;

public class a2p$b implements wnd	// class@001748 from classes9.dex
{
    public static IpChange $ipChange;

    public void a2p$b(){
       super();
    }
    public ActivateWidget a(Activity p0,ude p1,SearchDoorContext p2,ViewGroup p3,vfw p4){
       IpChange $ipChange = a2p$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4};
          return $ipChange.ipc$dispatch("f2364ba2", objArray);
       }else {
          ji3 v6 = new ji3(p0, p1, p2, p3, p4);
          return v6;
       }
    }
    public SearchBarWidget b(Activity p0,ude p1,SearchDoorContext p2,ViewGroup p3,vfw p4){
       IpChange $ipChange = a2p$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4};
          return $ipChange.ipc$dispatch("6b094976", objArray);
       }else if(Localization.o()){
          I18nSearchBarWidget $ipChange1 = new I18nSearchBarWidget(p0, p1, p2, p3, p4);
          return $ipChange;
       }else if(p2.F()){
          PopupSearchBarWidget $ipChange2 = new PopupSearchBarWidget(p0, p1, p2, p3, p4);
          return $ipChange;
       }else {
          SearchBarWidget $ipChange3 = new SearchBarWidget(p0, p1, p2, p3, p4);
          return $ipChange;
       }
    }
}
