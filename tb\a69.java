package tb.a69;
import tb.t2o;
import android.graphics.Rect;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class a69	// class@001afe from classes8.dex
{
    public final Rect a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1f600267);
    }
    public void a69(Rect p0){
       super();
       this.a = p0;
    }
    public Rect a(){
       IpChange $ipChange = a69.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8dbc0fc2", objArray);
    }
}
