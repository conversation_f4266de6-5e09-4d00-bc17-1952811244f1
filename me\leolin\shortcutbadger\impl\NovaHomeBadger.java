package me.leolin.shortcutbadger.impl.NovaHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.content.ContentValues;
import java.lang.StringBuilder;
import java.lang.Integer;
import android.content.ContentResolver;
import android.net.Uri;

public class NovaHomeBadger implements po1	// class@000764 from classes11.dex
{

    public void NovaHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"com.teslacoilsw.launcher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       ContentValues uContentValu = new ContentValues();
       uContentValu.put("tag", p1.getPackageName()+"/"+p1.getClassName());
       uContentValu.put("count", Integer.valueOf(p2));
       p0.getContentResolver().insert(Uri.parse("content://com.teslacoilsw.notifier/unread_count"), uContentValu);
    }
}
