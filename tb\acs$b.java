package tb.acs$b;
import tb.mwd$a;
import tb.acs;
import java.lang.Object;
import tb.eas;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import tb.ckf;

public final class acs$b implements mwd$a	// class@001ec0 from classes10.dex
{
    public final acs a;
    public static IpChange $ipChange;

    public void acs$b(acs p0){
       super();
       this.a = p0;
    }
    public void a(int p0,eas p1){
       IpChange $ipChange = acs$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("b9f6bd85", objArray);
          return;
       }else {
          ckf.g(p1, "error");
          acs.k(this.a, p1);
          return;
       }
    }
    public void onSuccess(){
       IpChange $ipChange = acs$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d0e393ab", objArray);
          return;
       }else {
          this.a.i();
          return;
       }
    }
}
