package androidx.appcompat.app.AppCompatDialogFragment;
import androidx.fragment.app.DialogFragment;
import android.os.Bundle;
import android.app.Dialog;
import androidx.appcompat.app.AppCompatDialog;
import android.content.Context;
import androidx.fragment.app.Fragment;
import android.view.Window;

public class AppCompatDialogFragment extends DialogFragment	// class@00057a from classes.dex
{

    public void AppCompatDialogFragment(){
       super();
    }
    public void AppCompatDialogFragment(int p0){
       super(p0);
    }
    public Dialog onCreateDialog(Bundle p0){
       return new AppCompatDialog(this.getContext(), this.getTheme());
    }
    public void setupDialog(Dialog p0,int p1){
       if (p0 instanceof AppCompatDialog) {
          AppCompatDialog uAppCompatDi = p0;
          if (p1 != 1 && p1 != 2) {
             if (p1 == 3) {
                p0.getWindow().addFlags(24);
             }
          }
          uAppCompatDi.supportRequestWindowFeature(1);
       }else {
          super.setupDialog(p0, p1);
       }
       return;
    }
}
