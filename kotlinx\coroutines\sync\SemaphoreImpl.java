package kotlinx.coroutines.sync.SemaphoreImpl;
import tb.y9p;
import java.lang.Object;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import java.util.concurrent.atomic.AtomicLongFieldUpdater;
import java.util.concurrent.atomic.AtomicIntegerFieldUpdater;
import tb.z9p;
import kotlinx.coroutines.sync.SemaphoreImpl$onCancellationRelease$1;
import java.lang.StringBuilder;
import java.lang.IllegalArgumentException;
import tb.qww;
import tb.ar4;
import tb.xhv;
import tb.dkf;
import tb.q23;
import tb.g1a;
import tb.ckf;
import kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt;
import kotlinx.coroutines.c;
import tb.s23;
import tb.jv6;
import kotlinx.coroutines.sync.SemaphoreImpl$addAcquireToQueue$createNewSegment$1;
import kotlinx.coroutines.sync.SemaphoreKt;
import tb.v8p;
import tb.u1a;
import tb.se4;
import tb.x8p;
import tb.h30;
import tb.te4;
import java.util.concurrent.atomic.AtomicReferenceArray;
import tb.yi3;
import tb.u1r;
import tb.k9p;
import java.lang.IllegalStateException;
import tb.dv6;
import java.lang.Math;
import kotlinx.coroutines.sync.SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1;

public class SemaphoreImpl implements y9p	// class@0006ce from classes11.dex
{
    public final int a;
    public Object b;
    public long c;
    public Object d;
    public long e;
    public int f;
    public final g1a g;
    public static final AtomicReferenceFieldUpdater h;
    public static final AtomicLongFieldUpdater i;
    public static final AtomicReferenceFieldUpdater j;
    public static final AtomicLongFieldUpdater k;
    public static final AtomicIntegerFieldUpdater l;

    static {
       SemaphoreImpl.h = AtomicReferenceFieldUpdater.newUpdater(SemaphoreImpl.class, Object.class, "b");
       SemaphoreImpl.i = AtomicLongFieldUpdater.newUpdater(SemaphoreImpl.class, "c");
       SemaphoreImpl.j = AtomicReferenceFieldUpdater.newUpdater(SemaphoreImpl.class, Object.class, "d");
       SemaphoreImpl.k = AtomicLongFieldUpdater.newUpdater(SemaphoreImpl.class, "e");
       SemaphoreImpl.l = AtomicIntegerFieldUpdater.newUpdater(SemaphoreImpl.class, "f");
    }
    public void SemaphoreImpl(int p0,int p1){
       super();
       this.a = p0;
       if (p0 <= 0) {
          throw new IllegalArgumentException("Semaphore should have at least 1 permit, but had "+p0.toString());
       }
       if (p1 < 0 || p1 > p0) {
          throw new IllegalArgumentException("The number of acquired permits should be in 0.."+p0.toString());
       }
       z9p oz9p = new z9p(0, null, 2);
       this.b = oz9p;
       this.d = oz9p;
       this.f = p0 - p1;
       this.g = new SemaphoreImpl$onCancellationRelease$1(this);
       return;
    }
    public static final AtomicLongFieldUpdater b(){
       return SemaphoreImpl.i;
    }
    public static final boolean c(SemaphoreImpl p0,qww p1){
       return p0.g(p1);
    }
    public static Object e(SemaphoreImpl p0,ar4 p1){
       if (p0.i() > 0) {
          return xhv.INSTANCE;
       }
       if ((p0 = p0.f(p1)) == dkf.d()) {
          return p0;
       }
       return xhv.INSTANCE;
    }
    public static final AtomicLongFieldUpdater o(){
       return SemaphoreImpl.k;
    }
    public static final AtomicReferenceFieldUpdater p(){
       return SemaphoreImpl.h;
    }
    public static final AtomicReferenceFieldUpdater q(){
       return SemaphoreImpl.j;
    }
    public static final AtomicIntegerFieldUpdater s(){
       return SemaphoreImpl.l;
    }
    public Object a(ar4 p0){
       return SemaphoreImpl.e(this, p0);
    }
    public final void d(q23 p0){
       do {
          if (this.i() > 0) {
             p0.l(xhv.INSTANCE, this.g);
             break ;
          }
          ckf.e(p0, "null cannot be cast to non-null type kotlinx.coroutines.Waiter");
       } while (this.g(p0));
       return;
    }
    public final Object f(ar4 p0){
       Object obj;
       c uoc = s23.b(IntrinsicsKt__IntrinsicsJvmKt.c(p0));
       if (!SemaphoreImpl.c(this, uoc)) {
          this.d(uoc);
       }
       if ((obj = uoc.A()) == dkf.d()) {
          jv6.c(p0);
       }
       if (obj == dkf.d()) {
          return obj;
       }else {
          return xhv.INSTANCE;
       }
    }
    public final boolean g(qww p0){
       Object obj = this;
       Object obj1 = p0;
       z9p oz9p = SemaphoreImpl.q().get(obj);
       long andIncrement = SemaphoreImpl.o().getAndIncrement(obj);
       SemaphoreImpl$addAcquireToQueue$createNewSegment$1 iNSTANCE = SemaphoreImpl$addAcquireToQueue$createNewSegment$1.INSTANCE;
       AtomicReferenceFieldUpdater uAtomicRefer = SemaphoreImpl.q();
       long l = andIncrement / (long)SemaphoreKt.h();
       while (true) {
          Object obj2 = se4.b(oz9p, l, iNSTANCE);
          if (!x8p.b(obj2)) {
             v8p ov8p = x8p.a(obj2);
             while (true) {
                v8p ov8p1 = uAtomicRefer.get(obj);
                if ((ov8p1.e - ov8p.e) >= 0) {
                   break ;
                }else if(!ov8p.q()){
                   continue ;
                }else if(h30.a(uAtomicRefer, obj, ov8p1, ov8p)){
                   if (ov8p1.m()) {
                      ov8p1.k();
                      break ;
                   }else {
                      break ;
                   }
                }else if(ov8p.m()){
                   ov8p.k();
                }
             }
             oz9p = x8p.a(obj2);
             int i = (int)(andIncrement % (long)SemaphoreKt.h());
             if (yi3.a(oz9p.r(), i, null, obj1)) {
                obj1.c(oz9p, i);
                return true;
             }else if(yi3.a(oz9p.r(), i, SemaphoreKt.g(), SemaphoreKt.i())){
                if (obj1 instanceof q23) {
                   ckf.e(obj1, "null cannot be cast to non-null type kotlinx.coroutines.CancellableContinuation<kotlin.Unit>");
                   obj1.l(xhv.INSTANCE, obj.g);
                }else if(obj1 instanceof k9p){
                   obj1.b(xhv.INSTANCE);
                }else {
                   throw new IllegalStateException("unexpected: "+obj1.toString());
                }
                return true;
             }else {
                return false;
             }
          }else {
          }
       }
    }
    public final void h(){
       int i;
       SemaphoreImpl ta;
       do {
          i = SemaphoreImpl.s().get(this);
          ta = this.a;
       } while (i > ta && !SemaphoreImpl.s().compareAndSet(this, i, ta));
       return;
    }
    public final int i(){
       int andDecrement;
       do {
       } while ((andDecrement = SemaphoreImpl.s().getAndDecrement(this)) <= this.a);
       return andDecrement;
    }
    public int j(){
       return Math.max(SemaphoreImpl.s().get(this), 0);
    }
    public final void k(k9p p0,Object p1){
       do {
          if (this.i() > 0) {
             p0.b(xhv.INSTANCE);
             break ;
          }
          ckf.e(p0, "null cannot be cast to non-null type kotlinx.coroutines.Waiter");
       } while (this.g(p0));
       return;
    }
    public boolean l(){
       int i;
       while ((i = SemaphoreImpl.s().get(this)) > this.a) {
          this.h();
       }
       if (i <= 0) {
          return false;
       }
       int i1 = i - 1;
       if (SemaphoreImpl.s().compareAndSet(this, i, i1)) {
          return true;
       }
    }
    public final boolean m(Object p0){
       Object obj;
       boolean b;
       if (p0 instanceof q23) {
          ckf.e(p0, "null cannot be cast to non-null type kotlinx.coroutines.CancellableContinuation<kotlin.Unit>");
          if ((obj = p0.q(xhv.INSTANCE, null, this.g)) != null) {
             p0.p(obj);
             b = true;
          }else {
             b = false;
          }
       }else if(p0 instanceof k9p){
          b = p0.e(this, xhv.INSTANCE);
       }else {
          throw new IllegalStateException("unexpected: "+p0.toString());
       }
       return b;
    }
    public final boolean n(){
       Object andSet;
       z9p oz9p = SemaphoreImpl.p().get(this);
       long andIncrement = SemaphoreImpl.b().getAndIncrement(this);
       long l = andIncrement / (long)SemaphoreKt.h();
       SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1 iNSTANCE = SemaphoreImpl$tryResumeNextFromQueue$createNewSegment$1.INSTANCE;
       AtomicReferenceFieldUpdater uAtomicRefer = SemaphoreImpl.p();
       while (true) {
          Object obj = se4.b(oz9p, l, iNSTANCE);
          if (!x8p.b(obj)) {
             v8p ov8p = x8p.a(obj);
             while (true) {
                v8p ov8p1 = uAtomicRefer.get(this);
                if ((ov8p1.e - ov8p.e) >= 0) {
                   break ;
                }else if(!ov8p.q()){
                   continue ;
                }else if(h30.a(uAtomicRefer, this, ov8p1, ov8p)){
                   if (ov8p1.m()) {
                      ov8p1.k();
                      break ;
                   }else {
                      break ;
                   }
                }else if(ov8p.m()){
                   ov8p.k();
                }
             }
             oz9p = x8p.a(obj);
             oz9p.b();
             boolean b = false;
             if ((oz9p.e - l) > 0) {
                return b;
             }
             int i = (int)(andIncrement % (long)SemaphoreKt.h());
             if ((andSet = oz9p.r().getAndSet(i, SemaphoreKt.g())) == null) {
                int i1 = SemaphoreKt.f();
                while (true) {
                   if (b >= i1) {
                      return (yi3.a(oz9p.r(), i, SemaphoreKt.g(), SemaphoreKt.d()) ^ true);
                   }
                   if (oz9p.r().get(i) == SemaphoreKt.i()) {
                      break ;
                   }else {
                      b = b + 1;
                   }
                }
                return true;
             }else if(andSet == SemaphoreKt.e()){
                return b;
             }else {
                return this.m(andSet);
             }
          }else {
          }
       }
    }
    public void release(){
       while (true) {
          int andIncrement = SemaphoreImpl.s().getAndIncrement(this);
          SemaphoreImpl ta = this.a;
          if (andIncrement < ta) {
             if (andIncrement >= 0) {
                break ;
             }else if(this.n()){
                return;
             }
          }else {
             this.h();
             throw new IllegalStateException("The number of released permits cannot be greater than "+ta.toString());
          }
       }
       return;
    }
}
