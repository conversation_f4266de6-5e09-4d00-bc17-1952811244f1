package kotlinx.serialization.internal.PairSerializer;
import tb.e730;
import tb.x530;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.a07;
import kotlinx.serialization.descriptors.a;
import kotlinx.serialization.internal.PairSerializer$descriptor$1;
import tb.g1a;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;
import kotlin.Pair;

public final class PairSerializer extends e730	// class@000747 from classes11.dex
{
    public final a c;

    public void PairSerializer(x530 p0,x530 p1){
       ckf.g(p0, "keySerializer");
       ckf.g(p1, "valueSerializer");
       super(p0, p1, null);
       a[] uoaArray = new a[0];
       this.c = SerialDescriptorsKt.b("kotlin.Pair", uoaArray, new PairSerializer$descriptor$1(p0, p1));
    }
    public Object c(Object p0){
       return this.e(p0);
    }
    public Object d(Object p0){
       return this.f(p0);
    }
    public Object e(Pair p0){
       ckf.g(p0, "<this>");
       return p0.getFirst();
    }
    public Object f(Pair p0){
       ckf.g(p0, "<this>");
       return p0.getSecond();
    }
    public a getDescriptor(){
       return this.c;
    }
}
