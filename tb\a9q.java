package tb.a9q;
import tb.t2o;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import android.util.Pair;
import java.lang.ref.WeakReference;
import java.lang.ref.Reference;
import android.app.Activity;
import java.lang.Integer;
import android.view.Window;
import java.lang.Throwable;
import android.content.Context;
import tb.dwv;
import android.view.WindowManager$LayoutParams;
import tb.a9q$a;

public class a9q	// class@001eae from classes10.dex
{
    public static IpChange $ipChange;
    public static a9q$a a;
    public static WeakReference b;

    static {
       t2o.a(0x3340004e);
    }
    public static synchronized void a(){
       a9q$a a;
       Pair first;
       _monitor_enter(a9q.class);
       IpChange $ipChange = a9q.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("45e2fd9b", objArray);
          _monitor_exit(a9q.class);
          return;
       }else if((a = a9q.a) != null && ((first = a.first) != null && first.get() != null)){
          a = null;
          try{
             a9q.a.first.get().getWindow().setSoftInputMode(a9q.a.second.intValue());
             a9q.a = a;
          }catch(java.lang.Exception e2){
             e2.printStackTrace();
             a9q.a = a;
          }
          _monitor_exit(a9q.class);
          return;
       }else {
          _monitor_exit(a9q.class);
          return;
       }
    }
    public static synchronized void b(Context p0){
       Activity uActivity;
       _monitor_enter(a9q.class);
       IpChange $ipChange = a9q.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("942977de", objArray);
          _monitor_exit(a9q.class);
          return;
       }else if((uActivity = dwv.a(p0)) == null){
          _monitor_exit(a9q.class);
          return;
       }else {
          a9q.b = new WeakReference(uActivity);
          _monitor_exit(a9q.class);
          return;
       }
    }
    public static synchronized void c(){
       WeakReference b;
       WindowManager$LayoutParams softInputMod;
       _monitor_enter(a9q.class);
       IpChange $ipChange = a9q.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("b9611383", objArray);
          _monitor_exit(a9q.class);
          return;
       }else if((b = a9q.b) != null && b.get() != null){
          try{
             Activity uActivity = a9q.b.get();
             if ((softInputMod = uActivity.getWindow().getAttributes().softInputMode) == 48) {
                _monitor_exit(a9q.class);
                return;
             }else {
                a9q.a = new a9q$a(a9q.b, Integer.valueOf(softInputMod));
                uActivity.getWindow().setSoftInputMode(48);
             }
          }catch(java.lang.Exception e1){
             e1.printStackTrace();
          }
          _monitor_exit(a9q.class);
          return;
       }else {
          _monitor_exit(a9q.class);
          return;
       }
    }
}
