package kotlinx.serialization.internal.EnumSerializer;
import tb.x530;
import java.lang.String;
import java.lang.Enum;
import java.lang.Object;
import tb.ckf;
import kotlinx.serialization.internal.EnumSerializer$descriptor$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import kotlinx.serialization.descriptors.a;
import java.lang.Class;
import kotlinx.serialization.internal.EnumDescriptor;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor;
import tb.gn20;
import tb.ic1;
import kotlinx.serialization.SerializationException;
import java.lang.StringBuilder;
import java.util.Arrays;

public final class EnumSerializer implements x530	// class@00073f from classes11.dex
{
    public final Enum[] a;
    public final njg b;

    public void EnumSerializer(String p0,Enum[] p1){
       ckf.g(p0, "serialName");
       ckf.g(p1, "values");
       super();
       this.a = p1;
       this.b = a.b(new EnumSerializer$descriptor$2(this, p0));
    }
    public static final a c(EnumSerializer p0,String p1){
       return p0.e(p1);
    }
    public static final a d(EnumSerializer p0){
       p0.getClass();
       return null;
    }
    public final a e(String p0){
       EnumSerializer ta = this.a;
       EnumDescriptor uEnumDescrip = new EnumDescriptor(p0, ta.length);
       int len = ta.length;
       for (int i = 0; i < len; i = i + 1) {
          PluginGeneratedSerialDescriptor.k(uEnumDescrip, ta[i].name(), false, 2, null);
       }
       return uEnumDescrip;
    }
    public void f(gn20 p0,Enum p1){
       int i;
       ckf.g(p0, "encoder");
       ckf.g(p1, "value");
       EnumSerializer ta = this.a;
       if ((i = ic1.N(ta, p1)) != -1) {
          p0.c(this.getDescriptor(), i);
          return;
       }else {
          String str = Arrays.toString(ta);
          ckf.f(str, "toString\(...\)");
          throw new SerializationException(p1+" is not a valid enum "+this.getDescriptor().f()+", must be one of "+str);
       }
    }
    public a getDescriptor(){
       return this.b.getValue();
    }
    public void serialize(gn20 p0,Object p1){
       this.f(p0, p1);
    }
    public String toString(){
       return "kotlinx.serialization.internal.EnumSerializer<"+this.getDescriptor().f()+'>';
    }
}
