package tb.a0l;
import tb.t2o;
import tb.a0l$a;
import tb.a07;
import java.lang.String;
import java.lang.Object;
import java.util.LinkedHashMap;
import java.util.Map$Entry;
import java.lang.CharSequence;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import java.lang.StringBuilder;
import java.lang.Number;
import com.taobao.kmp.nexus.arch.openArch.service.message.logger.OpenArchMessageCheckResult;
import tb.iky;
import java.lang.Integer;
import java.util.Map;
import tb.a0l$b;
import java.lang.Enum;
import kotlin.NoWhenBranchMatchedException;
import tb.a2l;
import tb.pus;
import tb.v2l;
import tb.v2l$a;
import kotlin.Pair;
import tb.jpu;
import java.util.Set;
import java.lang.Iterable;
import tb.zzk;
import tb.g1a;
import tb.i04;
import kotlin.collections.a;

public final class a0l	// class@001acd from classes8.dex
{
    public boolean a;
    public int b;
    public int c;
    public int d;
    public int e;
    public final Map f;
    public final String g;
    public static IpChange $ipChange;
    public static final a0l$a Companion;
    public static final String LOGCATEMSGCHECK;
    public static final String LOGTAG;
    public static final String UTARG1MSGCHECKSTAT;

    static {
       t2o.a(0x3f1002f7);
       a0l.Companion = new a0l$a(null);
    }
    public void a0l(String p0){
       super();
       this.g = p0;
       this.a = true;
       this.f = new LinkedHashMap();
    }
    public static CharSequence a(Map$Entry p0){
       return a0l.d(p0);
    }
    public static final CharSequence d(Map$Entry p0){
       IpChange $ipChange = a0l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("9f06667b", objArray);
       }else {
          ckf.g(p0, "it");
          return p0.getKey().intValue()+'_'+p0.getValue().intValue();
       }
    }
    public final void b(OpenArchMessageCheckResult p0,iky p1){
       Integer integer;
       int i = 2;
       int i1 = 0;
       int i2 = 3;
       IpChange $ipChange = a0l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i2];
          objArray[i1] = this;
          objArray[1] = p0;
          objArray[i] = p1;
          $ipChange.ipc$dispatch("43cb5846", objArray);
          return;
       }else {
          ckf.g(p0, "checkResult");
          ckf.g(p1, "message");
          if (this.a == null) {
             return;
          }
          if (ckf.b(p1.fetchMsgId(), "-1")) {
             return;
          }
          this.d = this.d + 1;
          if ((integer = p1.fetchType()) != null) {
             int i3 = integer.intValue();
             a0l tf = this.f;
             Integer integer1 = Integer.valueOf(i3);
             if ((integer = this.f.get(Integer.valueOf(i3))) != null) {
                i1 = integer.intValue();
             }
             tf.put(integer1, Integer.valueOf((i1 + 1)));
          }
          if ((i1 = a0l$b.$EnumSwitchMapping$0[p0.ordinal()]) != 1) {
             if (i1 != i) {
                if (i1 != i2) {
                   if (i1 == 4) {
                      this.e = this.e + 1;
                   }else {
                      throw new NoWhenBranchMatchedException();
                   }
                }
             }else {
                this.b = this.b + 1;
             }
          }else {
             this.c = this.c + 1;
          }
          if (p0 != OpenArchMessageCheckResult.MessageCheckResultPassed) {
             this.e(p1);
          }
          return;
       }
    }
    public final void c(){
       object oobject = this;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = a0l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[i1] = oobject;
          $ipChange.ipc$dispatch("20ac5eae", objArray);
          return;
       }else {
          oobject.a = i1;
          if (!a2l.INSTANCE.h()) {
             return;
          }
          pus.INSTANCE.m("MessageCheck", oobject.g, "commitLogger");
          Pair[] pairArray = new Pair[]{jpu.a("duplicateMsgIdCounts", String.valueOf(oobject.b)),jpu.a("notMatchTopicsCounts", String.valueOf(oobject.c)),jpu.a("msgCount", String.valueOf(oobject.d)),jpu.a("emptyDataCounts", String.valueOf(oobject.e)),jpu.a("from", "KMP"),jpu.a("typeList", i04.j0(oobject.f.entrySet(), "#", null, null, 0, null, new zzk(), 30, null))};
          v2l.Companion.a().e("OpenArchMessage_MsgCheck", a.k(pairArray));
          return;
       }
    }
    public final void e(iky p0){
       IpChange $ipChange = a0l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("155e9526", objArray);
          return;
       }else {
          pus.INSTANCE.m("MessageCheck", this.g, "MsgCheck "+this.f(p0));
          return;
       }
    }
    public final String f(iky p0){
       IpChange $ipChange = a0l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "topic : "+p0.getTopic()+" | msgId : "+p0.fetchMsgId()+" | msgType : "+p0.fetchType();
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("c1a3eaea", objArray);
    }
    public final void g(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a0l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("9cff6e5a", objArray);
          return;
       }else {
          this.a = i1;
          this.b = i;
          this.c = i;
          this.d = i;
          this.e = i;
          this.f.clear();
          return;
       }
    }
}
