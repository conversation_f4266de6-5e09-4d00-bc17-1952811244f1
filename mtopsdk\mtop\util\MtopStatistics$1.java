package mtopsdk.mtop.util.MtopStatistics$1;
import java.lang.Runnable;
import mtopsdk.mtop.util.MtopStatistics;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class MtopStatistics$1 implements Runnable	// class@000825 from classes11.dex
{
    public final MtopStatistics this$0;
    public static IpChange $ipChange;

    public void MtopStatistics$1(MtopStatistics p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = MtopStatistics$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          MtopStatistics.access$000(this.this$0);
          return;
       }
    }
}
