package zoloz.ap.com.toolkit.R$dimen;
import com.taobao.taobao.R$dimen;
import java.lang.Object;

public final class R$dimen	// class@0010fe from classes11.dex
{
    public static int dialog_btn_divide;
    public static int dialog_btn_height;
    public static int dialog_btn_margin_left;
    public static int dialog_btn_margin_top;
    public static int dialog_btn_text_size;
    public static int dialog_close_btn;
    public static int dialog_close_btn_margin_top;
    public static int dialog_count_margin_top;
    public static int dialog_count_size;
    public static int dialog_protocal_size;
    public static int dialog_subtitle_margin_top;
    public static int dialog_subtitle_size;
    public static int dialog_title_margin_top;
    public static int dialog_title_size;
    public static int font_x_large;
    public static int z_dimen_10;
    public static int z_dimen_15;
    public static int z_dimen_20;
    public static int z_dimen_30;
    public static int z_dimen_300;
    public static int z_dimen_35;
    public static int z_dimen_5;
    public static int z_dimen_60;
    public static int z_dimen_80;
    public static int z_text_size_12;
    public static int z_text_size_15;
    public static int z_text_size_16;
    public static int z_text_size_18;
    public static int z_text_size_23;
    public static int z_text_size_24;

    static {
       R$dimen.dialog_btn_divide = R$dimen.dialog_btn_divide;
       R$dimen.dialog_btn_height = R$dimen.dialog_btn_height;
       R$dimen.dialog_btn_margin_left = R$dimen.dialog_btn_margin_left;
       R$dimen.dialog_btn_margin_top = R$dimen.dialog_btn_margin_top;
       R$dimen.dialog_btn_text_size = R$dimen.dialog_btn_text_size;
       R$dimen.dialog_close_btn = R$dimen.dialog_close_btn;
       R$dimen.dialog_close_btn_margin_top = R$dimen.dialog_close_btn_margin_top;
       R$dimen.dialog_count_margin_top = R$dimen.dialog_count_margin_top;
       R$dimen.dialog_count_size = R$dimen.dialog_count_size;
       R$dimen.dialog_protocal_size = R$dimen.dialog_protocal_size;
       R$dimen.dialog_subtitle_margin_top = R$dimen.dialog_subtitle_margin_top;
       R$dimen.dialog_subtitle_size = R$dimen.dialog_subtitle_size;
       R$dimen.dialog_title_margin_top = R$dimen.dialog_title_margin_top;
       R$dimen.dialog_title_size = R$dimen.dialog_title_size;
       R$dimen.font_x_large = R$dimen.font_x_large;
       R$dimen.z_dimen_10 = R$dimen.z_dimen_10;
       R$dimen.z_dimen_15 = R$dimen.z_dimen_15;
       R$dimen.z_dimen_20 = R$dimen.z_dimen_20;
       R$dimen.z_dimen_30 = R$dimen.z_dimen_30;
       R$dimen.z_dimen_300 = R$dimen.z_dimen_300;
       R$dimen.z_dimen_35 = R$dimen.z_dimen_35;
       R$dimen.z_dimen_5 = R$dimen.z_dimen_5;
       R$dimen.z_dimen_60 = R$dimen.z_dimen_60;
       R$dimen.z_dimen_80 = R$dimen.z_dimen_80;
       R$dimen.z_text_size_12 = R$dimen.z_text_size_12;
       R$dimen.z_text_size_15 = R$dimen.z_text_size_15;
       R$dimen.z_text_size_16 = R$dimen.z_text_size_16;
       R$dimen.z_text_size_18 = R$dimen.z_text_size_18;
       R$dimen.z_text_size_23 = R$dimen.z_text_size_23;
       R$dimen.z_text_size_24 = R$dimen.z_text_size_24;
    }
    public void R$dimen(){
       super();
    }
}
