package tb.a250;
import tb.g1a;
import tb.ozm;
import java.lang.Object;
import tb.mx30;
import tb.xhv;
import com.taobao.compose.tucaoba.utils.UtilsKt$takeFromCamera$2;

public final class a250 implements g1a	// class@001843 from classes7.dex
{
    public final ozm a;

    public void a250(ozm p0){
       super();
       this.a = p0;
    }
    public final Object invoke(Object p0){
       return UtilsKt$takeFromCamera$2.a(this.a, p0);
    }
}
