package androidx.activity.result.ActivityResultCallerKt;
import tb.g1a;
import java.lang.Object;
import androidx.activity.result.ActivityResultCaller;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.activity.result.ActivityResultRegistry;
import androidx.activity.result.ActivityResultLauncher;
import java.lang.String;
import tb.ckf;
import tb.bc0;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultCallerLauncher;
import tb.cc0;

public final class ActivityResultCallerKt	// class@0004ad from classes.dex
{

    public static void a(g1a p0,Object p1){
       ActivityResultCallerKt.registerForActivityResult$lambda$0(p0, p1);
    }
    public static void b(g1a p0,Object p1){
       ActivityResultCallerKt.registerForActivityResult$lambda$1(p0, p1);
    }
    public static final ActivityResultLauncher registerForActivityResult(ActivityResultCaller p0,ActivityResultContract p1,Object p2,ActivityResultRegistry p3,g1a p4){
       ckf.g(p0, "<this>");
       ckf.g(p1, "contract");
       ckf.g(p3, "registry");
       ckf.g(p4, "callback");
       return new ActivityResultCallerLauncher(p0.registerForActivityResult(p1, p3, new bc0(p4)), p1, p2);
    }
    public static final ActivityResultLauncher registerForActivityResult(ActivityResultCaller p0,ActivityResultContract p1,Object p2,g1a p3){
       ckf.g(p0, "<this>");
       ckf.g(p1, "contract");
       ckf.g(p3, "callback");
       return new ActivityResultCallerLauncher(p0.registerForActivityResult(p1, new cc0(p3)), p1, p2);
    }
    private static final void registerForActivityResult$lambda$0(g1a p0,Object p1){
       ckf.g(p0, "$callback");
       p0.invoke(p1);
    }
    private static final void registerForActivityResult$lambda$1(g1a p0,Object p1){
       ckf.g(p0, "$callback");
       p0.invoke(p1);
    }
}
