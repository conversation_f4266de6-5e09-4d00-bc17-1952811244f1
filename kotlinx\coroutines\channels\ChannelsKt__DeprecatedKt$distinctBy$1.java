package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$distinctBy$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import java.util.HashSet;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;
import java.util.Collection;

public final class ChannelsKt__DeprecatedKt$distinctBy$1 extends SuspendLambda implements u1a	// class@0004e0 from classes11.dex
{
    public final u1a $selector;
    public final ReceiveChannel $this_distinctBy;
    private Object L$0;
    public Object L$1;
    public Object L$2;
    public Object L$3;
    public int label;

    public void ChannelsKt__DeprecatedKt$distinctBy$1(ReceiveChannel p0,u1a p1,ar4 p2){
       this.$this_distinctBy = p0;
       this.$selector = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$distinctBy$1 uodistinctBy = new ChannelsKt__DeprecatedKt$distinctBy$1(this.$this_distinctBy, this.$selector, p1);
       uodistinctBy.L$0 = p0;
       return uodistinctBy;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$distinctBy$1 tlabel;
       ChannelsKt__DeprecatedKt$distinctBy$1 tL$2;
       ChannelsKt__DeprecatedKt$distinctBy$1 tL$1;
       ChannelsKt__DeprecatedKt$distinctBy$1 tL$0;
       Object obj1;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel != 2) {
                if (tlabel == 3) {
                   tlabel = this.L$3;
                   tL$2 = this.L$2;
                   tL$1 = this.L$1;
                   tL$0 = this.L$0;
                   b.b(p0);
                label_00b6 :
                   tL$1.add(tlabel);
                   tlabel = tL$2;
                label_00ba :
                   tL$2 = tL$1;
                   tL$1 = tL$0;
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                tL$1 = this.L$1;
                tL$0 = this.L$0;
                b.b(p0);
                tL$2 = this.L$3;
                tlabel = this.L$2;
             label_009d :
                if (!tL$1.contains(p0)) {
                   this.L$0 = tL$0;
                   this.L$1 = tL$1;
                   this.L$2 = tlabel;
                   this.L$3 = p0;
                   this.label = 3;
                   if (tL$0.d(tL$2, this) == obj) {
                      return obj;
                   }else {
                      tL$2 = tlabel;
                      tlabel = p0;
                      goto label_00b6 ;
                   }
                }else {
                   goto label_00ba ;
                }
             }
          }else {
             tlabel = this.L$2;
             tL$2 = this.L$1;
             tL$1 = this.L$0;
             b.b(p0);
          label_0079 :
             if (p0.booleanValue()) {
                p0 = tlabel.next();
                this.L$0 = tL$1;
                this.L$1 = tL$2;
                this.L$2 = tlabel;
                this.L$3 = p0;
                this.label = 2;
                if ((obj1 = this.$selector.invoke(p0, this)) == obj) {
                   return obj;
                }else {
                   tL$2 = p0;
                   p0 = obj1;
                   tL$0 = tL$1;
                   tL$1 = tL$2;
                   goto label_009d ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          tL$1 = this.L$0;
          HashSet hashSet = new HashSet();
          ChannelIterator uChannelIter = this.$this_distinctBy.iterator();
       }
       this.L$0 = tL$1;
       this.L$1 = tL$2;
       this.L$2 = tlabel;
       this.L$3 = 0;
       this.label = 1;
       if ((p0 = tlabel.a(this)) == obj) {
          return obj;
       }else {
          goto label_0079 ;
       }
    }
}
