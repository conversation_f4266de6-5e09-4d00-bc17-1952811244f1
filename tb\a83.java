package tb.a83;
import tb.t2o;
import java.lang.String;
import tb.o4p;
import java.util.HashMap;
import java.util.HashSet;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import com.alibaba.ability.localization.Localization;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.cvr;
import android.app.Application;
import com.taobao.tao.Globals;
import android.content.Context;
import tb.eno;
import com.alibaba.ability.localization.constants.Language;
import java.lang.Number;
import java.lang.Boolean;

public final class a83	// class@001771 from classes9.dex
{
    public static IpChange $ipChange;
    public static final String CARD_BABA_FARM;
    public static final String CARD_FARM_DATA_KEY;
    public static final String CARD_HB_DATA_KEY;
    public static final String CARD_HONG_BAO;
    public static final String CARD_LOCAL_M1_DATA_KEY;
    public static final String CARD_M3;
    public static final String CARD_M3_DATA_KEY;
    public static final String CARD_M3_LOW_CUSTOM;
    public static final String CARD_NEW_M3;
    public static final String CARD_TAO_FACTORY;
    public static final String CARD_TGC_DATA_KEY;
    public static final String CARD_TJB;
    public static final String CARD_TJB_DATA_KEY;
    public static final String CARD_TMGJ;
    public static final String CARD_TM_DATA_KEY;
    public static final String ELDER_M3;
    public static final a83 INSTANCE;
    public static final String KEY_MAIN_SEARCH_CARD_TYPE;
    public static final String NEW_CARD_M3_DATA_KEY;
    public static final String TOP_BAR_CHANNEL;
    public static final String TOP_BAR_CHANNEL_DATA_KEY;
    public static final String TOP_BAR_DATA_KEY;
    public static final String TOP_BAR_DEFAULT;
    public static final HashMap a;
    public static final HashMap b;
    public static final HashSet c;
    public static final String d;
    public static final String e;
    public static final String f;

    static {
       t2o.a(0x33200211);
       a83.INSTANCE = new a83();
       a83.d = o4p.l("search_biz", "cardI18M1", "ms_tmall-ovs-rax_tmg_search_item_all_m1");
       a83.e = o4p.l("search_biz", "cardI18LocalM1", "ms_tmall-ovs-rax_tmg_search_item_local_m2");
       a83.f = o4p.l("search_biz", "cardI18MultiM1", "ms_tmall-ovs-rax_tmg_search_item_multi_lang_m1");
       HashMap hashMap = new HashMap();
       a83.a = hashMap;
       a83.b = new HashMap();
       HashSet hashSet = new HashSet();
       a83.c = hashSet;
       hashMap.put("hongbaosrp", "ms_tb-webb-widget_hbqd-search-card");
       hashMap.put("taojinbi", "ms_tb-webb-widget-cloud_tbs_widget_m3_tjb");
       hashMap.put("taojinbi_md", "ms_tb-webb-widget-cloud_tbs_widget_m3_tjb");
       hashMap.put("babanongchang_shouye", "ms_tb-webb-widget-cloud_farm-feeds-search-result");
       hashMap.put("babanongchang_chm", "ms_tb-webb-widget-cloud_farm-feeds-search-result");
       hashMap.put("babanongchang_zghc", "ms_tb-webb-widget-cloud_farm-feeds-search-result");
       hashMap.put("temaizhiying", "ms_tgcmod_search-good-card");
       hashMap.put("tgc_mj", "ms_tgcmod_search-good-card");
       hashMap.put("tmgj_wangzhan", "ms_tm-global_tbs_widget_m1_tmall_global");
       hashMap.put("haiwaizhigou", "ms_tm-global_tbs_widget_m1_tmall_global");
       hashMap.put("haiwaizg", "ms_tm-global_tbs_widget_m1_tmall_global");
       hashMap.put("tmgj_xiaojiuguan", "ms_tm-global_tbs_widget_m1_tmall_global");
       hashMap.put("tmgj_meijia", "ms_tm-global_tbs_widget_m1_tmall_global");
       hashMap.put("meiguozg", "ms_tm-global_tbs_widget_m1_tmall_global");
       hashMap.put("tmgj_shishangdian", "ms_tm-global_tbs_widget_m1_tmall_global");
       hashMap.put("tmgj_jinkouchaoshi", "ms_tm-global_tbs_widget_m1_tmall_global");
       hashSet.add("tmgj_wangzhan");
       hashSet.add("haiwaizhigou");
    }
    public void a83(){
       super();
    }
    public final String a(String p0){
       String str;
       IpChange $ipChange = a83.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("e65dbe4f", objArray);
       }else {
          ckf.g(p0, "channelSrp");
          int b = Localization.p();
          if (Localization.n()) {
             p0 = a83.a.get(p0);
             if (TextUtils.isEmpty(p0)) {
                if (cvr.INSTANCE.f()) {
                   return "ms_tb-webb-widget_elder_tbs_item_widget";
                }else {
                   p0 = eno.d(Globals.getApplication(), "mainSearchCardType", "ms_tb-webb-widget_tbs_widget_m3");
                   ckf.f(p0, "getString\(...\)");
                   return p0;
                }
             }else {
                ckf.d(p0);
                return p0;
             }
          }else {
             p0 = "getI18CardType\(...\)";
             if (b) {
                str = o4p.a1("zh", a83.d);
                ckf.f(str, p0);
                return str;
             }else {
                str = Localization.h().getLanguage();
                ckf.f(str, "getLanguage\(...\)");
                if (TextUtils.isEmpty(str)) {
                   p0 = a83.e;
                   ckf.f(p0, "CARD_I18_LOCAL_M2");
                   return p0;
                }else if((b = str.hashCode()) != 3494){
                   if (b != 3651) {
                      if (b != 3700 || !str.equals("th")) {
                      label_00bd :
                         str = o4p.a1(str, a83.e);
                         ckf.f(str, p0);
                         return str;
                      }
                   }else if(!str.equals("ru")){
                      goto label_00bd ;
                   }
                }else if(str.equals("ms")){
                }
                str = o4p.a1(str, a83.f);
                ckf.f(str, p0);
                return str;
             }
          }
       }
    }
    public final int b(){
       IpChange $ipChange = a83.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return 6;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("17b4f410", objArray).intValue();
    }
    public final String c(String p0){
       IpChange $ipChange = a83.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("c02b0fa1", objArray);
       }else {
          ckf.g(p0, "channelSrp");
          if (!Localization.n()) {
             return "ms_tmall-ovs-rax_search_weexv2_topbar_sort";
          }
          String str = a83.b.get(p0);
          if (TextUtils.isEmpty(str)) {
             p0 = (TextUtils.isEmpty(p0))? "ms_tb-webb-widget-cloud_search_new_weexv2_topbar_sort": "ms_tb-webb-widget_search_weexv2_topbar_sort";
             return p0;
          }else {
             ckf.d(str);
             return str;
          }
       }
    }
    public final boolean d(String p0){
       IpChange $ipChange = a83.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("8447e1f5", objArray).booleanValue();
       }else {
          ckf.g(p0, "channelSrp");
          return (a83.c.contains(p0) ^ 1);
       }
    }
    public final String e(){
       IpChange $ipChange = a83.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a83.e;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9f285f38", objArray);
    }
    public final String f(){
       IpChange $ipChange = a83.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a83.d;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("effe3b8d", objArray);
    }
    public final String g(){
       IpChange $ipChange = a83.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a83.f;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("44720b72", objArray);
    }
}
