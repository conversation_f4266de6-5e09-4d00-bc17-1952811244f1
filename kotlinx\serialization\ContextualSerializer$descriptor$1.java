package kotlinx.serialization.ContextualSerializer$descriptor$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.ContextualSerializer;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;
import tb.x530;
import kotlinx.serialization.descriptors.a;
import tb.qb40;
import java.util.List;
import tb.yz3;

public final class ContextualSerializer$descriptor$1 extends Lambda implements g1a	// class@000702 from classes11.dex
{
    public final ContextualSerializer this$0;

    public void ContextualSerializer$descriptor$1(ContextualSerializer p0){
       this.this$0 = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       x530 ox530;
       a descriptor;
       ckf.g(p0, "$this$buildSerialDescriptor");
       List annotations = ((ox530 = ContextualSerializer.access$getFallbackSerializer$p(this.this$0)) != null && (descriptor = ox530.getDescriptor()) != null)? descriptor.getAnnotations(): null;
       if (annotations == null) {
          annotations = yz3.i();
       }
       p0.h(annotations);
       return;
    }
}
