package mtopsdk.mtop.upload.domain.FileStreamInfo;
import mtopsdk.mtop.domain.IMTOPDataObject;
import tb.t2o;
import java.io.InputStream;
import java.lang.String;
import java.lang.Object;
import java.lang.Class;
import mtopsdk.common.util.StringUtils;
import java.lang.StringBuilder;

public class FileStreamInfo implements IMTOPDataObject	// class@00080c from classes11.dex
{
    public long fileLength;
    private String fileName;
    private InputStream fileStream;

    static {
       t2o.a(0x25900010);
       t2o.a(0x253000cf);
    }
    public void FileStreamInfo(InputStream p0,String p1){
       super();
       this.fileStream = p0;
       this.fileName = p1;
    }
    public boolean equals(Object p0){
       FileStreamInfo tfileName;
       if (this == p0) {
          return true;
       }
       if (p0 == null) {
          return false;
       }
       if (this.getClass() != p0.getClass()) {
          return false;
       }
       if ((tfileName = this.fileName) == null) {
          if (p0.fileName != null) {
             return false;
          }
       }else if(!tfileName.equals(p0.fileName)){
          return false;
       }
       if ((tfileName = this.fileStream) == null) {
          if (p0.fileStream != null) {
             return false;
          }
       }else if(!tfileName.equals(p0.fileStream)){
          return false;
       }
       return true;
    }
    public String getFileName(){
       return this.fileName;
    }
    public InputStream getFileStream(){
       return this.fileStream;
    }
    public int hashCode(){
       FileStreamInfo tfileStream;
       FileStreamInfo tfileName = this.fileName;
       int i = 0;
       int i1 = (tfileName == null)? 0: tfileName.hashCode();
       i1 = (i1 + 31) * 31;
       if ((tfileStream = this.fileStream) != null) {
          i = tfileStream.hashCode();
       }
       return (i1 + i);
    }
    public boolean isValid(){
       boolean b = (!StringUtils.isBlank(this.fileName) && this.fileStream != null)? true: false;
       return b;
    }
    public String toString(){
       return new StringBuilder(32)+"FileStreamInfo [fileStream="+this.fileStream+", fileName="+this.fileName+"]";
    }
}
