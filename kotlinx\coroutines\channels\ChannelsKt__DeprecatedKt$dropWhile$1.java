package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$dropWhile$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;

public final class ChannelsKt__DeprecatedKt$dropWhile$1 extends SuspendLambda implements u1a	// class@0004e2 from classes11.dex
{
    public final u1a $predicate;
    public final ReceiveChannel $this_dropWhile;
    private Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;

    public void ChannelsKt__DeprecatedKt$dropWhile$1(ReceiveChannel p0,u1a p1,ar4 p2){
       this.$this_dropWhile = p0;
       this.$predicate = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$dropWhile$1 uodropWhile$ = new ChannelsKt__DeprecatedKt$dropWhile$1(this.$this_dropWhile, this.$predicate, p1);
       uodropWhile$.L$0 = p0;
       return uodropWhile$;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$dropWhile$1 tL$0;
       Object obj1;
       ChannelsKt__DeprecatedKt$dropWhile$1 uodropWhile$1;
       Object obj2;
       Object obj = dkf.d();
       ChannelsKt__DeprecatedKt$dropWhile$1 tlabel = this.label;
       int i = 3;
       if (tlabel != null) {
          if (tlabel != 1) {
             if (tlabel != 2) {
                if (tlabel != i) {
                   if (tlabel != 4) {
                      if (tlabel == 5) {
                         tlabel = this.L$1;
                         tL$0 = this.L$0;
                         b.b(p0);
                      label_0023 :
                         p0 = tlabel;
                         tlabel = tL$0;
                      label_00c5 :
                         this.L$0 = tlabel;
                         this.L$1 = p0;
                         this.label = 4;
                         if ((obj1 = p0.a(this)) == obj) {
                            return obj;
                         }else {
                            tlabel = p0;
                            p0 = obj1;
                            tL$0 = tlabel;
                         label_00d6 :
                            if (p0.booleanValue()) {
                               this.L$0 = tL$0;
                               this.L$1 = tlabel;
                               this.label = 5;
                               if (tL$0.d(tlabel.next(), this) == obj) {
                                  return obj;
                               }else {
                                  goto label_0023 ;
                               }
                            }else {
                               return xhv.INSTANCE;
                            }
                         }
                      }else {
                         throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                      }
                   }else {
                      tlabel = this.L$1;
                      tL$0 = this.L$0;
                      b.b(p0);
                      goto label_00d6 ;
                   }
                }else {
                   tlabel = this.L$0;
                   b.b(p0);
                }
             }else {
                ChannelsKt__DeprecatedKt$dropWhile$1 tL$01 = this.L$0;
                b.b(p0);
                ChannelsKt__DeprecatedKt$dropWhile$1 uodropWhile$ = this.L$1;
                uodropWhile$1 = this.L$2;
             label_0054 :
                tlabel = uodropWhile$;
                if (!p0.booleanValue()) {
                   this.L$0 = tL$01;
                   this.L$1 = 0;
                   this.L$2 = 0;
                   this.label = i;
                   if (tL$01.d(uodropWhile$1, this) == obj) {
                      return obj;
                   }else {
                      tlabel = tL$01;
                   }
                }else {
                   uodropWhile$1 = tL$01;
                label_0073 :
                   this.L$0 = uodropWhile$1;
                   this.L$1 = tlabel;
                   this.L$2 = 0;
                   this.label = 1;
                   if ((p0 = tlabel.a(this)) == obj) {
                      return obj;
                   }else {
                   label_0061 :
                      uodropWhile$1 = tlabel;
                      tlabel = uodropWhile$1;
                      if (p0.booleanValue()) {
                         p0 = uodropWhile$1.next();
                         this.L$0 = tlabel;
                         this.L$1 = uodropWhile$1;
                         this.L$2 = p0;
                         this.label = 2;
                         if ((obj2 = this.$predicate.invoke(p0, this)) == obj) {
                            return obj;
                         }else {
                            uodropWhile$1 = uodropWhile$1;
                            uodropWhile$1 = p0;
                            p0 = obj2;
                            tL$01 = tlabel;
                            goto label_0054 ;
                         }
                      }
                   }
                }
             }
          }else {
             tlabel = this.L$1;
             uodropWhile$1 = this.L$0;
             b.b(p0);
             goto label_0061 ;
          }
       }else {
          b.b(p0);
          ChannelIterator uChannelIter = this.$this_dropWhile.iterator();
          uodropWhile$1 = this.L$0;
          goto label_0073 ;
       }
       p0 = this.$this_dropWhile.iterator();
       goto label_00c5 ;
    }
}
