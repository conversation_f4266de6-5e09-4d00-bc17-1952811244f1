package tb.a0i;
import tb.r30;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import tb.imn;
import com.alibaba.fastjson.JSONObject;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import tb.vqa;
import tb.yyj;
import com.taobao.tao.recommend3.gateway.model.response.AwesomeGetContainerData;
import com.taobao.tao.recommend3.gateway.model.response.AwesomeGetContainerInnerData;
import tb.phg;
import com.taobao.android.upp.d;
import com.taobao.android.upp.UppProtocolImpl;
import java.util.Map;
import com.alibaba.fastjson.JSON;

public class a0i extends r30	// class@001738 from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2e0003dd);
    }
    public void a0i(String p0){
       super(p0);
    }
    public static Object ipc$super(a0i p0,String p1,Object[] p2){
       if (p1.hashCode() != -1062072309) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/tao/recommend3/newface/subcontainer/cn/MainLandSubContainerDataProcess");
       }
       super.e(p2[0], p2[1]);
       return null;
    }
    public void e(imn p0,JSONObject p1){
       AwesomeGetContainerData uAwesomeGetC;
       AwesomeGetContainerData base;
       AwesomeGetContainerInnerData ext;
       JSONObject jSONObject;
       IpChange $ipChange = a0i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("c0b2100b", objArray);
          return;
       }else {
          super.e(p0, p1);
          vqa ovqa = vqa.k().i("MTopRequest").j("dataProcess").e("MainLandSubContainerDataProcess;");
          String str = "requestType";
          ovqa.g(str, p1.getString(str));
          if ((uAwesomeGetC = p0.u(yyj.e().k())) == null) {
             ovqa.g("containerData", "null").a();
             return;
          }else if((base = uAwesomeGetC.base) != null && ((ext = base.ext) != null && (base.dataChange != null && base.isFirstPage != null))){
             base.infoFlowCardStartBizCode = ext.getString("feedsStartSectionBizCode");
             ovqa.g("infoFlowCardStartBizCode", uAwesomeGetC.base.infoFlowCardStartBizCode);
             if ((jSONObject = uAwesomeGetC.base.ext.getJSONObject("ucpConfig")) == null) {
                jSONObject = new JSONObject();
             }
             phg.b("addBizFeatures");
             UppProtocolImpl.getInstance().addBizFeatures(jSONObject, "Page_Home_Sub");
             phg.a("addBizFeatures");
             ovqa.g("ucpConfig content ", jSONObject.toString());
          }
          phg.b("uploadEvent");
          ovqa.a();
          phg.a("uploadEvent");
          return;
       }
    }
}
