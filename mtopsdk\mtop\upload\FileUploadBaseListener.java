package mtopsdk.mtop.upload.FileUploadBaseListener;
import mtopsdk.mtop.upload.FileUploadListener;
import java.lang.String;
import mtopsdk.mtop.upload.domain.UploadFileInfo;

public interface abstract FileUploadBaseListener implements FileUploadListener	// class@0007ff from classes11.dex
{

    void onError(String p0,String p1,String p2);
    void onFinish(UploadFileInfo p0,String p1);
    void onProgress(int p0);
    void onStart();
}
