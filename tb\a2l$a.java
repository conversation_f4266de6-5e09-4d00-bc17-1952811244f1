package tb.a2l$a;
import tb.t2o;
import com.taobao.kmp.nexus.arch.openArch.definition.OpenArchBizCode;
import java.lang.Enum;

public final class a2l$a	// class@001add from classes8.dex
{
    public static final int[] $EnumSwitchMapping$0;

    static {
       t2o.a(0x3f100003);
       int len = OpenArchBizCode.values().length;
       try{
          int[] ointArray = new int[len];
          ointArray[OpenArchBizCode.SingleLiveRoomPublic.ordinal()] = 1;
          try{
             e0[OpenArchBizCode.SingleLiveRoomShopTab3.ordinal()] = 2;
             try{
                e0[OpenArchBizCode.SingleLiveRoomShopLoft.ordinal()] = 3;
                try{
                   e0[OpenArchBizCode.SingleLiveRoomCard.ordinal()] = 4;
                   try{
                      e0[OpenArchBizCode.WatchLiveRoomPublic.ordinal()] = 5;
                      try{
                         e0[OpenArchBizCode.WatchLiveRoomAppTab2.ordinal()] = 6;
                         try{
                            e0[OpenArchBizCode.WatchLiveRoomHomeTab.ordinal()] = 7;
                            try{
                               e0[OpenArchBizCode.WatchLiveRoomChanelMix.ordinal()] = 8;
                               a2l$a.$EnumSwitchMapping$0 = e0;
                            }catch(java.lang.NoSuchFieldError e0){
                            }
                         }catch(java.lang.NoSuchFieldError e0){
                         }
                      }catch(java.lang.NoSuchFieldError e0){
                      }
                   }catch(java.lang.NoSuchFieldError e0){
                   }
                }catch(java.lang.NoSuchFieldError e0){
                }
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
