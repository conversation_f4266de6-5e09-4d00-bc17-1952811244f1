package kotlinx.coroutines.channels.c;
import kotlinx.coroutines.channels.i;
import kotlinx.coroutines.channels.ReceiveChannel;
import kotlinx.coroutines.channels.c$b;

public interface abstract c implements i, ReceiveChannel	// class@000518 from classes11.dex
{
    public static final int BUFFERED = 254;
    public static final int CONFLATED = 255;
    public static final String DEFAULT_BUFFER_PROPERTY_NAME = "kotlinx.coroutines.channels.defaultBuffer";
    public static final c$b Factory;
    public static final int OPTIONAL_CHANNEL;
    public static final int RENDEZVOUS;
    public static final int UNLIMITED;

    static {
       c.Factory = c$b.$$INSTANCE;
    }
}
