package tb.a3n;
import tb.a90;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.content.Context;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import com.taobao.taobao.R$layout;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.RelativeLayout$LayoutParams;
import tb.xcs;
import android.view.ViewGroup$LayoutParams;
import com.taobao.taobao.R$id;
import android.widget.ImageView;
import tb.a3n$a;
import android.view.View$OnClickListener;
import java.lang.NullPointerException;

public final class a3n extends a90	// class@001e97 from classes10.dex
{
    public RelativeLayout c;
    public static IpChange $ipChange;

    static {
       t2o.a(0x34d00062);
    }
    public void a3n(){
       super();
    }
    public static Object ipc$super(a3n p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/themis/pub/titlebar/action/PubGameCloseMoreAction");
    }
    public View l(Context p0){
       View view;
       ImageView imageView;
       IpChange $ipChange = a3n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("1ed25ea8", objArray);
       }else {
          ckf.g(p0, "context");
          if (this.c == null) {
             if ((view = View.inflate(p0, R$layout.tms_game_close_action, null)) != null) {
                RelativeLayout$LayoutParams layoutParams = new RelativeLayout$LayoutParams(xcs.a(p0, 75.00f), xcs.a(p0, 29.00f));
                layoutParams.rightMargin = xcs.a(p0, 12.00f);
                view.setLayoutParams(layoutParams);
                this.c = view;
                if ((imageView = view.findViewById(R$id.right_close)) != null) {
                   imageView.setOnClickListener(new a3n$a(p0));
                }
             }else {
                throw new NullPointerException("null cannot be cast to non-null type android.widget.RelativeLayout");
             }
          }
          return this.c;
       }
    }
}
