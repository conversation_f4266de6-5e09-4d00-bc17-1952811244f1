package androidx.appcompat.app.WindowDecorActionBar$3;
import androidx.core.view.ViewPropertyAnimatorUpdateListener;
import androidx.appcompat.app.WindowDecorActionBar;
import java.lang.Object;
import android.view.View;
import android.view.ViewParent;

public class WindowDecorActionBar$3 implements ViewPropertyAnimatorUpdateListener	// class@00058e from classes.dex
{
    public final WindowDecorActionBar this$0;

    public void WindowDecorActionBar$3(WindowDecorActionBar p0){
       this.this$0 = p0;
       super();
    }
    public void onAnimationUpdate(View p0){
       this.this$0.mContainerView.getParent().invalidate();
    }
}
