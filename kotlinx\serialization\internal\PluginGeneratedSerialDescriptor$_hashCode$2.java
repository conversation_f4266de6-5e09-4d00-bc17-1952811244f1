package kotlinx.serialization.internal.PluginGeneratedSerialDescriptor$_hashCode$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor;
import java.lang.Integer;
import kotlinx.serialization.descriptors.a;
import tb.xy30;
import java.lang.Object;

public final class PluginGeneratedSerialDescriptor$_hashCode$2 extends Lambda implements d1a	// class@000748 from classes11.dex
{
    public final PluginGeneratedSerialDescriptor this$0;

    public void PluginGeneratedSerialDescriptor$_hashCode$2(PluginGeneratedSerialDescriptor p0){
       this.this$0 = p0;
       super(0);
    }
    public final Integer invoke(){
       PluginGeneratedSerialDescriptor$_hashCode$2 tthis$0 = this.this$0;
       return Integer.valueOf(xy30.a(tthis$0, tthis$0.n()));
    }
    public Object invoke(){
       return this.invoke();
    }
}
