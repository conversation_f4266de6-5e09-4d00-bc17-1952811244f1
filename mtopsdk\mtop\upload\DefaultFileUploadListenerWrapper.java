package mtopsdk.mtop.upload.DefaultFileUploadListenerWrapper;
import mtopsdk.mtop.upload.FileUploadBaseListener;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import java.lang.Boolean;
import mtopsdk.mtop.upload.FileUploadListener;
import java.lang.System;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import java.lang.Integer;

public class DefaultFileUploadListenerWrapper implements FileUploadBaseListener	// class@0007fe from classes11.dex
{
    private AtomicBoolean isCancelled;
    private AtomicBoolean isFinished;
    private FileUploadBaseListener listener;
    private AtomicInteger retryTimes;
    public long segmentNum;
    public long serverRT;
    private long startTime;
    private long totalTime;
    public static IpChange $ipChange;

    static {
       t2o.a(0x25900002);
       t2o.a(0x25900003);
    }
    public void DefaultFileUploadListenerWrapper(FileUploadBaseListener p0){
       super();
       this.isCancelled = new AtomicBoolean(false);
       this.isFinished = new AtomicBoolean(false);
       this.retryTimes = new AtomicInteger(false);
       this.listener = p0;
    }
    public void cancel(){
       int i = 1;
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("707fe601", objArray);
          return;
       }else {
          this.isCancelled.set(i);
          return;
       }
    }
    public void countRetryTimes(){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("744489f4", objArray);
          return;
       }else {
          this.retryTimes.incrementAndGet();
          return;
       }
    }
    public int getTotalRetryTimes(){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.retryTimes.get();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e0d77026", objArray).intValue();
    }
    public long getUploadTotalTime(){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.totalTime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("28deec75", objArray).longValue();
    }
    public boolean isCancelled(){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.isCancelled.get();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6ff5f452", objArray).booleanValue();
    }
    public AtomicBoolean isFinished(){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.isFinished;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5e519fc3", objArray);
    }
    public void onError(String p0,String p1){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("97d08c84", objArray);
          return;
       }else if(this.listener != null && !this.isCancelled()){
          this.listener.onError(p0, p1);
       }
       return;
    }
    public void onError(String p0,String p1,String p2){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("ffe51d4e", objArray);
          return;
       }else {
          this.totalTime = System.currentTimeMillis() - this.startTime;
          if (this.listener != null && !this.isCancelled()) {
             this.listener.onError(p0, p1, p2);
          }
          return;
       }
    }
    public void onFinish(String p0){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("99807463", objArray);
          return;
       }else if(this.listener != null && !this.isCancelled()){
          this.listener.onFinish(p0);
       }
       return;
    }
    public void onFinish(UploadFileInfo p0,String p1){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("57d514e0", objArray);
          return;
       }else {
          this.totalTime = System.currentTimeMillis() - this.startTime;
          if (this.listener != null && !this.isCancelled()) {
             this.listener.onFinish(p0, p1);
             this.cancel();
          }
          return;
       }
    }
    public void onProgress(int p0){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("5ec8f5b0", objArray);
          return;
       }else if(this.listener != null && !this.isCancelled()){
          this.listener.onProgress(p0);
       }
       return;
    }
    public void onStart(){
       IpChange $ipChange = DefaultFileUploadListenerWrapper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("7f2d84ca", objArray);
          return;
       }else {
          this.startTime = System.currentTimeMillis();
          if (this.listener != null && !this.isCancelled()) {
             this.listener.onStart();
          }
          return;
       }
    }
}
