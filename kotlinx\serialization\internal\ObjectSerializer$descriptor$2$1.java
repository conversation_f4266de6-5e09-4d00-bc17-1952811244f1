package kotlinx.serialization.internal.ObjectSerializer$descriptor$2$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.internal.ObjectSerializer;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;
import java.util.List;

public final class ObjectSerializer$descriptor$2$1 extends Lambda implements g1a	// class@000743 from classes11.dex
{
    public final ObjectSerializer this$0;

    public void ObjectSerializer$descriptor$2$1(ObjectSerializer p0){
       this.this$0 = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       ckf.g(p0, "$this$buildSerialDescriptor");
       p0.h(ObjectSerializer.c(this.this$0));
    }
}
