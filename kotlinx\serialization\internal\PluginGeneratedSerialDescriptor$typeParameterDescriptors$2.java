package kotlinx.serialization.internal.PluginGeneratedSerialDescriptor$typeParameterDescriptors$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor;
import java.lang.Object;
import kotlinx.serialization.descriptors.a;
import tb.eu20;
import tb.x530;
import java.util.ArrayList;
import tb.qb40;
import java.util.List;
import tb.wy30;

public final class PluginGeneratedSerialDescriptor$typeParameterDescriptors$2 extends Lambda implements d1a	// class@00074b from classes11.dex
{
    public final PluginGeneratedSerialDescriptor this$0;

    public void PluginGeneratedSerialDescriptor$typeParameterDescriptors$2(PluginGeneratedSerialDescriptor p0){
       this.this$0 = p0;
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final a[] invoke(){
       eu20 uoeu20;
       x530[] ox530Array;
       ArrayList uArrayList;
       if ((uoeu20 = PluginGeneratedSerialDescriptor.i(this.this$0)) != null && (ox530Array = uoeu20.a()) != null) {
          uArrayList = new ArrayList(ox530Array.length);
          int len = ox530Array.length;
          for (int i = 0; i < len; i = i + 1) {
             uArrayList.add(ox530Array[i].getDescriptor());
          }
       }else {
          uArrayList = null;
       }
       return wy30.b(uArrayList);
    }
}
