package mtopsdk.mtop.upload.domain.FileBaseInfo;
import tb.t2o;
import java.io.File;
import java.lang.Object;
import java.io.InputStream;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;

public class FileBaseInfo	// class@00080b from classes11.dex
{
    public File file;
    public String fileId;
    public InputStream fileInputStream;
    public String fileName;
    public long fileSize;
    public String fileType;
    public static IpChange $ipChange;

    static {
       t2o.a(0x2590000f);
    }
    public void FileBaseInfo(File p0){
       super();
       this.file = p0;
    }
    public void FileBaseInfo(InputStream p0){
       super();
       this.fileInputStream = p0;
    }
    public String toString(){
       IpChange $ipChange = FileBaseInfo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(64)+"FileBaseInfo [file="+this.file+", fileInputStream="+this.fileInputStream+", fileName="+this.fileName+", fileType="+this.fileType+", fileId="+this.fileId+", fileSize="+this.fileSize+"]";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
