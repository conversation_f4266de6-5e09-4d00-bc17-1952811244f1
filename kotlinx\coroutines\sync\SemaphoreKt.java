package kotlinx.coroutines.sync.SemaphoreKt;
import java.lang.String;
import java.lang.Object;
import tb.o3r;
import tb.u1r;
import tb.y9p;
import kotlinx.coroutines.sync.SemaphoreImpl;
import tb.z9p;
import tb.d1a;
import tb.ar4;
import kotlinx.coroutines.sync.SemaphoreKt$withPermit$1;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;

public final class SemaphoreKt	// class@0006d0 from classes11.dex
{
    public static final int a;
    public static final u1r b;
    public static final u1r c;
    public static final u1r d;
    public static final u1r e;
    public static final int f;

    static {
       SemaphoreKt.a = o3r.g("kotlinx.coroutines.semaphore.maxSpinCycles", 100, 0, 0, 12, null);
       SemaphoreKt.b = new u1r("PERMIT");
       SemaphoreKt.c = new u1r("TAKEN");
       SemaphoreKt.d = new u1r("BROKEN");
       SemaphoreKt.e = new u1r("CANCELLED");
       SemaphoreKt.f = o3r.g("kotlinx.coroutines.semaphore.segmentSize", 16, 0, 0, 12, null);
    }
    public static final y9p a(int p0,int p1){
       return new SemaphoreImpl(p0, p1);
    }
    public static y9p b(int p0,int p1,int p2,Object p3){
       if ((p2 & 0x02)) {
          p1 = 0;
       }
       return SemaphoreKt.a(p0, p1);
    }
    public static final z9p c(long p0,z9p p1){
       return SemaphoreKt.j(p0, p1);
    }
    public static final u1r d(){
       return SemaphoreKt.d;
    }
    public static final u1r e(){
       return SemaphoreKt.e;
    }
    public static final int f(){
       return SemaphoreKt.a;
    }
    public static final u1r g(){
       return SemaphoreKt.b;
    }
    public static final int h(){
       return SemaphoreKt.f;
    }
    public static final u1r i(){
       return SemaphoreKt.c;
    }
    public static final z9p j(long p0,z9p p1){
       return new z9p(p0, p1, 0);
    }
    public static final Object k(y9p p0,d1a p1,ar4 p2){
       SemaphoreKt$withPermit$1 owithPermit$;
       SemaphoreKt$withPermit$1 label1;
       SemaphoreKt$withPermit$1 owithPermit$1;
       SemaphoreKt$withPermit$1 l$0;
       if (p2 instanceof SemaphoreKt$withPermit$1) {
          owithPermit$ = p2;
          SemaphoreKt$withPermit$1 label = owithPermit$.label;
          int i = Integer.MIN_VALUE;
          if ((label & i)) {
             owithPermit$.label = label - i;
          label_0018 :
             SemaphoreKt$withPermit$1 result = owithPermit$.result;
             Object obj = dkf.d();
             if ((label1 = owithPermit$.label) != null) {
                if (label1 == 1) {
                   owithPermit$1 = owithPermit$.L$1;
                   l$0 = owithPermit$.L$0;
                   b.b(result);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                owithPermit$.L$0 = p0;
                owithPermit$.L$1 = p1;
                owithPermit$.label = 1;
                if (p0.a(owithPermit$) == obj) {
                   return obj;
                }
             }
             l$0.release();
             return owithPermit$1.invoke();
          }
       }
       owithPermit$ = new SemaphoreKt$withPermit$1(p2);
       goto label_0018 ;
    }
}
