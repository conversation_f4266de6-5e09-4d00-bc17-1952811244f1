package androidx.appcompat.app.AppLocalesMetadataHolderService;
import android.app.Service;
import android.content.Context;
import android.content.pm.ServiceInfo;
import android.os.Build$VERSION;
import androidx.appcompat.app.AppLocalesMetadataHolderService$Api24Impl;
import android.content.pm.PackageManager;
import android.content.ComponentName;
import java.lang.Class;
import android.content.Intent;
import android.os.IBinder;
import java.lang.UnsupportedOperationException;

public final class AppLocalesMetadataHolderService extends Service	// class@00057e from classes.dex
{

    public void AppLocalesMetadataHolderService(){
       super();
    }
    public static ServiceInfo getServiceInfo(Context p0){
       int i = (Build$VERSION.SDK_INT >= 24)? AppLocalesMetadataHolderService$Api24Impl.getDisabledComponentFlag() | 0x0080: 640;
       return p0.getPackageManager().getServiceInfo(new ComponentName(p0, AppLocalesMetadataHolderService.class), i);
    }
    public IBinder onBind(Intent p0){
       throw new UnsupportedOperationException();
    }
}
