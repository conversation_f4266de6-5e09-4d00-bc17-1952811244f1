package tb.ad7;
import tb.t2o;
import android.content.Context;
import tb.cd7;
import tb.bd7;
import java.lang.Object;
import java.util.HashMap;
import tb.hf7;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.request.MtopRequestClient;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.request.DescDynamicClient;
import tb.zc7;
import tb.ad7$b;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.request.DescDynamicParams;
import com.alibaba.fastjson.JSONObject;
import mtopsdk.mtop.global.SDKConfig;
import tb.l6j;
import java.lang.Integer;
import tb.ad7$a;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.request.DescMtopStaticRequestParams;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.request.DescMtopStaticRequestClient;
import tb.gf7;

public class ad7	// class@001877 from classes5.dex
{
    public final cd7 a;
    public final bd7 b;
    public final hf7 c;
    public gf7 d;
    public l6j e;
    public l6j f;
    public MtopRequestClient g;
    public final HashMap h;
    public final Context i;
    public static IpChange $ipChange;

    static {
       t2o.a(0x38e005c7);
    }
    public void ad7(Context p0,cd7 p1,bd7 p2){
       super();
       this.h = new HashMap();
       this.a = p1;
       this.b = p2;
       this.i = p0;
       this.c = new hf7(p0);
    }
    public static void a(ad7 p0,HashMap p1){
       IpChange $ipChange = ad7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("cca7d494", objArray);
          return;
       }else {
          p0.e(p1);
          return;
       }
    }
    public static HashMap b(ad7 p0){
       IpChange $ipChange = ad7.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.h;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("fdb3cce3", objArray);
    }
    public void c(){
       ad7 tg;
       IpChange $ipChange = ad7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("707fe601", objArray);
          return;
       }else if((tg = this.g) != null){
          tg.cancel();
       }
       if ((tg = this.h) != null && !tg.isEmpty()) {
          Iterator iterator = this.h.entrySet().iterator();
          while (iterator.hasNext()) {
             iterator.next().getValue().cancel();
          }
       }
       return;
    }
    public void d(){
       IpChange $ipChange = ad7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("e48bb97c", objArray);
          return;
       }else {
          this.g();
          return;
       }
    }
    public final void e(HashMap p0){
       IpChange $ipChange = ad7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("85133d1c", objArray);
          return;
       }else {
          Iterator iterator = p0.keySet().iterator();
          while (iterator.hasNext()) {
             this.f(p0.get(iterator.next()));
          }
          return;
       }
    }
    public final void f(zc7 p0){
       IpChange $ipChange = ad7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("999a0548", objArray);
          return;
       }else {
          this.f = new ad7$b(this);
          DescDynamicClient uDescDynamic = new DescDynamicClient(new DescDynamicParams(p0.b), SDKConfig.getInstance().getGlobalTtid(), this.f, this.c);
          this.h.put(Integer.valueOf(uDescDynamic.hashCode()), uDescDynamic);
          uDescDynamic.execute();
          return;
       }
    }
    public final void g(){
       ad7 tg;
       IpChange $ipChange = ad7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("6a25944c", objArray);
          return;
       }else {
          this.e = new ad7$a(this);
          DescMtopStaticRequestClient uDescMtopSta = new DescMtopStaticRequestClient(new DescMtopStaticRequestParams(this.a), SDKConfig.getInstance().getGlobalTtid(), this.e, this.c);
          this.g = uDescMtopSta;
          uDescMtopSta.setContext(this.i);
          if ((tg = this.g) != null) {
             tg.execute();
          }
          return;
       }
    }
    public void h(){
       ad7 td;
       IpChange $ipChange = ad7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("32c73e43", objArray);
          return;
       }else if((td = this.d) != null && (td.a() && this.h.isEmpty())){
          this.e(this.d.b);
       }
       return;
    }
}
