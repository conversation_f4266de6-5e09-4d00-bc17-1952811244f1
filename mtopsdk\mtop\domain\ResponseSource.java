package mtopsdk.mtop.domain.ResponseSource;
import java.io.Serializable;
import tb.t2o;
import tb.w4j;
import mtopsdk.mtop.cache.CacheManager;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.common.util.StringUtils;
import mtopsdk.mtop.domain.MtopRequest;

public class ResponseSource implements Serializable	// class@0007c0 from classes11.dex
{
    private String cacheBlock;
    private String cacheKey;
    public CacheManager cacheManager;
    public MtopResponse cacheResponse;
    public w4j mtopContext;
    public boolean requireConnection;
    public RpcCache rpcCache;
    public String seqNo;
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000d7);
    }
    public void ResponseSource(w4j p0,CacheManager p1){
       super();
       this.rpcCache = null;
       this.requireConnection = true;
       this.mtopContext = p0;
       this.cacheManager = p1;
       this.seqNo = p0.h;
    }
    public String getCacheBlock(){
       IpChange $ipChange = ResponseSource.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("3f5abf78", objArray);
       }else if(StringUtils.isNotBlank(this.cacheBlock)){
          return this.cacheBlock;
       }else {
          String blockName = this.cacheManager.getBlockName(this.mtopContext.b.getKey());
          this.cacheBlock = blockName;
          return blockName;
       }
    }
    public String getCacheKey(){
       IpChange $ipChange = ResponseSource.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("e0d367e6", objArray);
       }else if(StringUtils.isNotBlank(this.cacheKey)){
          return this.cacheKey;
       }else {
          String cacheKey = this.cacheManager.getCacheKey(this.mtopContext);
          this.cacheKey = cacheKey;
          return cacheKey;
       }
    }
}
