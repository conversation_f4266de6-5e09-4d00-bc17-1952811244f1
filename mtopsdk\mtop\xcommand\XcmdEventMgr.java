package mtopsdk.mtop.xcommand.XcmdEventMgr;
import tb.t2o;
import java.util.concurrent.CopyOnWriteArraySet;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.mtop.xcommand.NewXcmdListener;
import java.util.Set;
import mtopsdk.common.util.StringUtils;
import mtopsdk.mtop.xcommand.NewXcmdEvent;
import java.util.Iterator;

public class XcmdEventMgr	// class@00082e from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;
    public static Set oxcmdListeners;
    private static XcmdEventMgr xm;

    static {
       t2o.a(0x2530013c);
       XcmdEventMgr.oxcmdListeners = new CopyOnWriteArraySet();
    }
    public void XcmdEventMgr(){
       super();
    }
    public static XcmdEventMgr getInstance(){
       IpChange $ipChange = XcmdEventMgr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("23c174e1", objArray);
       }else if(XcmdEventMgr.xm == null){
          XcmdEventMgr xcmdEventMgr = XcmdEventMgr.class;
          _monitor_enter(xcmdEventMgr);
          if (XcmdEventMgr.xm == null) {
             XcmdEventMgr.xm = new XcmdEventMgr();
          }
          _monitor_exit(xcmdEventMgr);
       }
       return XcmdEventMgr.xm;
    }
    public void addOrangeXcmdListener(NewXcmdListener p0){
       IpChange $ipChange = XcmdEventMgr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("9d154011", objArray);
          return;
       }else {
          XcmdEventMgr.oxcmdListeners.add(p0);
          return;
       }
    }
    public void onOrangeEvent(String p0){
       IpChange $ipChange = XcmdEventMgr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("60c79c7e", objArray);
          return;
       }else if(StringUtils.isBlank(p0)){
          return;
       }else {
          NewXcmdEvent newXcmdEvent = new NewXcmdEvent(p0);
          Iterator iterator = XcmdEventMgr.oxcmdListeners.iterator();
          while (iterator.hasNext()) {
             iterator.next().onEvent(newXcmdEvent);
          }
          return;
       }
    }
    public void removeOrangeXcmdListener(NewXcmdListener p0){
       IpChange $ipChange = XcmdEventMgr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("16291b0e", objArray);
          return;
       }else {
          XcmdEventMgr.oxcmdListeners.remove(p0);
          return;
       }
    }
}
