package kotlinx.datetime.internal.format.SignedFormatStructure$formatter$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import tb.uc40;
import tb.ckf$a;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Boolean;

public final class SignedFormatStructure$formatter$1 extends FunctionReferenceImpl implements g1a	// class@0006fb from classes11.dex
{
    public final uc40 this$0;

    public void SignedFormatStructure$formatter$1(uc40 p0){
       super(1, ckf$a.class, "checkIfAllNegative", "formatter$checkIfAllNegative\(Lkotlinx/datetime/internal/format/SignedFormatStructure;Ljava/lang/Object;\)Z", 0);
    }
    public final Boolean invoke(Object p0){
       uc40.a(null, p0);
       throw null;
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
}
