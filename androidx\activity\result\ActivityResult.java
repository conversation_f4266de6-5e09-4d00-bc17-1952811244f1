package androidx.activity.result.ActivityResult;
import android.os.Parcelable;
import androidx.activity.result.ActivityResult$Companion;
import tb.a07;
import androidx.activity.result.ActivityResult$Companion$CREATOR$1;
import android.content.Intent;
import java.lang.Object;
import android.os.Parcel;
import java.lang.String;
import tb.ckf;
import android.os.Parcelable$Creator;
import java.lang.StringBuilder;

public final class ActivityResult implements Parcelable	// class@0004aa from classes.dex
{
    private final Intent data;
    private final int resultCode;
    public static final Parcelable$Creator CREATOR;
    public static final ActivityResult$Companion Companion;

    static {
       ActivityResult.Companion = new ActivityResult$Companion(null);
       ActivityResult.CREATOR = new ActivityResult$Companion$CREATOR$1();
    }
    public void ActivityResult(int p0,Intent p1){
       super();
       this.resultCode = p0;
       this.data = p1;
    }
    public void ActivityResult(Parcel p0){
       ckf.g(p0, "parcel");
       int i = p0.readInt();
       Intent intent = (!p0.readInt())? null: Intent.CREATOR.createFromParcel(p0);
       super(i, intent);
       return;
    }
    public static final String resultCodeToString(int p0){
       return ActivityResult.Companion.resultCodeToString(p0);
    }
    public int describeContents(){
       return 0;
    }
    public final Intent getData(){
       return this.data;
    }
    public final int getResultCode(){
       return this.resultCode;
    }
    public String toString(){
       return "ActivityResult{resultCode="+ActivityResult.Companion.resultCodeToString(this.resultCode)+", data="+this.data+'}';
    }
    public void writeToParcel(Parcel p0,int p1){
       ActivityResult tdata;
       ckf.g(p0, "dest");
       p0.writeInt(this.resultCode);
       int i = (this.data == null)? 0: 1;
       p0.writeInt(i);
       if ((tdata = this.data) != null) {
          tdata.writeToParcel(p0, p1);
       }
       return;
    }
}
