package kotlinx.datetime.internal.format.OptionalFormatStructure$formatter$2;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.Object;
import tb.yy40;
import java.lang.Class;
import java.lang.String;
import java.lang.Boolean;
import kotlin.jvm.internal.CallableReference;

public final class OptionalFormatStructure$formatter$2 extends FunctionReferenceImpl implements g1a	// class@0006f9 from classes11.dex
{

    public void OptionalFormatStructure$formatter$2(Object p0){
       super(1, p0, yy40.class, "test", "test\(Ljava/lang/Object;\)Z", 0);
    }
    public final Boolean invoke(Object p0){
       this.receiver.getClass();
       return Boolean.TRUE;
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
}
