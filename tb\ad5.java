package tb.ad5;
import tb.t2o;
import com.taobao.android.dinamicx.DXEngineConfig;
import java.lang.Object;
import java.lang.String;
import tb.av5;
import com.taobao.android.dinamicx.p;
import com.taobao.android.dinamicx.m;
import com.android.alibaba.ip.runtime.IpChange;
import tb.eb5;
import tb.y66;

public class ad5	// class@001873 from classes5.dex
{
    public final String a;
    public av5 b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1c500096);
    }
    public void ad5(DXEngineConfig p0){
       super();
       if (p0 == null) {
          p0 = new DXEngineConfig("default_bizType");
       }
       this.a = p0.a;
       return;
    }
    public void ad5(av5 p0){
       super();
       if (p0 == null) {
          DXEngineConfig uDXEngineCon = new DXEngineConfig("default_bizType");
          this.a = uDXEngineCon.a;
          this.b = new av5(uDXEngineCon);
          return;
       }else {
          this.b = p0;
          this.a = p0.c().a;
          return;
       }
    }
    public m b(av5 p0,p p1){
       IpChange $ipChange = ad5.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("f453cc87", objArray);
       }else if(eb5.w()){
          return new y66(p0, p1);
       }else {
          return new m(p0, p1);
       }
    }
    public String c(){
       IpChange $ipChange = ad5.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9c07dca2", objArray);
    }
    public DXEngineConfig d(){
       IpChange $ipChange = ad5.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b.c();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2f487a5b", objArray);
    }
    public av5 f(){
       IpChange $ipChange = ad5.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("351370a9", objArray);
    }
}
