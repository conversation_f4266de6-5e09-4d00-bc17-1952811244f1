package kotlinx.serialization.SerializationException;
import java.lang.IllegalArgumentException;
import java.lang.String;
import java.lang.Throwable;

public class SerializationException extends IllegalArgumentException	// class@000719 from classes11.dex
{

    public void SerializationException(){
       super();
    }
    public void SerializationException(String p0){
       super(p0);
    }
    public void SerializationException(String p0,Throwable p1){
       super(p0, p1);
    }
    public void SerializationException(Throwable p0){
       super(p0);
    }
}
