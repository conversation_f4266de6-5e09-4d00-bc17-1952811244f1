package androidx.activity.result.PickVisualMediaRequestKt;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType;
import androidx.activity.result.PickVisualMediaRequest;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.activity.result.PickVisualMediaRequest$Builder;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$ImageAndVideo;

public final class PickVisualMediaRequestKt	// class@0004c3 from classes.dex
{

    public static final PickVisualMediaRequest PickVisualMediaRequest(ActivityResultContracts$PickVisualMedia$VisualMediaType p0){
       ckf.g(p0, "mediaType");
       return new PickVisualMediaRequest$Builder().setMediaType(p0).build();
    }
    public static PickVisualMediaRequest PickVisualMediaRequest$default(ActivityResultContracts$PickVisualMedia$VisualMediaType p0,int p1,Object p2){
       ActivityResultContracts$PickVisualMedia$ImageAndVideo iNSTANCE;
       if ((p1 & 0x01)) {
          iNSTANCE = ActivityResultContracts$PickVisualMedia$ImageAndVideo.INSTANCE;
       }
       return PickVisualMediaRequestKt.PickVisualMediaRequest(iNSTANCE);
    }
}
