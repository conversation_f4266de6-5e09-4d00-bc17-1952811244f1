package kotlinx.serialization.internal.ObjectSerializer;
import tb.x530;
import java.lang.String;
import java.lang.Object;
import tb.ckf;
import java.util.List;
import tb.yz3;
import kotlin.LazyThreadSafetyMode;
import kotlinx.serialization.internal.ObjectSerializer$descriptor$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import kotlinx.serialization.descriptors.a;
import tb.gn20;
import tb.ha20;
import tb.g43;

public final class ObjectSerializer implements x530	// class@000745 from classes11.dex
{
    public final Object a;
    public final List b;
    public final njg c;

    public void ObjectSerializer(String p0,Object p1){
       ckf.g(p0, "serialName");
       ckf.g(p1, "objectInstance");
       super();
       this.a = p1;
       this.b = yz3.i();
       this.c = a.a(LazyThreadSafetyMode.PUBLICATION, new ObjectSerializer$descriptor$2(p0, this));
    }
    public static final List c(ObjectSerializer p0){
       return p0.b;
    }
    public a getDescriptor(){
       return this.c.getValue();
    }
    public void serialize(gn20 p0,Object p1){
       ckf.g(p0, "encoder");
       ckf.g(p1, "value");
       p0.b(this.getDescriptor()).s(this.getDescriptor());
    }
}
