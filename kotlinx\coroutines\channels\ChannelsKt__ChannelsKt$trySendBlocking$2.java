package kotlinx.coroutines.channels.ChannelsKt__ChannelsKt$trySendBlocking$2;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.i;
import java.lang.Object;
import tb.ar4;
import tb.uu4;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import kotlin.Result;
import java.lang.Throwable;
import kotlinx.coroutines.channels.e;
import kotlinx.coroutines.channels.e$b;

public final class ChannelsKt__ChannelsKt$trySendBlocking$2 extends SuspendLambda implements u1a	// class@0004d6 from classes11.dex
{
    public final Object $element;
    public final i $this_trySendBlocking;
    private Object L$0;
    public int label;

    public void ChannelsKt__ChannelsKt$trySendBlocking$2(i p0,Object p1,ar4 p2){
       this.$this_trySendBlocking = p0;
       this.$element = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__ChannelsKt$trySendBlocking$2 otrySendBloc = new ChannelsKt__ChannelsKt$trySendBlocking$2(this.$this_trySendBlocking, this.$element, p1);
       otrySendBloc.L$0 = p0;
       return otrySendBloc;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(uu4 p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__ChannelsKt$trySendBlocking$2 tlabel;
       xhv obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel == 1) {
             b.b(p0);
          }else {
             throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
          }
       }else {
          b.b(p0);
          this.label = 1;
          if (this.$this_trySendBlocking.d(this.$element, this) == obj) {
             return obj;
          }
       }
       p0 = Result.constructor-impl(xhv.INSTANCE);
       if (Result.isSuccess-impl(p0)) {
          obj = xhv.INSTANCE;
          e.Companion.b(obj);
       }else {
          obj = e.Companion.a(Result.exceptionOrNull-impl(p0));
       }
       return e.a(obj);
    }
}
