package kotlinx.coroutines.JobSupport$children$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.RestrictedSuspendLambda;
import kotlinx.coroutines.JobSupport;
import tb.ar4;
import java.lang.Object;
import tb.vbp;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.internal.LockFreeLinkedListNode;
import kotlinx.coroutines.internal.a;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import tb.ir3;
import tb.yse;
import tb.f5k;
import tb.ckf;

public final class JobSupport$children$1 extends RestrictedSuspendLambda implements u1a	// class@0004a5 from classes11.dex
{
    private Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;
    public final JobSupport this$0;

    public void JobSupport$children$1(JobSupport p0,ar4 p1){
       this.this$0 = p0;
       super(2, p1);
    }
    public final ar4 create(Object p0,ar4 p1){
       JobSupport$children$1 uochildren$1 = new JobSupport$children$1(this.this$0, p1);
       uochildren$1.L$0 = p0;
       return uochildren$1;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(vbp p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       JobSupport$children$1 tL$0;
       LockFreeLinkedListNode lockFreeLink;
       Object obj = dkf.d();
       JobSupport$children$1 tlabel = this.label;
       int i = 1;
       if (tlabel != null) {
          if (tlabel != i) {
             if (tlabel == 2) {
                tlabel = this.L$2;
                JobSupport$children$1 tL$1 = this.L$1;
                tL$0 = this.L$0;
                b.b(p0);
             label_0082 :
                lockFreeLink = tlabel.i();
             label_0064 :
                if (!ckf.b(lockFreeLink, tL$1)) {
                   if (lockFreeLink instanceof ir3) {
                      this.L$0 = tL$0;
                      this.L$1 = tL$1;
                      this.L$2 = lockFreeLink;
                      this.label = 2;
                      if (tL$0.a(lockFreeLink.h, this) == obj) {
                         return obj;
                      }else {
                         goto label_0082 ;
                      }
                   }else {
                      goto label_0082 ;
                   }
                }
             }else {
                throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
             }
          }else {
             b.b(p0);
          }
       }else {
          b.b(p0);
          p0 = this.L$0;
          f5k obj1 = this.this$0.v0();
          if (obj1 instanceof ir3) {
             this.label = i;
             if (p0.a(obj1.h, this) == obj) {
                return obj;
             }
          }else if(obj1 instanceof yse && (obj1 = obj1.getList()) != null){
             f5k obj2 = obj1.h();
             ckf.e(obj2, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeLinkedListNode{ kotlinx.coroutines.internal.LockFreeLinkedListKt.Node }");
             tL$0 = p0;
             Object obj3 = obj2;
             obj2 = obj1;
             lockFreeLink = obj3;
             goto label_0064 ;
          }
       }
       return xhv.INSTANCE;
    }
}
