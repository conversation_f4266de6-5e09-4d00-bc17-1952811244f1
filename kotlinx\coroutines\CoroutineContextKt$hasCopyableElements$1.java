package kotlinx.coroutines.CoroutineContextKt$hasCopyableElements$1;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import kotlin.coroutines.d$b;
import java.lang.Boolean;
import tb.st4;
import java.lang.Object;

public final class CoroutineContextKt$hasCopyableElements$1 extends Lambda implements u1a	// class@00048f from classes11.dex
{
    public static final CoroutineContextKt$hasCopyableElements$1 INSTANCE;

    static {
       CoroutineContextKt$hasCopyableElements$1.INSTANCE = new CoroutineContextKt$hasCopyableElements$1();
    }
    public void CoroutineContextKt$hasCopyableElements$1(){
       super(2);
    }
    public final Boolean invoke(boolean p0,d$b p1){
       p0 = (!p0 && !p1 instanceof st4)? false: true;
       return Boolean.valueOf(p0);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0.booleanValue(), p1);
    }
}
