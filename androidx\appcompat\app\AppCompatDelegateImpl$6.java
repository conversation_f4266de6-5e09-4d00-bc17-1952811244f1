package androidx.appcompat.app.AppCompatDelegateImpl$6;
import java.lang.Runnable;
import androidx.appcompat.app.AppCompatDelegateImpl;
import java.lang.Object;
import android.view.View;
import android.widget.PopupWindow;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.core.view.ViewCompat;
import androidx.appcompat.app.AppCompatDelegateImpl$6$1;
import androidx.core.view.ViewPropertyAnimatorListener;
import androidx.appcompat.widget.ActionBarContextView;

public class AppCompatDelegateImpl$6 implements Runnable	// class@000563 from classes.dex
{
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$6(AppCompatDelegateImpl p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       AppCompatDelegateImpl$6 tthis$0 = this.this$0;
       tthis$0.mActionModePopup.showAtLocation(tthis$0.mActionModeView, 55, 0, 0);
       this.this$0.endOnGoingFadeAnimation();
       float f = 1.00f;
       if (this.this$0.shouldAnimateActionModeView()) {
          this.this$0.mActionModeView.setAlpha(0);
          tthis$0 = this.this$0;
          tthis$0.mFadeAnim = ViewCompat.animate(tthis$0.mActionModeView).alpha(f);
          this.this$0.mFadeAnim.setListener(new AppCompatDelegateImpl$6$1(this));
       }else {
          this.this$0.mActionModeView.setAlpha(f);
          this.this$0.mActionModeView.setVisibility(0);
       }
       return;
    }
}
