package androidx.activity.OnBackPressedDispatcherKt;
import androidx.activity.OnBackPressedDispatcher;
import androidx.lifecycle.LifecycleOwner;
import tb.g1a;
import androidx.activity.OnBackPressedCallback;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.activity.OnBackPressedDispatcherKt$addCallback$callback$1;

public final class OnBackPressedDispatcherKt	// class@000464 from classes.dex
{

    public static final OnBackPressedCallback addCallback(OnBackPressedDispatcher p0,LifecycleOwner p1,boolean p2,g1a p3){
       ckf.g(p0, "<this>");
       ckf.g(p3, "onBackPressed");
       OnBackPressedDispatcherKt$addCallback$callback$1 uoaddCallbac = new OnBackPressedDispatcherKt$addCallback$callback$1(p2, p3);
       if (p1 != null) {
          p0.addCallback(p1, uoaddCallbac);
       }else {
          p0.addCallback(uoaddCallbac);
       }
       return uoaddCallbac;
    }
    public static OnBackPressedCallback addCallback$default(OnBackPressedDispatcher p0,LifecycleOwner p1,boolean p2,g1a p3,int p4,Object p5){
       if ((p4 & 0x01)) {
          p1 = null;
       }
       if ((p4 & 0x02)) {
          p2 = true;
       }
       return OnBackPressedDispatcherKt.addCallback(p0, p1, p2, p3);
    }
}
