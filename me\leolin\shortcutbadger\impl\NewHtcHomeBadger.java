package me.leolin.shortcutbadger.impl.NewHtcHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.content.Intent;
import tb.ol2;
import me.leolin.shortcutbadger.ShortcutBadgeException;
import java.lang.StringBuilder;

public class NewHtcHomeBadger implements po1	// class@000763 from classes11.dex
{
    public static final String COUNT = "count";
    public static final String EXTRA_COMPONENT = "com.htc.launcher.extra.COMPONENT";
    public static final String EXTRA_COUNT = "com.htc.launcher.extra.COUNT";
    public static final String INTENT_SET_NOTIFICATION = "com.htc.launcher.action.SET_NOTIFICATION";
    public static final String INTENT_UPDATE_SHORTCUT = "com.htc.launcher.action.UPDATE_SHORTCUT";
    public static final String PACKAGENAME = "packagename";

    public void NewHtcHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"com.htc.launcher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       Intent intent = new Intent("com.htc.launcher.action.SET_NOTIFICATION");
       intent.putExtra("com.htc.launcher.extra.COMPONENT", p1.flattenToShortString());
       intent.putExtra("com.htc.launcher.extra.COUNT", p2);
       Intent intent1 = new Intent("com.htc.launcher.action.UPDATE_SHORTCUT");
       intent1.putExtra("packagename", p1.getPackageName());
       intent1.putExtra("count", p2);
       if (!ol2.a(p0, intent) && !ol2.a(p0, intent1)) {
          throw new ShortcutBadgeException("unable to resolve intent: "+intent1.toString());
       }
       p0.sendBroadcast(intent);
       p0.sendBroadcast(intent1);
       return;
    }
}
