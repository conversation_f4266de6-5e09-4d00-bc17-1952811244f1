package androidx.activity.result.ActivityResultRegistry$register$3;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.ActivityResultRegistry;
import java.lang.String;
import androidx.activity.result.contract.ActivityResultContract;
import java.lang.Object;
import androidx.core.app.ActivityOptionsCompat;
import java.util.Map;
import java.lang.Number;
import java.util.List;
import java.lang.StringBuilder;
import java.lang.IllegalStateException;

public final class ActivityResultRegistry$register$3 extends ActivityResultLauncher	// class@0004b9 from classes.dex
{
    public final ActivityResultContract $contract;
    public final String $key;
    public final ActivityResultRegistry this$0;

    public void ActivityResultRegistry$register$3(ActivityResultRegistry p0,String p1,ActivityResultContract p2){
       this.this$0 = p0;
       this.$key = p1;
       this.$contract = p2;
       super();
    }
    public ActivityResultContract getContract(){
       return this.$contract;
    }
    public void launch(Object p0,ActivityOptionsCompat p1){
       Object obj = ActivityResultRegistry.access$getKeyToRc$p(this.this$0).get(this.$key);
       ActivityResultRegistry$register$3 t$contract = this.$contract;
       if (obj == null) {
          throw new IllegalStateException("Attempting to launch an unregistered ActivityResultLauncher with contract "+t$contract+" and input "+p0+". You must ensure the ActivityResultLauncher is registered before calling launch\(\).".toString());
       }
       int i = obj.intValue();
       List list = ActivityResultRegistry.access$getLaunchedKeys$p(this.this$0);
       ActivityResultRegistry$register$3 t$key = this.$key;
       try{
          list.add(t$key);
          this.this$0.onLaunch(i, this.$contract, p0, p1);
          return;
       }catch(java.lang.Exception e4){
          ActivityResultRegistry.access$getLaunchedKeys$p(this.this$0).remove(this.$key);
          throw e4;
       }
    }
    public void unregister(){
       this.this$0.unregister$activity_release(this.$key);
    }
}
