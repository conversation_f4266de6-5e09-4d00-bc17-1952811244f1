package tb.a2l;
import tb.t2o;
import tb.u2g;
import tb.u2g$a;
import kotlin.LazyThreadSafetyMode;
import tb.y1l;
import tb.d1a;
import tb.njg;
import kotlin.a;
import java.lang.Object;
import tb.fyk;
import tb.hic;
import tb.oyk;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ckf;
import tb.i2g;
import tb.h2g;
import tb.gyk;
import com.taobao.kmp.nexus.arch.openArch.definition.OpenArchBizCode;
import tb.a2l$a;
import java.lang.Enum;
import tb.qyk;
import tb.pyk;
import tb.syk;
import tb.vyk;
import tb.tyk;
import tb.uyk;
import tb.ryk;
import java.lang.Boolean;
import com.taobao.uniinfra_kmp.common_utils.app.a;
import kotlin.random.Random;
import kotlin.random.Random$Default;
import tb.j2g;
import tb.ewe;
import tb.ewe$a;
import java.util.Map;
import tb.j2g$a;
import tb.uhc;
import tb.vhc;
import tb.z1l;
import tb.g1a;
import tb.iic;
import tb.t2g;
import tb.m2g;
import java.util.List;
import java.lang.Iterable;
import java.lang.CharSequence;
import tb.i04;
import tb.pus;
import java.lang.StringBuilder;
import tb.xz3;

public final class a2l	// class@001ade from classes8.dex
{
    public static IpChange $ipChange;
    public static final a2l INSTANCE;
    public static final u2g a;
    public static boolean b;
    public static final njg c;

    static {
       t2o.a(0x3f100002);
       a2l uoa2l = new a2l();
       a2l.INSTANCE = uoa2l;
       a2l.a = u2g.Companion.a();
       a2l.c = a.a(LazyThreadSafetyMode.PUBLICATION, new y1l());
       uoa2l.c();
    }
    public void a2l(){
       super();
    }
    public static oyk a(fyk p0,hic p1){
       return a2l.e(p0, p1);
    }
    public static boolean b(){
       return a2l.k();
    }
    public static final oyk e(fyk p0,hic p1){
       OpenArchBizCode openArchBizC;
       ryk oryk;
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("4c1530e6", objArray);
       }else {
          ckf.g(p1, "it");
          i2g oi2g = p0.l();
          if (!oi2g instanceof gyk) {
             oi2g = null;
          }
          if (oi2g == null || (openArchBizC = oi2g.e()) == null) {
             openArchBizC = OpenArchBizCode.Undefined;
          }
          switch (a2l$a.$EnumSwitchMapping$0[openArchBizC.ordinal()]){
              case 1:
                oryk = new ryk(p0);
                break;
              case 2:
                oryk = new uyk(p0);
                break;
              case 3:
                oryk = new tyk(p0);
                break;
              case 4:
                oryk = new vyk(p0);
                break;
              case 5:
                oryk = new syk(p0);
                break;
              case 6:
                oryk = new pyk(p0);
                break;
              case 7:
                oryk = new qyk(p0);
                break;
              case 8:
                oryk = new qyk(p0);
                break;
              default:
                oryk = new oyk(p0);
          }
          return oryk;
       }
    }
    public static final boolean k(){
       IpChange $ipChange = a2l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a.g();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("dd1ef002", objArray).booleanValue();
    }
    public final void c(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("157e62e0", objArray);
          return;
       }else {
          int i2 = 100;
          i = Random.Default.nextInt(i, i2);
          Object obj = j2g.Companion.a().get(ewe.Companion.n().x());
          if (!obj instanceof uhc) {
             obj = null;
          }
          if (obj != null) {
             i2 = obj.o(vhc.a(obj), "LogServiceSamplingRate", i2);
          }
          if (i <= i2) {
             a2l.b = i1;
          }
          return;
       }
    }
    public final oyk d(fyk p0){
       String str1;
       oyk ooyk1;
       List list;
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("aa2769d0", objArray);
       }else {
          ckf.g(p0, "context");
          oyk ooyk = p0.e(new z1l(p0));
          String str = u2g.e(a2l.a, ooyk, p0, false, 4, null).a();
          ooyk.v(str);
          p0.n(str);
          if ((p0 = p0.m()) != null && ((str1 = p0.k()) != null && ((ooyk1 = a2l.INSTANCE.g(str1)) != null && (list = ooyk1.e()) != null))) {
             list.add(str);
          }
          ooyk.y();
          return ooyk;
       }
    }
    public final t2g f(oyk p0){
       IpChange $ipChange = a2l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a2l.a.c(p0.k());
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("d99ffad", objArray);
    }
    public final oyk g(String p0){
       t2g ot2g;
       oyk ooyk;
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f2cd5ce2", objArray);
       }else if((ot2g = a2l.a.c(p0)) != null){
          ooyk = ot2g.b();
       }else {
          ooyk = null;
       }
       return ooyk;
    }
    public final boolean h(){
       IpChange $ipChange = a2l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a2l.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("847e1cc0", objArray).booleanValue();
    }
    public final void i(oyk p0){
       t2g ot2g;
       int i = 0;
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b53adccf", objArray);
          return;
       }else {
          ckf.g(p0, "entity");
          if ((ot2g = this.f(p0)) != null) {
             ot2g.c(i);
          }
          return;
       }
    }
    public final boolean j(){
       IpChange $ipChange = a2l.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a2l.c.getValue().booleanValue();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("20df622f", objArray).booleanValue();
    }
    public final void l(List p0,String p1,String p2,boolean p3){
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,new Boolean(p3)};
          $ipChange.ipc$dispatch("adaa4058", objArray);
          return;
       }else {
          String str = i04.j0(p0, " ", null, null, 0, null, null, 62, null);
          if (p3) {
             pus.INSTANCE.f(p1, p2, str);
          }else {
             pus.INSTANCE.k(p1, p2, str);
          }
          return;
       }
    }
    public final void m(List p0,String p1,String p2){
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("b98e9e4f", objArray);
          return;
       }else {
          this.l(p0, p1, p2, 1);
          return;
       }
    }
    public final void n(Object p0,String p1,oyk p2){
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("931a98eb", objArray);
          return;
       }else {
          ckf.g(p0, "from");
          ckf.g(p1, "msg");
          if (!this.j()) {
             return;
          }
          p0 = xz3.e("|"+p1+"|from:"+p0);
          p1 = (p2 != null)? p2.k(): null;
          this.m(p0, "EntityInfo", p1);
          return;
       }
    }
    public final void o(Object p0,String p1){
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("6ff156a5", objArray);
          return;
       }else {
          ckf.g(p0, "from");
          ckf.g(p1, "msg");
          if (!this.j()) {
             return;
          }
          this.m(xz3.e("|"+p1+"|from:"+p0), "IntentPerform", "");
          return;
       }
    }
    public final void p(Object p0,String p1,oyk p2){
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("22507ef2", objArray);
          return;
       }else {
          ckf.g(p0, "from");
          ckf.g(p1, "msg");
          if (!this.j()) {
             return;
          }
          p0 = xz3.e("|"+p1+"|from:"+p0);
          p1 = (p2 != null)? p2.k(): null;
          this.m(p0, "MemoryIssue", p1);
          return;
       }
    }
    public final void q(oyk p0){
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6b419a6b", objArray);
          return;
       }else {
          ckf.g(p0, "entity");
          this.i(p0);
          this.r(p0);
          p0.f();
          return;
       }
    }
    public final void r(oyk p0){
       t2g ot2g;
       IpChange $ipChange = a2l.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("77767c68", objArray);
          return;
       }else {
          ckf.g(p0, "entity");
          if ((ot2g = this.f(p0)) != null) {
             a2l.a.f(ot2g);
          }
          return;
       }
    }
}
