package tb.a7j;
import tb.nb5;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.dinamicx.DXRuntimeContext;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;

public class a7j extends nb5	// class@00184b from classes5.dex
{
    public static IpChange $ipChange;
    public static final long MTOP_RETMSGDATA_PARSER;

    static {
       t2o.a(0x26b00098);
    }
    public void a7j(){
       super();
    }
    public static Object ipc$super(a7j p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/order/core/dinamicX/parser/MtopRetMsgDataParser");
    }
    public Object evalWithArgs(Object[] p0,DXRuntimeContext p1){
       String[] stringArray;
       int i = 0;
       IpChange $ipChange = a7j.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("ccd8bd96", objArray);
       }else {
          object oobject = null;
          if (p0 != null && p0.length) {
             object oobject1 = p0[i];
             if (!oobject1 instanceof String) {
                return oobject;
             }else if(!TextUtils.isEmpty(oobject1) && ((stringArray = oobject1.split("::")) != null && stringArray.length > 1)){
                oobject = stringArray[(stringArray.length - 1)];
             }
          }
          return oobject;
       }
    }
}
