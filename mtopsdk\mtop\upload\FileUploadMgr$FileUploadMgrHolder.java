package mtopsdk.mtop.upload.FileUploadMgr$FileUploadMgrHolder;
import tb.t2o;
import mtopsdk.mtop.upload.FileUploadMgr;
import mtopsdk.mtop.upload.FileUploadMgr$1;
import java.lang.Object;

public class FileUploadMgr$FileUploadMgrHolder	// class@000804 from classes11.dex
{
    public static final FileUploadMgr instance;

    static {
       t2o.a(0x25900009);
       FileUploadMgr$FileUploadMgrHolder.instance = new FileUploadMgr(null);
    }
    private void FileUploadMgr$FileUploadMgrHolder(){
       super();
    }
}
