package androidx.activity.ImmLeaksCleaner$ValidCleaner;
import androidx.activity.ImmLeaksCleaner$Cleaner;
import java.lang.reflect.Field;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.a07;
import android.view.inputmethod.InputMethodManager;
import android.view.View;

public final class ImmLeaksCleaner$ValidCleaner extends ImmLeaksCleaner$Cleaner	// class@000453 from classes.dex
{
    private final Field hField;
    private final Field nextServedViewField;
    private final Field servedViewField;

    public void ImmLeaksCleaner$ValidCleaner(Field p0,Field p1,Field p2){
       ckf.g(p0, "hField");
       ckf.g(p1, "servedViewField");
       ckf.g(p2, "nextServedViewField");
       super(null);
       this.hField = p0;
       this.servedViewField = p1;
       this.nextServedViewField = p2;
    }
    public boolean clearNextServedView(InputMethodManager p0){
       boolean b;
       String str = "<this>";
       try{
          ckf.g(p0, str);
          this.nextServedViewField.set(p0, null);
          b = true;
       }catch(java.lang.IllegalAccessException e0){
          b = false;
       }
       return b;
    }
    public Object getLock(InputMethodManager p0){
       String str = "<this>";
       try{
          ckf.g(p0, str);
          p0 = this.hField.get(p0);
       }catch(java.lang.IllegalAccessException e0){
          p0 = null;
       }
       return p0;
    }
    public View getServedView(InputMethodManager p0){
       ckf.g(p0, "<this>");
       try{
          int i = 0;
          View view = this.servedViewField.get(p0);
          return e0;
       }catch(java.lang.IllegalAccessException e0){
       }catch(java.lang.ClassCastException e0){
       }
    }
}
