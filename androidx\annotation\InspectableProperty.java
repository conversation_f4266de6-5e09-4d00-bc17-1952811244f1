package androidx.annotation.InspectableProperty;
import java.lang.annotation.Annotation;
import androidx.annotation.InspectableProperty$EnumEntry;
import androidx.annotation.InspectableProperty$FlagEntry;
import java.lang.String;
import androidx.annotation.InspectableProperty$ValueType;

public interface abstract InspectableProperty implements Annotation	// class@000505 from classes.dex
{

    int attributeId();
    InspectableProperty$EnumEntry[] enumMapping();
    InspectableProperty$FlagEntry[] flagMapping();
    boolean hasAttributeId();
    String name();
    InspectableProperty$ValueType valueType();
}
