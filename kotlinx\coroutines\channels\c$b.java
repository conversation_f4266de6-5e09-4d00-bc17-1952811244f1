package kotlinx.coroutines.channels.c$b;
import java.lang.String;
import tb.o3r;
import java.lang.Object;

public final class c$b	// class@000517 from classes11.dex
{
    public static final c$b $$INSTANCE;
    public static final int BUFFERED;
    public static final int CONFLATED;
    public static final String DEFAULT_BUFFER_PROPERTY_NAME;
    public static final int OPTIONAL_CHANNEL;
    public static final int RENDEZVOUS;
    public static final int UNLIMITED;
    public static final int b;

    static {
       c$b.$$INSTANCE = new c$b();
       c$b.b = o3r.b("kotlinx.coroutines.channels.defaultBuffer", 64, 1, 0x7ffffffe);
    }
    public void c$b(){
       super();
    }
    public final int a(){
       return c$b.b;
    }
}
