package tb.ae5$a;
import tb.qub;
import tb.t2o;
import java.lang.Object;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ae5;

public class ae5$a implements qub	// class@001b27 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1f1000be);
       t2o.a(0x1c500477);
    }
    public void ae5$a(){
       super();
    }
    public DXWidgetNode build(Object p0){
       IpChange $ipChange = ae5$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new ae5();
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("966917b0", objArray);
    }
}
