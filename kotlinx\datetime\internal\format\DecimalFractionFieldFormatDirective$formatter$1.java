package kotlinx.datetime.internal.format.DecimalFractionFieldFormatDirective$formatter$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.Object;
import tb.lb7;
import java.lang.Class;
import java.lang.String;
import tb.fg20;
import kotlin.jvm.internal.CallableReference;

public final class DecimalFractionFieldFormatDirective$formatter$1 extends FunctionReferenceImpl implements g1a	// class@0006f4 from classes11.dex
{

    public void DecimalFractionFieldFormatDirective$formatter$1(Object p0){
       super(1, p0, lb7.class, "getterNotNull", "getterNotNull\(Ljava/lang/Object;\)Ljava/lang/Object;", 0);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final fg20 invoke(Object p0){
       return this.receiver.b(p0);
    }
}
