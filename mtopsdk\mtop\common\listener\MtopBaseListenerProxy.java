package mtopsdk.mtop.common.listener.MtopBaseListenerProxy;
import mtopsdk.mtop.common.DefaultMtopCallback;
import tb.t2o;
import mtopsdk.mtop.common.MtopListener;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import mtopsdk.mtop.common.MtopProgressEvent;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.mtop.common.MtopCallback$MtopProgressListener;
import mtopsdk.mtop.common.MtopFinishEvent;
import mtopsdk.mtop.domain.MtopResponse;
import mtopsdk.common.util.TBSdkLog;
import mtopsdk.mtop.common.MtopCallback$MtopFinishListener;
import mtopsdk.mtop.common.MtopHeaderEvent;
import mtopsdk.mtop.common.MtopCallback$MtopHeaderListener;

public class MtopBaseListenerProxy extends DefaultMtopCallback	// class@0007ae from classes11.dex
{
    public boolean isCached;
    public MtopListener listener;
    public Object reqContext;
    public MtopResponse response;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x253000c2);
    }
    public void MtopBaseListenerProxy(MtopListener p0){
       super();
       this.response = null;
       this.reqContext = null;
       this.isCached = false;
       this.listener = p0;
    }
    public static Object ipc$super(MtopBaseListenerProxy p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/common/listener/MtopBaseListenerProxy");
    }
    public void onDataReceived(MtopProgressEvent p0,Object p1){
       IpChange $ipChange = MtopBaseListenerProxy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4ea8f321", objArray);
          return;
       }else {
          MtopBaseListenerProxy tlistener = this.listener;
          if (tlistener instanceof MtopCallback$MtopProgressListener) {
             tlistener.onDataReceived(p0, p1);
          }
          return;
       }
    }
    public void onFinished(MtopFinishEvent p0,Object p1){
       MtopBaseListenerProxy tresponse;
       IpChange $ipChange = MtopBaseListenerProxy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("732e17e0", objArray);
          return;
       }else if(p0 != null && p0.getMtopResponse() != null){
          this.response = p0.getMtopResponse();
          this.reqContext = p1;
       }
       _monitor_enter(this);
       try{
          this.notifyAll();
       }catch(java.lang.Exception e0){
          TBSdkLog.e("mtopsdk.MtopListenerProxy", "[onFinished] notify error");
       }
       _monitor_exit(this);
       if (this.listener instanceof MtopCallback$MtopFinishListener) {
          if (this.isCached != null && ((tresponse = this.response) == null && tresponse.isApiSuccess())) {
             return;
          }else {
             this.listener.onFinished(p0, p1);
          }
       }
       return;
    }
    public void onHeader(MtopHeaderEvent p0,Object p1){
       IpChange $ipChange = MtopBaseListenerProxy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("34d28e9f", objArray);
          return;
       }else {
          MtopBaseListenerProxy tlistener = this.listener;
          if (tlistener instanceof MtopCallback$MtopHeaderListener) {
             tlistener.onHeader(p0, p1);
          }
          return;
       }
    }
}
