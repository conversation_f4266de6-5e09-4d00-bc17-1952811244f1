package tb.a2s$a$a$a;
import tb.x1s$c;
import tb.a2s$a$a;
import java.lang.Object;
import android.content.DialogInterface;
import java.lang.CharSequence;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a2s$a;
import android.view.View;

public class a2s$a$a$a implements x1s$c	// class@001825 from classes5.dex
{
    public final a2s$a$a a;
    public static IpChange $ipChange;

    public void a2s$a$a$a(a2s$a$a p0){
       super();
       this.a = p0;
    }
    public void onClick(DialogInterface p0,CharSequence p1){
       IpChange $ipChange = a2s$a$a$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("bdadd41a", objArray);
          return;
       }else {
          a2s$a$a$a ta = this.a;
          a2s$a$a b = ta.b;
          b.e(ta.a, a2s$a.b(b));
          return;
       }
    }
}
