package tb.a1g;
import tb.t2o;
import tb.a1g$a;
import tb.a07;
import java.lang.Object;
import java.util.List;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public final class a1g	// class@001ad2 from classes8.dex
{
    public List a;
    public static IpChange $ipChange;
    public static final a1g$a Companion;

    static {
       t2o.a(0x3ee0012a);
       a1g.Companion = new a1g$a(null);
    }
    public void a1g(){
       super();
    }
    public final List a(){
       IpChange $ipChange = a1g.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("bc1c54e", objArray);
    }
    public final void b(List p0){
       IpChange $ipChange = a1g.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("90fd533e", objArray);
          return;
       }else {
          this.a = p0;
          return;
       }
    }
}
