package kotlinx.coroutines.channels.BufferOverflow;
import java.lang.Enum;
import java.lang.String;
import tb.fg8;
import kotlin.enums.a;
import java.lang.Class;
import java.lang.Object;

public final class BufferOverflow extends Enum	// class@0004be from classes11.dex
{
    private static final fg8 $ENTRIES;
    private static final BufferOverflow[] $VALUES;
    public static final BufferOverflow DROP_LATEST;
    public static final BufferOverflow DROP_OLDEST;
    public static final BufferOverflow SUSPEND;

    private static final BufferOverflow[] $values(){
       BufferOverflow[] uBufferOverf = new BufferOverflow[]{BufferOverflow.SUSPEND,BufferOverflow.DROP_OLDEST,BufferOverflow.DROP_LATEST};
       return uBufferOverf;
    }
    static {
       BufferOverflow.SUSPEND = new BufferOverflow("SUSPEND", 0);
       BufferOverflow.DROP_OLDEST = new BufferOverflow("DROP_OLDEST", 1);
       BufferOverflow.DROP_LATEST = new BufferOverflow("DROP_LATEST", 2);
       BufferOverflow[] uBufferOverf = BufferOverflow.$values();
       BufferOverflow.$VALUES = uBufferOverf;
       BufferOverflow.$ENTRIES = a.a(uBufferOverf);
    }
    private void BufferOverflow(String p0,int p1){
       super(p0, p1);
    }
    public static fg8 getEntries(){
       return BufferOverflow.$ENTRIES;
    }
    public static BufferOverflow valueOf(String p0){
       return Enum.valueOf(BufferOverflow.class, p0);
    }
    public static BufferOverflow[] values(){
       return BufferOverflow.$VALUES.clone();
    }
}
