package androidx.activity.result.ActivityResult$Companion;
import java.lang.Object;
import tb.a07;
import java.lang.String;

public final class ActivityResult$Companion	// class@0004a9 from classes.dex
{

    private void ActivityResult$Companion(){
       super();
    }
    public void ActivityResult$Companion(a07 p0){
       super();
    }
    public static void getCREATOR$annotations(){
    }
    public final String resultCodeToString(int p0){
       String str;
       if (p0 != -1) {
          str = (p0)? String.valueOf(p0): "RESULT_CANCELED";
       }else {
          str = "RESULT_OK";
       }
       return str;
    }
}
