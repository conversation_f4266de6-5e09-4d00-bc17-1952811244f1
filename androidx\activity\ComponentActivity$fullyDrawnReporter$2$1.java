package androidx.activity.ComponentActivity$fullyDrawnReporter$2$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.ComponentActivity;
import java.lang.Object;
import tb.xhv;

public final class ComponentActivity$fullyDrawnReporter$2$1 extends Lambda implements d1a	// class@00043d from classes.dex
{
    public final ComponentActivity this$0;

    public void ComponentActivity$fullyDrawnReporter$2$1(ComponentActivity p0){
       this.this$0 = p0;
       super(0);
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       this.this$0.reportFullyDrawn();
    }
}
