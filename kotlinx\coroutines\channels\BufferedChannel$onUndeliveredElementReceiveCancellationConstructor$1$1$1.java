package kotlinx.coroutines.channels.BufferedChannel$onUndeliveredElementReceiveCancellationConstructor$1$1$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import kotlinx.coroutines.channels.BufferedChannel;
import tb.k9p;
import java.lang.Throwable;
import tb.xhv;
import tb.u1r;
import kotlinx.coroutines.channels.BufferedChannelKt;
import kotlin.coroutines.d;
import kotlinx.coroutines.internal.OnUndeliveredElementKt;

public final class BufferedChannel$onUndeliveredElementReceiveCancellationConstructor$1$1$1 extends Lambda implements g1a	// class@0004c9 from classes11.dex
{
    public final Object $element;
    public final k9p $select;
    public final BufferedChannel this$0;

    public void BufferedChannel$onUndeliveredElementReceiveCancellationConstructor$1$1$1(Object p0,BufferedChannel p1,k9p p2){
       this.$element = p0;
       this.this$0 = p1;
       this.$select = p2;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(Throwable p0){
       if (this.$element != BufferedChannelKt.z()) {
          OnUndeliveredElementKt.b(this.this$0.b, this.$element, this.$select.getContext());
       }
       return;
    }
}
