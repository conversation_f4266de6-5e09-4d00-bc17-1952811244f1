package tb.a5n;
import tb.t2o;
import android.view.View;
import java.lang.Object;
import android.app.Application;
import tb.p91;
import android.content.Context;
import android.view.ViewConfiguration;
import tb.pr9;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.Integer;
import java.lang.String;
import android.view.ViewGroup;
import android.widget.SeekBar;
import java.lang.Float;
import android.view.MotionEvent;
import tb.ir9;
import tb.ied;
import java.lang.Math;

public class a5n	// class@0016b9 from classes6.dex
{
    public float a;
    public float b;
    public float c;
    public float d;
    public boolean e;
    public boolean f;
    public int g;
    public final int h;
    public final float i;
    public final View j;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1de002b9);
    }
    public void a5n(View p0){
       super();
       this.g = -1;
       this.h = ViewConfiguration.get(p91.a()).getScaledTouchSlop();
       this.i = (float)pr9.c(p91.a(), 40);
       this.j = p0;
    }
    public final boolean a(View p0,boolean p1,int p2,int p3,int p4){
       View childAt;
       int i5;
       int i6;
       int i7;
       object oobject = p0;
       boolean b = p1;
       int i = p2;
       int i1 = p3;
       int i2 = p4;
       int i3 = 1;
       IpChange $ipChange = a5n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,oobject,new Boolean(b),new Integer(i),new Integer(i1),new Integer(i2)};
          return $ipChange.ipc$dispatch("d4259e1", objArray).booleanValue();
       }else if(oobject instanceof ViewGroup){
          object oobject1 = oobject;
          int scrollX = p0.getScrollX();
          int scrollY = p0.getScrollY();
          int i4 = oobject1.getChildCount() - i3;
          while (true) {
             if (i4 >= 0) {
                if ((childAt = oobject1.getChildAt(i4)) != null && (i6 = i1 + scrollX) >= childAt.getLeft()) {
                   float f = (float)childAt.getRight() * childAt.getScaleX();
                   if (((float)i6 - f) < 0 && ((i7 = i2 + scrollY) >= childAt.getTop() && i7 < childAt.getBottom())) {
                      i6 = i6 - childAt.getLeft();
                      float f1 = (float)i6 / childAt.getScaleX();
                      int i8 = (int)f1;
                      int i9 = i7 - childAt.getTop();
                      i5 = i4;
                      if (this.a(childAt, true, p2, i8, i9)) {
                         return i3;
                      }else {
                      label_00ae :
                         i4 = i5 - 1;
                      }
                   }
                }
                i5 = i4;
                goto label_00ae ;
             }
          }
       }else if(oobject instanceof SeekBar){
          return i3;
       }
       if (!b || !oobject.canScrollHorizontally((- i))) {
          i3 = false;
       }
       return i3;
    }
    public final void b(float p0){
       IpChange $ipChange = a5n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Float(p0)};
          $ipChange.ipc$dispatch("5c3cc5d3", objArray);
          return;
       }else if((this.i - (- p0)) > 0){
          this.c();
       }
       return;
    }
    public void c(){
       throw null;
    }
    public boolean d(MotionEvent p0){
       int actionMasked;
       a5n tg;
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = a5n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("e7b587fe", objArray).booleanValue();
       }else if(actionMasked = p0.getActionMasked()){
          int i2 = -1;
          if (actionMasked != i) {
             if (actionMasked != i1) {
                if (actionMasked != 3) {
                   if (actionMasked == 6) {
                      this.e(p0);
                   }
                }
             }else if(this.g(this.j)){
                return 0;
             }else if(this.f != null){
                return 0;
             }else if(this.e != null){
                return i;
             }else if((tg = this.g) == i2){
                ir9.b("PullLeftHelper", "Got ACTION_MOVE event but don\'t have an active pointer id.");
                return 0;
             }else if((i1 = p0.findPointerIndex(tg)) < 0){
                return 0;
             }else {
                float rawX = p0.getRawX();
                float rawY = p0.getRawY();
                float x = p0.getX(i1);
                float y = p0.getY(i1);
                float f = x - this.a;
                if ((0 - f) && this.a(this.j, false, (int)f, (int)x, (int)y)) {
                   this.a = x;
                   this.f = i;
                   return 0;
                }else {
                   this.h(rawX, rawY);
                }
             }
          }
          this.e = false;
          this.f = false;
          this.g = i2;
       }else if(this.g(this.j)){
          return 0;
       }else {
          a5n tj = this.j;
          if (tj instanceof ViewGroup) {
             tj.requestDisallowInterceptTouchEvent(0);
          }
          i = p0.getPointerId(0);
          this.g = i;
          this.e = false;
          this.f = false;
          if (p0.findPointerIndex(i) < 0) {
             return 0;
          }else {
             this.c = p0.getRawX();
             this.d = p0.getRawY();
             this.a = p0.getX();
             p0.getY();
          }
       }
       return this.e;
    }
    public final void e(MotionEvent p0){
       int i = 1;
       IpChange $ipChange = a5n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f24b537f", objArray);
          return;
       }else {
          int actionIndex = p0.getActionIndex();
          if (p0.getPointerId(actionIndex) == this.g) {
             if (actionIndex) {
                i = 0;
             }
             this.g = p0.getPointerId(i);
          }
          return;
       }
    }
    public boolean f(MotionEvent p0){
       int actionMasked;
       int i = 1;
       int i1 = 0;
       int i2 = 2;
       IpChange $ipChange = a5n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i2];
          objArray[i1] = this;
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("a9b14c3a", objArray).booleanValue();
       }else if(actionMasked = p0.getActionMasked()){
          if (actionMasked != i) {
             if (actionMasked != i2) {
                if (actionMasked != 3) {
                   if (actionMasked != 5) {
                      if (actionMasked == 6) {
                         this.e(p0);
                      }
                   }else if((i2 = p0.getActionIndex()) < 0){
                      ir9.b("PullLeftHelper", "Got ACTION_POINTER_DOWN event but have an invalid action index.");
                      return i1;
                   }else {
                      this.g = p0.getPointerId(i2);
                   }
                }else {
                   return i1;
                }
             }else if(p0.findPointerIndex(this.g) < 0){
                ir9.b("PullLeftHelper", "Got ACTION_MOVE event but have an invalid active pointer id.");
                return i1;
             }else {
                this.h(p0.getRawX(), p0.getRawY());
             }
          }else if(p0.findPointerIndex(this.g) < 0){
             ir9.b("PullLeftHelper", "Got ACTION_UP event but don\'t have an active pointer id.");
             return i1;
          }else if(this.e != null){
             this.e = i1;
             this.b(((p0.getRawX() - this.b) * 1.00f));
          }
          this.g = -1;
          return i1;
       }else {
          this.g = p0.getPointerId(i1);
          this.e = i1;
       }
       return i;
    }
    public final boolean g(View p0){
       View childAt;
       IpChange $ipChange = a5n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6a1b4452", objArray).booleanValue();
       }else if(p0 instanceof ied && p0.canPullIntercept("HorizontalDrag")){
          return 1;
       }else if(p0 instanceof ViewGroup){
          int childCount = p0.getChildCount();
          int i = 0;
          while (i < childCount) {
             if ((childAt = p0.getChildAt(i)) != null) {
                if (this.g(childAt)) {
                   return 1;
                }else if(childAt instanceof ied){
                }
             }
             i = i + 1;
          }
       }
       return 0;
    }
    public final void h(float p0,float p1){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = a5n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Float(p0),new Float(p1)};
          $ipChange.ipc$dispatch("8dcd19d8", objArray);
          return;
       }else if(this.e != null){
          return;
       }else {
          p0 = p0 - this.c;
          if ((Math.abs((p1 - this.d)) - (Math.abs(p0) * 0.50f)) > 0) {
             i1 = 1;
          }
          a5n th = this.h;
          if (((float)th - (- p0)) > 0 && i1) {
             this.b = this.c + (float)th;
             this.e = i;
          }
          return;
       }
    }
}
