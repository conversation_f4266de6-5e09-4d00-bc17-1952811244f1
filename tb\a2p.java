package tb.a2p;
import tb.t2o;
import tb.a2p$a;
import tb.a2p$b;
import com.taobao.search.searchdoor.sf.widgets.SearchDoorContext;
import tb.wnd;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.CharSequence;
import android.text.TextUtils;

public class a2p	// class@001749 from classes9.dex
{
    public static IpChange $ipChange;
    public static final wnd a;
    public static final wnd b;

    static {
       t2o.a(0x3320036c);
       a2p.a = new a2p$a();
       a2p.b = new a2p$b();
    }
    public static wnd a(SearchDoorContext p0){
       IpChange $ipChange = a2p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("35935dff", objArray);
       }else if(TextUtils.isEmpty(p0.d())){
          return a2p.a;
       }else {
          return a2p.b;
       }
    }
}
