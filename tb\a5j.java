package tb.a5j;
import java.lang.Runnable;
import com.taobao.tbpoplayer.filter.MtopGroupPreCheckManager;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.poplayer.trigger.a;
import com.taobao.tbpoplayer.filter.MtopGroupPreCheckManager$a;
import java.lang.Object;

public final class a5j implements Runnable	// class@001e9c from classes10.dex
{
    public final MtopGroupPreCheckManager a;
    public final String b;
    public final JSONObject c;
    public final a d;
    public final MtopGroupPreCheckManager$a e;

    public void a5j(MtopGroupPreCheckManager p0,String p1,JSONObject p2,a p3,MtopGroupPreCheckManager$a p4){
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
       this.d = p3;
       this.e = p4;
    }
    public final void run(){
       MtopGroupPreCheckManager.b(this.a, this.b, this.c, this.d, this.e);
    }
}
