package androidx.activity.Api34Impl;
import java.lang.Object;
import android.window.BackEvent;
import java.lang.String;
import tb.ckf;

public final class Api34Impl	// class@000430 from classes.dex
{
    public static final Api34Impl INSTANCE;

    static {
       Api34Impl.INSTANCE = new Api34Impl();
    }
    private void Api34Impl(){
       super();
    }
    public final BackEvent createOnBackEvent(float p0,float p1,float p2,int p3){
       return new BackEvent(p0, p1, p2, p3);
    }
    public final float progress(BackEvent p0){
       ckf.g(p0, "backEvent");
       return p0.getProgress();
    }
    public final int swipeEdge(BackEvent p0){
       ckf.g(p0, "backEvent");
       return p0.getSwipeEdge();
    }
    public final float touchX(BackEvent p0){
       ckf.g(p0, "backEvent");
       return p0.getTouchX();
    }
    public final float touchY(BackEvent p0){
       ckf.g(p0, "backEvent");
       return p0.getTouchY();
    }
}
