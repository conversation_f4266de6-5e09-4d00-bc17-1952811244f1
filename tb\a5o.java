package tb.a5o;
import tb.ybj;
import tb.fs1;
import java.lang.Class;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.String;
import java.lang.Object;
import tb.huo;
import tb.oh3;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.kk4;
import com.android.alibaba.ip.runtime.IpChange;
import tb.yto;
import java.lang.Throwable;
import tb.swn;
import java.lang.Boolean;
import java.lang.Float;
import java.util.ArrayList;
import tb.w3o;
import java.lang.Integer;
import tb.xv8;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.reflect.Type;
import tb.jjo;

public class a5o extends fs1 implements ybj	// class@001af5 from classes8.dex
{
    public final Class j;
    public final Map k;
    public final Map l;
    public static IpChange $ipChange;

    public void a5o(Class p0){
       super(1, 29);
       this.k = new ConcurrentHashMap();
       this.l = new ConcurrentHashMap();
       this.j = p0;
    }
    public static Object ipc$super(a5o p0,String p1,Object[] p2){
       if (p1.hashCode() == 0x7fbfee6b) {
          return super.o();
       }
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/rxm/produce/RequestMultiplexProducer");
    }
    public void I(kk4 p0){
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("ce179037", objArray);
          return;
       }else {
          this.Q(p0, new yto(8, 1));
          return;
       }
    }
    public void J(kk4 p0,Throwable p1){
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4a6a662d", objArray);
          return;
       }else {
          yto oyto = new yto(16, 1);
          oyto.e = p1;
          this.Q(p0, oyto);
          return;
       }
    }
    public void K(kk4 p0,boolean p1,swn p2){
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1),p2};
          $ipChange.ipc$dispatch("ef5213ed", objArray);
          return;
       }else {
          yto oyto = new yto(1, p1);
          oyto.c = p2;
          this.Q(p0, oyto);
          return;
       }
    }
    public void L(kk4 p0,float p1){
       int i = 0;
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Float(p1)};
          $ipChange.ipc$dispatch("7ff1797c", objArray);
          return;
       }else {
          yto oyto = new yto(4, i);
          oyto.d = p1;
          this.Q(p0, oyto);
          return;
       }
    }
    public final void P(ArrayList p0,kk4 p1){
       int i = 0;
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("cf34075e", objArray);
          return;
       }else {
          p0.add(p1);
          int i1 = p1.getContext().h();
          w3o context = this.R(p0).getContext();
          if (i1 > context.h()) {
             context.q(i1);
          }
          if (context.i() && !p1.getContext().j()) {
             context.c(i);
          }
          return;
       }
    }
    public final void Q(kk4 p0,yto p1){
       yto a;
       object oobject = this;
       object oobject1 = p0;
       object oobject2 = p1;
       int i = 4;
       int i1 = 3;
       int i2 = 1;
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = oobject;
          objArray[i2] = oobject1;
          objArray[2] = oobject2;
          $ipChange.ipc$dispatch("5dbb95f1", objArray);
          return;
       }else {
          w3o context = p0.getContext();
          ArrayList uArrayList = oobject.l.get(Integer.valueOf(context.d()));
          String str = context.e();
          if (uArrayList == null) {
             Object[] objArray1 = new Object[i];
             objArray1[0] = str;
             objArray1[i2] = Integer.valueOf(context.d());
             objArray1[2] = Integer.valueOf(context.f());
             objArray1[i1] = Integer.valueOf(oobject2.a);
             xv8.k("RxSysLog", "[RequestMultiplex] group has been removed from multiplex, but pipeline is still producing new result\(multiplex:%s, id:%d, pipeline:%d, type:%d\)", objArray1);
             return;
          }else {
             _monitor_enter(this);
             i1 = uArrayList.size();
             int i3 = 0;
             while (i3 < i1) {
                kk4 okk4 = uArrayList.get(i3);
                w3o context1 = okk4.getContext();
                if (okk4 != oobject1) {
                   context1.r(context);
                }
                int i4 = 16;
                if (!context1.j()) {
                   if ((a = oobject2.a) != i2) {
                      if (a != i) {
                         if (a != 8) {
                            if (a == i4) {
                               okk4.a(oobject2.e);
                            }
                         }else {
                            Object[] objArray2 = new Object[i2];
                            objArray2[0] = Integer.valueOf(context1.d());
                            xv8.c("RxSysLog", "[RequestMultiplex] ID=%d consumers of the group were not all cancelled, but pipeline dispatched cancellation result", objArray2);
                            okk4.b();
                         }
                      }else {
                         okk4.onProgressUpdate(oobject2.d);
                      }
                   }else {
                      okk4.c(oobject2.c, oobject2.b);
                   }
                }else if(oobject2.a == i4){
                   Object[] objArray3 = new Object[]{Integer.valueOf(context1.d()),oobject2.e};
                   xv8.f("RxSysLog", "[RequestMultiplex] ID=%d received error after cancellation, throwable=%s", objArray3);
                }
                okk4.b();
                i = 1;
                i3 = i3 + i;
                i = 4;
                i2 = 1;
             }
             if (oobject2.b != null) {
                if (!TextUtils.isEmpty(str)) {
                   oobject.k.remove(str);
                }
                oobject.l.remove(Integer.valueOf(context.d()));
             }
             _monitor_exit(this);
             return;
          }
       }
    }
    public final kk4 R(ArrayList p0){
       int i = 0;
       IpChange $ipChange = a5o.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.get(i);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("4e1b6a22", objArray);
    }
    public final boolean S(ArrayList p0){
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("a0ba45ab", objArray).booleanValue();
       }else {
          int i = 0;
          while (true) {
             if (i >= p0.size()) {
                return 1;
             }
             if (!p0.get(i).getContext().j()) {
                break ;
             }else {
                i = i + 1;
             }
          }
          return 0;
       }
    }
    public void T(w3o p0){
       ArrayList uArrayList;
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("41618f89", objArray);
          return;
       }else {
          String str = p0.e();
          if (!this.k.containsKey(str)) {
             return;
          }
          if ((uArrayList = this.l.get(Integer.valueOf(p0.f()))) == null) {
             return;
          }
          _monitor_enter(this);
          kk4 okk4 = this.R(uArrayList);
          uArrayList = (!okk4.getContext().i() && !this.S(uArrayList))? 0: 1;
          if (uArrayList) {
             this.k.remove(str);
             Object[] objArray1 = new Object[]{str};
             xv8.a("RxSysLog", "[RequestMultiplex] all of context in group[key:%s] were cancelled, remove it from KeyToGroupId", objArray1);
          }
          _monitor_exit(this);
          if (uArrayList) {
             okk4.getContext().c(1);
          }
          return;
       }
    }
    public boolean c(kk4 p0){
       Integer integer;
       ArrayList uArrayList;
       int i = 1;
       int i1 = 0;
       int i2 = 2;
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i2];
          objArray[i1] = this;
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("a3214a19", objArray).booleanValue();
       }else {
          w3o context = p0.getContext();
          String str = context.e();
          _monitor_enter(this);
          if ((integer = this.k.get(str)) == null) {
             integer = Integer.valueOf(context.d());
             this.k.put(str, integer);
             uArrayList = new ArrayList(i2);
             this.l.put(integer, uArrayList);
          }else {
             uArrayList = this.l.get(integer);
             i1 = true;
          }
          context.o(integer.intValue());
          context.n(this);
          this.P(uArrayList, p0);
          _monitor_exit(this);
          return i1;
       }
    }
    public Type l(){
       IpChange $ipChange = a5o.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.j;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1e37107f", objArray);
    }
    public Type n(){
       IpChange $ipChange = a5o.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.j;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f5f8712c", objArray);
    }
    public huo o(){
       IpChange $ipChange = a5o.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("7fbfee6b", objArray);
       }else if(jjo.b()){
          return super.o();
       }else {
          return null;
       }
    }
}
