package mtopsdk.mtop.upload.SegmentFileUploadTask;
import mtopsdk.mtop.upload.FileUploadTask;
import tb.t2o;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import mtopsdk.mtop.upload.DefaultFileUploadListenerWrapper;
import mtopsdk.mtop.upload.domain.UploadToken;
import mtopsdk.mtop.upload.service.UploadFileService;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.common.util.StringUtils;
import java.lang.Long;
import mtopsdk.common.util.TBSdkLog;
import mtopsdk.mtop.util.Result;
import mtopsdk.mtop.upload.domain.UploadResult;
import mtopsdk.mtop.upload.domain.FileBaseInfo;
import java.lang.Math;
import java.util.concurrent.atomic.AtomicLong;
import mtopsdk.mtop.upload.FileUploadMgr;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import java.util.concurrent.atomic.AtomicBoolean;

public class SegmentFileUploadTask extends FileUploadTask	// class@000807 from classes11.dex
{
    private long offset;
    private UploadToken token;
    private UploadFileService uploadService;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x2590000b);
    }
    public void SegmentFileUploadTask(UploadFileInfo p0,DefaultFileUploadListenerWrapper p1,UploadToken p2,long p3,UploadFileService p4){
       super(p0, p1);
       this.token = p2;
       this.offset = p3;
       this.uploadService = p4;
    }
    public static Object ipc$super(SegmentFileUploadTask p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/upload/SegmentFileUploadTask");
    }
    private void parseServerRT(String p0){
       IpChange $ipChange = SegmentFileUploadTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4c47c929", objArray);
          return;
       }else if(StringUtils.isNotBlank(p0)){
          try{
             this.listener.serverRT = Long.parseLong(p0);
          }catch(java.lang.NumberFormatException e0){
             TBSdkLog.w("mtopsdk.SegmentFileUploadTask", "[parseServerRT] invalid X-Server-Rt header. X-Server-Rt="+p0);
          }
       }
       return;
    }
    public void upload(){
       boolean b;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = SegmentFileUploadTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("f170aa08", objArray);
          return;
       }else if(this.isCancelled()){
          return;
       }else {
          int i2 = 0;
          while (true) {
             Result result = this.uploadService.fileUpload(this.token, this.offset, i2);
             if (this.isCancelled()) {
                return;
             }
             if (b = result.isSuccess()) {
                UploadResult model = result.getModel();
                SegmentFileUploadTask ttoken = this.token;
                this.notifyProgress(ttoken.uploadedLength.addAndGet(Math.min(ttoken.segmentSize, (ttoken.fileBaseInfo.fileSize - this.offset))), this.token.fileBaseInfo.fileSize);
                if (result.getModel().isFinish != null) {
                   this.listener.onFinish(this.fileInfo, model.location);
                   this.parseServerRT(model.serverRT);
                   this.commitUploadStatsRecord(result, this.token);
                   FileUploadMgr.getInstance().removeTask(this.fileInfo);
                   if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)) {
                      TBSdkLog.d("mtopsdk.SegmentFileUploadTask", "[upload]entire file upload succeed.");
                   }
                }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)){
                   TBSdkLog.d("mtopsdk.SegmentFileUploadTask", "[upload] segment upload succeed.offset="+this.offset);
                }
                return;
             }else if(!((long)i2 - this.token.retryCount) && this.listener.isFinished().compareAndSet(i, i1)){
                this.listener.onError(result.getErrType(), result.getErrCode(), result.getErrInfo());
                this.listener.cancel();
                this.commitUploadStatsRecord(result, this.token);
             }
             this.listener.countRetryTimes();
             if ("token_expired".equalsIgnoreCase(result.getErrCode())) {
                result = this.uploadService.getUploadToken(this.fileInfo);
                if (result.isSuccess()) {
                   this.token = result.getModel();
                }
             }
             if (!b) {
                i2 = i2 + i1;
                if (((long)i2 - this.token.retryCount) <= 0) {
                }
             }
             break ;
          }
          return;
       }
    }
}
