package androidx.activity.result.ActivityResultRegistry;
import androidx.activity.result.ActivityResultRegistry$Companion;
import tb.a07;
import java.lang.Object;
import java.util.LinkedHashMap;
import java.util.ArrayList;
import android.os.Bundle;
import java.lang.String;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Lifecycle$Event;
import java.util.Map;
import java.util.List;
import java.lang.Integer;
import android.content.Intent;
import androidx.activity.result.ActivityResultRegistry$CallbackAndContract;
import androidx.activity.result.ActivityResult;
import android.os.Parcelable;
import androidx.activity.result.ActivityResultRegistry$generateRandomNumber$1;
import tb.d1a;
import tb.sbp;
import tb.acp;
import java.util.Iterator;
import java.lang.Number;
import java.util.NoSuchElementException;
import tb.ckf;
import java.lang.Class;
import androidx.core.os.BundleCompat;
import androidx.core.app.ActivityOptionsCompat;
import java.util.Collection;
import android.os.BaseBundle;
import tb.kqu;
import java.util.Set;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.ActivityResultRegistry$register$3;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.Lifecycle$State;
import androidx.activity.result.ActivityResultRegistry$LifecycleContainer;
import tb.ic0;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.activity.result.ActivityResultRegistry$register$2;
import java.lang.StringBuilder;
import java.lang.IllegalStateException;

public abstract class ActivityResultRegistry	// class@0004ba from classes.dex
{
    private final Map keyToCallback;
    private final Map keyToLifecycleContainers;
    private final Map keyToRc;
    private final List launchedKeys;
    private final Map parsedPendingResults;
    private final Bundle pendingResults;
    private final Map rcToKey;
    private static final ActivityResultRegistry$Companion Companion;
    private static final int INITIAL_REQUEST_CODE_VALUE;
    private static final String KEY_COMPONENT_ACTIVITY_LAUNCHED_KEYS;
    private static final String KEY_COMPONENT_ACTIVITY_PENDING_RESULTS;
    private static final String KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS;
    private static final String KEY_COMPONENT_ACTIVITY_REGISTERED_RCS;
    private static final String LOG_TAG;

    static {
       ActivityResultRegistry.Companion = new ActivityResultRegistry$Companion(null);
    }
    public void ActivityResultRegistry(){
       super();
       this.rcToKey = new LinkedHashMap();
       this.keyToRc = new LinkedHashMap();
       this.keyToLifecycleContainers = new LinkedHashMap();
       this.launchedKeys = new ArrayList();
       this.keyToCallback = new LinkedHashMap();
       this.parsedPendingResults = new LinkedHashMap();
       this.pendingResults = new Bundle();
    }
    public static void a(ActivityResultRegistry p0,String p1,ActivityResultCallback p2,ActivityResultContract p3,LifecycleOwner p4,Lifecycle$Event p5){
       ActivityResultRegistry.register$lambda$1(p0, p1, p2, p3, p4, p5);
    }
    public static final Map access$getKeyToRc$p(ActivityResultRegistry p0){
       return p0.keyToRc;
    }
    public static final List access$getLaunchedKeys$p(ActivityResultRegistry p0){
       return p0.launchedKeys;
    }
    private final void bindRcKey(int p0,String p1){
       this.rcToKey.put(Integer.valueOf(p0), p1);
       this.keyToRc.put(p1, Integer.valueOf(p0));
    }
    private final void doDispatch(String p0,int p1,Intent p2,ActivityResultRegistry$CallbackAndContract p3){
       ActivityResultCallback callback = (p3 != null)? p3.getCallback(): null;
       if (callback != null && this.launchedKeys.contains(p0)) {
          p3.getCallback().onActivityResult(p3.getContract().parseResult(p1, p2));
          this.launchedKeys.remove(p0);
       }else {
          this.parsedPendingResults.remove(p0);
          this.pendingResults.putParcelable(p0, new ActivityResult(p1, p2));
       }
       return;
    }
    private final int generateRandomNumber(){
       Number number;
       Iterator iterator = acp.g(ActivityResultRegistry$generateRandomNumber$1.INSTANCE).iterator();
       while (true) {
          if (!iterator.hasNext()) {
             throw new NoSuchElementException("Sequence contains no element matching the predicate.");
          }
          number = iterator.next();
          if (!this.rcToKey.containsKey(Integer.valueOf(number.intValue()))) {
             break ;
          }
       }
       return number.intValue();
    }
    private static final void register$lambda$1(ActivityResultRegistry p0,String p1,ActivityResultCallback p2,ActivityResultContract p3,LifecycleOwner p4,Lifecycle$Event p5){
       ActivityResult parcelable;
       ckf.g(p0, "this$0");
       ckf.g(p1, "$key");
       ckf.g(p2, "$callback");
       ckf.g(p3, "$contract");
       ckf.g(p4, "<anonymous parameter 0>");
       ckf.g(p5, "event");
       if (Lifecycle$Event.ON_START == p5) {
          p0.keyToCallback.put(p1, new ActivityResultRegistry$CallbackAndContract(p2, p3));
          if (p0.parsedPendingResults.containsKey(p1)) {
             p0.parsedPendingResults.remove(p1);
             p2.onActivityResult(p0.parsedPendingResults.get(p1));
          }
          if ((parcelable = BundleCompat.getParcelable(p0.pendingResults, p1, ActivityResult.class)) != null) {
             p0.pendingResults.remove(p1);
             p2.onActivityResult(p3.parseResult(parcelable.getResultCode(), parcelable.getData()));
          }
       }else if(Lifecycle$Event.ON_STOP == p5){
          p0.keyToCallback.remove(p1);
       }else if(Lifecycle$Event.ON_DESTROY == p5){
          p0.unregister$activity_release(p1);
       }
       return;
    }
    private final void registerKey(String p0){
       if (this.keyToRc.get(p0) != null) {
          return;
       }
       this.bindRcKey(this.generateRandomNumber(), p0);
       return;
    }
    public final boolean dispatchResult(int p0,int p1,Intent p2){
       String str;
       if ((str = this.rcToKey.get(Integer.valueOf(p0))) == null) {
          return false;
       }
       this.doDispatch(str, p1, p2, this.keyToCallback.get(str));
       return true;
    }
    public final boolean dispatchResult(int p0,Object p1){
       String str;
       ActivityResultRegistry$CallbackAndContract uCallbackAnd;
       if ((str = this.rcToKey.get(Integer.valueOf(p0))) == null) {
          return false;
       }
       ActivityResultCallback callback = ((uCallbackAnd = this.keyToCallback.get(str)) != null)? uCallbackAnd.getCallback(): null;
       if (callback == null) {
          this.pendingResults.remove(str);
          this.parsedPendingResults.put(str, p1);
       }else {
          ActivityResultCallback callback1 = uCallbackAnd.getCallback();
          ckf.e(callback1, "null cannot be cast to non-null type androidx.activity.result.ActivityResultCallback<O of androidx.activity.result.ActivityResultRegistry.dispatchResult>");
          if (this.launchedKeys.remove(str)) {
             callback1.onActivityResult(p1);
          }
       }
       return true;
    }
    public abstract void onLaunch(int p0,ActivityResultContract p1,Object p2,ActivityOptionsCompat p3);
    public final void onRestoreInstanceState(Bundle p0){
       ArrayList stringArrayL;
       ArrayList stringArrayL1;
       if (p0 == null) {
          return;
       }
       ArrayList integerArray = p0.getIntegerArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_RCS");
       if ((stringArrayL = p0.getStringArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS")) != null && integerArray != null) {
          if ((stringArrayL1 = p0.getStringArrayList("KEY_COMPONENT_ACTIVITY_LAUNCHED_KEYS")) != null) {
             this.launchedKeys.addAll(stringArrayL1);
          }
          if ((p0 = p0.getBundle("KEY_COMPONENT_ACTIVITY_PENDING_RESULT")) != null) {
             this.pendingResults.putAll(p0);
          }
          int i = stringArrayL.size();
          for (int i1 = 0; i1 < i; i1 = i1 + 1) {
             String str = stringArrayL.get(i1);
             if (this.keyToRc.containsKey(str)) {
                Integer integer = this.keyToRc.remove(str);
                if (!this.pendingResults.containsKey(str)) {
                   kqu.d(this.rcToKey).remove(integer);
                }
             }
             Object obj = integerArray.get(i1);
             ckf.f(obj, "rcs[i]");
             Object obj1 = stringArrayL.get(i1);
             ckf.f(obj1, "keys[i]");
             this.bindRcKey(obj.intValue(), obj1);
          }
       }
       return;
    }
    public final void onSaveInstanceState(Bundle p0){
       ckf.g(p0, "outState");
       p0.putIntegerArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_RCS", new ArrayList(this.keyToRc.values()));
       p0.putStringArrayList("KEY_COMPONENT_ACTIVITY_REGISTERED_KEYS", new ArrayList(this.keyToRc.keySet()));
       p0.putStringArrayList("KEY_COMPONENT_ACTIVITY_LAUNCHED_KEYS", new ArrayList(this.launchedKeys));
       p0.putBundle("KEY_COMPONENT_ACTIVITY_PENDING_RESULT", new Bundle(this.pendingResults));
    }
    public final ActivityResultLauncher register(String p0,ActivityResultContract p1,ActivityResultCallback p2){
       ActivityResult parcelable;
       ckf.g(p0, "key");
       ckf.g(p1, "contract");
       ckf.g(p2, "callback");
       this.registerKey(p0);
       this.keyToCallback.put(p0, new ActivityResultRegistry$CallbackAndContract(p2, p1));
       if (this.parsedPendingResults.containsKey(p0)) {
          this.parsedPendingResults.remove(p0);
          p2.onActivityResult(this.parsedPendingResults.get(p0));
       }
       if ((parcelable = BundleCompat.getParcelable(this.pendingResults, p0, ActivityResult.class)) != null) {
          this.pendingResults.remove(p0);
          p2.onActivityResult(p1.parseResult(parcelable.getResultCode(), parcelable.getData()));
       }
       return new ActivityResultRegistry$register$3(this, p0, p1);
    }
    public final ActivityResultLauncher register(String p0,LifecycleOwner p1,ActivityResultContract p2,ActivityResultCallback p3){
       ActivityResultRegistry$LifecycleContainer lifecycleCon;
       ckf.g(p0, "key");
       ckf.g(p1, "lifecycleOwner");
       ckf.g(p2, "contract");
       ckf.g(p3, "callback");
       Lifecycle lifecycle = p1.getLifecycle();
       if (lifecycle.getCurrentState().isAtLeast(Lifecycle$State.STARTED)) {
          throw new IllegalStateException("LifecycleOwner "+p1+" is attempting to register while current state is "+lifecycle.getCurrentState()+". LifecycleOwners must call register before they are STARTED.".toString());
       }
       this.registerKey(p0);
       if ((lifecycleCon = this.keyToLifecycleContainers.get(p0)) == null) {
          lifecycleCon = new ActivityResultRegistry$LifecycleContainer(lifecycle);
       }
       lifecycleCon.addObserver(new ic0(this, p0, p3, p2));
       this.keyToLifecycleContainers.put(p0, lifecycleCon);
       return new ActivityResultRegistry$register$2(this, p0, p2);
    }
    public final void unregister$activity_release(String p0){
       Integer integer;
       ActivityResultRegistry$LifecycleContainer lifecycleCon;
       ckf.g(p0, "key");
       if (!this.launchedKeys.contains(p0) && (integer = this.keyToRc.remove(p0)) != null) {
          this.rcToKey.remove(integer);
       }
       this.keyToCallback.remove(p0);
       if (this.parsedPendingResults.containsKey(p0)) {
          StringBuilder str = "Dropping pending result for request "+p0+": "+this.parsedPendingResults.get(p0);
          this.parsedPendingResults.remove(p0);
       }
       if (this.pendingResults.containsKey(p0)) {
          "Dropping pending result for request "+p0+": "+BundleCompat.getParcelable(this.pendingResults, p0, ActivityResult.class);
          this.pendingResults.remove(p0);
       }
       if ((lifecycleCon = this.keyToLifecycleContainers.get(p0)) != null) {
          lifecycleCon.clearObservers();
          this.keyToLifecycleContainers.remove(p0);
       }
       return;
    }
}
