package kotlinx.datetime.format.OffsetFields$totalHoursAbs$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.r150;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class OffsetFields$totalHoursAbs$1 extends MutablePropertyReference1Impl	// class@0006eb from classes11.dex
{
    public static final OffsetFields$totalHoursAbs$1 INSTANCE;

    static {
       OffsetFields$totalHoursAbs$1.INSTANCE = new OffsetFields$totalHoursAbs$1();
    }
    public void OffsetFields$totalHoursAbs$1(){
       super(r150.class, "totalHoursAbs", "getTotalHoursAbs\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.z();
    }
    public void set(Object p0,Object p1){
       p0.e(p1);
    }
}
