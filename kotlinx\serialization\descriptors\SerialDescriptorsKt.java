package kotlinx.serialization.descriptors.SerialDescriptorsKt;
import java.lang.String;
import tb.g140;
import kotlinx.serialization.descriptors.a;
import java.lang.Object;
import tb.ckf;
import java.lang.CharSequence;
import tb.wsq;
import tb.k140;
import java.lang.IllegalArgumentException;
import tb.g1a;
import tb.f520;
import kotlinx.serialization.descriptors.SerialDescriptorImpl;
import kotlinx.serialization.descriptors.b$a;
import java.util.List;
import java.util.ArrayList;
import tb.ic1;
import tb.ob40;
import kotlinx.serialization.descriptors.SerialDescriptorsKt$buildSerialDescriptor$1;

public final class SerialDescriptorsKt	// class@00072e from classes11.dex
{

    public static final a a(String p0,g140 p1){
       ckf.g(p0, "serialName");
       ckf.g(p1, "kind");
       if (!wsq.a0(p0)) {
          return k140.a(p0, p1);
       }
       throw new IllegalArgumentException("Blank serial names are prohibited");
    }
    public static final a b(String p0,a[] p1,g1a p2){
       SerialDescriptorImpl p2;
       ckf.g(p0, "serialName");
       ckf.g(p1, "typeParameters");
       ckf.g(p2, "builderAction");
       if (wsq.a0(p0)) {
          throw new IllegalArgumentException("Blank serial names are prohibited");
       }
       f520 uof520 = new f520(p0);
       p2.invoke(uof520);
       p2 = new SerialDescriptorImpl(p0, b$a.INSTANCE, uof520.f().size(), ic1.i0(p1), uof520);
       return p2;
    }
    public static final a c(String p0,ob40 p1,a[] p2,g1a p3){
       SerialDescriptorImpl p3;
       ckf.g(p0, "serialName");
       ckf.g(p1, "kind");
       ckf.g(p2, "typeParameters");
       ckf.g(p3, "builder");
       if (wsq.a0(p0)) {
          throw new IllegalArgumentException("Blank serial names are prohibited");
       }
       if (ckf.b(p1, b$a.INSTANCE)) {
          throw new IllegalArgumentException("For StructureKind.CLASS please use \'buildClassSerialDescriptor\' instead");
       }
       f520 uof520 = new f520(p0);
       p3.invoke(uof520);
       p3 = new SerialDescriptorImpl(p0, p1, uof520.f().size(), ic1.i0(p2), uof520);
       return p3;
    }
    public static a d(String p0,ob40 p1,a[] p2,g1a p3,int p4,Object p5){
       SerialDescriptorsKt$buildSerialDescriptor$1 iNSTANCE;
       if ((p4 & 0x08)) {
          iNSTANCE = SerialDescriptorsKt$buildSerialDescriptor$1.INSTANCE;
       }
       return SerialDescriptorsKt.c(p0, p1, p2, iNSTANCE);
    }
}
