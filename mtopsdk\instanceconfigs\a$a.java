package mtopsdk.instanceconfigs.a$a;
import java.util.concurrent.Callable;
import mtopsdk.instanceconfigs.a;
import android.content.Context;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;

public class a$a implements Callable	// class@000781 from classes11.dex
{
    public final Context a;
    public final String b;
    public final String c;
    public final a d;
    public static IpChange $ipChange;

    public void a$a(a p0,Context p1,String p2,String p3){
       super();
       this.d = p0;
       this.a = p1;
       this.b = p2;
       this.c = p3;
    }
    public String a(){
       IpChange $ipChange = a$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a.a(this.d, this.a, this.b, this.c);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5fe77b5b", objArray);
    }
    public Object call(){
       return this.a();
    }
}
