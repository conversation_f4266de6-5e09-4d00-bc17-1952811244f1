package mtopsdk.mtop.common.listener.MtopCacheListenerProxy;
import mtopsdk.mtop.common.MtopCallback$MtopCacheListener;
import mtopsdk.mtop.common.listener.MtopBaseListenerProxy;
import tb.t2o;
import mtopsdk.mtop.common.MtopListener;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import mtopsdk.mtop.common.MtopCacheEvent;
import com.android.alibaba.ip.runtime.IpChange;

public class MtopCacheListenerProxy extends MtopBaseListenerProxy implements MtopCallback$MtopCacheListener	// class@0007af from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x253000c3);
       t2o.a(0x253000b5);
    }
    public void MtopCacheListenerProxy(MtopListener p0){
       super(p0);
    }
    public static Object ipc$super(MtopCacheListenerProxy p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/common/listener/MtopCacheListenerProxy");
    }
    public void onCached(MtopCacheEvent p0,Object p1){
       IpChange $ipChange = MtopCacheListenerProxy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("1c0c28d9", objArray);
          return;
       }else {
          MtopBaseListenerProxy tlistener = this.listener;
          if (tlistener instanceof MtopCallback$MtopCacheListener) {
             tlistener.onCached(p0, p1);
             this.isCached = true;
          }
          return;
       }
    }
}
