package kotlinx.coroutines.channels.BufferedChannel$onSend$1;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.channels.BufferedChannel;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import tb.k9p;
import tb.xhv;

public final class BufferedChannel$onSend$1 extends FunctionReferenceImpl implements w1a	// class@0004c7 from classes11.dex
{
    public static final BufferedChannel$onSend$1 INSTANCE;

    static {
       BufferedChannel$onSend$1.INSTANCE = new BufferedChannel$onSend$1();
    }
    public void BufferedChannel$onSend$1(){
       super(3, BufferedChannel.class, "registerSelectForSend", "registerSelectForSend\(Lkotlinx/coroutines/selects/SelectInstance;Ljava/lang/Object;\)V", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       this.invoke(p0, p1, p2);
       return xhv.INSTANCE;
    }
    public final void invoke(BufferedChannel p0,k9p p1,Object p2){
       p0.k1(p1, p2);
    }
}
