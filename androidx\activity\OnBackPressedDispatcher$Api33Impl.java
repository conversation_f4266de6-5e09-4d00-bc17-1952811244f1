package androidx.activity.OnBackPressedDispatcher$Api33Impl;
import java.lang.Object;
import tb.d1a;
import java.lang.String;
import tb.ckf;
import android.window.OnBackInvokedCallback;
import tb.lpk;
import android.window.OnBackInvokedDispatcher;

public final class OnBackPressedDispatcher$Api33Impl	// class@00045b from classes.dex
{
    public static final OnBackPressedDispatcher$Api33Impl INSTANCE;

    static {
       OnBackPressedDispatcher$Api33Impl.INSTANCE = new OnBackPressedDispatcher$Api33Impl();
    }
    private void OnBackPressedDispatcher$Api33Impl(){
       super();
    }
    public static void a(d1a p0){
       OnBackPressedDispatcher$Api33Impl.createOnBackInvokedCallback$lambda$0(p0);
    }
    private static final void createOnBackInvokedCallback$lambda$0(d1a p0){
       ckf.g(p0, "$onBackInvoked");
       p0.invoke();
    }
    public final OnBackInvokedCallback createOnBackInvokedCallback(d1a p0){
       ckf.g(p0, "onBackInvoked");
       return new lpk(p0);
    }
    public final void registerOnBackInvokedCallback(Object p0,int p1,Object p2){
       ckf.g(p0, "dispatcher");
       ckf.g(p2, "callback");
       p0.registerOnBackInvokedCallback(p1, p2);
    }
    public final void unregisterOnBackInvokedCallback(Object p0,Object p1){
       ckf.g(p0, "dispatcher");
       ckf.g(p1, "callback");
       p0.unregisterOnBackInvokedCallback(p1);
    }
}
