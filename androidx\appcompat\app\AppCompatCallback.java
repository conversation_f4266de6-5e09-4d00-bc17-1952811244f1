package androidx.appcompat.app.AppCompatCallback;
import androidx.appcompat.view.ActionMode;
import androidx.appcompat.view.ActionMode$Callback;

public interface abstract AppCompatCallback	// class@000556 from classes.dex
{

    void onSupportActionModeFinished(ActionMode p0);
    void onSupportActionModeStarted(ActionMode p0);
    ActionMode onWindowStartingSupportActionMode(ActionMode$Callback p0);
}
