package tb.a4w;
import tb.t2o;
import java.lang.Object;
import android.database.DataSetObservable;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import java.lang.UnsupportedOperationException;
import android.view.ViewGroup;
import java.lang.Number;
import android.database.DataSetObserver;
import android.database.Observable;
import android.os.Parcelable;
import java.lang.ClassLoader;

public abstract class a4w	// class@00175c from classes9.dex
{
    public final DataSetObservable a;
    public static IpChange $ipChange;
    public static final int POSITION_NONE;
    public static final int POSITION_UNCHANGED;

    static {
       t2o.a(0x301000bb);
    }
    public void a4w(){
       super();
       this.a = new DataSetObservable();
    }
    public void a(View p0,int p1,Object p2){
       IpChange $ipChange = a4w.$ipChange;
       if (!$ipChange instanceof IpChange) {
          throw new UnsupportedOperationException("Required method destroyItem was not overridden");
       }
       Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
       $ipChange.ipc$dispatch("c889cb41", objArray);
       return;
    }
    public void b(ViewGroup p0,int p1,Object p2){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("2a141ccc", objArray);
          return;
       }else {
          this.a(p0, p1, p2);
          return;
       }
    }
    public void c(View p0){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3a80a62f", objArray);
       }
       return;
    }
    public void d(ViewGroup p0){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fad9102", objArray);
          return;
       }else {
          this.c(p0);
          return;
       }
    }
    public abstract int e();
    public int f(Object p0){
       IpChange $ipChange = a4w.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return -1;
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("304bee8", objArray).intValue();
    }
    public Object g(View p0,int p1){
       IpChange $ipChange = a4w.$ipChange;
       if (!$ipChange instanceof IpChange) {
          throw new UnsupportedOperationException("Required method instantiateItem was not overridden");
       }
       Object[] objArray = new Object[]{this,p0,new Integer(p1)};
       return $ipChange.ipc$dispatch("542dde3d", objArray);
    }
    public Object h(ViewGroup p0,int p1){
       IpChange $ipChange = a4w.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.g(p0, p1);
       }
       Object[] objArray = new Object[]{this,p0,new Integer(p1)};
       return $ipChange.ipc$dispatch("1038d332", objArray);
    }
    public abstract boolean i(View p0,Object p1);
    public void j(){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("eee9a4ec", objArray);
          return;
       }else {
          this.a.notifyChanged();
          return;
       }
    }
    public void k(DataSetObserver p0){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("63701d27", objArray);
          return;
       }else {
          this.a.registerObserver(p0);
          return;
       }
    }
    public void l(Parcelable p0,ClassLoader p1){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("c4ccff61", objArray);
       }
       return;
    }
    public Parcelable m(){
       IpChange $ipChange = a4w.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("b5f2b05e", objArray);
    }
    public void n(View p0,int p1,Object p2){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("383ff807", objArray);
       }
       return;
    }
    public void o(ViewGroup p0,int p1,Object p2){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("8338fbc6", objArray);
          return;
       }else {
          this.n(p0, p1, p2);
          return;
       }
    }
    public void p(View p0){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("481d94c0", objArray);
       }
       return;
    }
    public void q(ViewGroup p0){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3c641511", objArray);
          return;
       }else {
          this.p(p0);
          return;
       }
    }
    public void r(DataSetObserver p0){
       IpChange $ipChange = a4w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("66db852e", objArray);
          return;
       }else {
          this.a.unregisterObserver(p0);
          return;
       }
    }
}
