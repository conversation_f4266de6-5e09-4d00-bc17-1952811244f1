package kotlinx.coroutines.sync.MutexImpl;
import tb.ofj;
import kotlinx.coroutines.sync.SemaphoreImpl;
import java.lang.Object;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import kotlinx.coroutines.sync.MutexKt;
import kotlinx.coroutines.sync.MutexImpl$onSelectCancellationUnlockConstructor$1;
import tb.ar4;
import tb.xhv;
import tb.dkf;
import kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt;
import kotlinx.coroutines.c;
import tb.s23;
import kotlinx.coroutines.sync.MutexImpl$CancellableContinuationWithOwner;
import tb.q23;
import tb.jv6;
import java.lang.StringBuilder;
import tb.ov6;
import tb.ckf;
import java.lang.IllegalStateException;
import tb.h30;
import tb.k9p;
import kotlinx.coroutines.sync.MutexImpl$a;
import tb.l9p;
import tb.dv6;

public class MutexImpl extends SemaphoreImpl implements ofj	// class@0006c8 from classes11.dex
{
    public Object m;
    public static final AtomicReferenceFieldUpdater n;

    static {
       MutexImpl.n = AtomicReferenceFieldUpdater.newUpdater(MutexImpl.class, Object.class, "m");
    }
    public void MutexImpl(boolean p0){
       super(1, p0);
       u1r ou1r = (p0)? null: MutexKt.a;
       this.m = ou1r;
       MutexImpl$onSelectCancellationUnlockConstructor$1 oonSelectCan = new MutexImpl$onSelectCancellationUnlockConstructor$1(this);
       return;
    }
    public static Object s(MutexImpl p0,Object p1,ar4 p2){
       if (p0.w(p1)) {
          return xhv.INSTANCE;
       }
       if ((p0 = p0.t(p1, p2)) == dkf.d()) {
          return p0;
       }
       return xhv.INSTANCE;
    }
    public static final AtomicReferenceFieldUpdater y(){
       return MutexImpl.z();
    }
    public static final AtomicReferenceFieldUpdater z(){
       return MutexImpl.n;
    }
    public Object b(Object p0,ar4 p1){
       return MutexImpl.s(this, p0, p1);
    }
    public boolean p(Object p0){
       boolean b = true;
       if (this.q(p0) != b) {
          b = false;
       }
       return b;
    }
    public final int q(Object p0){
       Object obj;
       int i;
       while (true) {
          if (!this.r()) {
             return 0;
          }
          if ((obj = MutexImpl.z().get(this)) != MutexKt.a) {
             i = (obj == p0)? 1: 2;
             break ;
          }
       }
       return i;
    }
    public boolean r(){
       boolean b = (!this.j())? true: false;
       return b;
    }
    public final Object t(Object p0,ar4 p1){
       c uoc = s23.b(IntrinsicsKt__IntrinsicsJvmKt.c(p1));
       this.d(new MutexImpl$CancellableContinuationWithOwner(this, uoc, p0));
       if ((p0 = uoc.A()) == dkf.d()) {
          jv6.c(p1);
       }
       if (p0 == dkf.d()) {
          return p0;
       }else {
          return xhv.INSTANCE;
       }
    }
    public String toString(){
       return "Mutex@"+ov6.b(this)+"[isLocked="+this.r()+",owner="+MutexImpl.z().get(this)+']';
    }
    public Object u(Object p0,Object p1){
       if (!ckf.b(p1, MutexKt.b)) {
          return this;
       }
       throw new IllegalStateException("This mutex is already locked by the specified owner: "+p0.toString());
    }
    public void unlock(Object p0){
       while (true) {
          if (!this.r()) {
             throw new IllegalStateException("This mutex is not locked");
          }
          Object obj = MutexImpl.z().get(this);
          u1r a = MutexKt.a;
          if (obj != a) {
             if (obj != p0 && p0 != null) {
                throw new IllegalStateException("This mutex is locked by "+obj+", but "+p0+" is expected".toString());
             }
             if (h30.a(MutexImpl.z(), this, obj, a)) {
                break ;
             }
          }
       }
       this.release();
       return;
    }
    public void v(k9p p0,Object p1){
       if (p1 != null && this.p(p1)) {
          p0.b(MutexKt.b);
       }else {
          ckf.e(p0, "null cannot be cast to non-null type kotlinx.coroutines.selects.SelectInstanceInternal<*>");
          this.k(new MutexImpl$a(this, p0, p1), p1);
       }
       return;
    }
    public boolean w(Object p0){
       int i = this.x(p0);
       boolean b = true;
       if (i) {
          if (i != b) {
             if (i != 2) {
                throw new IllegalStateException("unexpected");
             }else {
                throw new IllegalStateException("This mutex is already locked by the specified owner: "+p0.toString());
             }
          }else {
             b = false;
          }
       }
       return b;
    }
    public final int x(Object p0){
       int i1;
       while (true) {
          if (this.l()) {
             MutexImpl.z().set(this, p0);
             return 0;
          }else {
             int i = 1;
             if (p0 == null) {
                return i;
             }
             if ((i1 = this.q(p0)) != i) {
                if (i1 == 2) {
                   return i;
                }
                continue ;
             }else {
                break ;
             }
          }
       }
       return 2;
    }
}
