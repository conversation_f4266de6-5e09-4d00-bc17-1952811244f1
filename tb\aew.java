package tb.aew;
import android.content.ContextWrapper;
import tb.t2o;
import android.content.Context;
import java.lang.ref.WeakReference;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.app.Application;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.content.res.Resources$Theme;
import android.content.Intent;
import android.os.Bundle;
import android.content.BroadcastReceiver;
import android.content.IntentFilter;
import android.os.Handler;
import android.content.res.Resources;
import java.lang.ref.Reference;

public class aew extends ContextWrapper	// class@00187f from classes5.dex
{
    public WeakReference a;
    public static IpChange $ipChange;
    public static final String TAG;

    static {
       t2o.a(0x16c0027c);
    }
    public void aew(Context p0){
       super(aew.b(p0));
       this.a = new WeakReference(p0);
    }
    public static Context b(Context p0){
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("1f874a0f", objArray);
       }else if(p0 instanceof Application){
          return p0;
       }else {
          return p0.getApplicationContext();
       }
    }
    public static Object ipc$super(aew p0,String p1,Object[] p2){
       int i = 1;
       Object obj = null;
       switch (p1.hashCode()){
           case 0x81e0455f:
             super.startActivity(p2[0], p2[i]);
             return obj;
           case 0xbfdcb6b5:
             super.unregisterReceiver(p2[0]);
             return obj;
           case 0xe67e711c:
             super.startActivities(p2[0]);
             return obj;
           case 0x6ee2d55:
             super.startActivity(p2[0]);
             return obj;
           case 0x36fe0307:
             return super.getResources();
           case 0x3fd0fe66:
             return super.registerReceiver(p2[0], p2[i], p2[2], p2[3]);
           case 0x41edc960:
             return super.registerReceiver(p2[0], p2[i]);
           case 0x4238c638:
             super.startActivities(p2[0], p2[i]);
             return obj;
           case 0x7642b746:
             return super.getTheme();
           default:
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/detail2/core/optimize/prerender/ViewContext");
       }
    }
    public Context a(){
       aew ta;
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("51c1976b", objArray);
       }else if((ta = this.a) != null){
          uContext = ta.get();
       }else {
          uContext = null;
       }
       return uContext;
    }
    public void c(Context p0){
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f93b0529", objArray);
          return;
       }else {
          this.a = new WeakReference(p0);
          return;
       }
    }
    public Resources getResources(){
       aew ta;
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("36fe0307", objArray);
       }else if((ta = this.a) != null){
          uContext = ta.get();
       }else {
          uContext = null;
       }
       if (uContext != null) {
          return uContext.getResources();
       }else {
          return super.getResources();
       }
    }
    public Resources$Theme getTheme(){
       aew ta;
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("7642b746", objArray);
       }else if((ta = this.a) != null){
          uContext = ta.get();
       }else {
          uContext = null;
       }
       if (uContext != null) {
          return uContext.getTheme();
       }else {
          return super.getTheme();
       }
    }
    public Intent registerReceiver(BroadcastReceiver p0,IntentFilter p1){
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("41edc960", objArray);
       }else if((uContext = this.a()) != null){
          return uContext.registerReceiver(p0, p1);
       }else {
          return super.registerReceiver(p0, p1);
       }
    }
    public Intent registerReceiver(BroadcastReceiver p0,IntentFilter p1,String p2,Handler p3){
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          return $ipChange.ipc$dispatch("3fd0fe66", objArray);
       }else if((uContext = this.a()) != null){
          return uContext.registerReceiver(p0, p1, p2, p3);
       }else {
          return super.registerReceiver(p0, p1, p2, p3);
       }
    }
    public void startActivities(Intent[] p0){
       aew ta;
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e67e711c", objArray);
          return;
       }else if((ta = this.a) != null){
          uContext = ta.get();
       }else {
          uContext = null;
       }
       if (uContext != null) {
          uContext.startActivities(p0);
       }else {
          super.startActivities(p0);
       }
       return;
    }
    public void startActivities(Intent[] p0,Bundle p1){
       aew ta;
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4238c638", objArray);
          return;
       }else if((ta = this.a) != null){
          uContext = ta.get();
       }else {
          uContext = null;
       }
       if (uContext != null) {
          uContext.startActivities(p0, p1);
       }else {
          super.startActivities(p0, p1);
       }
       return;
    }
    public void startActivity(Intent p0){
       aew ta;
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6ee2d55", objArray);
          return;
       }else if((ta = this.a) != null){
          uContext = ta.get();
       }else {
          uContext = null;
       }
       if (uContext != null) {
          uContext.startActivity(p0);
       }else {
          super.startActivity(p0);
       }
       return;
    }
    public void startActivity(Intent p0,Bundle p1){
       aew ta;
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("81e0455f", objArray);
          return;
       }else if((ta = this.a) != null){
          uContext = ta.get();
       }else {
          uContext = null;
       }
       if (uContext != null) {
          uContext.startActivity(p0, p1);
       }else {
          super.startActivity(p0, p1);
       }
       return;
    }
    public void unregisterReceiver(BroadcastReceiver p0){
       Context uContext;
       IpChange $ipChange = aew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("bfdcb6b5", objArray);
          return;
       }else if((uContext = this.a()) != null){
          uContext.unregisterReceiver(p0);
          return;
       }else {
          super.unregisterReceiver(p0);
          return;
       }
    }
}
