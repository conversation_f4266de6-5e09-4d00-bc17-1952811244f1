package kotlinx.coroutines.DelayKt$awaitCancellation$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.DelayKt;

public final class DelayKt$awaitCancellation$1 extends ContinuationImpl	// class@000497 from classes11.dex
{
    public int label;
    public Object result;

    public void DelayKt$awaitCancellation$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return DelayKt.a(this);
    }
}
