package me.leolin.shortcutbadger.impl.LGHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.content.Intent;
import tb.ol2;
import me.leolin.shortcutbadger.ShortcutBadgeException;
import java.lang.StringBuilder;

public class LGHomeBadger implements po1	// class@000762 from classes11.dex
{

    public void LGHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"com.lge.launcher","com.lge.launcher2"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       Intent intent = new Intent("android.intent.action.BADGE_COUNT_UPDATE");
       intent.putExtra("badge_count", p2);
       intent.putExtra("badge_count_package_name", p1.getPackageName());
       intent.putExtra("badge_count_class_name", p1.getClassName());
       if (!ol2.a(p0, intent)) {
          throw new ShortcutBadgeException("unable to resolve intent: "+intent.toString());
       }
       p0.sendBroadcast(intent);
       return;
    }
}
