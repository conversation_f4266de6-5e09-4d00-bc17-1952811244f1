package tb.a34$e;
import tb.a950;
import tb.a34;
import java.lang.Object;
import com.taobao.tao.messagekit.core.model.Command;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.util.SparseArray;
import com.taobao.tao.messagekit.core.model.BaseMessage;
import tb.dva;
import tb.wnb;
import com.taobao.tao.messagekit.core.model.Ack;
import java.lang.Integer;
import com.taobao.tao.messagekit.core.utils.MsgLog;

public class a34$e implements a950	// class@00174f from classes9.dex
{
    public final a34 a;
    public static IpChange $ipChange;

    public void a34$e(a34 p0){
       super();
       this.a = p0;
    }
    public void a(Command p0){
       wnb ownb;
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = a34$e.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          $ipChange.ipc$dispatch("22b42a9", objArray);
          return;
       }else if((ownb = a34.a(this.a).get(p0.header.g)) != null){
          ownb.a(p0);
       }
       BaseMessage header = p0.header;
       Object[] objArray1 = new Object[]{"command:",header.a,"subType:",Integer.valueOf(header.g)};
       MsgLog.i("CommandManager", objArray1);
       return;
    }
    public void accept(Object p0){
       this.a(p0);
    }
}
