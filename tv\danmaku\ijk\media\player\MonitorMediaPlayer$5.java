package tv.danmaku.ijk.media.player.MonitorMediaPlayer$5;
import java.lang.ThreadLocal;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;

public final class MonitorMediaPlayer$5 extends ThreadLocal	// class@0010f5 from classes11.dex
{
    public static IpChange $ipChange;

    public void MonitorMediaPlayer$5(){
       super();
    }
    public static Object ipc$super(MonitorMediaPlayer$5 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in tv/danmaku/ijk/media/player/MonitorMediaPlayer$5");
    }
    public Object initialValue(){
       return this.initialValue();
    }
    public StringBuilder initialValue(){
       IpChange $ipChange = MonitorMediaPlayer$5.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(1024);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7693d6af", objArray);
    }
}
