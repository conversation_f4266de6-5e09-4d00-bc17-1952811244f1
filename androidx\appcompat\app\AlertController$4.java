package androidx.appcompat.app.AlertController$4;
import android.widget.AbsListView$OnScrollListener;
import androidx.appcompat.app.AlertController;
import android.view.View;
import java.lang.Object;
import android.widget.AbsListView;

public class AlertController$4 implements AbsListView$OnScrollListener	// class@000545 from classes.dex
{
    public final AlertController this$0;
    public final View val$bottom;
    public final View val$top;

    public void AlertController$4(AlertController p0,View p1,View p2){
       this.this$0 = p0;
       this.val$top = p1;
       this.val$bottom = p2;
       super();
    }
    public void onScroll(AbsListView p0,int p1,int p2,int p3){
       AlertController.manageScrollIndicators(p0, this.val$top, this.val$bottom);
    }
    public void onScrollStateChanged(AbsListView p0,int p1){
    }
}
