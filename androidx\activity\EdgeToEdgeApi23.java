package androidx.activity.EdgeToEdgeApi23;
import androidx.activity.EdgeToEdgeBase;
import androidx.activity.SystemBarStyle;
import android.view.Window;
import android.view.View;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsControllerCompat;

public final class EdgeToEdgeApi23 extends EdgeToEdgeBase	// class@000444 from classes.dex
{

    public void EdgeToEdgeApi23(){
       super();
    }
    public void setUp(SystemBarStyle p0,SystemBarStyle p1,Window p2,View p3,boolean p4,boolean p5){
       ckf.g(p0, "statusBarStyle");
       ckf.g(p1, "navigationBarStyle");
       ckf.g(p2, "window");
       ckf.g(p3, "view");
       WindowCompat.setDecorFitsSystemWindows(p2, false);
       p2.setStatusBarColor(p0.getScrim$activity_release(p4));
       p2.setNavigationBarColor(p1.getDarkScrim$activity_release());
       new WindowInsetsControllerCompat(p2, p3).setAppearanceLightStatusBars((p4 ^ 0x01));
    }
}
