package kotlinx.datetime.format.TimeFields$amPm$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.fw40;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import kotlinx.datetime.format.AmPmMarker;

public final class TimeFields$amPm$1 extends MutablePropertyReference1Impl	// class@0006ed from classes11.dex
{
    public static final TimeFields$amPm$1 INSTANCE;

    static {
       TimeFields$amPm$1.INSTANCE = new TimeFields$amPm$1();
    }
    public void TimeFields$amPm$1(){
       super(fw40.class, "amPm", "getAmPm\(\)Lkotlinx/datetime/format/AmPmMarker;", 0);
    }
    public Object get(Object p0){
       return p0.t();
    }
    public void set(Object p0,Object p1){
       p0.i(p1);
    }
}
