package tb.ad8;
import tb.lid;
import java.lang.Object;
import java.lang.String;
import tb.w2l;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.android.remoteso.api.RSoException;
import java.io.InputStream;
import com.taobao.android.remoteso.api.assets.CheckAssetsResult;

public class ad8 implements lid	// class@0016f4 from classes6.dex
{
    public static IpChange $ipChange;
    public static final lid INSTANCE;

    static {
       ad8.INSTANCE = new ad8();
    }
    public void ad8(){
       super();
    }
    public w2l a(String p0){
       IpChange $ipChange = ad8.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new w2l(p0, null, RSoException.error(1001));
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("7df8ac43", objArray);
    }
    public CheckAssetsResult b(String p0){
       IpChange $ipChange = ad8.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new CheckAssetsResult(p0, 1);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("bc930abe", objArray);
    }
}
