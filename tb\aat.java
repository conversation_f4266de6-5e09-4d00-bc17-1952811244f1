package tb.aat;
import tb.nt1;
import tb.t2o;
import tb.b0d;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Long;
import java.lang.CharSequence;
import android.text.TextUtils;
import com.taobao.taolive.room.business.taoke.TaokeRequest;
import tb.q4e;
import tb.gq0;
import tb.v2s;
import tb.ghb;
import android.app.Application;
import android.content.Context;
import com.taobao.taolive.sdk.adapter.network.INetDataObject;
import java.lang.Class;

public class aat extends nt1	// class@00177c from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x30f00270);
    }
    public void aat(b0d p0){
       super(p0);
    }
    public static Object ipc$super(aat p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/taolive/room/business/taoke/TaokeBusiness");
    }
    public void K(String p0,long p1,String p2){
       IpChange $ipChange = aat.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1),p2};
          $ipChange.ipc$dispatch("5e334d55", objArray);
          return;
       }else if(TextUtils.isEmpty(p2)){
          p2 = "taolive";
       }
       TaokeRequest taokeRequest = new TaokeRequest();
       taokeRequest.accountId = p0;
       taokeRequest.bizType = p2;
       taokeRequest.itemId = p1;
       taokeRequest.utdid = gq0.n().getUtdid(v2s.o().f().getApplication().getApplicationContext());
       taokeRequest.platform = "phone";
       taokeRequest.sourceType = 2;
       this.C(0, taokeRequest, null);
       return;
    }
}
