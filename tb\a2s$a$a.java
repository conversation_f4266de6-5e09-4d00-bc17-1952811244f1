package tb.a2s$a$a;
import android.view.View$OnTouchListener;
import tb.a2s$a;
import android.view.View;
import java.lang.Object;
import android.view.MotionEvent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import tb.x1s;
import android.content.Context;
import tb.a2s;
import android.widget.EditText;
import tb.a2s$a$a$a;
import tb.x1s$c;

public class a2s$a$a implements View$OnTouchListener	// class@001826 from classes5.dex
{
    public final View a;
    public final a2s$a b;
    public static IpChange $ipChange;

    public void a2s$a$a(a2s$a p0,View p1){
       super();
       this.b = p0;
       this.a = p1;
    }
    public boolean onTouch(View p0,MotionEvent p1){
       IpChange $ipChange = a2s$a$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("d4aa3aa4", objArray).booleanValue();
       }else if(p1.getActionMasked() == 1 && !p0.isFocusable()){
          x1s ox1s = new x1s(a2s$a.a(this.b));
          ox1s.j(a2s.a(this.b.c));
          ox1s.h(this.a);
          ox1s.i(new a2s$a$a$a(this));
          ox1s.show();
          return 1;
       }else {
          return 0;
       }
    }
}
