package kotlinx.coroutines.sync.MutexKt$withLock$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import tb.ofj;
import tb.d1a;
import kotlinx.coroutines.sync.MutexKt;

public final class MutexKt$withLock$1 extends ContinuationImpl	// class@0006c9 from classes11.dex
{
    public Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;
    public Object result;

    public void MutexKt$withLock$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return MutexKt.c(null, null, null, this);
    }
}
