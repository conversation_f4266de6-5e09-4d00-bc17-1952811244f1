package androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatCallback;
import androidx.core.app.TaskStackBuilder$SupportParentable;
import androidx.appcompat.app.ActionBarDrawerToggle$DelegateProvider;
import androidx.fragment.app.FragmentActivity;
import androidx.savedstate.SavedStateRegistry;
import androidx.activity.ComponentActivity;
import androidx.appcompat.app.AppCompatActivity$1;
import java.lang.String;
import androidx.savedstate.SavedStateRegistry$SavedStateProvider;
import androidx.appcompat.app.AppCompatActivity$2;
import androidx.activity.contextaware.OnContextAvailableListener;
import android.view.Window;
import android.app.Activity;
import android.view.View;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.lifecycle.ViewTreeViewModelStoreOwner;
import androidx.savedstate.SavedStateRegistryOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;
import androidx.activity.OnBackPressedDispatcherOwner;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner;
import android.view.KeyEvent;
import android.os.Build$VERSION;
import android.view.ViewGroup$LayoutParams;
import androidx.appcompat.app.AppCompatDelegate;
import android.content.Context;
import androidx.appcompat.app.ActionBar;
import androidx.core.app.ComponentActivity;
import androidx.appcompat.app.ActionBarDrawerToggle$Delegate;
import android.view.MenuInflater;
import android.content.res.Resources;
import androidx.appcompat.widget.VectorEnabledTintResources;
import android.content.Intent;
import androidx.core.app.NavUtils;
import android.content.res.Configuration;
import android.util.DisplayMetrics;
import androidx.core.app.TaskStackBuilder;
import androidx.core.os.LocaleListCompat;
import android.view.MenuItem;
import android.view.Menu;
import android.os.Bundle;
import androidx.appcompat.view.ActionMode;
import androidx.core.app.ActivityCompat;
import java.lang.CharSequence;
import androidx.appcompat.view.ActionMode$Callback;
import androidx.appcompat.widget.Toolbar;

public class AppCompatActivity extends FragmentActivity implements AppCompatCallback, TaskStackBuilder$SupportParentable, ActionBarDrawerToggle$DelegateProvider	// class@000555 from classes.dex
{
    private AppCompatDelegate mDelegate;
    private Resources mResources;
    private static final String DELEGATE_TAG = "androidx:appcompat";
    public static final int X0;

    public void AppCompatActivity(){
       super();
       this.initDelegate();
    }
    public void AppCompatActivity(int p0){
       super(p0);
       this.initDelegate();
    }
    private void initDelegate(){
       this.getSavedStateRegistry().registerSavedStateProvider("androidx:appcompat", new AppCompatActivity$1(this));
       this.addOnContextAvailableListener(new AppCompatActivity$2(this));
    }
    private void initViewTreeOwners(){
       ViewTreeLifecycleOwner.set(this.getWindow().getDecorView(), this);
       ViewTreeViewModelStoreOwner.set(this.getWindow().getDecorView(), this);
       ViewTreeSavedStateRegistryOwner.set(this.getWindow().getDecorView(), this);
       ViewTreeOnBackPressedDispatcherOwner.set(this.getWindow().getDecorView(), this);
    }
    private boolean performMenuItemShortcut(KeyEvent p0){
       Window window;
       if (Build$VERSION.SDK_INT < 26 && (!p0.isCtrlPressed() && (!KeyEvent.metaStateHasNoModifiers(p0.getMetaState()) && (!p0.getRepeatCount() && (!KeyEvent.isModifierKey(p0.getKeyCode()) && ((window = this.getWindow()) != null && (window.getDecorView() != null && window.getDecorView().dispatchKeyShortcutEvent(p0)))))))) {
          return true;
       }
       return false;
    }
    public void addContentView(View p0,ViewGroup$LayoutParams p1){
       this.initViewTreeOwners();
       this.getDelegate().addContentView(p0, p1);
    }
    public void attachBaseContext(Context p0){
       super.attachBaseContext(this.getDelegate().attachBaseContext2(p0));
    }
    public void closeOptionsMenu(){
       ActionBar supportActio = this.getSupportActionBar();
       if (this.getWindow().hasFeature(0) && (supportActio == null && supportActio.closeOptionsMenu())) {
          super.closeOptionsMenu();
       }
       return;
    }
    public boolean dispatchKeyEvent(KeyEvent p0){
       ActionBar supportActio = this.getSupportActionBar();
       if (p0.getKeyCode() == 82 && (supportActio != null && supportActio.onMenuKeyEvent(p0))) {
          return true;
       }
       return super.dispatchKeyEvent(p0);
    }
    public View findViewById(int p0){
       return this.getDelegate().findViewById(p0);
    }
    public AppCompatDelegate getDelegate(){
       if (this.mDelegate == null) {
          this.mDelegate = AppCompatDelegate.create(this, this);
       }
       return this.mDelegate;
    }
    public ActionBarDrawerToggle$Delegate getDrawerToggleDelegate(){
       return this.getDelegate().getDrawerToggleDelegate();
    }
    public MenuInflater getMenuInflater(){
       return this.getDelegate().getMenuInflater();
    }
    public Resources getResources(){
       AppCompatActivity tmResources;
       if (this.mResources == null && VectorEnabledTintResources.shouldBeUsed()) {
          this.mResources = new VectorEnabledTintResources(this, super.getResources());
       }
       if ((tmResources = this.mResources) == null) {
          tmResources = super.getResources();
       }
       return tmResources;
    }
    public ActionBar getSupportActionBar(){
       return this.getDelegate().getSupportActionBar();
    }
    public Intent getSupportParentActivityIntent(){
       return NavUtils.getParentActivityIntent(this);
    }
    public void invalidateOptionsMenu(){
       this.getDelegate().invalidateOptionsMenu();
    }
    public void onConfigurationChanged(Configuration p0){
       super.onConfigurationChanged(p0);
       this.getDelegate().onConfigurationChanged(p0);
       if (this.mResources != null) {
          this.mResources.updateConfiguration(super.getResources().getConfiguration(), super.getResources().getDisplayMetrics());
       }
       return;
    }
    public void onContentChanged(){
       this.onSupportContentChanged();
    }
    public void onCreateSupportNavigateUpTaskStack(TaskStackBuilder p0){
       p0.addParentStack(this);
    }
    public void onDestroy(){
       super.onDestroy();
       this.getDelegate().onDestroy();
    }
    public boolean onKeyDown(int p0,KeyEvent p1){
       if (this.performMenuItemShortcut(p1)) {
          return true;
       }
       return super.onKeyDown(p0, p1);
    }
    public void onLocalesChanged(LocaleListCompat p0){
    }
    public final boolean onMenuItemSelected(int p0,MenuItem p1){
       if (super.onMenuItemSelected(p0, p1)) {
          return true;
       }
       ActionBar supportActio = this.getSupportActionBar();
       if (p1.getItemId() == 0x102002c && (supportActio != null && ((supportActio.getDisplayOptions() & 0x04)))) {
          return this.onSupportNavigateUp();
       }
       return false;
    }
    public boolean onMenuOpened(int p0,Menu p1){
       return super.onMenuOpened(p0, p1);
    }
    public void onNightModeChanged(int p0){
    }
    public void onPanelClosed(int p0,Menu p1){
       super.onPanelClosed(p0, p1);
    }
    public void onPostCreate(Bundle p0){
       super.onPostCreate(p0);
       this.getDelegate().onPostCreate(p0);
    }
    public void onPostResume(){
       super.onPostResume();
       this.getDelegate().onPostResume();
    }
    public void onPrepareSupportNavigateUpTaskStack(TaskStackBuilder p0){
    }
    public void onStart(){
       super.onStart();
       this.getDelegate().onStart();
    }
    public void onStop(){
       super.onStop();
       this.getDelegate().onStop();
    }
    public void onSupportActionModeFinished(ActionMode p0){
    }
    public void onSupportActionModeStarted(ActionMode p0){
    }
    public void onSupportContentChanged(){
    }
    public boolean onSupportNavigateUp(){
       Intent supportParen;
       if ((supportParen = this.getSupportParentActivityIntent()) == null) {
          return false;
       }
       if (this.supportShouldUpRecreateTask(supportParen)) {
          TaskStackBuilder taskStackBui = TaskStackBuilder.create(this);
          this.onCreateSupportNavigateUpTaskStack(taskStackBui);
          this.onPrepareSupportNavigateUpTaskStack(taskStackBui);
          try{
             taskStackBui.startActivities();
             ActivityCompat.finishAffinity(this);
          }catch(java.lang.IllegalStateException e0){
             this.finish();
          }
       }else {
          this.supportNavigateUpTo(supportParen);
       }
       return true;
    }
    public void onTitleChanged(CharSequence p0,int p1){
       super.onTitleChanged(p0, p1);
       this.getDelegate().setTitle(p0);
    }
    public ActionMode onWindowStartingSupportActionMode(ActionMode$Callback p0){
       return null;
    }
    public void openOptionsMenu(){
       ActionBar supportActio = this.getSupportActionBar();
       if (this.getWindow().hasFeature(0) && (supportActio == null && supportActio.openOptionsMenu())) {
          super.openOptionsMenu();
       }
       return;
    }
    public void setContentView(int p0){
       this.initViewTreeOwners();
       this.getDelegate().setContentView(p0);
    }
    public void setContentView(View p0){
       this.initViewTreeOwners();
       this.getDelegate().setContentView(p0);
    }
    public void setContentView(View p0,ViewGroup$LayoutParams p1){
       this.initViewTreeOwners();
       this.getDelegate().setContentView(p0, p1);
    }
    public void setSupportActionBar(Toolbar p0){
       this.getDelegate().setSupportActionBar(p0);
    }
    public void setSupportProgress(int p0){
    }
    public void setSupportProgressBarIndeterminate(boolean p0){
    }
    public void setSupportProgressBarIndeterminateVisibility(boolean p0){
    }
    public void setSupportProgressBarVisibility(boolean p0){
    }
    public void setTheme(int p0){
       super.setTheme(p0);
       this.getDelegate().setTheme(p0);
    }
    public ActionMode startSupportActionMode(ActionMode$Callback p0){
       return this.getDelegate().startSupportActionMode(p0);
    }
    public void supportInvalidateOptionsMenu(){
       this.getDelegate().invalidateOptionsMenu();
    }
    public void supportNavigateUpTo(Intent p0){
       NavUtils.navigateUpTo(this, p0);
    }
    public boolean supportRequestWindowFeature(int p0){
       return this.getDelegate().requestWindowFeature(p0);
    }
    public boolean supportShouldUpRecreateTask(Intent p0){
       return NavUtils.shouldUpRecreateTask(this, p0);
    }
}
