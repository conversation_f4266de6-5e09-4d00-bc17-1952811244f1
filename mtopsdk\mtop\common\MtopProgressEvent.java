package mtopsdk.mtop.common.MtopProgressEvent;
import mtopsdk.mtop.common.MtopEvent;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;

public class MtopProgressEvent extends MtopEvent	// class@0007ac from classes11.dex
{
    public String desc;
    public String seqNo;
    public int size;
    public int total;
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000bf);
    }
    public void MtopProgressEvent(String p0,int p1,int p2){
       super();
       this.desc = p0;
       this.size = p1;
       this.total = p2;
    }
    public static Object ipc$super(MtopProgressEvent p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/common/MtopProgressEvent");
    }
    public String getDesc(){
       IpChange $ipChange = MtopProgressEvent.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.desc;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f24b4252", objArray);
    }
    public int getSize(){
       IpChange $ipChange = MtopProgressEvent.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.size;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ae43b971", objArray).intValue();
    }
    public int getTotal(){
       IpChange $ipChange = MtopProgressEvent.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.total;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dcf28f08", objArray).intValue();
    }
    public String toString(){
       IpChange $ipChange = MtopProgressEvent.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(32)+"MtopProgressEvent [seqNo="+this.seqNo+", desc="+this.desc+", size="+this.size+", total="+this.total+"]";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
