package kotlinx.serialization.modules.SerializerAlreadyRegisteredException;
import java.lang.IllegalArgumentException;
import java.lang.String;
import java.lang.Object;
import tb.ckf;
import tb.wyf;
import java.lang.StringBuilder;

public final class SerializerAlreadyRegisteredException extends IllegalArgumentException	// class@000752 from classes11.dex
{

    public void SerializerAlreadyRegisteredException(String p0){
       ckf.g(p0, "msg");
       super(p0);
    }
    public void SerializerAlreadyRegisteredException(wyf p0,wyf p1){
       ckf.g(p0, "baseClass");
       ckf.g(p1, "concreteClass");
       super("Serializer for "+p1+" already registered in the scope of "+p0);
    }
}
