package kotlinx.coroutines.JobSupport$f;
import kotlinx.coroutines.internal.LockFreeLinkedListNode$a;
import kotlinx.coroutines.internal.LockFreeLinkedListNode;
import kotlinx.coroutines.JobSupport;
import java.lang.Object;
import tb.sch;

public final class JobSupport$f extends LockFreeLinkedListNode$a	// class@0004a8 from classes11.dex
{
    public final JobSupport e;
    public final Object f;

    public void JobSupport$f(LockFreeLinkedListNode p0,JobSupport p1,Object p2){
       this.e = p1;
       this.f = p2;
       super(p0);
    }
    public Object d(Object p0){
       return this.f(p0);
    }
    public Object f(LockFreeLinkedListNode p0){
       p0 = (this.e.v0() == this.f)? null: sch.a;
       return p0;
    }
}
