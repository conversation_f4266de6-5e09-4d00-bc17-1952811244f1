package tb.acy;
import tb.xux;
import com.unionpay.UPPayWapActivity;
import java.lang.Object;
import java.lang.String;
import tb.zux;
import android.content.Context;
import tb.fyu;

public final class acy implements xux	// class@001ec3 from classes10.dex
{
    public final UPPayWapActivity a;

    public void acy(UPPayWapActivity p0){
       super();
       this.a = p0;
    }
    public final void a(String p0,zux p1){
       p0 = fyu.c(this.a);
       if (p1 != null) {
          p1.a(UPPayWapActivity.b("0", "success", p0));
       }
       return;
    }
}
