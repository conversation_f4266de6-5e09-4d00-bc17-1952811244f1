package androidx.activity.ComponentActivity$onBackPressedDispatcher$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.ComponentActivity;
import androidx.activity.OnBackPressedDispatcher;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import java.lang.Throwable;
import tb.ya4;
import java.lang.Runnable;
import android.os.Build$VERSION;
import android.os.Looper;
import android.os.Handler;
import tb.za4;

public final class ComponentActivity$onBackPressedDispatcher$2 extends Lambda implements d1a	// class@00043f from classes.dex
{
    public final ComponentActivity this$0;

    public void ComponentActivity$onBackPressedDispatcher$2(ComponentActivity p0){
       this.this$0 = p0;
       super(0);
    }
    public static void a(ComponentActivity p0,OnBackPressedDispatcher p1){
       ComponentActivity$onBackPressedDispatcher$2.invoke$lambda$2$lambda$1(p0, p1);
    }
    public static void b(ComponentActivity p0){
       ComponentActivity$onBackPressedDispatcher$2.invoke$lambda$0(p0);
    }
    private static final void invoke$lambda$0(ComponentActivity p0){
       try{
          ckf.g(p0, "this$0");
          ComponentActivity.access$onBackPressed$s1027565324(p0);
       }catch(java.lang.IllegalStateException e2){
          if (!ckf.b(e2.getMessage(), "Can not perform this action after onSaveInstanceState")) {
             throw e2;
          }
       }catch(java.lang.NullPointerException e2){
          if (!ckf.b(e2.getMessage(), "Attempt to invoke virtual method \'android.os.Handler android.app.FragmentHostCallback.getHandler\(\)\' on a null object reference")) {
             throw e2;
          }
       }
       return;
    }
    private static final void invoke$lambda$2$lambda$1(ComponentActivity p0,OnBackPressedDispatcher p1){
       ckf.g(p0, "this$0");
       ckf.g(p1, "$dispatcher");
       ComponentActivity.access$addObserverForBackInvoker(p0, p1);
    }
    public final OnBackPressedDispatcher invoke(){
       OnBackPressedDispatcher onBackPresse = new OnBackPressedDispatcher(new ya4(this.this$0));
       ComponentActivity$onBackPressedDispatcher$2 tthis$0 = this.this$0;
       if (Build$VERSION.SDK_INT >= 33) {
          if (!ckf.b(Looper.myLooper(), Looper.getMainLooper())) {
             new Handler(Looper.getMainLooper()).post(new za4(tthis$0, onBackPresse));
          }else {
             ComponentActivity.access$addObserverForBackInvoker(tthis$0, onBackPresse);
          }
       }
       return onBackPresse;
    }
    public Object invoke(){
       return this.invoke();
    }
}
