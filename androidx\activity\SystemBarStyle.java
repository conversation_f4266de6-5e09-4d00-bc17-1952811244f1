package androidx.activity.SystemBarStyle;
import androidx.activity.SystemBarStyle$Companion;
import tb.a07;
import tb.g1a;
import java.lang.Object;

public final class SystemBarStyle	// class@00046f from classes.dex
{
    private final int darkScrim;
    private final g1a detectDarkMode;
    private final int lightScrim;
    private final int nightMode;
    public static final SystemBarStyle$Companion Companion;

    static {
       SystemBarStyle.Companion = new SystemBarStyle$Companion(null);
    }
    private void SystemBarStyle(int p0,int p1,int p2,g1a p3){
       super();
       this.lightScrim = p0;
       this.darkScrim = p1;
       this.nightMode = p2;
       this.detectDarkMode = p3;
    }
    public void SystemBarStyle(int p0,int p1,int p2,g1a p3,a07 p4){
       super(p0, p1, p2, p3);
    }
    public static final SystemBarStyle auto(int p0,int p1){
       return SystemBarStyle.Companion.auto(p0, p1);
    }
    public static final SystemBarStyle auto(int p0,int p1,g1a p2){
       return SystemBarStyle.Companion.auto(p0, p1, p2);
    }
    public static final SystemBarStyle dark(int p0){
       return SystemBarStyle.Companion.dark(p0);
    }
    public static final SystemBarStyle light(int p0,int p1){
       return SystemBarStyle.Companion.light(p0, p1);
    }
    public final int getDarkScrim$activity_release(){
       return this.darkScrim;
    }
    public final g1a getDetectDarkMode$activity_release(){
       return this.detectDarkMode;
    }
    public final int getNightMode$activity_release(){
       return this.nightMode;
    }
    public final int getScrim$activity_release(boolean p0){
       SystemBarStyle tdarkScrim = (p0)? this.darkScrim: this.lightScrim;
       return tdarkScrim;
    }
    public final int getScrimWithEnforcedContrast$activity_release(boolean p0){
       int i;
       if (this.nightMode == null) {
          i = 0;
       }else if(p0){
          i = this.darkScrim;
       }else {
          i = this.lightScrim;
       }
       return i;
    }
}
