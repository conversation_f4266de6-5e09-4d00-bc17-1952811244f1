package me.leolin.shortcutbadger.impl.ZTEHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.util.ArrayList;
import android.content.Context;
import android.content.ComponentName;
import android.os.Bundle;
import java.lang.String;
import android.os.BaseBundle;
import android.content.ContentResolver;
import android.net.Uri;

public class ZTEHomeBadger implements po1	// class@00076b from classes11.dex
{

    public void ZTEHomeBadger(){
       super();
    }
    public List a(){
       return new ArrayList(0);
    }
    public void b(Context p0,ComponentName p1,int p2){
       Bundle uBundle = new Bundle();
       uBundle.putInt("app_badge_count", p2);
       uBundle.putString("app_badge_component_name", p1.flattenToString());
       p0.getContentResolver().call(Uri.parse("content://com.android.launcher3.cornermark.unreadbadge"), "setAppUnreadCount", null, uBundle);
    }
}
