package kotlinx.serialization.SealedClassSerializer$descriptor$2$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.SealedClassSerializer;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;
import tb.si40;
import tb.x530;
import tb.cz10;
import tb.fj40;
import kotlinx.serialization.descriptors.a;
import java.util.List;
import java.lang.StringBuilder;
import tb.wyf;
import tb.ob40$a;
import kotlinx.serialization.SealedClassSerializer$descriptor$2$1$elementDescriptor$1;
import tb.ob40;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;

public final class SealedClassSerializer$descriptor$2$1 extends Lambda implements g1a	// class@000713 from classes11.dex
{
    public final SealedClassSerializer this$0;

    public void SealedClassSerializer$descriptor$2$1(SealedClassSerializer p0){
       this.this$0 = p0;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       ckf.g(p0, "$this$buildSerialDescriptor");
       f520.b(p0, "type", cz10.G(si40.INSTANCE).getDescriptor(), null, false, 12, null);
       a[] uoaArray = new a[0];
       f520.b(p0, "value", SerialDescriptorsKt.c("kotlinx.serialization.Sealed<"+this.this$0.getBaseClass().getSimpleName()+'>', ob40$a.INSTANCE, uoaArray, new SealedClassSerializer$descriptor$2$1$elementDescriptor$1(this.this$0)), null, false, 12, null);
       p0.h(SealedClassSerializer.access$get_annotations$p(this.this$0));
    }
}
