package tb.a86;
import com.taobao.android.dinamicx.widget.DXScrollerLayout;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import java.lang.Boolean;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import tb.s32;
import tb.cxb;
import android.content.Context;
import android.view.View;
import com.taobao.android.dinamicx.view.DXNativeRecyclerView;
import androidx.recyclerview.widget.RecyclerView;

public class a86 extends DXScrollerLayout	// class@001b0b from classes8.dex
{
    public static IpChange $ipChange;
    public static final long DX_SCROLLER_LAYOUT;

    static {
       t2o.a(0x2ef00223);
       t2o.a(0x1c50031d);
    }
    public void a86(){
       super();
    }
    public static Object ipc$super(a86 p0,String p1,Object[] p2){
       if (p1.hashCode() != 0x7e58628a) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/mytaobao/ultron/dinamicX/widget/DXPlayControlScrollLayout");
       }
       super.onClone(p2[0], p2[1].booleanValue());
       return null;
    }
    public DXWidgetNode build(Object p0){
       IpChange $ipChange = a86.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("966917b0", objArray);
       }else {
          s32.c().i("DXPlayControlScrollLayout.build1");
          return new a86();
       }
    }
    public boolean isPipelineDefaultCreateRootView(){
       int i = 0;
       IpChange $ipChange = a86.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("d04cd663", objArray).booleanValue();
       }
       return i;
    }
    public void onCanPlay(cxb p0,String p1){
       IpChange $ipChange = a86.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("74c767ef", objArray);
       }
       return;
    }
    public void onClone(DXWidgetNode p0,boolean p1){
       IpChange $ipChange = a86.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("7e58628a", objArray);
          return;
       }else if(p0 != null && p0 instanceof a86){
          super.onClone(p0, p1);
       }
       return;
    }
    public View onCreateView(Context p0){
       IpChange $ipChange = a86.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("93d55e23", objArray);
       }else {
          DXNativeRecyclerView uDXNativeRec = new DXNativeRecyclerView(p0);
          this.closeDefaultAnimator(uDXNativeRec);
          return uDXNativeRec;
       }
    }
    public void onShouldStop(cxb p0,String p1){
       IpChange $ipChange = a86.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("c812a17e", objArray);
       }
       return;
    }
}
