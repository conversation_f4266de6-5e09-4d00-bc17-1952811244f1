package tb.a8s;
import tb.t2o;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.StringBuffer;
import tb.hsq;
import java.lang.Integer;
import java.util.HashMap;
import com.alibaba.analytics.core.model.LogField;
import java.lang.StringBuilder;
import com.ut.mini.UTAnalytics;
import com.ut.mini.UTTracker;
import java.util.Map;

public class a8s	// class@001854 from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1aa00024);
    }
    public static String a(String[] p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a8s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("6c16f9ea", objArray);
       }else if(p0 != null && !p0.length){
          return null;
       }else {
          StringBuffer str = "";
          if (p0 != null && p0.length > 0) {
             int i2 = 0;
             while (i < p0.length) {
                if (!hsq.f(p0[i])) {
                   if (i2) {
                      str = str.append(",");
                   }
                   str = str.append(p0[i]);
                   i2 = 1;
                }
                i = i + i1;
             }
          }
          return str;
       }
    }
    public static void b(String p0,int p1,Object p2,String[] p3){
       IpChange $ipChange = a8s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,new Integer(p1),p2,p3};
          $ipChange.ipc$dispatch("d6fccbf9", objArray);
          return;
       }else {
          String str = a8s.a(p3);
          HashMap hashMap = new HashMap();
          hashMap.put(LogField.PAGE.toString(), p0);
          hashMap.put(LogField.EVENTID.toString(), p1);
          hashMap.put(LogField.ARG1.toString(), hsq.c(p2));
          if (str != null) {
             hashMap.put(LogField.ARGS.toString(), str);
          }
          hashMap.put("_bmbu", "yes");
          UTAnalytics.getInstance().getDefaultTracker().send(hashMap);
          return;
       }
    }
}
