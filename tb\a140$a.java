package tb.a140$a;
import android.os.Handler;
import tb.a140;
import android.os.Looper;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.os.Message;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import java.util.List;
import java.lang.Number;
import java.util.Set;
import java.lang.Integer;
import java.util.Collection;

public final class a140$a extends Handler	// class@001836 from classes7.dex
{
    public final a140 a;
    public static IpChange $ipChange;

    public void a140$a(a140 p0,Looper p1){
       this.a = p0;
       super(p1);
    }
    public static Object ipc$super(a140$a p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/turbo/pagebuilder/service/preload/PreloadService$preloadHandler$1");
    }
    public void handleMessage(Message p0){
       int i = 0;
       IpChange $ipChange = a140$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("282a8c19", objArray);
          return;
       }else {
          ckf.g(p0, "msg");
          if (a140.p(this.a).isEmpty()) {
             return;
          }
          int i1 = a140.p(this.a).remove(i).intValue();
          if (!a140.w(this.a).contains(Integer.valueOf(i1))) {
             a140.w(this.a).add(Integer.valueOf(i1));
             this.a.B(i1);
          }
          if (!a140.p(this.a).isEmpty()) {
             this.sendEmptyMessage(1);
          }
          return;
       }
    }
}
