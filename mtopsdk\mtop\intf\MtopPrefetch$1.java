package mtopsdk.mtop.intf.MtopPrefetch$1;
import java.lang.Runnable;
import mtopsdk.mtop.intf.MtopPrefetch;
import java.lang.String;
import java.util.HashMap;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.mtop.intf.MtopPrefetch$IPrefetchCallback;

public final class MtopPrefetch$1 implements Runnable	// class@0007dc from classes11.dex
{
    public final HashMap val$data;
    public final MtopPrefetch val$prefetch;
    public final String val$type;
    public static IpChange $ipChange;

    public void MtopPrefetch$1(MtopPrefetch p0,String p1,HashMap p2){
       this.val$prefetch = p0;
       this.val$type = p1;
       this.val$data = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = MtopPrefetch$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.val$prefetch.getCallback().onPrefetch(this.val$type, this.val$data);
          return;
       }
    }
}
