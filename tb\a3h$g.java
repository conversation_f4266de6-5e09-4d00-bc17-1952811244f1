package tb.a3h$g;
import java.lang.Runnable;
import tb.a3h;
import java.util.Map;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a3h$r0;
import java.lang.Integer;

public class a3h$g implements Runnable	// class@001e71 from classes10.dex
{
    public final Map a;
    public final a3h b;
    public static IpChange $ipChange;

    public void a3h$g(a3h p0,Map p1){
       this.b = p0;
       this.a = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$g.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.z(this.b) != null){
          a3h.z(this.b).m(this.a.get("peerId"), this.a.get("peerRole").intValue(), this.a.get("extension"));
       }
       return;
    }
}
