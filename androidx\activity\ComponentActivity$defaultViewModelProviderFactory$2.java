package androidx.activity.ComponentActivity$defaultViewModelProviderFactory$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.ComponentActivity;
import androidx.lifecycle.SavedStateViewModelFactory;
import android.app.Application;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.savedstate.SavedStateRegistryOwner;
import java.lang.Object;

public final class ComponentActivity$defaultViewModelProviderFactory$2 extends Lambda implements d1a	// class@00043c from classes.dex
{
    public final ComponentActivity this$0;

    public void ComponentActivity$defaultViewModelProviderFactory$2(ComponentActivity p0){
       this.this$0 = p0;
       super(0);
    }
    public final SavedStateViewModelFactory invoke(){
       Application application = this.this$0.getApplication();
       ComponentActivity$defaultViewModelProviderFactory$2 tthis$0 = this.this$0;
       Bundle extras = (tthis$0.getIntent() != null)? this.this$0.getIntent().getExtras(): null;
       return new SavedStateViewModelFactory(application, tthis$0, extras);
    }
    public Object invoke(){
       return this.invoke();
    }
}
