package kotlinx.serialization.PolymorphicSerializer$descriptor$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.PolymorphicSerializer;
import java.lang.Object;
import kotlinx.serialization.descriptors.a;
import tb.xz30$a;
import kotlinx.serialization.PolymorphicSerializer$descriptor$2$1;
import java.lang.String;
import tb.ob40;
import tb.g1a;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;
import tb.wyf;
import tb.sb20;

public final class PolymorphicSerializer$descriptor$2 extends Lambda implements d1a	// class@00070e from classes11.dex
{
    public final PolymorphicSerializer this$0;

    public void PolymorphicSerializer$descriptor$2(PolymorphicSerializer p0){
       this.this$0 = p0;
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final a invoke(){
       a[] uoaArray = new a[0];
       return sb20.b(SerialDescriptorsKt.c("kotlinx.serialization.Polymorphic", xz30$a.INSTANCE, uoaArray, new PolymorphicSerializer$descriptor$2$1(this.this$0)), this.this$0.getBaseClass());
    }
}
