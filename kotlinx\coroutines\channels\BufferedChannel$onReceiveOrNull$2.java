package kotlinx.coroutines.channels.BufferedChannel$onReceiveOrNull$2;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.channels.BufferedChannel;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;

public final class BufferedChannel$onReceiveOrNull$2 extends FunctionReferenceImpl implements w1a	// class@0004c6 from classes11.dex
{
    public static final BufferedChannel$onReceiveOrNull$2 INSTANCE;

    static {
       BufferedChannel$onReceiveOrNull$2.INSTANCE = new BufferedChannel$onReceiveOrNull$2();
    }
    public void BufferedChannel$onReceiveOrNull$2(){
       super(3, BufferedChannel.class, "processResultSelectReceiveOrNull", "processResultSelectReceiveOrNull\(Ljava/lang/Object;Ljava/lang/Object;\)Ljava/lang/Object;", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final Object invoke(BufferedChannel p0,Object p1,Object p2){
       return BufferedChannel.D(p0, p1, p2);
    }
}
