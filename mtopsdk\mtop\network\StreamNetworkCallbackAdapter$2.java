package mtopsdk.mtop.network.StreamNetworkCallbackAdapter$2;
import java.lang.Runnable;
import mtopsdk.mtop.network.StreamNetworkCallbackAdapter;
import tb.o9o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.w4j;
import mtopsdk.mtop.util.MtopStatistics;
import mtopsdk.mtop.util.FullTraceHelper;
import mtopsdk.mtop.domain.MtopResponse;
import tb.jpq;
import tb.f10;
import java.lang.Throwable;
import mtopsdk.common.util.TBSdkLog;

public class StreamNetworkCallbackAdapter$2 implements Runnable	// class@0007ef from classes11.dex
{
    public final StreamNetworkCallbackAdapter this$0;
    public final o9o val$response;
    public static IpChange $ipChange;

    public void StreamNetworkCallbackAdapter$2(StreamNetworkCallbackAdapter p0,o9o p1){
       this.this$0 = p0;
       this.val$response = p1;
       super();
    }
    public void run(){
       o9o h;
       int i = 1;
       IpChange $ipChange = StreamNetworkCallbackAdapter$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          w4j g = this.this$0.mtopContext.g;
          g.startCallbackTime = g.currentTimeMillis();
          FullTraceHelper.recordRspProcessStart(this.this$0.mtopContext.g);
          StreamNetworkCallbackAdapter$2 tthis$0 = this.this$0;
          StreamNetworkCallbackAdapter mtopContext = tthis$0.mtopContext;
          StreamNetworkCallbackAdapter$2 tval$respons = this.val$response;
          mtopContext.g.netStats = tval$respons.f;
          mtopContext.n = tval$respons;
          mtopContext.c = StreamNetworkCallbackAdapter.access$000(tthis$0, tval$respons);
          if ((h = this.val$response.h) != null) {
             w4j g1 = this.this$0.mtopContext.g;
             g1.streamResponse = i;
             g1.responseCount = h.a;
             g1.responseValidCount = h.b;
             g1.receivedNetDuration = h.j;
          }
          StreamNetworkCallbackAdapter$2 tthis$01 = this.this$0;
          tthis$01.filterManager.c(null, tthis$01.mtopContext);
          return;
       }
    }
}
