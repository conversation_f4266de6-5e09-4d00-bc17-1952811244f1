package androidx.appcompat.app.ToolbarActionBar$ActionMenuPresenterCallback;
import androidx.appcompat.view.menu.MenuPresenter$Callback;
import androidx.appcompat.app.ToolbarActionBar;
import java.lang.Object;
import androidx.appcompat.view.menu.MenuBuilder;
import androidx.appcompat.widget.DecorToolbar;
import android.view.Menu;
import android.view.Window$Callback;

public final class ToolbarActionBar$ActionMenuPresenterCallback implements MenuPresenter$Callback	// class@000585 from classes.dex
{
    private boolean mClosingActionMenu;
    public final ToolbarActionBar this$0;

    public void ToolbarActionBar$ActionMenuPresenterCallback(ToolbarActionBar p0){
       this.this$0 = p0;
       super();
    }
    public void onCloseMenu(MenuBuilder p0,boolean p1){
       if (this.mClosingActionMenu != null) {
          return;
       }
       this.mClosingActionMenu = true;
       this.this$0.mDecorToolbar.dismissPopupMenus();
       this.this$0.mWindowCallback.onPanelClosed(108, p0);
       this.mClosingActionMenu = false;
       return;
    }
    public boolean onOpenSubMenu(MenuBuilder p0){
       this.this$0.mWindowCallback.onMenuOpened(108, p0);
       return true;
    }
}
