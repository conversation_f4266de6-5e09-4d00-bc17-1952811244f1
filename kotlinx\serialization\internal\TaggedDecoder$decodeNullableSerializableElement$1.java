package kotlinx.serialization.internal.TaggedDecoder$decodeNullableSerializableElement$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import tb.oo40;
import tb.qh20;
import java.lang.Object;
import kotlinx.serialization.descriptors.a;

public final class TaggedDecoder$decodeNullableSerializableElement$1 extends Lambda implements d1a	// class@00074e from classes11.dex
{
    public final qh20 $deserializer;
    public final Object $previousValue;
    public final oo40 this$0;

    public void TaggedDecoder$decodeNullableSerializableElement$1(oo40 p0,qh20 p1,Object p2){
       this.$deserializer = p1;
       this.$previousValue = p2;
       super(0);
    }
    public final Object invoke(){
       if (!this.$deserializer.getDescriptor().b()) {
          throw null;
       }
       throw null;
    }
}
