package kotlinx.coroutines.m;
import kotlin.coroutines.d$b;
import kotlinx.coroutines.m$b;
import tb.jr3;
import tb.hr3;
import tb.g1a;
import tb.rr7;
import java.util.concurrent.CancellationException;
import tb.ar4;
import java.lang.Object;

public interface abstract m implements d$b	// class@0006a1 from classes11.dex
{
    public static final m$b Key;

    static {
       m.Key = m$b.$$INSTANCE;
    }
    hr3 B0(jr3 p0);
    rr7 D0(g1a p0);
    rr7 F(boolean p0,boolean p1,g1a p2);
    void a(CancellationException p0);
    Object f0(ar4 p0);
    m getParent();
    boolean isActive();
    boolean isCancelled();
    boolean start();
    CancellationException u0();
}
