package tb.a71;
import tb.t2o;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import anet.channel.statist.AlarmObject;
import anet.channel.appmonitor.IAppMonitor;
import anet.channel.appmonitor.AppMonitor;
import java.lang.Double;
import anet.channel.statist.CountObject;

public class a71	// class@00176c from classes9.dex
{
    public static IpChange $ipChange;
    public static final String POINT_ACCS_EXCEPTION;
    public static final String POINT_DELTA_TIME;
    public static final String POINT_DISPATCH;
    public static final String POINT_INTERNAL_INIT;
    public static final String POINT_MSG_ORDER;
    public static final String POINT_ORANGE_SWITCH;
    public static final String POINT_PARSE_MESSAGE;

    static {
       t2o.a(0x27800031);
    }
    public static void a(String p0,String p1,String p2,String p3){
       IpChange $ipChange = a71.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2,p3};
          $ipChange.ipc$dispatch("8725fb47", objArray);
          return;
       }else {
          AlarmObject uAlarmObject = new AlarmObject();
          uAlarmObject.module = "pm";
          uAlarmObject.modulePoint = p0;
          uAlarmObject.arg = p1;
          uAlarmObject.errorCode = p2;
          uAlarmObject.errorMsg = p3;
          uAlarmObject.isSuccess = false;
          AppMonitor.getInstance().commitAlarm(uAlarmObject);
          return;
       }
    }
    public static void b(String p0,String p1){
       IpChange $ipChange = a71.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("3fe63864", objArray);
          return;
       }else {
          AlarmObject uAlarmObject = new AlarmObject();
          uAlarmObject.module = "pm";
          uAlarmObject.modulePoint = p0;
          uAlarmObject.arg = p1;
          uAlarmObject.isSuccess = true;
          AppMonitor.getInstance().commitAlarm(uAlarmObject);
          return;
       }
    }
    public static void c(String p0,String p1,double p2){
       IpChange $ipChange = a71.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Double(p2)};
          $ipChange.ipc$dispatch("aec33a6b", objArray);
          return;
       }else {
          CountObject uCountObject = new CountObject();
          uCountObject.module = "pm";
          uCountObject.modulePoint = p0;
          uCountObject.arg = p1;
          uCountObject.value = p2;
          AppMonitor.getInstance().commitCount(uCountObject);
          return;
       }
    }
}
