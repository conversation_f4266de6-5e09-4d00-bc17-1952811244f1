package tb.a4j$c;
import tb.vav$b;
import java.lang.String;
import java.lang.Object;
import java.lang.Boolean;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import java.lang.CharSequence;
import android.text.TextUtils;

public final class a4j$c extends vav$b	// class@001ae9 from classes8.dex
{
    public static IpChange $ipChange;

    public void a4j$c(){
       super();
    }
    public static Object ipc$super(a4j$c p0,String p1,Object[] p2){
       if (p1.hashCode() == 0x68ca2184) {
          return new Boolean(super.a(p2[0], p2[1].booleanValue()));
       }
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/mytaobao/ultron/MtbUltronHelper$doInit$1");
    }
    public boolean a(String p0,boolean p1){
       int i = 1;
       IpChange $ipChange = a4j$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          return $ipChange.ipc$dispatch("68ca2184", objArray).booleanValue();
       }else {
          ckf.h(p0, "switchKey");
          if (!TextUtils.equals(p0, "enableDXPreCreateView") && !TextUtils.equals(p0, "enablePipelineCache")) {
             i = super.a(p0, p1);
          }
          return i;
       }
    }
}
