package mtopsdk.mtop.intf.MtopPrefetch$CompareResult;
import tb.t2o;
import java.lang.Object;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;

public class MtopPrefetch$CompareResult	// class@0007dd from classes11.dex
{
    public HashMap data;
    public boolean same;
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000fd);
    }
    public void MtopPrefetch$CompareResult(){
       super();
       this.same = false;
       this.data = new HashMap();
    }
    public HashMap getData(){
       IpChange $ipChange = MtopPrefetch$CompareResult.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.data;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1d90c64a", objArray);
    }
    public boolean isSame(){
       IpChange $ipChange = MtopPrefetch$CompareResult.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.same;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("45c0493b", objArray).booleanValue();
    }
    public void setData(HashMap p0){
       IpChange $ipChange = MtopPrefetch$CompareResult.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f0fd2920", objArray);
          return;
       }else {
          this.data = p0;
          return;
       }
    }
    public void setSame(boolean p0){
       IpChange $ipChange = MtopPrefetch$CompareResult.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("87cd8045", objArray);
          return;
       }else {
          this.same = p0;
          return;
       }
    }
}
