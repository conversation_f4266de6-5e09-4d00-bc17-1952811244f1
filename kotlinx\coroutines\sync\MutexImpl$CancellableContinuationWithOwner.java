package kotlinx.coroutines.sync.MutexImpl$CancellableContinuationWithOwner;
import tb.q23;
import tb.qww;
import kotlinx.coroutines.sync.MutexImpl;
import kotlinx.coroutines.c;
import java.lang.Object;
import tb.xhv;
import tb.g1a;
import tb.dv6;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import kotlinx.coroutines.sync.MutexImpl$CancellableContinuationWithOwner$resume$2;
import kotlinx.coroutines.CoroutineDispatcher;
import tb.v8p;
import kotlinx.coroutines.sync.MutexImpl$CancellableContinuationWithOwner$tryResume$token$1;
import kotlin.coroutines.d;
import java.lang.Throwable;

public final class MutexImpl$CancellableContinuationWithOwner implements q23, qww	// class@0006c2 from classes11.dex
{
    public final c a;
    public final Object b;
    public final MutexImpl c;

    public void MutexImpl$CancellableContinuationWithOwner(MutexImpl p0,c p1,Object p2){
       super();
       this.c = p0;
       this.a = p1;
       this.b = p2;
    }
    public void a(xhv p0,g1a p1){
       MutexImpl$CancellableContinuationWithOwner tc = this.c;
       MutexImpl.y().set(tc, this.b);
       this.a.l(p0, new MutexImpl$CancellableContinuationWithOwner$resume$2(tc, this));
    }
    public void b(CoroutineDispatcher p0,xhv p1){
       this.a.r(p0, p1);
    }
    public void c(v8p p0,int p1){
       this.a.c(p0, p1);
    }
    public Object d(xhv p0,Object p1,g1a p2){
       MutexImpl$CancellableContinuationWithOwner tc = this.c;
       if ((p0 = this.a.q(p0, p1, new MutexImpl$CancellableContinuationWithOwner$tryResume$token$1(tc, this))) != null) {
          MutexImpl.y().set(tc, this.b);
       }
       return p0;
    }
    public d getContext(){
       return this.a.getContext();
    }
    public void h(g1a p0){
       this.a.h(p0);
    }
    public boolean isActive(){
       return this.a.isActive();
    }
    public void l(Object p0,g1a p1){
       this.a(p0, p1);
    }
    public void p(Object p0){
       this.a.p(p0);
    }
    public Object q(Object p0,Object p1,g1a p2){
       return this.d(p0, p1, p2);
    }
    public void r(CoroutineDispatcher p0,Object p1){
       this.b(p0, p1);
    }
    public void resumeWith(Object p0){
       this.a.resumeWith(p0);
    }
    public boolean t(Throwable p0){
       return this.a.t(p0);
    }
}
