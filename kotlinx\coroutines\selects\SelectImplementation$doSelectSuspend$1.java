package kotlinx.coroutines.selects.SelectImplementation$doSelectSuspend$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import kotlinx.coroutines.selects.SelectImplementation;
import tb.ar4;
import java.lang.Object;

public final class SelectImplementation$doSelectSuspend$1 extends ContinuationImpl	// class@0006b2 from classes11.dex
{
    public Object L$0;
    public int label;
    public Object result;
    public final SelectImplementation this$0;

    public void SelectImplementation$doSelectSuspend$1(SelectImplementation p0,ar4 p1){
       this.this$0 = p0;
       super(p1);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return SelectImplementation.g(this.this$0, this);
    }
}
