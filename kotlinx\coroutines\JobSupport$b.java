package kotlinx.coroutines.JobSupport$b;
import tb.ruf;
import kotlinx.coroutines.JobSupport;
import kotlinx.coroutines.JobSupport$c;
import tb.ir3;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;

public final class JobSupport$b extends ruf	// class@0004a3 from classes11.dex
{
    public final JobSupport h;
    public final JobSupport$c i;
    public final ir3 j;
    public final Object k;

    public void JobSupport$b(JobSupport p0,JobSupport$c p1,ir3 p2,Object p3){
       super();
       this.h = p0;
       this.i = p1;
       this.j = p2;
       this.k = p3;
    }
    public Object invoke(Object p0){
       this.p(p0);
       return xhv.INSTANCE;
    }
    public void p(Throwable p0){
       JobSupport.v(this.h, this.i, this.j, this.k);
    }
}
