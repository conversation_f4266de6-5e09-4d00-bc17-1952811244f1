package kotlinx.serialization.EncodeDefault$Mode;
import java.lang.Enum;
import java.lang.String;
import tb.fg8;
import kotlin.enums.a;
import java.lang.Class;
import java.lang.Object;

public final class EncodeDefault$Mode extends Enum	// class@000704 from classes11.dex
{
    private static final fg8 $ENTRIES;
    private static final EncodeDefault$Mode[] $VALUES;
    public static final EncodeDefault$Mode ALWAYS;
    public static final EncodeDefault$Mode NEVER;

    private static final EncodeDefault$Mode[] $values(){
       EncodeDefault$Mode[] modeArray = new EncodeDefault$Mode[]{EncodeDefault$Mode.ALWAYS,EncodeDefault$Mode.NEVER};
       return modeArray;
    }
    static {
       EncodeDefault$Mode.ALWAYS = new EncodeDefault$Mode("ALWAYS", 0);
       EncodeDefault$Mode.NEVER = new EncodeDefault$Mode("NEVER", 1);
       EncodeDefault$Mode[] modeArray = EncodeDefault$Mode.$values();
       EncodeDefault$Mode.$VALUES = modeArray;
       EncodeDefault$Mode.$ENTRIES = a.a(modeArray);
    }
    private void EncodeDefault$Mode(String p0,int p1){
       super(p0, p1);
    }
    public static fg8 getEntries(){
       return EncodeDefault$Mode.$ENTRIES;
    }
    public static EncodeDefault$Mode valueOf(String p0){
       return Enum.valueOf(EncodeDefault$Mode.class, p0);
    }
    public static EncodeDefault$Mode[] values(){
       return EncodeDefault$Mode.$VALUES.clone();
    }
}
