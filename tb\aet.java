package tb.aet;
import tb.pzf;
import tb.t2o;
import java.lang.Object;
import tb.o77;
import com.taobao.uniinfra_kmp.common_utils.ContainerType;
import tb.d1a;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ckf;
import tb.sdt;
import java.lang.Runnable;
import android.os.Handler;
import java.lang.Double;
import tb.tdt;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import tb.udt;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ScheduledFuture;
import java.lang.Integer;
import tb.qqt;
import java.util.concurrent.ScheduledExecutorService;
import com.taobao.android.virtual_thread.face.VExecutors;
import android.os.Looper;
import tb.qdt;
import tb.rdt;
import java.util.concurrent.Executor;
import java.lang.IllegalStateException;

public final class aet implements pzf	// class@001ecf from classes10.dex
{
    public ExecutorService a;
    public final qqt b;
    public boolean c;
    public ContainerType d;
    public Handler e;
    public static IpChange $ipChange;

    static {
       t2o.a(0x40c00024);
       t2o.a(0x40c00016);
    }
    public void aet(){
       super();
       this.b = new o77();
       this.d = ContainerType.KSerial;
    }
    public static void e(d1a p0){
       aet.m(p0);
    }
    public static void f(d1a p0){
       aet.p(p0);
    }
    public static void g(d1a p0){
       aet.n(p0);
    }
    public static void h(d1a p0){
       aet.q(p0);
    }
    public static void i(d1a p0){
       aet.o(p0);
    }
    public static final void m(d1a p0){
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("d278d7a6", objArray);
          return;
       }else {
          p0.invoke();
          return;
       }
    }
    public static final void n(d1a p0){
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("e620ab27", objArray);
          return;
       }else {
          p0.invoke();
          return;
       }
    }
    public static final void o(d1a p0){
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("a5110602", objArray);
          return;
       }else {
          p0.invoke();
          return;
       }
    }
    public static final void p(d1a p0){
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("b8b8d983", objArray);
          return;
       }else {
          p0.invoke();
          return;
       }
    }
    public static final void q(d1a p0){
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("2e376668", objArray);
          return;
       }else {
          p0.invoke();
          return;
       }
    }
    public void a(d1a p0){
       aet te;
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3582568d", objArray);
          return;
       }else {
          ckf.g(p0, "task");
          if ((te = this.e) != null) {
             te.post(new sdt(p0));
          }
          return;
       }
    }
    public void b(double p0,d1a p1){
       aet te;
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Double(p0),p1};
          $ipChange.ipc$dispatch("fd2fde58", objArray);
          return;
       }else {
          ckf.g(p1, "task");
          if (this.j() == ContainerType.KMain) {
             if ((te = this.e) != null) {
                te.postDelayed(new tdt(p1), (long)p0);
             }
          }else {
             ExecutorService uExecutorSer = this.k();
             ckf.e(uExecutorSer, "null cannot be cast to non-null type java.util.concurrent.ScheduledThreadPoolExecutor");
             uExecutorSer.schedule(new udt(p1), (long)p0, TimeUnit.MILLISECONDS);
          }
          return;
       }
    }
    public void c(String p0,ContainerType p1,int p2){
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2)};
          $ipChange.ipc$dispatch("f3b99fd4", objArray);
          return;
       }else {
          ckf.g(p0, "label");
          ckf.g(p1, "type");
          this.d = p1;
          if (p1 == ContainerType.KSerial) {
             this.a = VExecutors.newScheduledThreadPool(1, this.b);
          }else if(p1 != ContainerType.KParallel && p1 != ContainerType.KGlobal){
             this.e = new Handler(Looper.getMainLooper());
          }else {
             this.a = VExecutors.newScheduledThreadPool(p2, this.b);
          }
          return;
       }
    }
    public void d(d1a p0){
       aet te;
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5508f64d", objArray);
          return;
       }else {
          ckf.g(p0, "task");
          if (this.j() == ContainerType.KMain) {
             if ((te = this.e) != null) {
                te.post(new qdt(p0));
             }
          }else {
             this.k().execute(new rdt(p0));
          }
          return;
       }
    }
    public final ContainerType j(){
       IpChange $ipChange = aet.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.d;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a99a8103", objArray);
    }
    public final ExecutorService k(){
       aet ta;
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("992aaecb", objArray);
       }else if(this.j() != ContainerType.KMain && this.a == null){
          throw new IllegalStateException("ExecutorService is not initialized, please invoke func createContainer");
       }else if((ta = this.a) != null){
          return ta;
       }else {
          ckf.y("executor");
          throw null;
       }
    }
    public final aet l(){
       IpChange $ipChange = aet.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("b8cc4b63", objArray);
    }
    public void shutdown(){
       aet ta;
       aet te;
       int i = 1;
       IpChange $ipChange = aet.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("1b466fdd", objArray);
          return;
       }else if(this.c == null){
          if ((ta = this.a) != null) {
             if (ta != null) {
                ta.shutdown();
             }else {
                ckf.y("executor");
                throw null;
             }
          }
          this.c = i;
       }
       if ((te = this.e) != null) {
          ckf.d(te);
          te.removeCallbacksAndMessages(null);
          this.e = null;
       }
       return;
    }
}
