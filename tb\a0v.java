package tb.a0v;
import com.alibaba.analytics.core.config.UTClientConfigMgr$a;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.alibaba.analytics.core.config.UTClientConfigMgr;
import tb.nhh;
import tb.hsq;
import java.util.Map;
import java.lang.Class;
import com.alibaba.fastjson.JSON;
import java.lang.Long;
import tb.b0v;

public class a0v implements UTClientConfigMgr$a	// class@001e53 from classes10.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3be001af);
       t2o.a(0x3be00040);
    }
    public void a0v(){
       super();
    }
    public static void init(){
       IpChange $ipChange = a0v.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("fede197", objArray);
          return;
       }else {
          UTClientConfigMgr.c().e(new a0v());
          return;
       }
    }
    public final void a(String p0){
       Object[] objArray;
       int i = 0;
       int i1 = 2;
       IpChange $ipChange = a0v.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i1];
          objArray[i] = this;
          objArray[1] = p0;
          $ipChange.ipc$dispatch("f758f4a6", objArray);
          return;
       }else {
          objArray = new Object[i1];
          objArray[i] = "parseConfig value";
          objArray[1] = p0;
          nhh.f("UTBehaviorConfigListener", objArray);
          long l = 0;
          String str = "";
          if (!hsq.f(p0)) {
             try{
                Map map = ((map = JSON.parseObject(p0, Map.class)) != null && map.size() > 0)? String.valueOf(map.get("config_dir")): str;
                if (!hsq.f(map)) {
                   l = Long.parseLong(map.substring((map.lastIndexOf("/") + 1)));
                }
                str = map;
             }catch(java.lang.Exception e0){
             }
          }
          b0v.updateConfig(str, l);
          return;
       }
    }
    public String getKey(){
       IpChange $ipChange = a0v.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "ut_event";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("16c52370", objArray);
    }
    public void onChange(String p0){
       IpChange $ipChange = a0v.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5bfd17c0", objArray);
          return;
       }else {
          this.a(p0);
          return;
       }
    }
}
