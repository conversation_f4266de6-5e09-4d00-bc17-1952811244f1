package tb.ael;
import tb.t2o;
import java.lang.Boolean;
import java.lang.Integer;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import tb.bqa;
import tb.fnn;
import java.lang.Throwable;
import com.taobao.infoflow.protocol.model.datamodel.response.IBizDataModel;
import tb.z4a;
import tb.f4b;
import com.alibaba.ability.localization.Localization;

public class ael	// class@00178d from classes9.dex
{
    public static IpChange $ipChange;
    public static final String GROUP;
    public static fnn a;
    public static IBizDataModel b;
    public static Boolean c;
    public static Integer d;

    static {
       t2o.a(0x2e0002fd);
       ael.a = null;
       ael.b = null;
       ael.c = Boolean.FALSE;
       ael.d = Integer.valueOf(0);
    }
    public static void a(){
       IpChange $ipChange = ael.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("6be74eb5", objArray);
          return;
       }else {
          ael.b = null;
          return;
       }
    }
    public static void b(){
       IpChange $ipChange = ael.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          $ipChange.ipc$dispatch("89c49781", objArray);
          return;
       }else {
          String[] stringArray = new String[]{"destroy"};
          bqa.e("OrderListPrefetchManager", stringArray);
          if (ael.f().booleanValue()) {
             return;
          }
          ael.a();
          ael.c = Boolean.FALSE;
          ael.d = Integer.valueOf(i);
          ael.d().c();
          ael.a = null;
          return;
       }
    }
    public static IBizDataModel c(){
       IpChange $ipChange = ael.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("a960c511", objArray);
       }else {
          String[] stringArray = new String[]{"getAndClearPrefetchData"};
          bqa.e("OrderListPrefetchManager", stringArray);
          IBizDataModel b = ael.b;
          ael.a();
          if (ael.c.booleanValue()) {
             ael.d().r();
             ael.c = Boolean.FALSE;
          }
          return b;
       }
    }
    public static fnn d(){
       IpChange $ipChange = ael.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("ef4f49b7", objArray);
       }else if(ael.a == null){
          ael.a = fnn.e(z4a.REC_ORDER_LIST);
       }
       return ael.a;
    }
    public static Boolean e(){
       IpChange $ipChange = ael.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("f59685ff", objArray);
       }else if(ael.b != null){
          i = true;
       }
       return Boolean.valueOf(i);
    }
    public static Boolean f(){
       IpChange $ipChange = ael.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("598a216b", objArray);
       }else if(f4b.b("disable_order_list_prefetch", i)){
          return Boolean.TRUE;
       }else if(Localization.o()){
          return Boolean.TRUE;
       }else {
          return Boolean.FALSE;
       }
    }
    public static Boolean g(){
       IpChange $ipChange = ael.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("602104bb", objArray);
       }else if(ael.d.intValue() > 1){
          i = true;
       }
       return Boolean.valueOf(i);
    }
    public static void h(){
       IpChange $ipChange = ael.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("c2b632fe", objArray);
          return;
       }else {
          String[] stringArray = new String[]{"prefetch"};
          bqa.e("OrderListPrefetchManager", stringArray);
          ael.d = Integer.valueOf((ael.d.intValue() + 1));
          if (ael.f().booleanValue()) {
             return;
          }
          if (ael.b != null) {
             ael.c = Boolean.TRUE;
             return;
          }else {
             ael.d().r();
             return;
          }
       }
    }
    public static void i(IBizDataModel p0){
       IpChange $ipChange = ael.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("6791b8c8", objArray);
          return;
       }else {
          String[] stringArray = new String[]{"savePrefetchData"};
          bqa.e("OrderListPrefetchManager", stringArray);
          ael.b = p0;
          return;
       }
    }
}
