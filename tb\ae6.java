package tb.ae6;
import com.taobao.android.dinamicx.widget.h;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import java.lang.Boolean;
import com.taobao.android.dinamicx.widget.j;
import java.lang.Number;
import android.content.Context;
import android.view.View;
import com.alibaba.fastjson.JSONObject;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Long;
import java.lang.Integer;

public class ae6 extends h	// class@001b2a from classes8.dex
{
    public static IpChange $ipChange;
    public static final long DXTBLFRAMELAYOUT_DATAINFO;
    public static final long DXTBLFRAMELAYOUT_TBLFRAMELAYOUT;

    static {
       t2o.a(0x32900226);
    }
    public void ae6(){
       super();
    }
    public static Object ipc$super(ae6 p0,String p1,Object[] p2){
       int i = 2;
       int i1 = 1;
       int i2 = 0;
       switch (p1.hashCode()){
           case 0x93d55e23:
             return super.onCreateView(p2[i2]);
           case 0x9625fd55:
             super.onBindEvent(p2[i2], p2[i1], p2[i].longValue());
             return null;
           case 0xa92acf76:
             super.onSetMapAttribute(p2[i2].longValue(), p2[i1]);
             return null;
           case 0xede516ab:
             super.onRenderView(p2[i2], p2[i1]);
             return null;
           case 0xf167cda4:
             super.onLayout(p2[i2].booleanValue(), p2[i1].intValue(), p2[i].intValue(), p2[3].intValue(), p2[4].intValue());
             return null;
           case 0x26cb6a66:
             super.onMeasure(p2[i2].intValue(), p2[i1].intValue());
             return null;
           case 0x7e58628a:
             super.onClone(p2[i2], p2[i1].booleanValue());
             return null;
           default:
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/live/home/<USER>/widget/DXTBLFrameLayoutWidgetNode");
       }
    }
    public DXWidgetNode build(Object p0){
       IpChange $ipChange = ae6.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new ae6();
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("966917b0", objArray);
    }
    public void onBindEvent(Context p0,View p1,long p2){
       IpChange $ipChange = ae6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Long(p2)};
          $ipChange.ipc$dispatch("9625fd55", objArray);
          return;
       }else {
          super.onBindEvent(p0, p1, p2);
          return;
       }
    }
    public void onClone(DXWidgetNode p0,boolean p1){
       IpChange $ipChange = ae6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("7e58628a", objArray);
          return;
       }else if(p0 != null && p0 instanceof ae6){
          super.onClone(p0, p1);
       }
       return;
    }
    public View onCreateView(Context p0){
       IpChange $ipChange = ae6.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return super.onCreateView(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("93d55e23", objArray);
    }
    public void onLayout(boolean p0,int p1,int p2,int p3,int p4){
       IpChange $ipChange = ae6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0),new Integer(p1),new Integer(p2),new Integer(p3),new Integer(p4)};
          $ipChange.ipc$dispatch("f167cda4", objArray);
          return;
       }else {
          super.onLayout(p0, p1, p2, p3, p4);
          return;
       }
    }
    public void onMeasure(int p0,int p1){
       IpChange $ipChange = ae6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),new Integer(p1)};
          $ipChange.ipc$dispatch("26cb6a66", objArray);
          return;
       }else {
          super.onMeasure(p0, p1);
          return;
       }
    }
    public void onRenderView(Context p0,View p1){
       IpChange $ipChange = ae6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("ede516ab", objArray);
          return;
       }else {
          super.onRenderView(p0, p1);
          return;
       }
    }
    public void onSetMapAttribute(long p0,JSONObject p1){
       IpChange $ipChange = ae6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0),p1};
          $ipChange.ipc$dispatch("a92acf76", objArray);
          return;
       }else if(!(p0 - 0x1c3cc60f510f582e)){
          super.onSetMapAttribute(p0, p1);
       }
       return;
    }
}
