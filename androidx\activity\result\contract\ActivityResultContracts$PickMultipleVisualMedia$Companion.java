package androidx.activity.result.contract.ActivityResultContracts$PickMultipleVisualMedia$Companion;
import java.lang.Object;
import tb.a07;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$Companion;
import tb.dc0;

public final class ActivityResultContracts$PickMultipleVisualMedia$Companion	// class@0004cf from classes.dex
{

    private void ActivityResultContracts$PickMultipleVisualMedia$Companion(){
       super();
    }
    public void ActivityResultContracts$PickMultipleVisualMedia$Companion(a07 p0){
       super();
    }
    public final int getMaxItems$activity_release(){
       int i = (ActivityResultContracts$PickVisualMedia.Companion.isSystemPickerAvailable$activity_release())? dc0.a(): Integer.MAX_VALUE;
       return i;
    }
}
