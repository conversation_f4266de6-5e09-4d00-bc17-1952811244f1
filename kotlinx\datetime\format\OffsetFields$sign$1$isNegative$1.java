package kotlinx.datetime.format.OffsetFields$sign$1$isNegative$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.r150;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Boolean;

public final class OffsetFields$sign$1$isNegative$1 extends MutablePropertyReference1Impl	// class@0006ea from classes11.dex
{
    public static final OffsetFields$sign$1$isNegative$1 INSTANCE;

    static {
       OffsetFields$sign$1$isNegative$1.INSTANCE = new OffsetFields$sign$1$isNegative$1();
    }
    public void OffsetFields$sign$1$isNegative$1(){
       super(r150.class, "isNegative", "isNegative\(\)Ljava/lang/Boolean;", 0);
    }
    public Object get(Object p0){
       return p0.j();
    }
    public void set(Object p0,Object p1){
       p0.r(p1);
    }
}
