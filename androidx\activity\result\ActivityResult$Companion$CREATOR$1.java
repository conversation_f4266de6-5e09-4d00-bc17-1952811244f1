package androidx.activity.result.ActivityResult$Companion$CREATOR$1;
import android.os.Parcelable$Creator;
import java.lang.Object;
import android.os.Parcel;
import androidx.activity.result.ActivityResult;
import java.lang.String;
import tb.ckf;

public final class ActivityResult$Companion$CREATOR$1 implements Parcelable$Creator	// class@0004a8 from classes.dex
{

    public void ActivityResult$Companion$CREATOR$1(){
       super();
    }
    public ActivityResult createFromParcel(Parcel p0){
       ckf.g(p0, "parcel");
       return new ActivityResult(p0);
    }
    public Object createFromParcel(Parcel p0){
       return this.createFromParcel(p0);
    }
    public ActivityResult[] newArray(int p0){
       ActivityResult[] uActivityRes = new ActivityResult[p0];
       return uActivityRes;
    }
    public Object[] newArray(int p0){
       return this.newArray(p0);
    }
}
