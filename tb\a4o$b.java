package tb.a4o$b;
import tb.t2o;
import java.lang.Object;
import tb.owc;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a4o$a;

public class a4o$b	// class@001837 from classes5.dex
{
    public a4o$a a;
    public owc b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x38e00499);
    }
    public void a4o$b(){
       super();
    }
    public owc a(){
       IpChange $ipChange = a4o$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d75fcc7a", objArray);
    }
    public a4o$a b(){
       IpChange $ipChange = a4o$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("bf5553b", objArray);
    }
    public void c(owc p0){
       IpChange $ipChange = a4o$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("2969c6f2", objArray);
          return;
       }else {
          this.b = p0;
          return;
       }
    }
    public void d(a4o$a p0){
       IpChange $ipChange = a4o$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("bad313fb", objArray);
          return;
       }else {
          this.a = p0;
          return;
       }
    }
}
