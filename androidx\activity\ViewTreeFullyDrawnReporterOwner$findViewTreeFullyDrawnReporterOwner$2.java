package androidx.activity.ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import android.view.View;
import androidx.activity.FullyDrawnReporterOwner;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import com.taobao.taobao.R$id;

public final class ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2 extends Lambda implements g1a	// class@000471 from classes.dex
{
    public static final ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2 INSTANCE;

    static {
       ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2.INSTANCE = new ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2();
    }
    public void ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2(){
       super(1);
    }
    public final FullyDrawnReporterOwner invoke(View p0){
       ckf.g(p0, "it");
       FullyDrawnReporterOwner tag = p0.getTag(R$id.report_drawn);
       if (tag instanceof FullyDrawnReporterOwner) {
       }else {
          tag = null;
       }
       return tag;
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
}
