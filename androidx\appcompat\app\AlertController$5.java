package androidx.appcompat.app.AlertController$5;
import java.lang.Runnable;
import androidx.appcompat.app.AlertController;
import android.view.View;
import java.lang.Object;

public class AlertController$5 implements Runnable	// class@000546 from classes.dex
{
    public final AlertController this$0;
    public final View val$bottom;
    public final View val$top;

    public void AlertController$5(AlertController p0,View p1,View p2){
       this.this$0 = p0;
       this.val$top = p1;
       this.val$bottom = p2;
       super();
    }
    public void run(){
       AlertController.manageScrollIndicators(this.this$0.mListView, this.val$top, this.val$bottom);
    }
}
