package kotlinx.coroutines.c;
import tb.q23;
import tb.vu4;
import tb.qww;
import kotlinx.coroutines.j;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicIntegerFieldUpdater;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import tb.ar4;
import tb.dv6;
import kotlin.coroutines.d;
import tb.ma0;
import tb.x7k;
import tb.w23;
import tb.rr7;
import kotlinx.coroutines.m;
import kotlin.coroutines.d$c;
import kotlin.coroutines.d$b;
import tb.gr3;
import tb.g1a;
import kotlinx.coroutines.m$a;
import tb.h30;
import tb.g23;
import tb.v8p;
import tb.fa4;
import java.lang.Throwable;
import tb.ckf;
import tb.da4;
import tb.a07;
import tb.wq7;
import tb.uq7;
import tb.tkf;
import java.lang.IllegalStateException;
import java.lang.StringBuilder;
import java.lang.Void;
import java.lang.UnsupportedOperationException;
import tb.u1r;
import tb.r23;
import java.lang.Integer;
import kotlinx.coroutines.CompletionHandlerException;
import tb.tu4;
import tb.d1a;
import java.lang.Number;
import tb.dkf;
import tb.rgq;
import java.util.concurrent.CancellationException;
import tb.o5k;
import kotlinx.coroutines.CoroutineDispatcher;
import java.lang.StackTraceElement;
import tb.h23;
import tb.ia4;
import tb.ov6;

public class c extends j implements q23, vu4, qww	// class@0004b6 from classes11.dex
{
    private final ar4 d;
    private final d e;
    private int h;
    private Object i;
    private Object j;
    private static final AtomicIntegerFieldUpdater k;
    private static final AtomicReferenceFieldUpdater m;
    private static final AtomicReferenceFieldUpdater n;

    static {
       c.k = AtomicIntegerFieldUpdater.newUpdater(c.class, "h");
       c.m = AtomicReferenceFieldUpdater.newUpdater(c.class, Object.class, "i");
       c.n = AtomicReferenceFieldUpdater.newUpdater(c.class, Object.class, "j");
    }
    public void c(ar4 p0,int p1){
       super(p1);
       this.d = p0;
       this.e = p0.getContext();
       this.h = 0x1fffffff;
       this.i = ma0.INSTANCE;
    }
    private final String D(){
       String obj = this.B();
       if (obj instanceof x7k) {
          obj = "Active";
       }else if(obj instanceof w23){
          obj = "Cancelled";
       }else {
          obj = "Completed";
       }
       return obj;
    }
    private final rr7 F(){
       d$b uob;
       if ((uob = this.getContext().get(m.Key)) == null) {
          return null;
       }
       rr7 orr7 = m$a.e(uob, true, false, new gr3(this), 2, null);
       h30.a(c.f0(), this, null, orr7);
       return orr7;
    }
    private final void G(Object p0){
       int i;
       da4 v12;
       Object obj = this;
       Object obj1 = p0;
       AtomicReferenceFieldUpdater uAtomicRefer = c.h0();
       while (true) {
          Object obj2 = uAtomicRefer.get(obj);
          if (obj2 instanceof ma0) {
             if (h30.a(c.h0(), obj, obj2, obj1)) {
                return;
             }
             continue ;
          }else if(obj2 instanceof g23){
             i = 1;
          }else {
             v3 = obj2 instanceof v8p;
          }
          Throwable throwable = null;
          if (!i) {
             if (obj2 instanceof fa4) {
                Throwable throwable1 = obj2;
                if (throwable1.b()) {
                   if (obj2 instanceof w23) {
                      if (!obj2 instanceof fa4) {
                         throwable1 = throwable;
                      }
                      if (throwable1 != null) {
                         throwable = throwable1.a;
                      }
                      if (obj1 instanceof g23) {
                         obj.k(obj1, throwable);
                         break ;
                      }else {
                         ckf.e(obj1, "null cannot be cast to non-null type kotlinx.coroutines.internal.Segment<*>");
                         obj.s(obj1, throwable);
                         break ;
                      }
                   }
                   break ;
                }else {
                   obj.P(obj1, obj2);
                   throw throwable;
                }
             }else {
                String str = "null cannot be cast to non-null type kotlinx.coroutines.CancelHandler";
                if (obj2 instanceof da4) {
                   Object obj3 = obj2;
                   if (obj3.b == null) {
                      if (obj1 instanceof v8p) {
                         return;
                      }else {
                         ckf.e(obj1, str);
                         Object obj4 = obj1;
                         if (obj3.c()) {
                            obj.k(obj4, obj3.e);
                            return;
                         }else if(h30.a(c.h0(), obj, obj2, da4.b(obj3, null, obj4, null, null, null, 29, null))){
                            return;
                         }
                      }
                   }else {
                      obj.P(obj1, obj2);
                      throw throwable;
                   }
                }else if(obj1 instanceof v8p){
                   return;
                }else {
                   ckf.e(obj1, str);
                   if (v12 = new da4(obj2, obj1, null, null, null, 28, null)) {
                      return;
                   }
                }
             }
          }else {
             obj.P(obj1, obj2);
             throw throwable;
          }
       }
       return;
    }
    private final boolean K(){
       boolean b;
       if (wq7.c(this.c)) {
          c td = this.d;
          ckf.e(td, "null cannot be cast to non-null type kotlinx.coroutines.internal.DispatchedContinuation<*>");
          if (td.s()) {
             b = true;
          label_001b :
             return b;
          }
       }
       b = false;
       goto label_001b ;
    }
    private final int M(){
       return this.h;
    }
    private static final AtomicIntegerFieldUpdater N(){
       return c.k;
    }
    private final g23 O(g1a p0){
       if (p0 instanceof g23) {
       }else {
          tkf otkf = new tkf(p0);
       }
       return p0;
    }
    private final void P(Object p0,Object p1){
       throw new IllegalStateException("It\'s prohibited to register multiple handlers, tried to register "+p0+", already has "+p1.toString());
    }
    private final void U(Object p0,int p1,g1a p2){
       AtomicReferenceFieldUpdater uAtomicRefer = c.h0();
       while (true) {
          Object obj = uAtomicRefer.get(this);
          if (obj instanceof x7k) {
             if (h30.a(c.h0(), this, obj, this.X(obj, p0, p1, p2, null))) {
                this.w();
                this.x(p1);
                return;
             }
          }else if(obj instanceof w23 && obj.c()){
             if (p2 != null) {
                this.o(p2, obj.a);
                break ;
             }
             break ;
          }else {
             this.j(p0);
             throw null;
          }
       }
       return;
    }
    public static void V(c p0,Object p1,int p2,g1a p3,int p4,Object p5){
       if (p5 != null) {
          throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: resumeImpl");
       }
       if ((p4 & 0x04)) {
          p3 = null;
       }
       p0.U(p1, p2, p3);
       return;
    }
    private final Object X(x7k p0,Object p1,int p2,g1a p3,Object p4){
       da4 p0 instanceof g23;
       if (p1 instanceof fa4) {
          String dEBUG_PROPER = dv6.DEBUG_PROPERTY_NAME;
       }else if(!wq7.b(p2) && (p4 == null || (p3 == null && (!p0 instanceof g23 && p4 == null)))){
          if (p0 instanceof g23) {
          }else {
             p0 = null;
          }
          g23 og23 = p0;
          p0 instanceof g23 = new da4(p1, og23, p3, p4, null, 16, null);
          p1 = p0 instanceof g23;
       }
       return p1;
    }
    private final boolean Z(){
       int i1;
       AtomicIntegerFieldUpdater uAtomicInteg = c.N();
       while (true) {
          int i = uAtomicInteg.get(this);
          if (i1 = i >> 29) {
             if (i1 != 1) {
                throw new IllegalStateException("Already resumed");
             }
             break ;
          }else {
             int i2 = 0x1fffffff & i;
             int i3 = 0x40000000 + i2;
             if (c.N().compareAndSet(this, i, i3)) {
                return true;
             }
             continue ;
          }
       }
       return false;
    }
    private final u1r a0(Object p0,Object p1,g1a p2){
       u1r ou1r;
       AtomicReferenceFieldUpdater uAtomicRefer = c.h0();
       while (true) {
          Object obj = uAtomicRefer.get(this);
          if (obj instanceof x7k) {
             if (h30.a(c.h0(), this, obj, this.X(obj, p0, this.c, p2, p1))) {
                this.w();
                return r23.RESUME_TOKEN;
             }
          }else {
             ou1r = null;
             if (obj instanceof da4 && (p1 != null && obj.d == p1)) {
                ou1r = r23.RESUME_TOKEN;
                break ;
             }
             break ;
          }
       }
       return ou1r;
    }
    private final boolean d0(){
       int i1;
       AtomicIntegerFieldUpdater uAtomicInteg = c.N();
       while (true) {
          int i = uAtomicInteg.get(this);
          if (i1 = i >> 29) {
             if (i1 != 2) {
                throw new IllegalStateException("Already suspended");
             }
             break ;
          }else {
             int i2 = 0x1fffffff & i;
             int i3 = 0x20000000 + i2;
             if (c.N().compareAndSet(this, i, i3)) {
                return true;
             }
             continue ;
          }
       }
       return false;
    }
    private final Object e0(){
       return this.j;
    }
    private static final AtomicReferenceFieldUpdater f0(){
       return c.n;
    }
    private final Object g0(){
       return this.i;
    }
    private static final AtomicReferenceFieldUpdater h0(){
       return c.m;
    }
    private final void i0(Object p0,AtomicIntegerFieldUpdater p1,g1a p2){
       while (true) {
          p2.invoke(Integer.valueOf(p1.get(p0)));
       }
    }
    private final Void j(Object p0){
       throw new IllegalStateException("Already resumed, but proposed with update "+p0.toString());
    }
    private final void j0(Object p0,AtomicReferenceFieldUpdater p1,g1a p2){
       while (true) {
          p2.invoke(p1.get(p0));
       }
    }
    private final void k0(int p0){
       this.h = p0;
    }
    private final void l0(Object p0){
       this.j = p0;
    }
    private final void m(g1a p0,Throwable p1){
       p0.invoke(p1);
       return;
    }
    private final void m0(Object p0){
       this.i = p0;
    }
    private final void n(d1a p0){
       p0.invoke();
       return;
    }
    private final void n0(Object p0,AtomicIntegerFieldUpdater p1,g1a p2){
       int i;
       do {
       } while (i = p1.get(p0));
       return;
    }
    private final void s(v8p p0,Throwable p1){
       int i1;
       int i = 0x1fffffff;
       if ((i1 = c.N().get(this) & i) == i) {
          throw new IllegalStateException("The index for Segment.onCancellation\(..\) is broken");
       }
       p0.o(i1, p1, this.getContext());
       return;
    }
    private final boolean u(Throwable p0){
       if (!this.K()) {
          return false;
       }
       c td = this.d;
       ckf.e(td, "null cannot be cast to non-null type kotlinx.coroutines.internal.DispatchedContinuation<*>");
       return td.u(p0);
    }
    private final void w(){
       if (!this.K()) {
          this.v();
       }
       return;
    }
    private final void x(int p0){
       if (this.Z()) {
          return;
       }
       wq7.a(this, p0);
       return;
    }
    private final rr7 z(){
       return c.f0().get(this);
    }
    public final Object A(){
       Throwable throwable;
       m om;
       boolean b = this.K();
       if (this.d0()) {
          if (this.z() == null) {
             this.F();
          }
          if (b) {
             this.S();
          }
          return dkf.d();
       }else if(b){
          this.S();
       }
       fa4 obj = this.B();
       if (obj instanceof fa4) {
          obj = obj.a;
          if (dv6.c()) {
             throwable = rgq.a(obj, this);
          }
          throw throwable;
       }else if(wq7.b(this.c) && ((om = this.getContext().get(m.Key)) != null && !om.isActive())){
          CancellationException uCancellatio = om.u0();
          this.b(obj, uCancellatio);
          if (dv6.c()) {
             uCancellatio = rgq.a(uCancellatio, this);
          }
          throw uCancellatio;
       }else {
          return this.f(obj);
       }
    }
    public final Object B(){
       return c.h0().get(this);
    }
    public void E(){
       rr7 orr7;
       if ((orr7 = this.F()) == null) {
          return;
       }
       if (this.J()) {
          orr7.dispose();
          c.f0().set(this, o5k.INSTANCE);
       }
       return;
    }
    public boolean I(){
       return this.B() instanceof w23;
    }
    public boolean J(){
       return (this.B() instanceof x7k ^ 0x01);
    }
    public String Q(){
       return "CancellableContinuation";
    }
    public final void R(Throwable p0){
       if (this.u(p0)) {
          return;
       }
       this.t(p0);
       this.w();
       return;
    }
    public final void S(){
       Throwable throwable;
       c td = this.d;
       if (td instanceof uq7) {
       }else {
          td = null;
       }
       if (td != null && (throwable = td.z(this)) != null) {
          this.v();
          this.t(throwable);
       }
       return;
    }
    public final boolean T(){
       Object obj = c.h0().get(this);
       if (obj instanceof da4 && obj.d != null) {
          this.v();
          return false;
       }else {
          c.N().set(this, 0x1fffffff);
          c.h0().set(this, ma0.INSTANCE);
          return true;
       }
    }
    public void W(CoroutineDispatcher p0,Throwable p1){
       c td = this.d;
       a07 uoa07 = null;
       if (td instanceof uq7) {
       }else {
          td = uoa07;
       }
       fa4 uofa4 = new fa4(p1, false, 2, uoa07);
       if (td != null) {
          uoa07 = td.d;
       }
       int i = (uoa07 == p0)? 4: this.c;
       c.V(this, uofa4, i, null, 4, null);
       return;
    }
    public Object Y(Object p0,Object p1){
       return this.a0(p0, p1, null);
    }
    public void b(Object p0,Throwable p1){
       da4 obj2;
       Object obj = this;
       AtomicReferenceFieldUpdater uAtomicRefer = c.h0();
       while (true) {
          Object obj1 = uAtomicRefer.get(obj);
          if (obj1 instanceof x7k) {
             throw new IllegalStateException("Not completed");
          }
          if (obj1 instanceof fa4) {
             break ;
          }else if(obj1 instanceof da4){
             obj2 = obj1;
             if (obj2.c()) {
                throw new IllegalStateException("Must be called at most once");
             }
             Throwable throwable = p1;
             if (h30.a(c.h0(), obj, obj1, da4.b(obj2, null, null, null, null, p1, 15, null))) {
                obj2.d(obj, throwable);
                return;
             }
          }else if(obj2 = new da4(obj1, null, null, null, p1, 14, null)){
             return;
          }
       }
       return;
    }
    public Object b0(Throwable p0){
       return this.a0(new fa4(p0, false, 2, null), null, null);
    }
    public void c(v8p p0,int p1){
       int i2;
       AtomicIntegerFieldUpdater uAtomicInteg = c.N();
       while (true) {
          int i = uAtomicInteg.get(this);
          int i1 = 0x1fffffff;
          if ((i2 = i & i1) != i1) {
             throw new IllegalStateException("invokeOnCancellation should be called at most once");
          }
          i1 = i >> 29;
          i1 = i1 << 29;
          i1 = i1 + p1;
          if (uAtomicInteg.compareAndSet(this, i, i1)) {
             break ;
          }
       }
       this.G(p0);
       return;
    }
    public final ar4 d(){
       return this.d;
    }
    public Throwable e(Object p0){
       if ((p0 = super.e(p0)) != null) {
          c td = this.d;
          if (dv6.c() && td instanceof vu4) {
             p0 = rgq.a(p0, td);
          }
       }else {
          p0 = null;
       }
       return p0;
    }
    public Object f(Object p0){
       if (p0 instanceof da4) {
          p0 = p0.a;
       }
       return p0;
    }
    public vu4 getCallerFrame(){
       c td = this.d;
       if (td instanceof vu4) {
       }else {
          td = null;
       }
       return td;
    }
    public d getContext(){
       return this.e;
    }
    public StackTraceElement getStackTraceElement(){
       return null;
    }
    public void h(g1a p0){
       this.G(this.O(p0));
    }
    public Object i(){
       return this.B();
    }
    public boolean isActive(){
       return this.B() instanceof x7k;
    }
    public final void k(g23 p0,Throwable p1){
       p0.f(p1);
       return;
    }
    public void l(Object p0,g1a p1){
       this.U(p0, this.c, p1);
    }
    public final void o(g1a p0,Throwable p1){
       p0.invoke(p1);
       return;
    }
    public void p(Object p0){
       this.x(this.c);
    }
    public Object q(Object p0,Object p1,g1a p2){
       return this.a0(p0, p1, p2);
    }
    public void r(CoroutineDispatcher p0,Object p1){
       c td = this.d;
       CoroutineDispatcher uCoroutineDi = null;
       if (td instanceof uq7) {
       }else {
          td = uCoroutineDi;
       }
       if (td != null) {
          uCoroutineDi = td.d;
       }
       int i = (uCoroutineDi == p0)? 4: this.c;
       c.V(this, p1, i, null, 4, null);
       return;
    }
    public void resumeWith(Object p0){
       c.V(this, ia4.b(p0, this), this.c, null, 4, null);
    }
    public boolean t(Throwable p0){
       AtomicReferenceFieldUpdater uAtomicRefer = c.h0();
       while (true) {
          Object obj = uAtomicRefer.get(this);
          boolean b = false;
          if (!obj instanceof x7k) {
             return b;
          }
          if (obj instanceof g23 || obj instanceof v8p) {
             b = true;
          }
          if (h30.a(c.h0(), this, obj, new w23(this, p0, b))) {
             Object obj1 = obj;
             if (obj1 instanceof g23) {
                this.k(obj, p0);
                break ;
             }else if(obj1 instanceof v8p){
                this.s(obj, p0);
                break ;
             }
             break ;
          }
       }
       this.w();
       this.x(this.c);
       return true;
    }
    public String toString(){
       return this.Q()+'('+ov6.c(this.d)+"\){"+this.D()+"}@"+ov6.b(this);
    }
    public final void v(){
       rr7 orr7;
       if ((orr7 = this.z()) == null) {
          return;
       }
       orr7.dispose();
       c.f0().set(this, o5k.INSTANCE);
       return;
    }
    public Throwable y(m p0){
       return p0.u0();
    }
}
