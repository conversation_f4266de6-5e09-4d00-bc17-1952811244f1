package tb.a48;
import tb.t2o;
import android.view.View;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.Class;
import com.taobao.android.dinamicx.DXRootView;
import com.taobao.android.dinamicx.view.DXRootNativeFrameLayout;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import com.taobao.android.dinamicx.DXRuntimeContext;
import android.view.ViewGroup;
import tb.ex5;
import tb.kl6;
import java.lang.Boolean;

public class a48	// class@001ae6 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1f600235);
    }
    public static View a(View p0,String p1){
       DXWidgetNode uDXWidgetNod;
       int i = 0;
       int i1 = 2;
       IpChange $ipChange = a48.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          objArray[1] = p1;
          return $ipChange.ipc$dispatch("843dbdee", objArray);
       }else if(p0 == null){
          return null;
       }else {
          Class[] uClassArray = new Class[i1];
          uClassArray[i] = DXRootView.class;
          uClassArray[1] = DXRootNativeFrameLayout.class;
          if ((p0 = a48.c(p0, uClassArray)) == null) {
             return null;
          }
          if ((uDXWidgetNod = a48.b(p0, p1)) != null) {
             return uDXWidgetNod.getDXRuntimeContext().D();
          }
          return null;
       }
    }
    public static DXWidgetNode b(View p0,String p1){
       DXWidgetNode uDXWidgetNod;
       IpChange $ipChange = a48.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("dc1d7e01", objArray);
       }else if((uDXWidgetNod = a48.d(p0)) == null){
          return null;
       }else {
          return uDXWidgetNod.queryWidgetNodeByUserId(p1);
       }
    }
    public static View c(View p0,Class[] p1){
       View view;
       int i = 0;
       IpChange $ipChange = a48.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("4e488b75", objArray);
       }else {
          int len = p1.length;
          int i1 = 0;
          while (true) {
             if (i1 < len) {
                if (p1[i1].isInstance(p0)) {
                   return p0;
                }else {
                   i1 = i1 + 1;
                }
             }else if(p0 instanceof ViewGroup){
                while (i < p0.getChildCount()) {
                   if ((view = a48.c(p0.getChildAt(i), p1)) != null) {
                      return view;
                   }
                   i = i + 1;
                }
             }
             break ;
          }
          return null;
       }
    }
    public static DXWidgetNode d(View p0){
       ex5 uoex5;
       IpChange $ipChange = a48.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("48c8d7a", objArray);
       }else if(a48.f(p0)){
          if ((uoex5 = kl6.a(p0)) == null) {
             return null;
          }
          return uoex5.B();
       }else {
          return a48.e(p0);
       }
    }
    public static DXWidgetNode e(View p0){
       IpChange $ipChange = a48.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("d8349ab6", objArray);
       }else if(p0 instanceof DXRootView){
          return p0.getFlattenWidgetNode();
       }else {
          p0 = p0.getTag(DXWidgetNode.TAG_WIDGET_NODE);
          if (p0 instanceof DXWidgetNode) {
             return p0;
          }
          return null;
       }
    }
    public static boolean f(View p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a48.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("10bb19cc", objArray).booleanValue();
       }else if(kl6.a(p0) != null){
          i = true;
       }
       return i;
    }
}
