package tb.af3$a;
import java.lang.Runnable;
import android.content.Context;
import com.taobao.taobao.internal.PayRequest;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.af3;

public final class af3$a implements Runnable	// class@00178f from classes9.dex
{
    public final Context a;
    public final PayRequest b;
    public static IpChange $ipChange;

    public void af3$a(Context p0,PayRequest p1){
       this.a = p0;
       this.b = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = af3$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          af3.c(this.a, this.b.getOrderIds(), this.b.getSuccessUrl());
          return;
       }
    }
}
