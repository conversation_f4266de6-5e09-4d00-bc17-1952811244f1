package mtopsdk.mtop.upload.FileUploadMgr$2;
import java.lang.Runnable;
import mtopsdk.mtop.upload.FileUploadMgr;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.concurrent.ConcurrentHashMap;
import android.util.Pair;
import mtopsdk.mtop.upload.DefaultFileUploadListenerWrapper;
import tb.z6e;
import tb.x6e;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import java.lang.StringBuilder;

public class FileUploadMgr$2 implements Runnable	// class@000802 from classes11.dex
{
    public final FileUploadMgr this$0;
    public final UploadFileInfo val$fileInfo;
    public static IpChange $ipChange;

    public void FileUploadMgr$2(FileUploadMgr p0,UploadFileInfo p1){
       this.this$0 = p0;
       this.val$fileInfo = p1;
       super();
    }
    public void run(){
       FileUploadMgr$2 tval$fileInf;
       IpChange $ipChange = FileUploadMgr$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if((tval$fileInf = this.val$fileInfo) != null && tval$fileInf.isValid()){
          if (FileUploadMgr.access$100(this.this$0).containsKey(this.val$fileInfo)) {
             Pair pair = FileUploadMgr.access$100(this.this$0).get(this.val$fileInfo);
             pair.first.cancel();
             FileUploadMgr.access$100(this.this$0).remove(this.val$fileInfo);
             if ((pair = pair.second) != null) {
                FileUploadMgr.access$200(this.this$0).cancelAsync(pair);
             }
             if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)) {
                TBSdkLog.d("mtopsdk.FileUploadMgr", "remove upload task succeed."+this.val$fileInfo.toString());
             }
          }
          return;
       }else {
          TBSdkLog.e("mtopsdk.FileUploadMgr", "remove upload task failed,fileInfo is invalid");
          return;
       }
    }
}
