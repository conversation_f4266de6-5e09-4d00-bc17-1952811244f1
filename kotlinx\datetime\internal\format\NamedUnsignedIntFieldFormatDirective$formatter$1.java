package kotlinx.datetime.internal.format.NamedUnsignedIntFieldFormatDirective$formatter$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.Object;
import tb.mm30;
import java.lang.Class;
import java.lang.String;
import kotlin.jvm.internal.CallableReference;

public final class NamedUnsignedIntFieldFormatDirective$formatter$1 extends FunctionReferenceImpl implements g1a	// class@0006f6 from classes11.dex
{

    public void NamedUnsignedIntFieldFormatDirective$formatter$1(Object p0){
       super(1, p0, mm30.class, "getStringValue", "getStringValue\(Ljava/lang/Object;\)Ljava/lang/String;", 0);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final String invoke(Object p0){
       mm30.a(this.receiver, p0);
       throw null;
    }
}
