package tb.a1n;
import tb.t2o;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.lang.Throwable;
import tb.wdm;
import java.lang.Long;
import java.lang.Double;
import java.lang.Float;
import tb.oe8;
import tb.a1n$a;
import java.lang.StringBuffer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.lang.Number;
import java.lang.StringBuilder;
import tb.me8;
import com.alibaba.fastjson.JSONObject;
import tb.jwf;
import com.alibaba.poplayer.trigger.a;
import java.util.Iterator;
import tb.a1n$a$a;

public class a1n	// class@001e5a from classes10.dex
{
    public static IpChange $ipChange;
    public static final String PROP_REGEX;

    static {
       t2o.a(0x31a0007a);
    }
    public static boolean a(String p0){
       IpChange $ipChange = a1n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("a11e7b37", objArray).booleanValue();
       }else if(!TextUtils.isEmpty(p0)){
          return Pattern.compile("\\{\\{\\+?store.*?\\}\\}").matcher(p0).find();
       }else {
          return 0;
       }
    }
    public static boolean b(Object p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a1n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("e244b33f", objArray).booleanValue();
       }else if(p0 == null){
          return i;
       }else if(!p0 instanceof String && (!p0 instanceof Long && (!p0 instanceof Double && !p0 instanceof Float))){
          i = true;
       }
       return i;
    }
    public static a1n$a c(oe8 p0,String p1,boolean p2){
       a1n$a uoa;
       String str = "";
       IpChange $ipChange = a1n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          return $ipChange.ipc$dispatch("e5918810", objArray);
       }else if(TextUtils.isEmpty(p1)){
          return new a1n$a(p1);
       }else if(p1.matches("^\\{\\{\\+?store.*?\\}\\}$")){
          return a1n.f(p0, p1.replaceAll("[$]", str), p2);
       }else {
          Matcher matcher = Pattern.compile("\\{\\{\\+?store.*?\\}\\}").matcher(p1);
          StringBuffer str1 = "";
          ArrayList uArrayList = new ArrayList();
          while (true) {
             if (matcher.find()) {
                if ((uoa = a1n.f(p0, matcher.group().replaceAll("[$]", str), p2)) != null && uoa.a != null) {
                   if (uoa.c()) {
                      uArrayList.addAll(uoa.b);
                   }
                   matcher.appendReplacement(str1, Matcher.quoteReplacement(String.valueOf(uoa.a)));
                }else {
                   break ;
                }
             }else {
                matcher.appendTail(str1);
                a1n$a uoa1 = new a1n$a(str1);
                uoa1.a(uArrayList);
                return uoa1;
             }
          }
          return null;
       }
    }
    public static Float d(oe8 p0,String p1){
       a1n$a uoa;
       int i = 1;
       IpChange $ipChange = a1n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("eb5f60ea", objArray);
       }else if((uoa = a1n.c(p0, p1, i)) != null && uoa.a != null){
          a1n.i(p0, uoa);
          uoa = uoa.a;
          if (uoa instanceof Number) {
             return Float.valueOf(uoa.floatValue());
          }
          return Float.valueOf(Float.parseFloat(String.valueOf(uoa)));
       }else {
          return null;
       }
    }
    public static Long e(oe8 p0,String p1){
       a1n$a uoa;
       int i = 1;
       IpChange $ipChange = a1n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("46002b90", objArray);
       }else if((uoa = a1n.c(p0, p1, i)) != null && uoa.a != null){
          a1n.i(p0, uoa);
          uoa = uoa.a;
          if (uoa instanceof Number) {
             return Long.valueOf(uoa.longValue());
          }
          return Long.valueOf(Long.parseLong(String.valueOf(uoa)));
       }else {
          return null;
       }
    }
    public static a1n$a f(oe8 p0,String p1,boolean p2){
       Object[] objArray;
       boolean b;
       Object obj;
       Float uFloat;
       a uoa1;
       int i = 2;
       int i1 = 0;
       IpChange $ipChange = a1n.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[]{p0,p1,new Boolean(p2)};
          return $ipChange.ipc$dispatch("678f8d61", objArray);
       }else if(!TextUtils.isEmpty(p1) && (p1.length() > 4 && (p1.startsWith("{{") && p1.endsWith("}}")))){
          String str = p1.substring(i, (p1.length() - i));
          if (b = str.startsWith("+")) {
             str = str.substring(1);
          }
          if (!str.startsWith("store.")) {
             return new a1n$a(p1);
          }else {
             str = str.substring(6);
             if ((obj = jwf.b(p0.j().x(), str)) == null) {
                if (p2) {
                   p0.s("PropValueProcessInvalid.group=".concat(p1), "");
                }else {
                   objArray = new Object[]{p1};
                   wdm.d("PropAnalise.parsePropResult.resultIsNull.group=%s", objArray);
                }
                return null;
             }else if(b){
                uFloat = Float.valueOf(Float.parseFloat(String.valueOf(obj)));
             }else {
                p1 = String.valueOf(obj);
             }
             if (!a1n.b(uFloat)) {
                return null;
             }else {
                a1n$a uoa = new a1n$a(uFloat);
                if (!p0.j().q()) {
                   b = str.startsWith("launchParam.");
                   if ((uoa1 = p0.j().j0()) != null && uoa1.V()) {
                      i1 = 1;
                   }
                   if (b && i1) {
                      uoa.b(str.substring((str.indexOf(46) + 1)), uFloat);
                   }
                }
                return uoa;
             }
          }
       }else {
          return new a1n$a(p1);
       }
    }
    public static String g(oe8 p0,String p1){
       IpChange $ipChange = a1n.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a1n.h(p0, p1, 1);
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("ab16d4b0", objArray);
    }
    public static String h(oe8 p0,String p1,boolean p2){
       a1n$a uoa;
       IpChange $ipChange = a1n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          return $ipChange.ipc$dispatch("5b01e63c", objArray);
       }else if((uoa = a1n.c(p0, p1, p2)) != null && uoa.a != null){
          a1n.i(p0, uoa);
          return String.valueOf(uoa.a);
       }else {
          return null;
       }
    }
    public static void i(oe8 p0,a1n$a p1){
       int i = 2;
       IpChange $ipChange = a1n.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          objArray[1] = p1;
          $ipChange.ipc$dispatch("90298406", objArray);
          return;
       }else if(p0 != null && (p1 != null && p1.c())){
          Iterator iterator = p1.b.iterator();
          while (iterator.hasNext()) {
             a1n$a$a uoa$a = iterator.next();
             p0.j().l0(uoa$a.a, uoa$a.b);
             Object[] objArray1 = new Object[i];
             objArray1[0] = uoa$a.a;
             objArray1[1] = uoa$a.b;
             wdm.d("DependencyTrack.Tracked dependency: %s = %s", objArray1);
          }
       }
       return;
    }
}
