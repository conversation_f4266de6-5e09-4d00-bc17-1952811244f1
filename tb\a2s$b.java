package tb.a2s$b;
import tb.qub;
import tb.t2o;
import java.lang.Object;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a2s;

public class a2s$b implements qub	// class@001828 from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x13c0002a);
       t2o.a(0x1c500477);
    }
    public void a2s$b(){
       super();
    }
    public DXWidgetNode build(Object p0){
       IpChange $ipChange = a2s$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new a2s();
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("966917b0", objArray);
    }
}
