package kotlinx.coroutines.channels.BroadcastChannelImpl$b;
import kotlinx.coroutines.channels.f;
import kotlinx.coroutines.channels.BroadcastChannelImpl;
import kotlinx.coroutines.channels.BufferOverflow;
import tb.g1a;
import tb.a07;
import java.lang.Throwable;
import kotlinx.coroutines.channels.ReceiveChannel;
import kotlinx.coroutines.channels.BufferedChannel;

public final class BroadcastChannelImpl$b extends f	// class@0004b8 from classes11.dex
{
    public final BroadcastChannelImpl w;

    public void BroadcastChannelImpl$b(BroadcastChannelImpl p0){
       this.w = p0;
       super(1, BufferOverflow.DROP_OLDEST, null, 4, null);
    }
    public boolean O(Throwable p0){
       return this.Q1(p0);
    }
    public boolean Q1(Throwable p0){
       BroadcastChannelImpl.N1(this.w, this);
       return super.O(p0);
    }
}
