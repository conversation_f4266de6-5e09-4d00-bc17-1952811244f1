package me.leolin.shortcutbadger.impl.VivoHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.content.Intent;

public class VivoHomeBadger implements po1	// class@000769 from classes11.dex
{

    public void VivoHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"com.vivo.launcher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       Intent intent = new Intent("launcher.action.CHANGE_APPLICATION_NOTIFICATION_NUM");
       intent.putExtra("packageName", p0.getPackageName());
       intent.putExtra("className", p1.getClassName());
       intent.putExtra("notificationNum", p2);
       p0.sendBroadcast(intent);
    }
}
