package tb.a6b$b;
import tb.h6c$a;
import tb.t2o;
import tb.a6b;
import java.lang.Object;
import tb.a6b$a;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import com.taobao.android.tbtheme.kit.ThemeFrameLayout;
import android.graphics.Canvas;
import android.view.ViewGroup;

public class a6b$b implements h6c$a	// class@001b00 from classes8.dex
{
    public final a6b a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1f6002b0);
       t2o.a(0x1f300035);
    }
    public void a6b$b(a6b p0){
       super();
       this.a = p0;
    }
    public void a6b$b(a6b p0,a6b$a p1){
       super(p0);
    }
    public void a(int p0){
       IpChange $ipChange = a6b$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("bd5e6c3d", objArray);
          return;
       }else {
          a6b$b ta = this.a;
          a6b.c(ta, a6b.a(ta), a6b.b(this.a));
          return;
       }
    }
    public void b(Canvas p0,ViewGroup p1){
       IpChange $ipChange = a6b$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("ed0a999c", objArray);
          return;
       }else {
          a6b.d(this.a, p0, p1);
          return;
       }
    }
}
