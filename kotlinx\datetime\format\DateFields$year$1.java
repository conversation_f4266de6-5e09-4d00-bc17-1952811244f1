package kotlinx.datetime.format.DateFields$year$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.jf20;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class DateFields$year$1 extends MutablePropertyReference1Impl	// class@0006d8 from classes11.dex
{
    public static final DateFields$year$1 INSTANCE;

    static {
       DateFields$year$1.INSTANCE = new DateFields$year$1();
    }
    public void DateFields$year$1(){
       super(jf20.class, "year", "getYear\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.g();
    }
    public void set(Object p0,Object p1){
       p0.n(p1);
    }
}
