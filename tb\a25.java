package tb.a25;
import tb.ypt;
import com.taobao.android.virtual_thread.face.VExecutors;
import java.lang.Object;
import java.util.concurrent.ScheduledExecutorService;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a25$c;
import tb.a25$a;
import tb.qqt;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import tb.a25$b;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ThreadPoolExecutor;
import java.lang.Boolean;
import android.os.Build$VERSION;
import tb.zsw;
import android.os.Build;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Runnable;
import tb.fs7;
import java.util.concurrent.Executor;
import java.lang.Throwable;
import java.lang.Long;
import java.util.concurrent.ScheduledFuture;

public class a25 implements ypt	// class@001844 from classes7.dex
{
    public Runnable a;
    public static IpChange $ipChange;
    public static ScheduledExecutorService b;
    public static final boolean c;

    static {
       a25.c = true;
    }
    public void a25(){
       super();
    }
    public static ScheduledExecutorService b(){
       IpChange $ipChange = a25.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("73e3b116", objArray);
       }else if(a25.b == null){
          a25 uoa25 = a25.class;
          _monitor_enter(uoa25);
          if (a25.b == null) {
             int i = 10;
             if (a25.c && a25.d()) {
                a25.b = VExecutors.newScheduledThreadPool(i, new a25$c(null));
             }else {
                a25.b = new ScheduledThreadPoolExecutor(i, new a25$b(i, "Custom_Pool"));
                if (a25.b instanceof ScheduledThreadPoolExecutor) {
                   a25.b.setKeepAliveTime(3, TimeUnit.SECONDS);
                   a25.b.allowCoreThreadTimeOut(true);
                }
             }
          }
          _monitor_exit(uoa25);
       }
       return a25.b;
    }
    public static boolean d(){
       IpChange $ipChange = a25.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("c3d24b10", objArray).booleanValue();
       }else {
          int sDK_INT = Build$VERSION.SDK_INT;
          int i1 = 23;
          if (sDK_INT >= i1 && !zsw.a()) {
             return true;
          }
          String bRAND = Build.BRAND;
          if (!TextUtils.equals(bRAND, "HUAWEI") && (!TextUtils.equals(bRAND, "HONOR") || (sDK_INT < i1 || sDK_INT > 27))) {
             return i;
          }
          return true;
       }
    }
    public void a(Runnable p0,boolean p1){
       int i = 0;
       IpChange $ipChange = a25.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("4467641a", objArray);
          return;
       }else if(p0 == null){
          return;
       }else {
          Object[] objArray1 = new Object[i];
          fs7.e("Download.CustomThreadImpl", "[execute] Custom ThreadPool", objArray1);
          a25.b().execute(p0);
          return;
       }
    }
    public void c(Runnable p0,long p1){
       IpChange $ipChange = a25.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("2f55df7d", objArray);
          return;
       }else if(this.a == null && p0 != null){
          this.a = p0;
          a25.b().schedule(new a25$a(this, p0), p1, TimeUnit.MILLISECONDS);
       }
       return;
    }
}
