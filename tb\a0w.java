package tb.a0w;
import com.taobao.vpm.adapter.IConfigAdapter;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import com.taobao.orange.OrangeConfig;
import com.taobao.vpm.utils.VPMConstant;

public class a0w implements IConfigAdapter	// class@001e54 from classes10.dex
{
    public static IpChange $ipChange;

    public void a0w(){
       super();
    }
    public String getConfig(String p0,String p1,String p2){
       IpChange $ipChange = a0w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("611c4ee3", objArray);
       }else if(TextUtils.isEmpty(p0)){
          return OrangeConfig.getInstance().getConfig(VPMConstant.VPM_ORANGE_GROUP_NAME, p1, p2);
       }else {
          return OrangeConfig.getInstance().getConfig(p0, p1, p2);
       }
    }
}
