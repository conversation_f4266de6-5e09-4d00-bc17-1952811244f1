package tb.abk;
import tb.t2o;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.Number;
import tb.f6p;
import java.lang.CharSequence;
import android.text.TextUtils;

public class abk	// class@00177d from classes9.dex
{
    public static IpChange $ipChange;
    public static final String FROM_FULL;
    public static final String FROM_SMALL;
    public static final int STRATEGY_DIRECT;
    public static final int STRATEGY_MUISE;
    public static final int STRATEGY_WEEX;

    static {
       t2o.a(0x33200207);
    }
    public static int a(String p0){
       IpChange $ipChange = abk.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("df1b973f", objArray).intValue();
       }else {
          String str = f6p.h(p0, "_wx_tpl");
          String str1 = f6p.h(p0, "_mus_tpl");
          p0 = f6p.h(p0, "wh_muise");
          if ("true".equals(f6p.h(p0, "wh_weex")) || !TextUtils.isEmpty(str)) {
             return 3;
          }
          if (TextUtils.isEmpty(str1) && TextUtils.isEmpty(p0)) {
             return 0;
          }
          return 6;
       }
    }
}
