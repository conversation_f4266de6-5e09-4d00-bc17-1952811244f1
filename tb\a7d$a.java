package tb.a7d$a;
import tb.t2o;
import tb.a7d;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.z5d;
import tb.z5d$a;
import com.taobao.themis.kernel.page.ITMSPage;

public final class a7d$a	// class@001ea2 from classes10.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3490011e);
    }
    public static void a(a7d p0){
       IpChange $ipChange = a7d$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("810c72d1", objArray);
          return;
       }else {
          ckf.g(p0, "this");
          z5d$a.a(p0);
          return;
       }
    }
    public static void b(a7d p0,ITMSPage p1){
       IpChange $ipChange = a7d$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("3c307f8b", objArray);
          return;
       }else {
          ckf.g(p0, "this");
          ckf.g(p1, "page");
          z5d$a.b(p0, p1);
          return;
       }
    }
    public static void c(a7d p0){
       IpChange $ipChange = a7d$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("b274d119", objArray);
          return;
       }else {
          ckf.g(p0, "this");
          z5d$a.c(p0);
          return;
       }
    }
}
