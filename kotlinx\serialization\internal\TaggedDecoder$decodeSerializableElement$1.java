package kotlinx.serialization.internal.TaggedDecoder$decodeSerializableElement$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import tb.oo40;
import tb.qh20;
import java.lang.Object;

public final class TaggedDecoder$decodeSerializableElement$1 extends Lambda implements d1a	// class@00074f from classes11.dex
{
    public final qh20 $deserializer;
    public final Object $previousValue;
    public final oo40 this$0;

    public void TaggedDecoder$decodeSerializableElement$1(oo40 p0,qh20 p1,Object p2){
       this.$deserializer = p1;
       this.$previousValue = p2;
       super(0);
    }
    public final Object invoke(){
       throw null;
    }
}
