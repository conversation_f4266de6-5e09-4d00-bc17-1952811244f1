package androidx.activity.result.ActivityResultCallerLauncher$resultContract$2$1;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.activity.result.ActivityResultCallerLauncher;
import android.content.Context;
import java.lang.Object;
import android.content.Intent;
import tb.xhv;
import java.lang.String;
import tb.ckf;

public final class ActivityResultCallerLauncher$resultContract$2$1 extends ActivityResultContract	// class@0004ae from classes.dex
{
    public final ActivityResultCallerLauncher this$0;

    public void ActivityResultCallerLauncher$resultContract$2$1(ActivityResultCallerLauncher p0){
       this.this$0 = p0;
       super();
    }
    public Intent createIntent(Context p0,Object p1){
       return this.createIntent(p0, p1);
    }
    public Intent createIntent(Context p0,xhv p1){
       ckf.g(p0, "context");
       ckf.g(p1, "input");
       return this.this$0.getCallerContract().createIntent(p0, this.this$0.getCallerInput());
    }
    public Object parseResult(int p0,Intent p1){
       return this.this$0.getCallerContract().parseResult(p0, p1);
    }
}
