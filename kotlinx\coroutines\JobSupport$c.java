package kotlinx.coroutines.JobSupport$c;
import tb.yse;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicIntegerFieldUpdater;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import tb.f5k;
import java.lang.Throwable;
import java.util.ArrayList;
import java.lang.IllegalStateException;
import java.lang.StringBuilder;
import tb.suf;
import java.util.List;
import tb.ckf;

public final class JobSupport$c implements yse	// class@0004a4 from classes11.dex
{
    public final f5k a;
    public int b;
    public Object c;
    public Object d;
    public static final AtomicIntegerFieldUpdater e;
    public static final AtomicReferenceFieldUpdater f;
    public static final AtomicReferenceFieldUpdater g;

    static {
       JobSupport$c.e = AtomicIntegerFieldUpdater.newUpdater(JobSupport$c.class, "b");
       JobSupport$c.f = AtomicReferenceFieldUpdater.newUpdater(JobSupport$c.class, Object.class, "c");
       JobSupport$c.g = AtomicReferenceFieldUpdater.newUpdater(JobSupport$c.class, Object.class, "d");
    }
    public void JobSupport$c(f5k p0,boolean p1,Throwable p2){
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
    }
    public static final AtomicReferenceFieldUpdater l(){
       return JobSupport$c.g;
    }
    public static final AtomicIntegerFieldUpdater m(){
       return JobSupport$c.e;
    }
    public static final AtomicReferenceFieldUpdater n(){
       return JobSupport$c.f;
    }
    public final void a(Throwable p0){
       Throwable throwable;
       Object obj;
       if ((throwable = this.d()) == null) {
          this.k(p0);
          return;
       }else if(p0 == throwable){
          return;
       }else if((obj = this.c()) == null){
          this.j(p0);
       }else if(obj instanceof Throwable){
          if (p0 == obj) {
             return;
          }else {
             ArrayList uArrayList = this.b();
             uArrayList.add(obj);
             uArrayList.add(p0);
             this.j(uArrayList);
          }
       }else if(obj instanceof ArrayList){
          obj.add(p0);
       }else {
          throw new IllegalStateException("State is "+obj.toString());
       }
       return;
    }
    public final ArrayList b(){
       return new ArrayList(4);
    }
    public final Object c(){
       return JobSupport$c.l().get(this);
    }
    public final Throwable d(){
       return JobSupport$c.n().get(this);
    }
    public final boolean e(){
       boolean b = (this.d() != null)? true: false;
       return b;
    }
    public final boolean f(){
       boolean b = (JobSupport$c.m().get(this))? true: false;
       return b;
    }
    public final boolean g(){
       boolean b = (this.c() == suf.d)? true: false;
       return b;
    }
    public f5k getList(){
       return this.a;
    }
    public final List h(Throwable p0){
       ArrayList uArrayList;
       Throwable throwable;
       if ((uArrayList = this.c()) == null) {
          uArrayList = this.b();
       }else if(uArrayList instanceof Throwable){
          ArrayList uArrayList1 = this.b();
          uArrayList1.add(uArrayList);
          uArrayList = uArrayList1;
       }else if(uArrayList instanceof ArrayList){
       }else {
          throw new IllegalStateException("State is "+uArrayList.toString());
       }
       if ((throwable = this.d()) != null) {
          uArrayList.add(0, throwable);
       }
       if (p0 != null && !ckf.b(p0, throwable)) {
          uArrayList.add(p0);
       }
       this.j(suf.d);
       return uArrayList;
    }
    public final void i(boolean p0){
       JobSupport$c.m().set(this, p0);
    }
    public boolean isActive(){
       boolean b = (this.d() == null)? true: false;
       return b;
    }
    public final void j(Object p0){
       JobSupport$c.l().set(this, p0);
    }
    public final void k(Throwable p0){
       JobSupport$c.n().set(this, p0);
    }
    public String toString(){
       return "Finishing[cancelling="+this.e()+", completing="+this.f()+", rootCause="+this.d()+", exceptions="+this.c()+", list="+this.getList()+']';
    }
}
