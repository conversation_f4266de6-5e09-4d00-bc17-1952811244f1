package kotlinx.coroutines.stream.StreamFlow$collect$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import kotlinx.coroutines.stream.StreamFlow;
import tb.ar4;
import java.lang.Object;
import tb.sp9;

public final class StreamFlow$collect$1 extends ContinuationImpl	// class@0006be from classes11.dex
{
    public Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;
    public Object result;
    public final StreamFlow this$0;

    public void StreamFlow$collect$1(StreamFlow p0,ar4 p1){
       this.this$0 = p0;
       super(p1);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return this.this$0.a(null, this);
    }
}
