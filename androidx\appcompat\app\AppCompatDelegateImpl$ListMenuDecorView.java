package androidx.appcompat.app.AppCompatDelegateImpl$ListMenuDecorView;
import androidx.appcompat.widget.ContentFrameLayout;
import androidx.appcompat.app.AppCompatDelegateImpl;
import android.content.Context;
import android.view.View;
import android.view.KeyEvent;
import android.widget.FrameLayout;
import android.view.MotionEvent;
import android.graphics.drawable.Drawable;
import androidx.appcompat.content.res.AppCompatResources;

public class AppCompatDelegateImpl$ListMenuDecorView extends ContentFrameLayout	// class@000573 from classes.dex
{
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$ListMenuDecorView(AppCompatDelegateImpl p0,Context p1){
       this.this$0 = p0;
       super(p1);
    }
    private boolean isOutOfBounds(int p0,int p1){
       int i = -5;
       boolean b = (p0 >= i && (p1 >= i && (p0 <= (this.getWidth() + 5) && p1 <= (this.getHeight() + 5))))? false: true;
       return b;
    }
    public boolean dispatchKeyEvent(KeyEvent p0){
       boolean b = (!this.this$0.dispatchKeyEvent(p0) && !super.dispatchKeyEvent(p0))? false: true;
       return b;
    }
    public boolean onInterceptTouchEvent(MotionEvent p0){
       if (p0.getAction() || !this.isOutOfBounds((int)p0.getX(), (int)p0.getY())) {
          return super.onInterceptTouchEvent(p0);
       }
       this.this$0.closePanel(0);
       return true;
    }
    public void setBackgroundResource(int p0){
       this.setBackgroundDrawable(AppCompatResources.getDrawable(this.getContext(), p0));
    }
}
