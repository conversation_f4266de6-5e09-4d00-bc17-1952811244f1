package kotlinx.coroutines.selects.WhileSelectKt$whileSelect$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import tb.g1a;
import kotlinx.coroutines.selects.WhileSelectKt;

public final class WhileSelectKt$whileSelect$1 extends ContinuationImpl	// class@0006b9 from classes11.dex
{
    public Object L$0;
    public int label;
    public Object result;

    public void WhileSelectKt$whileSelect$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return WhileSelectKt.a(null, this);
    }
}
