package tb.a7n$a;
import tb.obk;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.orange.OrangeConfig;
import android.content.SharedPreferences;
import tb.a7n;
import android.content.SharedPreferences$Editor;
import tb.w6n;

public final class a7n$a implements obk	// class@00184c from classes5.dex
{
    public static IpChange $ipChange;

    public void a7n$a(){
       super();
    }
    public void onConfigUpdate(String p0,Map p1){
       IpChange $ipChange = a7n$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4f2fc4ea", objArray);
          return;
       }else {
          String str = "tb_quality_android";
          if (str.equals(p0)) {
             String config = OrangeConfig.getInstance().getConfig(str, "switch_on", "false");
             a7n.a().edit().putString("switch_on", config).apply();
             String[] stringArray = new String[]{"orange config updated, orange update from network",";namespace:","tb_quality_android","; KEY_SWITCH:",config,";"};
             w6n.a("TBQualitySwitches", stringArray);
          }
          return;
       }
    }
}
