package kotlinx.serialization.MissingFieldException;
import kotlinx.serialization.SerializationException;
import java.lang.String;
import java.lang.Object;
import tb.ckf;
import java.util.List;
import tb.xz3;
import java.lang.StringBuilder;
import java.lang.Throwable;

public final class MissingFieldException extends SerializationException	// class@00070b from classes11.dex
{
    private final List missingFields;

    public void MissingFieldException(String p0){
       ckf.g(p0, "missingField");
       super(xz3.e(p0), "Field \'"+p0+"\' is required, but it was missing", null);
    }
    public void MissingFieldException(String p0,String p1){
       ckf.g(p0, "missingField");
       ckf.g(p1, "serialName");
       super(xz3.e(p0), "Field \'"+p0+"\' is required for type with serial name \'"+p1+"\', but it was missing", null);
    }
    public void MissingFieldException(List p0,String p1){
       ckf.g(p0, "missingFields");
       ckf.g(p1, "serialName");
       p1 = (p0.size() == 1)? "Field \'"+p0.get(0)+"\' is required for type with serial name \'"+p1+"\', but it was missing": "Fields "+p0+" are required for type with serial name \'"+p1+"\', but they were missing";
       super(p0, p1, null);
       return;
    }
    public void MissingFieldException(List p0,String p1,Throwable p2){
       ckf.g(p0, "missingFields");
       super(p1, p2);
       this.missingFields = p0;
    }
    public final List getMissingFields(){
       return this.missingFields;
    }
}
