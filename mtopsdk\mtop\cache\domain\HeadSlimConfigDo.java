package mtopsdk.mtop.cache.domain.HeadSlimConfigDo;
import java.io.Serializable;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import mtopsdk.common.util.SerialLruCache;
import java.lang.System;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.util.Objects;
import java.lang.StringBuilder;
import java.lang.Number;
import java.lang.Long;
import java.util.List;
import java.util.AbstractMap;

public class HeadSlimConfigDo implements Serializable	// class@000793 from classes11.dex
{
    public List cookie;
    public List errCode;
    public List headers;
    public String policy;
    public String sceneType;
    public long ttl;
    public long updateTime;
    public long v;
    public SerialLruCache whiteHitLruCache;
    public static IpChange $ipChange;
    private static final long serialVersionUID;

    static {
       t2o.a(0x253000a2);
    }
    public void HeadSlimConfigDo(String p0){
       super();
       this.v = -1;
       this.sceneType = "api";
       this.policy = "disable";
       this.whiteHitLruCache = new SerialLruCache(256);
       this.updateTime = System.currentTimeMillis() / 1000;
       this.policy = "disable";
       this.sceneType = p0;
    }
    public boolean equals(Object p0){
       int i = 1;
       IpChange $ipChange = HeadSlimConfigDo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6c2a9726", objArray).booleanValue();
       }else if(this == p0){
          return i;
       }else if(!p0 instanceof HeadSlimConfigDo){
          return 0;
       }else if(!(this.v - p0.v) && (!(this.ttl - p0.ttl) && (Objects.equals(this.sceneType, p0.sceneType) && (Objects.equals(this.policy, p0.policy) && (Objects.equals(this.errCode, p0.errCode) && (Objects.equals(this.headers, p0.headers) && Objects.equals(this.cookie, p0.cookie))))))){
          i = false;
       }
       return i;
    }
    public String getRequestXMSlimHead(){
       IpChange $ipChange = HeadSlimConfigDo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.v+";"+this.sceneType+";"+this.policy+";";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("48fd5dee", objArray);
    }
    public int hashCode(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = HeadSlimConfigDo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("53a9ab15", objArray).intValue();
       }else {
          Object[] objArray1 = new Object[]{Long.valueOf(this.v),Long.valueOf(this.ttl),this.sceneType,this.policy,this.errCode,this.headers,this.cookie};
          return Objects.hash(objArray1);
       }
    }
    public boolean isCookiesValid(){
       HeadSlimConfigDo tcookie;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = HeadSlimConfigDo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("53abc202", objArray).booleanValue();
       }else if((tcookie = this.cookie) != null && !tcookie.isEmpty()){
          i = true;
       }
       return i;
    }
    public boolean isExpired(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = HeadSlimConfigDo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("a65eada6", objArray).booleanValue();
       }else if(((System.currentTimeMillis() / 1000) - (this.ttl + this.updateTime)) > 0){
          i = true;
       }
       return i;
    }
    public boolean isHeadsValid(){
       HeadSlimConfigDo theaders;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = HeadSlimConfigDo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("884e24fe", objArray).booleanValue();
       }else if((theaders = this.headers) != null && !theaders.isEmpty()){
          i = true;
       }
       return i;
    }
    public boolean isValid(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = HeadSlimConfigDo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("3fef87d", objArray).booleanValue();
       }else if(!this.isCookiesValid() && !this.isHeadsValid()){
          i = true;
       }
       return i;
    }
    public String toString(){
       IpChange $ipChange = HeadSlimConfigDo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(64)+"HeadSlim [ v="+this.v+", ttl="+this.ttl+", scene="+this.sceneType+", errCode="+this.errCode+", headers="+this.headers+", cookie="+this.cookie+", LruCacheLen="+this.whiteHitLruCache.size()+"]";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
