package androidx.appcompat.app.AppCompatDelegateImpl$PanelMenuPresenterCallback;
import androidx.appcompat.view.menu.MenuPresenter$Callback;
import androidx.appcompat.app.AppCompatDelegateImpl;
import java.lang.Object;
import androidx.appcompat.view.menu.MenuBuilder;
import android.view.Menu;
import androidx.appcompat.app.AppCompatDelegateImpl$PanelFeatureState;
import android.view.Window$Callback;

public final class AppCompatDelegateImpl$PanelMenuPresenterCallback implements MenuPresenter$Callback	// class@000577 from classes.dex
{
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$PanelMenuPresenterCallback(AppCompatDelegateImpl p0){
       this.this$0 = p0;
       super();
    }
    public void onCloseMenu(MenuBuilder p0,boolean p1){
       MenuBuilder rootMenu;
       AppCompatDelegateImpl$PanelFeatureState panelFeature;
       int i = ((rootMenu = p0.getRootMenu()) != p0)? 1: 0;
       AppCompatDelegateImpl$PanelMenuPresenterCallback tthis$0 = this.this$0;
       if (i) {
          p0 = rootMenu;
       }
       if ((panelFeature = tthis$0.findMenuPanel(p0)) != null) {
          if (i) {
             this.this$0.callOnPanelClosed(panelFeature.featureId, panelFeature, rootMenu);
             this.this$0.closePanel(panelFeature, true);
          }else {
             this.this$0.closePanel(panelFeature, p1);
          }
       }
       return;
    }
    public boolean onOpenSubMenu(MenuBuilder p0){
       Window$Callback windowCallba;
       if (p0 == p0.getRootMenu()) {
          AppCompatDelegateImpl$PanelMenuPresenterCallback tthis$0 = this.this$0;
          if (tthis$0.mHasActionBar != null && ((windowCallba = tthis$0.getWindowCallback()) != null && this.this$0.mDestroyed == null)) {
             windowCallba.onMenuOpened(108, p0);
          }
       }
       return true;
    }
}
