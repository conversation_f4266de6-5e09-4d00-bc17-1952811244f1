package mtopsdk.common.util.SerialLruCache;
import java.util.LinkedHashMap;
import tb.t2o;
import java.util.Map;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.util.Map$Entry;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.util.AbstractMap;

public class SerialLruCache extends LinkedHashMap	// class@000775 from classes11.dex
{
    private int cacheSize;
    public static IpChange $ipChange;
    private static final int DEFAULT_CACHE_SIZE;
    private static final long serialVersionUID;

    static {
       t2o.a(0x25300057);
    }
    public void SerialLruCache(){
       super(256);
    }
    public void SerialLruCache(int p0){
       super((p0 + 1), 1.00f, true);
       this.cacheSize = p0;
    }
    public void SerialLruCache(LinkedHashMap p0,int p1){
       super(p0);
       this.cacheSize = p1;
    }
    public static Object ipc$super(SerialLruCache p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/common/util/SerialLruCache");
    }
    public boolean removeEldestEntry(Map$Entry p0){
       int i = 1;
       IpChange $ipChange = SerialLruCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6471a6cf", objArray).booleanValue();
       }else if(this.size() > this.cacheSize){
          i = false;
       }
       return i;
    }
}
