package tb.aeb;
import android.content.Context;
import android.view.ViewGroup;
import java.util.Map;
import java.lang.String;
import android.view.View;
import com.taobao.taolive.sdk.adapter.network.INetDataObject;
import tb.b3d;
import com.alibaba.fastjson.JSONObject;

public interface abstract aeb	// class@001ec9 from classes10.dex
{

    ViewGroup A(Context p0);
    boolean a(Context p0);
    boolean b(Context p0);
    Map f();
    String p();
    View q(String p0);
    INetDataObject r();
    void s(Context p0,String p1,boolean p2);
    boolean t();
    int u(Context p0);
    void v(b3d p0);
    void w(JSONObject p0);
    void x(b3d p0);
    void y(String p0);
    void z(Context p0,String p1);
}
