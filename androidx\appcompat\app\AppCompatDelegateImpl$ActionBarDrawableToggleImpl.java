package androidx.appcompat.app.AppCompatDelegateImpl$ActionBarDrawableToggleImpl;
import androidx.appcompat.app.ActionBarDrawerToggle$Delegate;
import androidx.appcompat.app.AppCompatDelegateImpl;
import java.lang.Object;
import android.content.Context;
import android.graphics.drawable.Drawable;
import com.taobao.taobao.R$attr;
import android.util.AttributeSet;
import androidx.appcompat.widget.TintTypedArray;
import androidx.appcompat.app.ActionBar;

public class AppCompatDelegateImpl$ActionBarDrawableToggleImpl implements ActionBarDrawerToggle$Delegate	// class@000565 from classes.dex
{
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$ActionBarDrawableToggleImpl(AppCompatDelegateImpl p0){
       this.this$0 = p0;
       super();
    }
    public Context getActionBarThemedContext(){
       return this.this$0.getActionBarThemedContext();
    }
    public Drawable getThemeUpIndicator(){
       int[] ointArray = new int[]{R$attr.homeAsUpIndicator};
       TintTypedArray tintTypedArr = TintTypedArray.obtainStyledAttributes(this.getActionBarThemedContext(), null, ointArray);
       tintTypedArr.recycle();
       return tintTypedArr.getDrawable(0);
    }
    public boolean isNavigationVisible(){
       ActionBar supportActio;
       boolean b = ((supportActio = this.this$0.getSupportActionBar()) != null && ((supportActio.getDisplayOptions() & 0x04)))? true: false;
       return b;
    }
    public void setActionBarDescription(int p0){
       ActionBar supportActio;
       if ((supportActio = this.this$0.getSupportActionBar()) != null) {
          supportActio.setHomeActionContentDescription(p0);
       }
       return;
    }
    public void setActionBarUpIndicator(Drawable p0,int p1){
       ActionBar supportActio;
       if ((supportActio = this.this$0.getSupportActionBar()) != null) {
          supportActio.setHomeAsUpIndicator(p0);
          supportActio.setHomeActionContentDescription(p1);
       }
       return;
    }
}
