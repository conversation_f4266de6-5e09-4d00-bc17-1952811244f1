package tb.a4o$a;
import tb.t2o;
import java.lang.Object;
import com.taobao.android.detail.ttdetail.request.MtopInfo;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class a4o$a	// class@001836 from classes5.dex
{
    public String a;
    public MtopInfo b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x38e00498);
    }
    public void a4o$a(){
       super();
    }
    public MtopInfo a(){
       IpChange $ipChange = a4o$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9535a51b", objArray);
    }
    public String b(){
       IpChange $ipChange = a4o$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("71829d11", objArray);
    }
    public void c(MtopInfo p0){
       IpChange $ipChange = a4o$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("72575c19", objArray);
          return;
       }else {
          this.b = p0;
          return;
       }
    }
    public void d(String p0){
       IpChange $ipChange = a4o$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5e7af285", objArray);
          return;
       }else {
          this.a = p0;
          return;
       }
    }
}
