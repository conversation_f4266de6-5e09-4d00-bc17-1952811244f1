package tb.a8x;
import tb.opc;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.weex_framework.a;
import java.lang.Number;
import com.taobao.android.weex_framework.MUSDKInstance;
import java.lang.Boolean;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.tao.log.TLog;
import java.lang.Integer;
import com.alibaba.fastjson.JSONObject;
import tb.s32;

public final class a8x extends opc	// class@001b0d from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2ef0005e);
    }
    public void a8x(){
       super();
    }
    public static Object ipc$super(a8x p0,String p1,Object[] p2){
       int i = 3;
       int i1 = 2;
       switch (p1.hashCode()){
           case 0x8c41b6de:
             super.onJSException(p2[0], p2[1].intValue(), p2[i1]);
             return null;
           case 0x963c382f:
             super.onRefreshSuccess(p2[0]);
             return null;
           case 0xcc0861e4:
             super.onRefreshFailed(p2[0], p2[1].intValue(), p2[i1], p2[i].booleanValue());
             return null;
           case 0xcc0cbe2b:
             super.onRenderFailed(p2[0], p2[1].intValue(), p2[i1], p2[i].booleanValue());
             return null;
           case 0x27a2635b:
             super.onPrepareSuccess(p2[0]);
             return null;
           case 0x2b2aeb48:
             super.onRenderSuccess(p2[0]);
             return null;
           case 0x32f7f995:
             super.onDestroyed(p2[0]);
             return null;
           case 0x3ba6b641:
             p0.a(p2[0], p2[1].intValue(), p2[i1]);
             return null;
           default:
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/mytaobao/basement/weex/WeexRenderMonitor");
       }
    }
    public void onDestroyed(MUSDKInstance p0){
       IpChange $ipChange = a8x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("32f7f995", objArray);
          return;
       }else {
          super.onDestroyed(p0);
          TLog.loge("basementWeexLog", "weex onDestroyed");
          return;
       }
    }
    public void onJSException(a p0,int p1,String p2){
       IpChange $ipChange = a8x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("8c41b6de", objArray);
          return;
       }else {
          super.onJSException(p0, p1, p2);
          TLog.loge("basementWeexLog", "weex onJSException: type="+p1+",errorMsg="+p2);
          JSONObject jSONObject = new JSONObject();
          jSONObject.put("errorType", "onJSException");
          jSONObject.put("type", Integer.valueOf(p1));
          jSONObject.put("errorMsg", p2);
          s32.c().g("weexLoadFailed", jSONObject, 1.00f, false);
          return;
       }
    }
    public void onPrepareSuccess(a p0){
       IpChange $ipChange = a8x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("27a2635b", objArray);
          return;
       }else {
          super.onPrepareSuccess(p0);
          TLog.loge("basementWeexLog", "weex onPrepareSuccess");
          return;
       }
    }
    public void onRefreshFailed(a p0,int p1,String p2,boolean p3){
       IpChange $ipChange = a8x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,new Boolean(p3)};
          $ipChange.ipc$dispatch("cc0861e4", objArray);
          return;
       }else {
          super.onRefreshFailed(p0, p1, p2, p3);
          TLog.loge("basementWeexLog", "weex onRefreshFailed: type="+p1+",errorMsg="+p2+",isFatal="+p3);
          return;
       }
    }
    public void onRefreshSuccess(a p0){
       IpChange $ipChange = a8x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("963c382f", objArray);
          return;
       }else {
          super.onRefreshSuccess(p0);
          TLog.loge("basementWeexLog", "weex onRefreshSuccess");
          s32.c().k("weexLoad");
          return;
       }
    }
    public void onRenderFailed(a p0,int p1,String p2,boolean p3){
       IpChange $ipChange = a8x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,new Boolean(p3)};
          $ipChange.ipc$dispatch("cc0cbe2b", objArray);
          return;
       }else {
          super.onRenderFailed(p0, p1, p2, p3);
          String str = "weex onRenderFailed: type="+p1+",errorMsg="+p2+",isFatal="+p3;
          TLog.loge("basementWeexLog", str);
          s32.c().j("weexLoad", "onRenderFailed", str);
          JSONObject jSONObject = new JSONObject();
          jSONObject.put("errorType", "onRenderFailed");
          jSONObject.put("type", Integer.valueOf(p1));
          jSONObject.put("errorMsg", p2);
          jSONObject.put("isFatal", Boolean.valueOf(p3));
          s32.c().g("weexLoadFailed", jSONObject, 1.00f, false);
          return;
       }
    }
    public void onRenderSuccess(a p0){
       IpChange $ipChange = a8x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("2b2aeb48", objArray);
          return;
       }else {
          super.onRenderSuccess(p0);
          TLog.loge("basementWeexLog", "weex onRenderSuccess");
          return;
       }
    }
}
