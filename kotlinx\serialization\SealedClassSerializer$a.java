package kotlinx.serialization.SealedClassSerializer$a;
import java.lang.Iterable;
import java.lang.Object;
import java.util.Map$Entry;
import tb.x530;
import kotlinx.serialization.descriptors.a;
import tb.qb40;
import java.lang.String;
import java.util.Iterator;

public final class SealedClassSerializer$a	// class@000711 from classes11.dex
{
    public final Iterable a;

    public void SealedClassSerializer$a(Iterable p0){
       super();
       this.a = p0;
    }
    public Object a(Object p0){
       return p0.getValue().getDescriptor().f();
    }
    public Iterator b(){
       return this.a.iterator();
    }
}
