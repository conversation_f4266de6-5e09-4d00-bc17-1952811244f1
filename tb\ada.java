package tb.ada;
import android.app.Application;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.Class;
import java.lang.reflect.Method;
import java.lang.reflect.Field;
import java.lang.reflect.AccessibleObject;
import java.lang.Throwable;

public class ada	// class@0016f5 from classes6.dex
{
    public static IpChange $ipChange;
    public static Application a;

    public static synchronized Application a(){
       _monitor_enter(ada.class);
       IpChange $ipChange = ada.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          _monitor_exit(ada.class);
          return $ipChange.ipc$dispatch("5749e470", objArray);
       }else if(ada.a == null){
          ada.a = ada.b();
       }
       _monitor_exit(ada.class);
       return ada.a;
    }
    public static Application b(){
       Object obj = null;
       try{
          Class uClass = Class.forName("android.app.ActivityThread");
          Class[] uClassArray = new Class[0];
          Method declaredMeth = uClass.getDeclaredMethod("currentActivityThread", uClassArray);
          Field declaredFiel = uClass.getDeclaredField("mInitialApplication");
          declaredFiel.setAccessible(true);
          Object[] objArray = new Object[0];
          return declaredFiel.get(declaredMeth.invoke(obj, objArray));
       }catch(java.lang.Exception e1){
          e1.printStackTrace();
          return obj;
       }
    }
}
