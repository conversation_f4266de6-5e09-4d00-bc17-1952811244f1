package zoloz.ap.com.toolkit.R$styleable;
import com.taobao.taobao.R$styleable;
import java.lang.Object;

public final class R$styleable	// class@001105 from classes11.dex
{
    public static int[] TitleBar;
    public static int TitleBar_z_background;
    public static int TitleBar_z_bg;
    public static int TitleBar_z_custom;
    public static int TitleBar_z_left_src;
    public static int TitleBar_z_position;
    public static int TitleBar_z_separate_visibility;
    public static int TitleBar_z_text;
    public static int TitleBar_z_text_color;

    static {
       R$styleable.TitleBar = R$styleable.TitleBar;
       R$styleable.TitleBar_z_background = R$styleable.TitleBar_z_background;
       R$styleable.TitleBar_z_bg = R$styleable.TitleBar_z_bg;
       R$styleable.TitleBar_z_custom = R$styleable.TitleBar_z_custom;
       R$styleable.TitleBar_z_left_src = R$styleable.TitleBar_z_left_src;
       R$styleable.TitleBar_z_position = R$styleable.TitleBar_z_position;
       R$styleable.TitleBar_z_separate_visibility = R$styleable.TitleBar_z_separate_visibility;
       R$styleable.TitleBar_z_text = R$styleable.TitleBar_z_text;
       R$styleable.TitleBar_z_text_color = R$styleable.TitleBar_z_text_color;
    }
    public void R$styleable(){
       super();
    }
}
