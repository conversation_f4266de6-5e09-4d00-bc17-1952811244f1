package zoloz.ap.com.toolkit.R$attr;
import com.taobao.taobao.R$attr;
import java.lang.Object;

public final class R$attr	// class@0010fc from classes11.dex
{
    public static int z_background;
    public static int z_bg;
    public static int z_custom;
    public static int z_left_src;
    public static int z_position;
    public static int z_separate_visibility;
    public static int z_text;
    public static int z_text_color;

    static {
       R$attr.z_background = R$attr.z_background;
       R$attr.z_bg = R$attr.z_bg;
       R$attr.z_custom = R$attr.z_custom;
       R$attr.z_left_src = R$attr.z_left_src;
       R$attr.z_position = R$attr.z_position;
       R$attr.z_separate_visibility = R$attr.z_separate_visibility;
       R$attr.z_text = R$attr.z_text;
       R$attr.z_text_color = R$attr.z_text_color;
    }
    public void R$attr(){
       super();
    }
}
