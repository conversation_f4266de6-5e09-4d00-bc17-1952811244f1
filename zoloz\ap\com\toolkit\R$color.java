package zoloz.ap.com.toolkit.R$color;
import com.taobao.taobao.R$color;
import java.lang.Object;

public final class R$color	// class@0010fd from classes11.dex
{
    public static int brand_text_color;
    public static int dialog_cancel;
    public static int dialog_message;
    public static int dialog_ok;
    public static int dialog_title;
    public static int title_back_color;
    public static int title_color;
    public static int titlebar_color;
    public static int titlebar_end_color;
    public static int titlebar_split_line_color;
    public static int titlebar_start_color;
    public static int z_grey_3;
    public static int z_white;

    static {
       R$color.brand_text_color = R$color.brand_text_color;
       R$color.dialog_cancel = R$color.dialog_cancel;
       R$color.dialog_message = R$color.dialog_message;
       R$color.dialog_ok = R$color.dialog_ok;
       R$color.dialog_title = R$color.dialog_title;
       R$color.title_back_color = R$color.title_back_color;
       R$color.title_color = R$color.title_color;
       R$color.titlebar_color = R$color.titlebar_color;
       R$color.titlebar_end_color = R$color.titlebar_end_color;
       R$color.titlebar_split_line_color = R$color.titlebar_split_line_color;
       R$color.titlebar_start_color = R$color.titlebar_start_color;
       R$color.z_grey_3 = R$color.z_grey_3;
       R$color.z_white = R$color.z_white;
    }
    public void R$color(){
       super();
    }
}
