package kotlinx.serialization.internal.MapEntrySerializer$descriptor$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import tb.x530;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;
import kotlinx.serialization.descriptors.a;
import tb.qb40;
import java.util.List;

public final class MapEntrySerializer$descriptor$1 extends Lambda implements g1a	// class@000740 from classes11.dex
{
    public final x530 $keySerializer;
    public final x530 $valueSerializer;

    public void MapEntrySerializer$descriptor$1(x530 p0,x530 p1){
       this.$keySerializer = p0;
       this.$valueSerializer = p1;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       ckf.g(p0, "$this$buildSerialDescriptor");
       f520 uof520 = p0;
       f520.b(uof520, "key", this.$keySerializer.getDescriptor(), null, false, 12, null);
       f520.b(uof520, "value", this.$valueSerializer.getDescriptor(), null, false, 12, null);
    }
}
