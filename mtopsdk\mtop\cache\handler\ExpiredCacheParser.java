package mtopsdk.mtop.cache.handler.ExpiredCacheParser;
import mtopsdk.mtop.cache.handler.ICacheParser;
import tb.t2o;
import java.lang.Object;
import mtopsdk.mtop.domain.ResponseSource;
import android.os.Handler;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import tb.w4j;
import mtopsdk.mtop.util.MtopStatistics;
import anetwork.network.cache.RpcCache;
import mtopsdk.mtop.domain.MtopRequest;
import mtopsdk.mtop.domain.MtopResponse;
import mtopsdk.mtop.cache.handler.CacheStatusHandler;
import mtopsdk.mtop.domain.MtopResponse$ResponseSource;
import mtopsdk.mtop.common.MtopNetworkProp;
import mtopsdk.mtop.common.MtopCallback$MtopCacheListener;
import mtopsdk.mtop.common.MtopCacheEvent;
import mtopsdk.mtop.common.MtopFinishEvent;
import mtopsdk.mtop.cache.handler.ExpiredCacheParser$1;
import mtopsdk.mtop.common.MtopListener;
import java.lang.Runnable;
import tb.ui9;
import mtopsdk.common.util.StringUtils;
import tb.h3o;

public class ExpiredCacheParser implements ICacheParser	// class@000799 from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x253000ab);
       t2o.a(0x253000af);
    }
    public void ExpiredCacheParser(){
       super();
    }
    public void parse(ResponseSource p0,Handler p1){
       w4j k;
       int i = 2;
       int i1 = 3;
       IpChange $ipChange = ExpiredCacheParser.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[i] = p1;
          $ipChange.ipc$dispatch("ec0249bc", objArray);
          return;
       }else {
          ResponseSource seqNo = p0.seqNo;
          if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)) {
             TBSdkLog.i("mtopsdk.ExpiredCacheParser", seqNo, "[parse]ExpiredCacheParser parse called");
          }
          ResponseSource mtopContext = p0.mtopContext;
          w4j g = mtopContext.g;
          g.cacheHitType = i;
          g.cacheResponseParseStartTime = g.currentTimeMillis();
          ResponseSource rpcCache = p0.rpcCache;
          MtopResponse mtopResponse = CacheStatusHandler.initResponseFromCache(rpcCache, mtopContext.b);
          mtopResponse.setSource(MtopResponse$ResponseSource.EXPIRED_CACHE);
          g.cacheResponseParseEndTime = g.currentTimeMillis();
          mtopResponse.setMtopStat(g);
          w4j e = mtopContext.e;
          MtopNetworkProp reqContext = mtopContext.d.reqContext;
          if (e instanceof MtopCallback$MtopCacheListener) {
             MtopCacheEvent mtopCacheEve = new MtopCacheEvent(mtopResponse);
             mtopCacheEve.seqNo = seqNo;
             g.cacheReturnTime = g.currentTimeMillis();
             CacheStatusHandler.finishMtopStatisticsOnExpiredCache(g, mtopResponse);
             if (mtopContext.d.skipCacheCallback == null) {
                ExpiredCacheParser$1 v11 = new ExpiredCacheParser$1(this, e, mtopCacheEve, reqContext, seqNo);
                ui9.d(p1, v11, mtopContext.h.hashCode());
             }
          }
          g.cacheHitType = i1;
          if ((k = mtopContext.k) != null) {
             if (StringUtils.isNotBlank(rpcCache.lastModified)) {
                k.c("if-modified-since", rpcCache.lastModified);
             }
             if (StringUtils.isNotBlank(rpcCache.etag)) {
                k.c("if-none-match", rpcCache.etag);
             }
          }
          p0.cacheResponse = mtopResponse;
          return;
       }
    }
}
