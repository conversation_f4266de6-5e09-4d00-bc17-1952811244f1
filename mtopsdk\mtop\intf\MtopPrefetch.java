package mtopsdk.mtop.intf.MtopPrefetch;
import tb.t2o;
import mtopsdk.mtop.stat.PrefetchStatistics;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.ArrayList;
import java.lang.System;
import java.lang.String;
import tb.w4j;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.mtop.domain.MtopRequest;
import java.util.Map;
import mtopsdk.mtop.intf.Mtop;
import java.util.Set;
import java.util.Iterator;
import mtopsdk.mtop.intf.MtopBuilder;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import java.lang.StringBuilder;
import java.lang.Throwable;
import mtopsdk.mtop.intf.MtopPrefetch$1;
import java.lang.Runnable;
import java.util.concurrent.Future;
import mtopsdk.mtop.util.MtopSDKThreadPoolExecutorFactory;
import mtopsdk.mtop.intf.MtopPrefetch$IPrefetchCallback;
import mtopsdk.mtop.intf.MtopPrefetch$IPrefetchComparator;
import java.lang.Number;
import java.lang.Boolean;
import java.lang.Long;
import java.lang.Integer;

public class MtopPrefetch	// class@0007e5 from classes11.dex
{
    private MtopPrefetch$IPrefetchCallback callback;
    private MtopPrefetch$IPrefetchComparator comparator;
    public ReentrantLock lock;
    public w4j mergeContext;
    private long prefetchExpireTime;
    public long prefetchHitTime;
    private int prefetchMode;
    public long prefetchResponseTime;
    public long prefetchStartTime;
    public AtomicBoolean response;
    public PrefetchStatistics stat;
    public List whiteListParams;
    public static IpChange $ipChange;
    public static final int MAX_PREFETCH_EXPIRE_TIME;
    private static final String TAG;

    static {
       t2o.a(0x253000fb);
    }
    public void MtopPrefetch(PrefetchStatistics p0){
       super();
       this.prefetchExpireTime = 5000;
       this.prefetchResponseTime = 0;
       this.prefetchStartTime = 0;
       this.prefetchHitTime = 0;
       this.response = new AtomicBoolean(false);
       this.mergeContext = null;
       this.lock = new ReentrantLock();
       this.whiteListParams = new ArrayList();
       this.callback = null;
       this.comparator = null;
       this.prefetchMode = 0;
       this.stat = p0;
       this.prefetchStartTime = System.currentTimeMillis();
    }
    private static HashMap buildCallbackData(String p0,w4j p1,MtopPrefetch p2,HashMap p3){
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2,p3};
          return $ipChange.ipc$dispatch("fecb37c1", objArray);
       }else if(p2 != null && p1 != null){
          HashMap hashMap = new HashMap();
          hashMap.put("data_seq", p1.h);
          hashMap.put("data_key", p1.b.getKey());
          hashMap.put("data_api", p1.b.getApiName());
          hashMap.put("data_version", p1.b.getVersion());
          MtopPrefetch prefetchHitT = p2.prefetchHitTime;
          long l = (prefetchHitT)? prefetchHitT - p2.prefetchResponseTime: -1;
          hashMap.put("data_cost_time", String.valueOf(l));
          if ("TYPE_MISS".equals(p0)) {
             hashMap.put("data_req_param", p1.b.getData());
          }
          if (p3 != null) {
             hashMap.putAll(p3);
          }
          return hashMap;
       }else {
          return null;
       }
    }
    public static void cleanPrefetchCache(Mtop p0){
       MtopBuilder mtopBuilder;
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("396ee122", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if(!p0.getPrefetchBuilderMap().isEmpty() && (0x3a98 - (System.currentTimeMillis() - p0.lastPrefetchResponseTime)) < 0){
          _monitor_enter(MtopPrefetch.class);
          if (!p0.getPrefetchBuilderMap().isEmpty()) {
             Iterator iterator = p0.getPrefetchBuilderMap().keySet().iterator();
             while (iterator.hasNext()) {
                if ((mtopBuilder = p0.getPrefetchBuilderMap().get(iterator.next())) != null) {
                   long l = System.currentTimeMillis() - mtopBuilder.getMtopPrefetch().prefetchResponseTime;
                   if ((l - mtopBuilder.getMtopPrefetch().getPrefetchExpireTime()) > 0) {
                      if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)) {
                         TBSdkLog.d("mtopsdk.MtopPrefetch", "".append("clean prefetch cache ").append(mtopBuilder.request.getKey()).toString());
                      }
                      MtopPrefetch.onPrefetchAndCommit("TYPE_CLEAR", mtopBuilder.getMtopPrefetch(), mtopBuilder.mtopContext, null);
                      iterator.remove();
                   }
                }
             }
          }
          _monitor_exit(MtopPrefetch.class);
       }
       return;
    }
    public static void onPrefetchAndCommit(String p0,MtopPrefetch p1,w4j p2,HashMap p3){
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2,p3};
          $ipChange.ipc$dispatch("157d9f83", objArray);
          return;
       }else if(p1 != null){
          HashMap hashMap = MtopPrefetch.buildCallbackData(p0, p2, p1, p3);
          MtopSDKThreadPoolExecutorFactory.submit(new MtopPrefetch$1(p1, p0, hashMap));
          if ((p1 = p1.stat) != null) {
             p1.doPrefetchCommit(p0, hashMap);
          }
       }
       return;
    }
    public MtopPrefetch$IPrefetchCallback getCallback(){
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.callback;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dc499df5", objArray);
    }
    public MtopPrefetch$IPrefetchComparator getComparator(){
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.comparator;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9b662193", objArray);
    }
    public long getPrefetchExpireTime(){
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.prefetchExpireTime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f99f03d4", objArray).longValue();
    }
    public boolean isLocalMode(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("688342f", objArray).booleanValue();
       }else if(this.prefetchMode == i1){
          i = true;
       }
       return i;
    }
    public void setCallback(MtopPrefetch$IPrefetchCallback p0){
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("13cbe62f", objArray);
          return;
       }else {
          this.callback = p0;
          return;
       }
    }
    public void setComparator(MtopPrefetch$IPrefetchComparator p0){
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("532f0e0f", objArray);
          return;
       }else {
          this.comparator = p0;
          return;
       }
    }
    public void setPrefetchExpireTime(long p0){
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("5071d458", objArray);
          return;
       }else {
          this.prefetchExpireTime = p0;
          return;
       }
    }
    public void setPrefetchMode(int p0){
       int i = 1;
       IpChange $ipChange = MtopPrefetch.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("c2934b20", objArray);
          return;
       }else if(p0 != i && p0){
          return;
       }else {
          this.prefetchMode = p0;
          return;
       }
    }
}
