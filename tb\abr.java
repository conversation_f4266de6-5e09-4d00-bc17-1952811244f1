package tb.abr;
import tb.aj$a;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.android.umf.node.service.parse.state.RenderComponent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.util.Map;
import com.alibaba.android.umf.datamodel.protocol.ultron.base.Component;
import java.util.Iterator;
import com.alipay.android.app.pay.PayTask;
import java.util.HashMap;
import com.alibaba.android.umbrella.trace.UmbrellaTracker;

public class abr implements aj$a	// class@001866 from classes5.dex
{
    public static IpChange $ipChange;
    public static final String DECRYPT_TYPE_ALIPAY;
    public static final String KEY_VALUE;

    static {
       t2o.a(0x2cc00083);
       t2o.a(0x6700045);
    }
    public void abr(){
       super();
    }
    public void a(String p0,JSONArray p1,RenderComponent p2){
       RenderComponent component;
       IpChange $ipChange = abr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("557d336d", objArray);
          return;
       }else if(!TextUtils.isEmpty(p0) && (p1 != null && (p2 != null && ((component = p2.component) != null && component.getFields() != null)))){
          Map fields = p2.component.getFields();
          Iterator iterator = p1.iterator();
          while (iterator.hasNext()) {
             Object obj = iterator.next();
             if (!obj instanceof String) {
                continue ;
             }else {
                Map obj1 = fields.get(obj);
                if (!obj1 instanceof String || !p0.equals("alipay")) {
                   continue ;
                }else if((obj1 = PayTask.decCashierObfs(obj1)) == null){
                   this.b(obj, fields);
                }else {
                   Object obj2 = obj1.get("value");
                   if (!obj2 instanceof String) {
                      this.b(obj, fields);
                   }else {
                      fields.put(obj, obj2);
                   }
                }
             }
          }
       }
       return;
    }
    public final void b(String p0,Map p1){
       IpChange $ipChange = abr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("17882ea", objArray);
          return;
       }else {
          p1.put(p0, "");
          this.c();
          return;
       }
    }
    public final void c(){
       IpChange $ipChange = abr.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d14a4758", objArray);
          return;
       }else {
          UmbrellaTracker.commitFailureStability("umbrella.ultron.parse", "feature.decrypt.alipay", "1.0", "ultronTrade", "unknown", new HashMap(), "decryptFailed", "alipay sdk decrypt failed");
          return;
       }
    }
}
