package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$filter$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;

public final class ChannelsKt__DeprecatedKt$filter$1 extends SuspendLambda implements u1a	// class@0004e5 from classes11.dex
{
    public final u1a $predicate;
    public final ReceiveChannel $this_filter;
    private Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;

    public void ChannelsKt__DeprecatedKt$filter$1(ReceiveChannel p0,u1a p1,ar4 p2){
       this.$this_filter = p0;
       this.$predicate = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$filter$1 uofilter$1 = new ChannelsKt__DeprecatedKt$filter$1(this.$this_filter, this.$predicate, p1);
       uofilter$1.L$0 = p0;
       return uofilter$1;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$filter$1 tlabel;
       ChannelsKt__DeprecatedKt$filter$1 tL$0;
       ChannelsKt__DeprecatedKt$filter$1 obj1;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel != 2) {
                if (tlabel == 3) {
                   tlabel = this.L$1;
                   tL$0 = this.L$0;
                   b.b(p0);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                obj1 = this.L$0;
                b.b(p0);
                tL$0 = this.L$2;
                tlabel = this.L$1;
             label_0081 :
                if (p0.booleanValue()) {
                   this.L$0 = obj1;
                   this.L$1 = tlabel;
                   this.L$2 = 0;
                   this.label = 3;
                   if (obj1.d(tL$0, this) == obj) {
                      return obj;
                   }
                }
                tL$0 = obj1;
             }
          }else {
             tlabel = this.L$1;
             tL$0 = this.L$0;
             b.b(p0);
          label_0060 :
             if (p0.booleanValue()) {
                p0 = tlabel.next();
                this.L$0 = tL$0;
                this.L$1 = tlabel;
                this.L$2 = p0;
                this.label = 2;
                if ((obj1 = this.$predicate.invoke(p0, this)) == obj) {
                   return obj;
                }else {
                   tL$0 = p0;
                   p0 = obj1;
                   obj1 = tL$0;
                   goto label_0081 ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          ChannelIterator uChannelIter = this.$this_filter.iterator();
          tL$0 = this.L$0;
       }
       this.L$0 = tL$0;
       this.L$1 = tlabel;
       this.L$2 = 0;
       this.label = 1;
       if ((p0 = tlabel.a(this)) == obj) {
          return obj;
       }else {
          goto label_0060 ;
       }
    }
}
