package kotlinx.datetime.internal.format.parser.StringSetParserOperation$special$$inlined$binarySearchBy$default$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Comparable;
import java.lang.Object;
import java.lang.Integer;
import kotlin.Pair;
import java.lang.String;
import tb.t84;

public final class StringSetParserOperation$special$$inlined$binarySearchBy$default$1 extends Lambda implements g1a	// class@000700 from classes11.dex
{
    public final Comparable $key;

    public void StringSetParserOperation$special$$inlined$binarySearchBy$default$1(Comparable p0){
       this.$key = p0;
       super(1);
    }
    public final Integer invoke(Object p0){
       return Integer.valueOf(t84.a(p0.getFirst(), this.$key));
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
}
