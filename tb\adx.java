package tb.adx;
import tb.t2o;
import java.lang.Object;
import android.view.MotionEvent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.ref.Reference;
import tb.bbs;
import tb.ddx;
import java.lang.Math;
import tb.ckf;
import java.lang.ref.WeakReference;

public final class adx	// class@001ec7 from classes10.dex
{
    public boolean a;
    public double b;
    public double c;
    public WeakReference d;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3550002c);
    }
    public void adx(){
       super();
       this.a = true;
    }
    public final void a(MotionEvent p0){
       adx td;
       bbs uobbs;
       IpChange $ipChange = adx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7ad3b64", objArray);
          return;
       }else if(this.a == null){
          if ((td = this.d) != null && (uobbs = td.get()) != null) {
             ddx.INSTANCE.c(uobbs);
          }
          this.a = true;
       }
       return;
    }
    public final void b(MotionEvent p0){
       int action;
       int i = 1;
       IpChange $ipChange = adx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d8aab07c", objArray);
          return;
       }else if(p0 == null){
          return;
       }else {
          double d = (double)p0.getX();
          double d1 = (double)p0.getY();
          if (action = p0.getAction()) {
             if (action == i || (action == 3 && ((Math.abs((this.b - d)) - 0x4000000000000000) < 0 && (Math.abs((this.c - d1)) - 0x4000000000000000) < 0))) {
                this.a(p0);
             }
          }else {
             this.a = false;
             this.b = d;
             this.c = d1;
          }
          return;
       }
    }
    public final void c(bbs p0){
       IpChange $ipChange = adx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e43873ba", objArray);
          return;
       }else {
          ckf.g(p0, "instance");
          this.d = new WeakReference(p0);
          return;
       }
    }
}
