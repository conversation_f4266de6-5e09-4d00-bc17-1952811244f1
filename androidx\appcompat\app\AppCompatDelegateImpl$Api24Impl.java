package androidx.appcompat.app.AppCompatDelegateImpl$Api24Impl;
import java.lang.Object;
import android.content.res.Configuration;
import android.os.LocaleList;
import tb.i31;
import tb.j31;
import tb.k31;
import androidx.core.os.LocaleListCompat;
import java.lang.String;
import tb.n31;
import tb.l31;
import tb.m31;

public class AppCompatDelegateImpl$Api24Impl	// class@00056b from classes.dex
{

    private void AppCompatDelegateImpl$Api24Impl(){
       super();
    }
    public static void generateConfigDelta_locale(Configuration p0,Configuration p1,Configuration p2){
       LocaleList localeList = i31.a(p1);
       if (!j31.a(i31.a(p0), localeList)) {
          k31.a(p2, localeList);
          p2.locale = p1.locale;
       }
       return;
    }
    public static LocaleListCompat getLocales(Configuration p0){
       return LocaleListCompat.forLanguageTags(n31.a(i31.a(p0)));
    }
    public static void setDefaultLocales(LocaleListCompat p0){
       m31.a(l31.a(p0.toLanguageTags()));
    }
    public static void setLocales(Configuration p0,LocaleListCompat p1){
       k31.a(p0, l31.a(p1.toLanguageTags()));
    }
}
