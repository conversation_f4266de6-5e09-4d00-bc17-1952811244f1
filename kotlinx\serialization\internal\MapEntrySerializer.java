package kotlinx.serialization.internal.MapEntrySerializer;
import tb.e730;
import tb.x530;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.a07;
import kotlinx.serialization.descriptors.b$c;
import kotlinx.serialization.descriptors.a;
import kotlinx.serialization.internal.MapEntrySerializer$descriptor$1;
import tb.ob40;
import tb.g1a;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;
import java.util.Map$Entry;

public final class MapEntrySerializer extends e730	// class@000741 from classes11.dex
{
    public final a c;

    public void MapEntrySerializer(x530 p0,x530 p1){
       ckf.g(p0, "keySerializer");
       ckf.g(p1, "valueSerializer");
       super(p0, p1, null);
       a[] uoaArray = new a[0];
       this.c = SerialDescriptorsKt.c("kotlin.collections.Map.Entry", b$c.INSTANCE, uoaArray, new MapEntrySerializer$descriptor$1(p0, p1));
    }
    public Object c(Object p0){
       return this.e(p0);
    }
    public Object d(Object p0){
       return this.f(p0);
    }
    public Object e(Map$Entry p0){
       ckf.g(p0, "<this>");
       return p0.getKey();
    }
    public Object f(Map$Entry p0){
       ckf.g(p0, "<this>");
       return p0.getValue();
    }
    public a getDescriptor(){
       return this.c;
    }
}
