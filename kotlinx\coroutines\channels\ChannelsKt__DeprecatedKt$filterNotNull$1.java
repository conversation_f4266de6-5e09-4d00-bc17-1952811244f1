package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$filterNotNull$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import tb.ar4;
import java.lang.Object;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.Boolean;
import tb.gk2;
import java.lang.IllegalStateException;
import java.lang.String;

public final class ChannelsKt__DeprecatedKt$filterNotNull$1 extends SuspendLambda implements u1a	// class@0004e8 from classes11.dex
{
    public Object L$0;
    public int label;

    public void ChannelsKt__DeprecatedKt$filterNotNull$1(ar4 p0){
       super(2, p0);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$filterNotNull$1 uofilterNotN = new ChannelsKt__DeprecatedKt$filterNotNull$1(p1);
       uofilterNotN.L$0 = p0;
       return uofilterNotN;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(Object p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       boolean b;
       dkf.d();
       if (this.label != null) {
          throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
       }
       b.b(p0);
       b = (this.L$0 != null)? true: false;
       return gk2.a(b);
    }
}
