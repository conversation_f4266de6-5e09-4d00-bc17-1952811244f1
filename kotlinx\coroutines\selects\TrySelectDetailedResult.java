package kotlinx.coroutines.selects.TrySelectDetailedResult;
import java.lang.Enum;
import java.lang.String;
import tb.fg8;
import kotlin.enums.a;
import java.lang.Class;
import java.lang.Object;

public final class TrySelectDetailedResult extends Enum	// class@0006b7 from classes11.dex
{
    private static final fg8 $ENTRIES;
    private static final TrySelectDetailedResult[] $VALUES;
    public static final TrySelectDetailedResult ALREADY_SELECTED;
    public static final TrySelectDetailedResult CANCELLED;
    public static final TrySelectDetailedResult REREGISTER;
    public static final TrySelectDetailedResult SUCCESSFUL;

    private static final TrySelectDetailedResult[] $values(){
       TrySelectDetailedResult[] trySelectDet = new TrySelectDetailedResult[]{TrySelectDetailedResult.SUCCESSFUL,TrySelectDetailedResult.REREGISTER,TrySelectDetailedResult.CANCELLED,TrySelectDetailedResult.ALREADY_SELECTED};
       return trySelectDet;
    }
    static {
       TrySelectDetailedResult.SUCCESSFUL = new TrySelectDetailedResult("SUCCESSFUL", 0);
       TrySelectDetailedResult.REREGISTER = new TrySelectDetailedResult("REREGISTER", 1);
       TrySelectDetailedResult.CANCELLED = new TrySelectDetailedResult("CANCELLED", 2);
       TrySelectDetailedResult.ALREADY_SELECTED = new TrySelectDetailedResult("ALREADY_SELECTED", 3);
       TrySelectDetailedResult[] trySelectDet = TrySelectDetailedResult.$values();
       TrySelectDetailedResult.$VALUES = trySelectDet;
       TrySelectDetailedResult.$ENTRIES = a.a(trySelectDet);
    }
    private void TrySelectDetailedResult(String p0,int p1){
       super(p0, p1);
    }
    public static fg8 getEntries(){
       return TrySelectDetailedResult.$ENTRIES;
    }
    public static TrySelectDetailedResult valueOf(String p0){
       return Enum.valueOf(TrySelectDetailedResult.class, p0);
    }
    public static TrySelectDetailedResult[] values(){
       return TrySelectDetailedResult.$VALUES.clone();
    }
}
