package kotlinx.datetime.format.OffsetFields$secondsOfMinute$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.r150;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class OffsetFields$secondsOfMinute$1 extends MutablePropertyReference1Impl	// class@0006e9 from classes11.dex
{
    public static final OffsetFields$secondsOfMinute$1 INSTANCE;

    static {
       OffsetFields$secondsOfMinute$1.INSTANCE = new OffsetFields$secondsOfMinute$1();
    }
    public void OffsetFields$secondsOfMinute$1(){
       super(r150.class, "secondsOfMinute", "getSecondsOfMinute\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.f();
    }
    public void set(Object p0,Object p1){
       p0.B(p1);
    }
}
