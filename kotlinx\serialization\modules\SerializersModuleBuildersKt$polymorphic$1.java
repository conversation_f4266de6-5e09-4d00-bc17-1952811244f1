package kotlinx.serialization.modules.SerializersModuleBuildersKt$polymorphic$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.yz30;
import tb.xhv;
import java.lang.String;
import tb.ckf;

public final class SerializersModuleBuildersKt$polymorphic$1 extends Lambda implements g1a	// class@000753 from classes11.dex
{
    public static final SerializersModuleBuildersKt$polymorphic$1 INSTANCE;

    static {
       SerializersModuleBuildersKt$polymorphic$1.INSTANCE = new SerializersModuleBuildersKt$polymorphic$1();
    }
    public void SerializersModuleBuildersKt$polymorphic$1(){
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(yz30 p0){
       ckf.g(p0, "$this$null");
    }
}
