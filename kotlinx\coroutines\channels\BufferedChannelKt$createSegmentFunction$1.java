package kotlinx.coroutines.channels.BufferedChannelKt$createSegmentFunction$1;
import tb.u1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.channels.BufferedChannelKt;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Number;
import tb.zi3;

public final class BufferedChannelKt$createSegmentFunction$1 extends FunctionReferenceImpl implements u1a	// class@0004d0 from classes11.dex
{
    public static final BufferedChannelKt$createSegmentFunction$1 INSTANCE;

    static {
       BufferedChannelKt$createSegmentFunction$1.INSTANCE = new BufferedChannelKt$createSegmentFunction$1();
    }
    public void BufferedChannelKt$createSegmentFunction$1(){
       super(2, BufferedChannelKt.class, "createSegment", "createSegment\(JLkotlinx/coroutines/channels/ChannelSegment;\)Lkotlinx/coroutines/channels/ChannelSegment;", 1);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0.longValue(), p1);
    }
    public final zi3 invoke(long p0,zi3 p1){
       return BufferedChannelKt.c(p0, p1);
    }
}
