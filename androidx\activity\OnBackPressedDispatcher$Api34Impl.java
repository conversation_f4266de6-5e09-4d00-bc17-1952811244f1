package androidx.activity.OnBackPressedDispatcher$Api34Impl;
import java.lang.Object;
import tb.g1a;
import tb.d1a;
import android.window.OnBackInvokedCallback;
import java.lang.String;
import tb.ckf;
import androidx.activity.OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1;

public final class OnBackPressedDispatcher$Api34Impl	// class@00045d from classes.dex
{
    public static final OnBackPressedDispatcher$Api34Impl INSTANCE;

    static {
       OnBackPressedDispatcher$Api34Impl.INSTANCE = new OnBackPressedDispatcher$Api34Impl();
    }
    private void OnBackPressedDispatcher$Api34Impl(){
       super();
    }
    public final OnBackInvokedCallback createOnBackAnimationCallback(g1a p0,g1a p1,d1a p2,d1a p3){
       ckf.g(p0, "onBackStarted");
       ckf.g(p1, "onBackProgressed");
       ckf.g(p2, "onBackInvoked");
       ckf.g(p3, "onBackCancelled");
       return new OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1(p0, p1, p2, p3);
    }
}
