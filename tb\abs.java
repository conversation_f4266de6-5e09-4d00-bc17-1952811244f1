package tb.abs;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import android.content.Context;
import tb.qwd;
import android.content.pm.PackageManager;
import java.lang.String;
import android.content.pm.ApplicationInfo;
import android.os.BaseBundle;
import java.lang.Class;
import java.lang.Object;
import java.lang.Throwable;
import com.taobao.themis.kernel.basic.TMSLogger;
import com.android.alibaba.ip.runtime.IpChange;
import tb.h8s;
import java.util.List;
import java.util.Iterator;
import tb.qwd$e;
import tb.qwd$b;
import com.taobao.themis.kernel.ability.register.AbilityType;
import tb.l8s;
import tb.qwd$a;
import tb.qwd$f;
import tb.qwd$d;
import tb.p8s;
import tb.qwd$c;
import tb.o8s;

public class abs	// class@001eb9 from classes10.dex
{
    public static IpChange $ipChange;
    public static final String TAG;
    public static final String a;
    public static final AtomicBoolean b;
    public static final AtomicBoolean c;

    static {
       t2o.a(0x34900178);
       abs.a = "tms_manifest";
       abs.b = new AtomicBoolean(false);
       abs.c = new AtomicBoolean(false);
    }
    public static qwd a(Context p0){
       ApplicationInfo metaData;
       ApplicationInfo applicationI = p0.getPackageManager().getApplicationInfo(p0.getPackageName(), 128);
       if ((metaData = applicationI.metaData) != null) {
          String a = abs.a;
          if (metaData.containsKey(a)) {
             return Class.forName(applicationI.metaData.getString(a)).newInstance();
          }
       }
       return null;
    }
    public static void b(Context p0){
       IpChange $ipChange = abs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("cc98bb4f", objArray);
          return;
       }else {
          abs.f(p0);
          abs.d(abs.a(p0));
          return;
       }
    }
    public static void c(Context p0){
       IpChange $ipChange = abs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("7314504b", objArray);
          return;
       }else {
          h8s.w(p0);
          abs.f(p0);
          return;
       }
    }
    public static void d(qwd p0){
       List abilities;
       int i = 1;
       IpChange $ipChange = abs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          $ipChange.ipc$dispatch("8e9d4a4c", objArray);
          return;
       }else {
          String str = "TMSInitializer";
          if (p0 == null) {
             TMSLogger.b(str, "registerAbilities but mainManifest is null!");
             return;
          }else if((abilities = p0.getAbilities()) != null && !abilities.isEmpty()){
             Iterator iterator = abilities.iterator();
             while (iterator.hasNext()) {
                qwd$e uoe = iterator.next();
                if (uoe instanceof qwd$b) {
                   l8s.d(uoe.b, uoe.a);
                }else if(uoe instanceof qwd$a){
                   uoe.getClass();
                   l8s.e(null, null, null);
                }
             }
             abs.b.set(i);
             return;
          }else {
             TMSLogger.b(str, "registerAbilities but abilityList is empty!");
             return;
          }
       }
    }
    public static void e(Context p0){
       IpChange $ipChange = abs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("545cbd7a", objArray);
          return;
       }else {
          abs.d(abs.a(p0));
          return;
       }
    }
    public static void f(Context p0){
       IpChange $ipChange = abs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("cc255d75", objArray);
          return;
       }else if(abs.c.get()){
          return;
       }else {
          abs.g(abs.a(p0));
          return;
       }
    }
    public static void g(qwd p0){
       List adapters;
       int i = 1;
       IpChange $ipChange = abs.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          $ipChange.ipc$dispatch("cde5c92a", objArray);
          return;
       }else {
          String str = "TMSInitializer";
          if (p0 == null) {
             TMSLogger.b(str, "registerAdapters but mainManifest is null!");
             return;
          }else if((adapters = p0.getAdapters()) != null && !adapters.isEmpty()){
             Iterator iterator = adapters.iterator();
             while (iterator.hasNext()) {
                qwd$f uof = iterator.next();
                if (uof instanceof qwd$d) {
                   uof.getClass();
                   p8s.i(null, null);
                }else if(uof instanceof qwd$c){
                   p8s.h(uof.a, uof.b);
                }
             }
             abs.c.set(i);
             return;
          }else {
             TMSLogger.b(str, "registerAdapters but adapterList is empty!");
             return;
          }
       }
    }
}
