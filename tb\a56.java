package tb.a56;
import tb.uu;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.android.dinamicx.DXRuntimeContext;
import tb.uw5;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import com.alibaba.fastjson.JSONArray;
import java.util.List;

public class a56 extends uu	// class@00183c from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1c5005b2);
    }
    public void a56(){
       super();
    }
    public static Object ipc$super(a56 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/dinamicx_v4/expr/fuction/list/DXMutableListOfFunction");
    }
    public uw5 execute(DXRuntimeContext p0,uw5 p1,int p2,uw5[] p3,Map p4){
       int i = 0;
       IpChange $ipChange = a56.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),p3,p4};
          return $ipChange.ipc$dispatch("5d66b176", objArray);
       }else {
          JSONArray jSONArray = new JSONArray();
          if (p3 != null && p3.length > 0) {
             int len = p3.length;
             for (; i < len; i = i + 1) {
                jSONArray.add(p3[i].z());
             }
          }
          return uw5.N(jSONArray);
       }
    }
    public String getDxFunctionName(){
       IpChange $ipChange = a56.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "mutableListOf";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("bc5916ec", objArray);
    }
}
