package mtopsdk.mtop.cache.handler.CacheParserFactory$1;
import anetwork.network.cache.RpcCache$CacheStatus;
import java.lang.Enum;

public class CacheParserFactory$1	// class@000794 from classes11.dex
{
    public static final int[] $SwitchMap$anetwork$network$cache$RpcCache$CacheStatus;

    static {
       int[] ointArray = new int[RpcCache$CacheStatus.values().length];
       try{
          CacheParserFactory$1.$SwitchMap$anetwork$network$cache$RpcCache$CacheStatus = ointArray;
          ointArray[RpcCache$CacheStatus.FRESH.ordinal()] = 1;
          try{
             CacheParserFactory$1.$SwitchMap$anetwork$network$cache$RpcCache$CacheStatus[RpcCache$CacheStatus.NEED_UPDATE.ordinal()] = 2;
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
