package androidx.appcompat.app.NavItemSelectedListener;
import android.widget.AdapterView$OnItemSelectedListener;
import androidx.appcompat.app.ActionBar$OnNavigationListener;
import java.lang.Object;
import android.widget.AdapterView;
import android.view.View;

public class NavItemSelectedListener implements AdapterView$OnItemSelectedListener	// class@000581 from classes.dex
{
    private final ActionBar$OnNavigationListener mListener;

    public void NavItemSelectedListener(ActionBar$OnNavigationListener p0){
       super();
       this.mListener = p0;
    }
    public void onItemSelected(AdapterView p0,View p1,int p2,long p3){
       NavItemSelectedListener tmListener;
       if ((tmListener = this.mListener) != null) {
          tmListener.onNavigationItemSelected(p2, p3);
       }
       return;
    }
    public void onNothingSelected(AdapterView p0){
    }
}
