package tb.a2s$a;
import tb.t2o;
import tb.a2s;
import android.content.Context;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import android.view.View;
import tb.a2s$c;
import android.widget.EditText;
import android.text.TextWatcher;
import android.widget.TextView;
import tb.a2s$a$a;
import android.view.View$OnTouchListener;
import com.taobao.schedule.ViewProxy;
import java.util.ArrayList;
import android.text.Editable;
import tb.mgw;

public class a2s$a	// class@001827 from classes5.dex
{
    public final String a;
    public final Context b;
    public final a2s c;
    public static IpChange $ipChange;

    static {
       t2o.a(0x13c0002b);
    }
    public void a2s$a(a2s p0,Context p1,String p2){
       super();
       this.c = p0;
       this.b = p1;
       this.a = p2;
    }
    public static Context a(a2s$a p0){
       IpChange $ipChange = a2s$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.b;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("518c1658", objArray);
    }
    public static String b(a2s$a p0){
       IpChange $ipChange = a2s$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.a;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("c6f5f47e", objArray);
    }
    public final void c(View p0){
       a2s$c tag;
       IpChange $ipChange = a2s$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a30575c5", objArray);
          return;
       }else if((tag = p0.getTag(a2s.m0())) != null){
          p0.removeTextChangedListener(tag);
       }
       EditText uEditText = p0;
       tag = new a2s$c(this.c, uEditText, this.a);
       p0.setTag(a2s.m0(), tag);
       uEditText.addTextChangedListener(tag);
       return;
    }
    public void d(View p0){
       IpChange $ipChange = a2s$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("369e9e1e", objArray);
          return;
       }else if(!p0 instanceof EditText){
          return;
       }else {
          this.c(p0);
          ViewProxy.setOnTouchListener(p0, new a2s$a$a(this, p0));
          return;
       }
    }
    public void e(View p0,String p1){
       IpChange $ipChange = a2s$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("6aa34c5f", objArray);
          return;
       }else {
          String str = "dialog";
          if (str.equals(p1)) {
             ArrayList uArrayList = new ArrayList(5);
             uArrayList.add(str);
             uArrayList.add(p0.getText());
             p0.setTag(mgw.DINAMICX_3_CUSTOM_INPUT_KEY, uArrayList);
             a2s.n0(this.c, p0.getText());
          }
          return;
       }
    }
}
