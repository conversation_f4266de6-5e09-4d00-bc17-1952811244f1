package tb.a66;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.String;

public class a66	// class@001840 from classes5.dex
{
    public double a;
    public int b;
    public boolean c;
    public int d;
    public boolean e;
    public int f;
    public int g;
    public int h;
    public boolean i;
    public String j;
    public HashMap k;
    public boolean l;
    public boolean m;
    public boolean n;
    public boolean o;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1c5003fe);
    }
    public void a66(){
       super();
       this.b = -1;
       this.c = false;
       this.d = 1;
       this.e = true;
       this.h = 0;
       this.i = false;
       this.l = false;
       this.m = true;
       this.n = false;
       this.o = false;
    }
    public a66 a(boolean p0){
       IpChange $ipChange = a66.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          return $ipChange.ipc$dispatch("898ffea2", objArray);
       }else if(!p0){
          return this;
       }else {
          a66 uoa66 = new a66();
          uoa66.a = this.a;
          uoa66.b = this.b;
          uoa66.c = this.c;
          uoa66.d = this.d;
          uoa66.e = this.e;
          uoa66.f = this.f;
          uoa66.g = this.g;
          uoa66.h = this.h;
          uoa66.i = this.i;
          uoa66.j = this.j;
          uoa66.k = this.k;
          uoa66.l = this.l;
          uoa66.n = this.n;
          uoa66.m = this.m;
          uoa66.o = this.o;
          return uoa66;
       }
    }
}
