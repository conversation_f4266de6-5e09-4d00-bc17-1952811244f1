package kotlinx.coroutines.internal.LockFreeLinkedListNode$a;
import tb.tg1;
import kotlinx.coroutines.internal.LockFreeLinkedListNode;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import tb.h30;
import tb.ckf;

public abstract class LockFreeLinkedListNode$a extends tg1	// class@00068d from classes11.dex
{
    public final LockFreeLinkedListNode c;
    public LockFreeLinkedListNode d;

    public void LockFreeLinkedListNode$a(LockFreeLinkedListNode p0){
       super();
       this.c = p0;
    }
    public void b(Object p0,Object p1){
       this.e(p0, p1);
    }
    public void e(LockFreeLinkedListNode p0,Object p1){
       int i = (p1 == null)? 1: 0;
       LockFreeLinkedListNode$a tc = this.c;
       LockFreeLinkedListNode$a uoa = (i)? tc: this.d;
       if (uoa != null && (h30.a(LockFreeLinkedListNode.b(), p0, this, uoa) && i)) {
          LockFreeLinkedListNode$a td = this.d;
          ckf.d(td);
          LockFreeLinkedListNode.a(tc, td);
       }
       return;
    }
}
