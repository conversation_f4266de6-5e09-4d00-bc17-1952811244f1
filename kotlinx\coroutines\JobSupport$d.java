package kotlinx.coroutines.JobSupport$d;
import tb.ruf;
import kotlinx.coroutines.JobSupport;
import tb.k9p;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;
import tb.fa4;
import tb.suf;

public final class JobSupport$d extends ruf	// class@0004a6 from classes11.dex
{
    public final k9p h;
    public final JobSupport i;

    public void JobSupport$d(JobSupport p0,k9p p1){
       this.i = p0;
       super();
       this.h = p1;
    }
    public Object invoke(Object p0){
       this.p(p0);
       return xhv.INSTANCE;
    }
    public void p(Throwable p0){
       JobSupport$d ti = this.i;
       Object obj = ti.v0();
       if (!obj instanceof fa4) {
          obj = suf.b(obj);
       }
       this.h.e(ti, obj);
       return;
    }
}
