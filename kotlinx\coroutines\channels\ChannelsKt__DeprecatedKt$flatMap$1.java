package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$flatMap$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;
import tb.bj3;

public final class ChannelsKt__DeprecatedKt$flatMap$1 extends SuspendLambda implements u1a	// class@0004ed from classes11.dex
{
    public final ReceiveChannel $this_flatMap;
    public final u1a $transform;
    private Object L$0;
    public Object L$1;
    public int label;

    public void ChannelsKt__DeprecatedKt$flatMap$1(ReceiveChannel p0,u1a p1,ar4 p2){
       this.$this_flatMap = p0;
       this.$transform = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$flatMap$1 uoflatMap$1 = new ChannelsKt__DeprecatedKt$flatMap$1(this.$this_flatMap, this.$transform, p1);
       uoflatMap$1.L$0 = p0;
       return uoflatMap$1;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$flatMap$1 tlabel;
       ChannelsKt__DeprecatedKt$flatMap$1 tL$0;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel != 2) {
                if (tlabel == 3) {
                   tlabel = this.L$1;
                   tL$0 = this.L$0;
                   b.b(p0);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                tlabel = this.L$1;
                tL$0 = this.L$0;
                b.b(p0);
             label_0073 :
                this.L$0 = tL$0;
                this.L$1 = tlabel;
                this.label = 3;
                if (bj3.s(p0, tL$0, this) == obj) {
                   return obj;
                }
             }
          }else {
             tlabel = this.L$1;
             tL$0 = this.L$0;
             b.b(p0);
          label_0058 :
             if (p0.booleanValue()) {
                this.L$0 = tL$0;
                this.L$1 = tlabel;
                this.label = 2;
                if ((p0 = this.$transform.invoke(tlabel.next(), this)) == obj) {
                   return obj;
                }else {
                   goto label_0073 ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          ChannelIterator uChannelIter = this.$this_flatMap.iterator();
          tL$0 = this.L$0;
       }
       this.L$0 = tL$0;
       this.L$1 = tlabel;
       this.label = 1;
       if ((p0 = tlabel.a(this)) == obj) {
          return obj;
       }else {
          goto label_0058 ;
       }
    }
}
