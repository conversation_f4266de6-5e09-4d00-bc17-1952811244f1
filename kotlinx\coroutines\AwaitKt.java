package kotlinx.coroutines.AwaitKt;
import java.util.Collection;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.AwaitKt$joinAll$3;
import tb.dkf;
import java.util.Iterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Iterable;
import kotlinx.coroutines.m;
import tb.xhv;
import kotlinx.coroutines.AwaitKt$joinAll$1;

public final class AwaitKt	// class@00048b from classes11.dex
{

    public static final Object a(Collection p0,ar4 p1){
       AwaitKt$joinAll$3 ojoinAll$3;
       int i1;
       AwaitKt$joinAll$3 label1;
       AwaitKt$joinAll$3 l$0;
       if (p1 instanceof AwaitKt$joinAll$3) {
          ojoinAll$3 = p1;
          AwaitKt$joinAll$3 label = ojoinAll$3.label;
          int i = Integer.MIN_VALUE;
          if (i1 = label & i) {
             int i2 = label - i;
             ojoinAll$3.label = i2;
          label_0018 :
             AwaitKt$joinAll$3 result = ojoinAll$3.result;
             Object obj = dkf.d();
             if ((label1 = ojoinAll$3.label) != null) {
                if (label1 == 1) {
                   l$0 = ojoinAll$3.L$0;
                   b.b(result);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                l$0 = p0.iterator();
             }
             while (true) {
                if (!l$0.hasNext()) {
                   return xhv.INSTANCE;
                }
                ojoinAll$3.L$0 = l$0;
                ojoinAll$3.label = 1;
                if (l$0.next().f0(ojoinAll$3) == obj) {
                   break ;
                }
             }
             return obj;
          }
       }
       ojoinAll$3 = new AwaitKt$joinAll$3(p1);
       goto label_0018 ;
    }
    public static final Object b(m[] p0,ar4 p1){
       AwaitKt$joinAll$1 ojoinAll$1;
       int i1;
       AwaitKt$joinAll$1 label1;
       AwaitKt$joinAll$1 i$1;
       if (p1 instanceof AwaitKt$joinAll$1) {
          ojoinAll$1 = p1;
          AwaitKt$joinAll$1 label = ojoinAll$1.label;
          int i = Integer.MIN_VALUE;
          if (i1 = label & i) {
             int i2 = label - i;
             ojoinAll$1.label = i2;
          label_0018 :
             AwaitKt$joinAll$1 result = ojoinAll$1.result;
             Object obj = dkf.d();
             if ((label1 = ojoinAll$1.label) != null) {
                if (label1 == 1) {
                   i$1 = ojoinAll$1.I$1;
                   label1 = ojoinAll$1.I$0;
                   b.b(result);
                   result = ojoinAll$1.L$0;
                label_0055 :
                   i = label1 + 1;
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                i = 0;
                result = p0;
                i$1 = p0.length;
             }
             if (i < i$1) {
                ojoinAll$1.L$0 = result;
                ojoinAll$1.I$0 = i;
                ojoinAll$1.I$1 = i$1;
                ojoinAll$1.label = 1;
                if (result[i].f0(ojoinAll$1) == obj) {
                   return obj;
                }else {
                   goto label_0055 ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }
       ojoinAll$1 = new AwaitKt$joinAll$1(p1);
       goto label_0018 ;
    }
}
