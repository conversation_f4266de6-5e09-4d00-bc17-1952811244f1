package kotlinx.coroutines.channels.BroadcastChannelImpl;
import tb.hl2;
import kotlinx.coroutines.channels.BufferedChannel;
import tb.g1a;
import java.lang.StringBuilder;
import java.lang.String;
import java.lang.IllegalArgumentException;
import java.lang.Object;
import java.util.concurrent.locks.ReentrantLock;
import java.util.List;
import tb.yz3;
import kotlinx.coroutines.channels.a;
import java.util.HashMap;
import kotlinx.coroutines.channels.ReceiveChannel;
import java.util.concurrent.locks.Lock;
import java.lang.Iterable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Collection;
import tb.xhv;
import java.lang.Throwable;
import java.lang.IllegalStateException;
import kotlinx.coroutines.channels.BroadcastChannelImpl$b;
import kotlinx.coroutines.channels.BroadcastChannelImpl$a;
import tb.i04;
import tb.ar4;
import kotlinx.coroutines.channels.BroadcastChannelImpl$send$1;
import tb.dkf;
import kotlin.b;
import java.lang.Boolean;
import tb.k9p;
import kotlin.coroutines.d;
import tb.uu4;
import kotlinx.coroutines.f;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.channels.BroadcastChannelImpl$registerSelectForSend$2;
import tb.u1a;
import kotlinx.coroutines.m;
import tb.mn2;
import kotlinx.coroutines.channels.e;
import java.lang.Class;
import kotlinx.coroutines.channels.e$b;
import java.lang.CharSequence;

public final class BroadcastChannelImpl extends BufferedChannel implements hl2	// class@0004bb from classes11.dex
{
    private final int v;
    private final ReentrantLock w;
    private List x;
    private Object y;
    private final HashMap z;

    public void BroadcastChannelImpl(int p0){
       super(0, null);
       this.v = p0;
       if (p0 < 1 && p0 != -1) {
          throw new IllegalArgumentException("BroadcastChannel capacity must be positive or Channel.CONFLATED, but "+p0+" was specified".toString());
       }
       this.w = new ReentrantLock();
       this.x = yz3.i();
       this.y = a.a;
       this.z = new HashMap();
       return;
    }
    public static final ReentrantLock L1(BroadcastChannelImpl p0){
       return p0.w;
    }
    public static final HashMap M1(BroadcastChannelImpl p0){
       return p0.z;
    }
    public static final void N1(BroadcastChannelImpl p0,ReceiveChannel p1){
       p0.T1(p1);
    }
    public static void Q1(){
    }
    public static void S1(){
    }
    private final void T1(ReceiveChannel p0){
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       ArrayList uArrayList = new ArrayList();
       Iterator iterator = this.x.iterator();
       while (iterator.hasNext()) {
          Object obj = iterator.next();
          if (obj != p0) {
             uArrayList.add(obj);
          }
       }
       this.x = uArrayList;
       tw.unlock();
       return;
    }
    public boolean O(Throwable p0){
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       Iterator iterator = this.x.iterator();
       while (iterator.hasNext()) {
          iterator.next().O(p0);
       }
       this.y = a.a;
       tw.unlock();
       return super.O(p0);
    }
    public final int O1(){
       return this.v;
    }
    public final Object P1(){
       Throwable throwable;
       BroadcastChannelImpl ty;
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       if (this.f()) {
          if ((throwable = this.d0()) == null) {
             throwable = new IllegalStateException("This broadcast channel is closed");
          }
          throw throwable;
       }else if((ty = this.y) != a.a){
          tw.unlock();
          return ty;
       }else {
          throw new IllegalStateException("No value");
       }
    }
    public final Object R1(){
       BroadcastChannelImpl ty;
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       BroadcastChannelImpl uBroadcastCh = null;
       if (!this.u0() && (ty = this.y) != a.a) {
          uBroadcastCh = ty;
       }
       tw.unlock();
       return uBroadcastCh;
    }
    public ReceiveChannel c(){
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       BroadcastChannelImpl$b uob = (this.v == -1)? new BroadcastChannelImpl$b(this): new BroadcastChannelImpl$a(this);
       u1r a = a.a;
       if (this.f() && this.y == a) {
          uob.s(this.d0());
          tw.unlock();
          return uob;
       }else if(this.y != a){
          uob.m(this.P1());
       }
       this.x = i04.p0(this.x, uob);
       tw.unlock();
       return uob;
    }
    public Object d(Object p0,ar4 p1){
       BroadcastChannelImpl$send$1 osend$1;
       int i1;
       BroadcastChannelImpl$send$1 label1;
       BroadcastChannelImpl$send$1 l$0;
       Object obj1;
       if (p1 instanceof BroadcastChannelImpl$send$1) {
          osend$1 = p1;
          BroadcastChannelImpl$send$1 label = osend$1.label;
          int i = Integer.MIN_VALUE;
          if (i1 = label & i) {
             int i2 = label - i;
             osend$1.label = i2;
          label_0018 :
             BroadcastChannelImpl$send$1 result = osend$1.result;
             Object obj = dkf.d();
             if ((label1 = osend$1.label) != null) {
                if (label1 == 1) {
                   p0 = osend$1.L$2;
                   label1 = osend$1.L$1;
                   l$0 = osend$1.L$0;
                   b.b(result);
                label_0080 :
                   if (!result.booleanValue() && l$0.f()) {
                      throw l$0.m0();
                   }else {
                      result = label1;
                   }
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                BroadcastChannelImpl tw = this.w;
                tw.lock();
                if (!this.f()) {
                   if (this.v == -1) {
                      this.y = p0;
                   }
                   tw.unlock();
                   l$0 = this;
                   result = p0;
                   p0 = this.x.iterator();
                }else {
                   throw this.m0();
                }
             }
             if (p0.hasNext()) {
                osend$1.L$0 = l$0;
                osend$1.L$1 = result;
                osend$1.L$2 = p0;
                osend$1.label = 1;
                if ((obj1 = p0.next().q1(result, osend$1)) == obj) {
                   return obj;
                }else {
                   label1 = result;
                   result = obj1;
                   goto label_0080 ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }
       osend$1 = new BroadcastChannelImpl$send$1(this, p1);
       goto label_0018 ;
    }
    public boolean f(){
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       tw.unlock();
       return super.f();
    }
    public void k1(k9p p0,Object p1){
       Object obj;
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       if ((obj = this.z.remove(p0)) != null) {
          p0.b(obj);
          tw.unlock();
          return;
       }else {
          tw.unlock();
          mn2.b(f.a(p0.getContext()), null, CoroutineStart.UNDISPATCHED, new BroadcastChannelImpl$registerSelectForSend$2(this, p1, p0, null), 1, null);
          return;
       }
    }
    public Object m(Object p0){
       Iterator iterator;
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       if (this.f()) {
          tw.unlock();
          return super.m(p0);
       }else {
          BroadcastChannelImpl tx = this.x;
          if (!tx instanceof Collection || !tx.isEmpty()) {
             iterator = tx.iterator();
             do {
                if (iterator.hasNext()) {
                }
             } while (iterator.next().x1());
             e.Companion.getClass();
             tw.unlock();
             return e.b;
          }
          if (this.v == -1) {
             this.y = p0;
          }
          iterator = this.x.iterator();
          while (iterator.hasNext()) {
             iterator.next().m(p0);
          }
          xhv iNSTANCE = xhv.INSTANCE;
          e.Companion.b(iNSTANCE);
          tw.unlock();
          return iNSTANCE;
       }
    }
    public boolean s(Throwable p0){
       BroadcastChannelImpl tw = this.w;
       tw.lock();
       Iterator iterator = this.x.iterator();
       while (iterator.hasNext()) {
          iterator.next().s(p0);
       }
       ArrayList uArrayList = new ArrayList();
       iterator = this.x.iterator();
       while (iterator.hasNext()) {
          Object obj = iterator.next();
          if (obj.o0()) {
             uArrayList.add(obj);
          }
       }
       this.x = uArrayList;
       tw.unlock();
       return super.s(p0);
    }
    public String toString(){
       StringBuilder str = "";
       String str1 = (this.y != a.a)? "CONFLATED_ELEMENT="+this.y+"; ": "";
       return str+str1+"BROADCAST=<"+super.toString()+">; SUBSCRIBERS="+i04.j0(this.x, ";", "<", ">", 0, null, null, 56, null);
    }
}
