package kotlinx.serialization.PolymorphicSerializer;
import tb.b63;
import tb.wyf;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import java.util.List;
import tb.yz3;
import kotlin.LazyThreadSafetyMode;
import kotlinx.serialization.PolymorphicSerializer$descriptor$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import kotlinx.serialization.descriptors.a;
import java.lang.StringBuilder;

public final class PolymorphicSerializer extends b63	// class@00070f from classes11.dex
{
    public final wyf a;
    public final List b;
    public final njg c;

    public void PolymorphicSerializer(wyf p0){
       ckf.g(p0, "baseClass");
       super();
       this.a = p0;
       this.b = yz3.i();
       this.c = a.a(LazyThreadSafetyMode.PUBLICATION, new PolymorphicSerializer$descriptor$2(this));
    }
    public static final List c(PolymorphicSerializer p0){
       return p0.b;
    }
    public wyf getBaseClass(){
       return this.a;
    }
    public a getDescriptor(){
       return this.c.getValue();
    }
    public String toString(){
       return "kotlinx.serialization.PolymorphicSerializer\(baseClass: "+this.getBaseClass()+')';
    }
}
