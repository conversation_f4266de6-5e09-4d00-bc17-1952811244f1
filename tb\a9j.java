package tb.a9j;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.taobao.android.order.core.dinamicX.view.barcode.BarcodeFormat;
import java.util.Map;
import tb.qb2;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import com.taobao.android.order.core.dinamicX.view.barcode.Code128Writer;
import tb.xwk;
import java.lang.IllegalArgumentException;
import java.lang.StringBuilder;

public final class a9j	// class@001858 from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x26b000bf);
       t2o.a(0x26b000c1);
    }
    public void a9j(){
       super();
    }
    public qb2 a(String p0,BarcodeFormat p1,int p2,int p3,Map p4){
       IpChange $ipChange = a9j.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),new Integer(p3),p4};
          return $ipChange.ipc$dispatch("64fb45a5", objArray);
       }else if(p1 == BarcodeFormat.CODE_128){
          return new Code128Writer().b(p0, p1, p2, p3, p4);
       }else {
          throw new IllegalArgumentException("No encoder available for format "+p1);
       }
    }
}
