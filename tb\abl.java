package tb.abl;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import com.taobao.orange.OrangeConfig;
import tb.ckf;

public final class abl	// class@001865 from classes5.dex
{
    public static IpChange $ipChange;
    public static final abl INSTANCE;
    public static boolean a;
    public static boolean b;

    static {
       t2o.a(0x15d00055);
       abl.INSTANCE = new abl();
    }
    public void abl(){
       super();
    }
    public static final boolean C(){
       IpChange $ipChange = abl.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("4794c5bb", objArray).booleanValue();
       }else if(abl.INSTANCE.u()){
          i = ckf.b("true", OrangeConfig.getInstance().getConfig("ability_kit", "loadingCancelable", "false"));
       }
       return i;
    }
    public static final boolean d(){
       boolean b;
       IpChange $ipChange = abl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("8039bdf1", objArray).booleanValue();
       }else if(!abl.INSTANCE.u()){
          b = true;
       }else {
          b = ckf.b("true", OrangeConfig.getInstance().getConfig("ability_kit", "dxLoadingTokenDegrade", "false"));
       }
       return b;
    }
    public static final boolean f(){
       boolean b;
       IpChange $ipChange = abl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("a2c887dc", objArray).booleanValue();
       }else if(!abl.INSTANCE.u()){
          b = true;
       }else {
          b = ckf.b("true", OrangeConfig.getInstance().getConfig("ability_kit", "dxToastDegrade", "false"));
       }
       return b;
    }
    public static final boolean k(){
       IpChange $ipChange = abl.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("e183850b", objArray).booleanValue();
       }else if(!abl.INSTANCE.u()){
          i = ckf.b("true", OrangeConfig.getInstance().getConfig("mega_analysis", "enableExecuteCount", "false"));
       }
       return i;
    }
    public static final String x(){
       String str;
       IpChange $ipChange = abl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("851ca0aa", objArray);
       }else if(!abl.INSTANCE.u()){
          str = null;
       }else {
          str = OrangeConfig.getInstance().getConfig("ability_kit", "dxLoadingTemplate", "https://dinamicx.alibabausercontent.com/l_pub/loading_ability_ui/1739947850725/loading_ability_ui.zip,12");
       }
       return str;
    }
    public static final String y(){
       String str;
       IpChange $ipChange = abl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("89625415", objArray);
       }else if(!abl.INSTANCE.u()){
          str = null;
       }else {
          str = OrangeConfig.getInstance().getConfig("ability_kit", "dxToastTemplate", "https://dinamicx.alibabausercontent.com/pub/toast_ability/1713778277014/toast_ability.zip,3");
       }
       return str;
    }
    public final boolean u(){
       int i = 1;
       IpChange $ipChange = abl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          return $ipChange.ipc$dispatch("497e2210", objArray).booleanValue();
       }else if(abl.b){
          return abl.a;
       }else {
          abl.a = i;
          abl.b = i;
          return i;
       }
    }
}
