package tb.abw$b;
import tb.dh7$l;
import tb.t2o;
import tb.abw;
import java.lang.Object;
import tb.abw$a;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.Boolean;
import java.lang.String;
import tb.faw;

public class abw$b implements dh7$l	// class@001868 from classes5.dex
{
    public final abw a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x16c00074);
       t2o.a(0x16c00194);
    }
    public void abw$b(abw p0){
       super();
       this.a = p0;
    }
    public void abw$b(abw p0,abw$a p1){
       super(p0);
    }
    public void a(int p0,int p1,boolean p2){
       IpChange $ipChange = abw$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),new Integer(p1),new Boolean(p2)};
          $ipChange.ipc$dispatch("9a5b043d", objArray);
          return;
       }else if(abw.r(this.a) == null){
          return;
       }else {
          abw.r(this.a).d();
          return;
       }
    }
}
