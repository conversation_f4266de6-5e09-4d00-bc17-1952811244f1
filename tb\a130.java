package tb.a130;
import tb.g1a;
import tb.y030;
import java.lang.Object;
import tb.z030;
import tb.xhv;
import tb.b130;

public final class a130 implements g1a	// class@001835 from classes7.dex
{
    public final y030 a;
    public final g1a b;

    public void a130(y030 p0,g1a p1){
       super();
       this.a = p0;
       this.b = p1;
    }
    public final Object invoke(Object p0){
       return b130.a(this.a, this.b, p0);
    }
}
