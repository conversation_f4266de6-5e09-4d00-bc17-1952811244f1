package mtopsdk.extra.antiattack.CheckCodeValidateActivity$a;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import mtopsdk.extra.antiattack.CheckCodeValidateActivity;
import android.content.Context;
import java.lang.String;
import java.lang.Object;
import com.uc.webview.export.WebView;
import java.lang.Boolean;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.app.Activity;

public class CheckCodeValidateActivity$a extends WVUCWebViewClient	// class@00077a from classes11.dex
{
    public final CheckCodeValidateActivity b;
    public static IpChange $ipChange;

    public void CheckCodeValidateActivity$a(CheckCodeValidateActivity p0,Context p1){
       this.b = p0;
       super(p1);
    }
    public static Object ipc$super(CheckCodeValidateActivity$a p0,String p1,Object[] p2){
       if (p1.hashCode() == -623958539) {
          return new Boolean(super.shouldOverrideUrlLoading(p2[0], p2[1]));
       }
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/extra/antiattack/CheckCodeValidateActivity$1");
    }
    public boolean shouldOverrideUrlLoading(WebView p0,String p1){
       IpChange $ipChange = CheckCodeValidateActivity$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("dacf25f5", objArray).booleanValue();
       }else if(!TextUtils.isEmpty(p1) && p1.equalsIgnoreCase(this.b.b)){
          CheckCodeValidateActivity.a(this.b, "success");
          this.b.finish();
          return 1;
       }else {
          return super.shouldOverrideUrlLoading(p0, p1);
       }
    }
}
