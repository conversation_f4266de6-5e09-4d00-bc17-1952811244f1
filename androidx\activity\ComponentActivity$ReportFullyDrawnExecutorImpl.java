package androidx.activity.ComponentActivity$ReportFullyDrawnExecutorImpl;
import androidx.activity.ComponentActivity$ReportFullyDrawnExecutor;
import android.view.ViewTreeObserver$OnDrawListener;
import java.lang.Runnable;
import androidx.activity.ComponentActivity;
import java.lang.Object;
import android.os.SystemClock;
import java.lang.String;
import tb.ckf;
import android.view.Window;
import android.app.Activity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.os.Looper;
import tb.va4;
import androidx.activity.FullyDrawnReporter;

public final class ComponentActivity$ReportFullyDrawnExecutorImpl implements ComponentActivity$ReportFullyDrawnExecutor, ViewTreeObserver$OnDrawListener, Runnable	// class@00043a from classes.dex
{
    private Runnable currentRunnable;
    private final long endWatchTimeMillis;
    private boolean onDrawScheduled;
    public final ComponentActivity this$0;

    public void ComponentActivity$ReportFullyDrawnExecutorImpl(ComponentActivity p0){
       this.this$0 = p0;
       super();
       this.endWatchTimeMillis = SystemClock.uptimeMillis() + (long)0x2710;
    }
    public static void a(ComponentActivity$ReportFullyDrawnExecutorImpl p0){
       ComponentActivity$ReportFullyDrawnExecutorImpl.execute$lambda$0(p0);
    }
    private static final void execute$lambda$0(ComponentActivity$ReportFullyDrawnExecutorImpl p0){
       ComponentActivity$ReportFullyDrawnExecutorImpl currentRunna;
       ckf.g(p0, "this$0");
       if ((currentRunna = p0.currentRunnable) != null) {
          ckf.d(currentRunna);
          currentRunna.run();
          p0.currentRunnable = null;
       }
       return;
    }
    public void activityDestroyed(){
       this.this$0.getWindow().getDecorView().removeCallbacks(this);
       this.this$0.getWindow().getDecorView().getViewTreeObserver().removeOnDrawListener(this);
    }
    public void execute(Runnable p0){
       ckf.g(p0, "runnable");
       this.currentRunnable = p0;
       View decorView = this.this$0.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       if (this.onDrawScheduled != null) {
          if (ckf.b(Looper.myLooper(), Looper.getMainLooper())) {
             decorView.invalidate();
          }else {
             decorView.postInvalidate();
          }
       }else {
          decorView.postOnAnimation(new va4(this));
       }
       return;
    }
    public final Runnable getCurrentRunnable(){
       return this.currentRunnable;
    }
    public final long getEndWatchTimeMillis(){
       return this.endWatchTimeMillis;
    }
    public final boolean getOnDrawScheduled(){
       return this.onDrawScheduled;
    }
    public void onDraw(){
       ComponentActivity$ReportFullyDrawnExecutorImpl tcurrentRunn;
       if ((tcurrentRunn = this.currentRunnable) != null) {
          tcurrentRunn.run();
          this.currentRunnable = null;
          if (this.this$0.getFullyDrawnReporter().isFullyDrawnReported()) {
             this.onDrawScheduled = false;
             this.this$0.getWindow().getDecorView().post(this);
          }
       }else if((SystemClock.uptimeMillis() - this.endWatchTimeMillis) > 0){
          this.onDrawScheduled = false;
          this.this$0.getWindow().getDecorView().post(this);
       }
       return;
    }
    public void run(){
       this.this$0.getWindow().getDecorView().getViewTreeObserver().removeOnDrawListener(this);
    }
    public final void setCurrentRunnable(Runnable p0){
       this.currentRunnable = p0;
    }
    public final void setOnDrawScheduled(boolean p0){
       this.onDrawScheduled = p0;
    }
    public void viewCreated(View p0){
       ckf.g(p0, "view");
       if (this.onDrawScheduled == null) {
          this.onDrawScheduled = true;
          p0.getViewTreeObserver().addOnDrawListener(this);
       }
       return;
    }
}
