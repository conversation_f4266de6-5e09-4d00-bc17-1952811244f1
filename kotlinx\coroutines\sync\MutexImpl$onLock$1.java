package kotlinx.coroutines.sync.MutexImpl$onLock$1;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.sync.MutexImpl;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import tb.k9p;
import tb.xhv;

public final class MutexImpl$onLock$1 extends FunctionReferenceImpl implements w1a	// class@0006c4 from classes11.dex
{
    public static final MutexImpl$onLock$1 INSTANCE;

    static {
       MutexImpl$onLock$1.INSTANCE = new MutexImpl$onLock$1();
    }
    public void MutexImpl$onLock$1(){
       super(3, MutexImpl.class, "onLockRegFunction", "onLockRegFunction\(Lkotlinx/coroutines/selects/SelectInstance;Ljava/lang/Object;\)V", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       this.invoke(p0, p1, p2);
       return xhv.INSTANCE;
    }
    public final void invoke(MutexImpl p0,k9p p1,Object p2){
       p0.v(p1, p2);
    }
}
