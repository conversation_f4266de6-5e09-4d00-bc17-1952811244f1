package androidx.activity.ComponentActivity;
import androidx.activity.contextaware.ContextAware;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.lifecycle.HasDefaultViewModelProviderFactory;
import androidx.savedstate.SavedStateRegistryOwner;
import androidx.activity.OnBackPressedDispatcherOwner;
import androidx.activity.result.ActivityResultRegistryOwner;
import androidx.activity.result.ActivityResultCaller;
import androidx.core.content.OnConfigurationChangedProvider;
import androidx.core.content.OnTrimMemoryProvider;
import androidx.core.app.OnNewIntentProvider;
import androidx.core.app.OnMultiWindowModeChangedProvider;
import androidx.core.app.OnPictureInPictureModeChangedProvider;
import androidx.core.app.OnUserLeaveHintProvider;
import androidx.core.view.MenuHost;
import androidx.activity.FullyDrawnReporterOwner;
import androidx.core.app.ComponentActivity;
import androidx.activity.ComponentActivity$Companion;
import tb.a07;
import androidx.activity.contextaware.ContextAwareHelper;
import androidx.core.view.MenuHostHelper;
import tb.pa4;
import java.lang.Runnable;
import androidx.savedstate.SavedStateRegistryController;
import androidx.savedstate.SavedStateRegistryController$Companion;
import androidx.activity.ComponentActivity$ReportFullyDrawnExecutor;
import androidx.activity.ComponentActivity$fullyDrawnReporter$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import java.util.concurrent.atomic.AtomicInteger;
import androidx.activity.ComponentActivity$activityResultRegistry$1;
import java.util.concurrent.CopyOnWriteArrayList;
import androidx.lifecycle.Lifecycle;
import tb.qa4;
import androidx.lifecycle.LifecycleObserver;
import tb.ra4;
import androidx.activity.ComponentActivity$4;
import androidx.lifecycle.SavedStateHandleSupport;
import android.os.Build$VERSION;
import androidx.activity.ImmLeaksCleaner;
import android.app.Activity;
import androidx.savedstate.SavedStateRegistry;
import tb.sa4;
import java.lang.String;
import androidx.savedstate.SavedStateRegistry$SavedStateProvider;
import tb.ta4;
import androidx.activity.contextaware.OnContextAvailableListener;
import androidx.activity.ComponentActivity$defaultViewModelProviderFactory$2;
import androidx.activity.ComponentActivity$onBackPressedDispatcher$2;
import java.lang.IllegalStateException;
import androidx.lifecycle.Lifecycle$Event;
import java.lang.Object;
import tb.ckf;
import android.view.Window;
import android.view.View;
import androidx.lifecycle.ViewModelStore;
import android.os.Bundle;
import androidx.activity.result.ActivityResultRegistry;
import android.content.Context;
import androidx.activity.OnBackPressedDispatcher;
import tb.ua4;
import androidx.activity.ComponentActivity$Api33Impl;
import android.window.OnBackInvokedDispatcher;
import androidx.activity.ComponentActivity$ReportFullyDrawnExecutorImpl;
import androidx.activity.ComponentActivity$NonConfigurationInstances;
import android.view.ViewGroup$LayoutParams;
import androidx.core.view.MenuProvider;
import androidx.lifecycle.Lifecycle$State;
import androidx.core.util.Consumer;
import androidx.lifecycle.viewmodel.CreationExtras;
import androidx.lifecycle.viewmodel.MutableCreationExtras;
import android.app.Application;
import androidx.lifecycle.ViewModelProvider$AndroidViewModelFactory;
import androidx.lifecycle.viewmodel.CreationExtras$Key;
import android.content.Intent;
import androidx.lifecycle.ViewModelProvider$Factory;
import androidx.activity.FullyDrawnReporter;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.lifecycle.ViewTreeViewModelStoreOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner;
import androidx.activity.ViewTreeFullyDrawnReporterOwner;
import android.content.res.Configuration;
import java.util.Iterator;
import androidx.lifecycle.ReportFragment;
import androidx.lifecycle.ReportFragment$Companion;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import androidx.core.app.MultiWindowModeChangedInfo;
import androidx.core.app.PictureInPictureModeChangedInfo;
import androidx.lifecycle.LifecycleRegistry;
import java.lang.Integer;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import java.lang.StringBuilder;
import tb.r8u;
import android.content.IntentSender;

public class ComponentActivity extends ComponentActivity implements ContextAware, LifecycleOwner, ViewModelStoreOwner, HasDefaultViewModelProviderFactory, SavedStateRegistryOwner, OnBackPressedDispatcherOwner, ActivityResultRegistryOwner, ActivityResultCaller, OnConfigurationChangedProvider, OnTrimMemoryProvider, OnNewIntentProvider, OnMultiWindowModeChangedProvider, OnPictureInPictureModeChangedProvider, OnUserLeaveHintProvider, MenuHost, FullyDrawnReporterOwner	// class@000440 from classes.dex
{
    private ViewModelStore _viewModelStore;
    private final ActivityResultRegistry activityResultRegistry;
    private int contentLayoutId;
    private final ContextAwareHelper contextAwareHelper;
    private final njg defaultViewModelProviderFactory$delegate;
    private boolean dispatchingOnMultiWindowModeChanged;
    private boolean dispatchingOnPictureInPictureModeChanged;
    private final njg fullyDrawnReporter$delegate;
    private final MenuHostHelper menuHostHelper;
    private final AtomicInteger nextLocalRequestCode;
    private final njg onBackPressedDispatcher$delegate;
    private final CopyOnWriteArrayList onConfigurationChangedListeners;
    private final CopyOnWriteArrayList onMultiWindowModeChangedListeners;
    private final CopyOnWriteArrayList onNewIntentListeners;
    private final CopyOnWriteArrayList onPictureInPictureModeChangedListeners;
    private final CopyOnWriteArrayList onTrimMemoryListeners;
    private final CopyOnWriteArrayList onUserLeaveHintListeners;
    private final ComponentActivity$ReportFullyDrawnExecutor reportFullyDrawnExecutor;
    private final SavedStateRegistryController savedStateRegistryController;
    private static final String ACTIVITY_RESULT_TAG = "android:support:activity-result";
    private static final ComponentActivity$Companion Companion;

    static {
       ComponentActivity.Companion = new ComponentActivity$Companion(null);
    }
    public void ComponentActivity(){
       super();
       this.contextAwareHelper = new ContextAwareHelper();
       this.menuHostHelper = new MenuHostHelper(new pa4(this));
       SavedStateRegistryController savedStateRe = SavedStateRegistryController.Companion.create(this);
       this.savedStateRegistryController = savedStateRe;
       this.reportFullyDrawnExecutor = this.createFullyDrawnExecutor();
       this.fullyDrawnReporter$delegate = a.b(new ComponentActivity$fullyDrawnReporter$2(this));
       this.nextLocalRequestCode = new AtomicInteger();
       this.activityResultRegistry = new ComponentActivity$activityResultRegistry$1(this);
       this.onConfigurationChangedListeners = new CopyOnWriteArrayList();
       this.onTrimMemoryListeners = new CopyOnWriteArrayList();
       this.onNewIntentListeners = new CopyOnWriteArrayList();
       this.onMultiWindowModeChangedListeners = new CopyOnWriteArrayList();
       this.onPictureInPictureModeChangedListeners = new CopyOnWriteArrayList();
       this.onUserLeaveHintListeners = new CopyOnWriteArrayList();
       if (this.getLifecycle() == null) {
          throw new IllegalStateException("getLifecycle\(\) returned null in ComponentActivity\'s constructor. Please make sure you are lazily constructing your Lifecycle in the first call to getLifecycle\(\) rather than relying on field initialization.");
       }
       this.getLifecycle().addObserver(new qa4(this));
       this.getLifecycle().addObserver(new ra4(this));
       this.getLifecycle().addObserver(new ComponentActivity$4(this));
       savedStateRe.performAttach();
       SavedStateHandleSupport.enableSavedStateHandles(this);
       if (Build$VERSION.SDK_INT <= 23) {
          this.getLifecycle().addObserver(new ImmLeaksCleaner(this));
       }
       this.getSavedStateRegistry().registerSavedStateProvider("android:support:activity-result", new sa4(this));
       this.addOnContextAvailableListener(new ta4(this));
       this.defaultViewModelProviderFactory$delegate = a.b(new ComponentActivity$defaultViewModelProviderFactory$2(this));
       this.onBackPressedDispatcher$delegate = a.b(new ComponentActivity$onBackPressedDispatcher$2(this));
       return;
    }
    public void ComponentActivity(int p0){
       super();
       this.contentLayoutId = p0;
    }
    private static final void _init_$lambda$2(ComponentActivity p0,LifecycleOwner p1,Lifecycle$Event p2){
       Window window;
       View view;
       ckf.g(p0, "this$0");
       ckf.g(p1, "<anonymous parameter 0>");
       ckf.g(p2, "event");
       if (p2 == Lifecycle$Event.ON_STOP && ((window = p0.getWindow()) != null && (view = window.peekDecorView()) != null)) {
          view.cancelPendingInputEvents();
       }
       return;
    }
    private static final void _init_$lambda$3(ComponentActivity p0,LifecycleOwner p1,Lifecycle$Event p2){
       ckf.g(p0, "this$0");
       ckf.g(p1, "<anonymous parameter 0>");
       ckf.g(p2, "event");
       if (p2 == Lifecycle$Event.ON_DESTROY) {
          p0.contextAwareHelper.clearAvailableContext();
          if (!p0.isChangingConfigurations()) {
             p0.getViewModelStore().clear();
          }
          p0.reportFullyDrawnExecutor.activityDestroyed();
       }
       return;
    }
    private static final Bundle _init_$lambda$4(ComponentActivity p0){
       ckf.g(p0, "this$0");
       Bundle uBundle = new Bundle();
       p0.activityResultRegistry.onSaveInstanceState(uBundle);
       return uBundle;
    }
    private static final void _init_$lambda$5(ComponentActivity p0,Context p1){
       Bundle uBundle;
       ckf.g(p0, "this$0");
       ckf.g(p1, "it");
       if ((uBundle = p0.getSavedStateRegistry().consumeRestoredStateForKey("android:support:activity-result")) != null) {
          p0.activityResultRegistry.onRestoreInstanceState(uBundle);
       }
       return;
    }
    public static final void access$addObserverForBackInvoker(ComponentActivity p0,OnBackPressedDispatcher p1){
       p0.addObserverForBackInvoker(p1);
    }
    public static final void access$ensureViewModelStore(ComponentActivity p0){
       p0.ensureViewModelStore();
    }
    public static final ComponentActivity$ReportFullyDrawnExecutor access$getReportFullyDrawnExecutor$p(ComponentActivity p0){
       return p0.reportFullyDrawnExecutor;
    }
    public static final void access$onBackPressed$s1027565324(ComponentActivity p0){
       super.onBackPressed();
    }
    private final void addObserverForBackInvoker(OnBackPressedDispatcher p0){
       this.getLifecycle().addObserver(new ua4(p0, this));
    }
    private static final void addObserverForBackInvoker$lambda$7(OnBackPressedDispatcher p0,ComponentActivity p1,LifecycleOwner p2,Lifecycle$Event p3){
       ckf.g(p0, "$dispatcher");
       ckf.g(p1, "this$0");
       ckf.g(p2, "<anonymous parameter 0>");
       ckf.g(p3, "event");
       if (p3 == Lifecycle$Event.ON_CREATE) {
          p0.setOnBackInvokedDispatcher(ComponentActivity$Api33Impl.INSTANCE.getOnBackInvokedDispatcher(p1));
       }
       return;
    }
    public static void b3(OnBackPressedDispatcher p0,ComponentActivity p1,LifecycleOwner p2,Lifecycle$Event p3){
       ComponentActivity.addObserverForBackInvoker$lambda$7(p0, p1, p2, p3);
    }
    public static void c3(ComponentActivity p0,Context p1){
       ComponentActivity._init_$lambda$5(p0, p1);
    }
    private final ComponentActivity$ReportFullyDrawnExecutor createFullyDrawnExecutor(){
       return new ComponentActivity$ReportFullyDrawnExecutorImpl(this);
    }
    public static void d3(ComponentActivity p0){
       ComponentActivity.menuHostHelper$lambda$0(p0);
    }
    public static void e3(ComponentActivity p0,LifecycleOwner p1,Lifecycle$Event p2){
       ComponentActivity._init_$lambda$2(p0, p1, p2);
    }
    private final void ensureViewModelStore(){
       ComponentActivity$NonConfigurationInstances lastNonConfi;
       if (this._viewModelStore == null) {
          if ((lastNonConfi = this.getLastNonConfigurationInstance()) != null) {
             this._viewModelStore = lastNonConfi.getViewModelStore();
          }
          if (this._viewModelStore == null) {
             this._viewModelStore = new ViewModelStore();
          }
       }
       return;
    }
    public static void f3(ComponentActivity p0,LifecycleOwner p1,Lifecycle$Event p2){
       ComponentActivity._init_$lambda$3(p0, p1, p2);
    }
    public static Bundle g3(ComponentActivity p0){
       return ComponentActivity._init_$lambda$4(p0);
    }
    public static void getOnBackPressedDispatcher$annotations(){
    }
    private static void getSavedStateRegistryController$annotations(){
    }
    private static final void menuHostHelper$lambda$0(ComponentActivity p0){
       ckf.g(p0, "this$0");
       p0.invalidateMenu();
    }
    public void addContentView(View p0,ViewGroup$LayoutParams p1){
       this.initializeViewTreeOwners();
       View decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       this.reportFullyDrawnExecutor.viewCreated(decorView);
       super.addContentView(p0, p1);
    }
    public void addMenuProvider(MenuProvider p0){
       ckf.g(p0, "provider");
       this.menuHostHelper.addMenuProvider(p0);
    }
    public void addMenuProvider(MenuProvider p0,LifecycleOwner p1){
       ckf.g(p0, "provider");
       ckf.g(p1, "owner");
       this.menuHostHelper.addMenuProvider(p0, p1);
    }
    public void addMenuProvider(MenuProvider p0,LifecycleOwner p1,Lifecycle$State p2){
       ckf.g(p0, "provider");
       ckf.g(p1, "owner");
       ckf.g(p2, "state");
       this.menuHostHelper.addMenuProvider(p0, p1, p2);
    }
    public final void addOnConfigurationChangedListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onConfigurationChangedListeners.add(p0);
    }
    public final void addOnContextAvailableListener(OnContextAvailableListener p0){
       ckf.g(p0, "listener");
       this.contextAwareHelper.addOnContextAvailableListener(p0);
    }
    public final void addOnMultiWindowModeChangedListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onMultiWindowModeChangedListeners.add(p0);
    }
    public final void addOnNewIntentListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onNewIntentListeners.add(p0);
    }
    public final void addOnPictureInPictureModeChangedListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onPictureInPictureModeChangedListeners.add(p0);
    }
    public final void addOnTrimMemoryListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onTrimMemoryListeners.add(p0);
    }
    public final void addOnUserLeaveHintListener(Runnable p0){
       ckf.g(p0, "listener");
       this.onUserLeaveHintListeners.add(p0);
    }
    public final ActivityResultRegistry getActivityResultRegistry(){
       return this.activityResultRegistry;
    }
    public CreationExtras getDefaultViewModelCreationExtras(){
       Intent intent;
       CreationExtras uCreationExt = null;
       MutableCreationExtras mutableCreat = new MutableCreationExtras(uCreationExt, 1, uCreationExt);
       if (this.getApplication() != null) {
          Application application = this.getApplication();
          ckf.f(application, "application");
          mutableCreat.set(ViewModelProvider$AndroidViewModelFactory.APPLICATION_KEY, application);
       }
       mutableCreat.set(SavedStateHandleSupport.SAVED_STATE_REGISTRY_OWNER_KEY, this);
       mutableCreat.set(SavedStateHandleSupport.VIEW_MODEL_STORE_OWNER_KEY, this);
       if ((intent = this.getIntent()) != null) {
          uCreationExt = intent.getExtras();
       }
       if (uCreationExt != null) {
          mutableCreat.set(SavedStateHandleSupport.DEFAULT_ARGS_KEY, uCreationExt);
       }
       return mutableCreat;
    }
    public ViewModelProvider$Factory getDefaultViewModelProviderFactory(){
       return this.defaultViewModelProviderFactory$delegate.getValue();
    }
    public FullyDrawnReporter getFullyDrawnReporter(){
       return this.fullyDrawnReporter$delegate.getValue();
    }
    public Object getLastCustomNonConfigurationInstance(){
       ComponentActivity$NonConfigurationInstances lastNonConfi;
       Object custom = ((lastNonConfi = this.getLastNonConfigurationInstance()) != null)? lastNonConfi.getCustom(): null;
       return custom;
    }
    public Lifecycle getLifecycle(){
       return super.getLifecycle();
    }
    public final OnBackPressedDispatcher getOnBackPressedDispatcher(){
       return this.onBackPressedDispatcher$delegate.getValue();
    }
    public final SavedStateRegistry getSavedStateRegistry(){
       return this.savedStateRegistryController.getSavedStateRegistry();
    }
    public ViewModelStore getViewModelStore(){
       if (this.getApplication() == null) {
          throw new IllegalStateException("Your activity is not yet attached to the Application instance. You can\'t request ViewModel before onCreate call.");
       }
       this.ensureViewModelStore();
       ComponentActivity t_viewModelS = this._viewModelStore;
       ckf.d(t_viewModelS);
       return t_viewModelS;
    }
    public void initializeViewTreeOwners(){
       View decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       ViewTreeLifecycleOwner.set(decorView, this);
       decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       ViewTreeViewModelStoreOwner.set(decorView, this);
       decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       ViewTreeSavedStateRegistryOwner.set(decorView, this);
       decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       ViewTreeOnBackPressedDispatcherOwner.set(decorView, this);
       decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       ViewTreeFullyDrawnReporterOwner.set(decorView, this);
    }
    public void invalidateMenu(){
       this.invalidateOptionsMenu();
    }
    public void onActivityResult(int p0,int p1,Intent p2){
       if (!this.activityResultRegistry.dispatchResult(p0, p1, p2)) {
          super.onActivityResult(p0, p1, p2);
       }
       return;
    }
    public void onBackPressed(){
       this.getOnBackPressedDispatcher().onBackPressed();
    }
    public void onConfigurationChanged(Configuration p0){
       ckf.g(p0, "newConfig");
       super.onConfigurationChanged(p0);
       Iterator iterator = this.onConfigurationChangedListeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().accept(p0);
       }
       return;
    }
    public void onCreate(Bundle p0){
       ComponentActivity tcontentLayo;
       this.savedStateRegistryController.performRestore(p0);
       this.contextAwareHelper.dispatchOnContextAvailable(this);
       super.onCreate(p0);
       ReportFragment.Companion.injectIfNeededIn(this);
       if ((tcontentLayo = this.contentLayoutId) != null) {
          this.setContentView(tcontentLayo);
       }
       return;
    }
    public boolean onCreatePanelMenu(int p0,Menu p1){
       ckf.g(p1, "menu");
       if (!p0) {
          super.onCreatePanelMenu(p0, p1);
          this.menuHostHelper.onCreateMenu(p1, this.getMenuInflater());
       }
       return true;
    }
    public boolean onMenuItemSelected(int p0,MenuItem p1){
       ckf.g(p1, "item");
       if (super.onMenuItemSelected(p0, p1)) {
          return true;
       }
       boolean b = (!p0)? this.menuHostHelper.onMenuItemSelected(p1): false;
       return b;
    }
    public void onMultiWindowModeChanged(boolean p0){
       if (this.dispatchingOnMultiWindowModeChanged != null) {
          return;
       }
       Iterator iterator = this.onMultiWindowModeChangedListeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().accept(new MultiWindowModeChangedInfo(p0));
       }
       return;
    }
    public void onMultiWindowModeChanged(boolean p0,Configuration p1){
       ckf.g(p1, "newConfig");
       this.dispatchingOnMultiWindowModeChanged = true;
       super.onMultiWindowModeChanged(p0, p1);
       this.dispatchingOnMultiWindowModeChanged = false;
       Iterator iterator = this.onMultiWindowModeChangedListeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().accept(new MultiWindowModeChangedInfo(p0, p1));
       }
       return;
    }
    public void onNewIntent(Intent p0){
       ckf.g(p0, "intent");
       super.onNewIntent(p0);
       Iterator iterator = this.onNewIntentListeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().accept(p0);
       }
       return;
    }
    public void onPanelClosed(int p0,Menu p1){
       ckf.g(p1, "menu");
       this.menuHostHelper.onMenuClosed(p1);
       super.onPanelClosed(p0, p1);
    }
    public void onPictureInPictureModeChanged(boolean p0){
       if (this.dispatchingOnPictureInPictureModeChanged != null) {
          return;
       }
       Iterator iterator = this.onPictureInPictureModeChangedListeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().accept(new PictureInPictureModeChangedInfo(p0));
       }
       return;
    }
    public void onPictureInPictureModeChanged(boolean p0,Configuration p1){
       ckf.g(p1, "newConfig");
       this.dispatchingOnPictureInPictureModeChanged = true;
       super.onPictureInPictureModeChanged(p0, p1);
       this.dispatchingOnPictureInPictureModeChanged = false;
       Iterator iterator = this.onPictureInPictureModeChangedListeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().accept(new PictureInPictureModeChangedInfo(p0, p1));
       }
       return;
    }
    public boolean onPreparePanel(int p0,View p1,Menu p2){
       ckf.g(p2, "menu");
       if (!p0) {
          super.onPreparePanel(p0, p1, p2);
          this.menuHostHelper.onPrepareMenu(p2);
       }
       return true;
    }
    public void onRequestPermissionsResult(int p0,String[] p1,int[] p2){
       ckf.g(p1, "permissions");
       ckf.g(p2, "grantResults");
       if (!this.activityResultRegistry.dispatchResult(p0, -1, new Intent().putExtra("androidx.activity.result.contract.extra.PERMISSIONS", p1).putExtra("androidx.activity.result.contract.extra.PERMISSION_GRANT_RESULTS", p2)) && Build$VERSION.SDK_INT >= 23) {
          super.onRequestPermissionsResult(p0, p1, p2);
       }
       return;
    }
    public Object onRetainCustomNonConfigurationInstance(){
       return null;
    }
    public final Object onRetainNonConfigurationInstance(){
       ComponentActivity t_viewModelS;
       ComponentActivity$NonConfigurationInstances lastNonConfi;
       Object obj = this.onRetainCustomNonConfigurationInstance();
       if ((t_viewModelS = this._viewModelStore) == null && (lastNonConfi = this.getLastNonConfigurationInstance()) != null) {
          t_viewModelS = lastNonConfi.getViewModelStore();
       }
       if (t_viewModelS == null && obj == null) {
          return null;
       }else {
          lastNonConfi = new ComponentActivity$NonConfigurationInstances();
          lastNonConfi.setCustom(obj);
          lastNonConfi.setViewModelStore(t_viewModelS);
          return lastNonConfi;
       }
    }
    public void onSaveInstanceState(Bundle p0){
       ckf.g(p0, "outState");
       if (this.getLifecycle() instanceof LifecycleRegistry) {
          Lifecycle lifecycle = this.getLifecycle();
          ckf.e(lifecycle, "null cannot be cast to non-null type androidx.lifecycle.LifecycleRegistry");
          lifecycle.setCurrentState(Lifecycle$State.CREATED);
       }
       super.onSaveInstanceState(p0);
       this.savedStateRegistryController.performSave(p0);
       return;
    }
    public void onTrimMemory(int p0){
       super.onTrimMemory(p0);
       Iterator iterator = this.onTrimMemoryListeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().accept(Integer.valueOf(p0));
       }
       return;
    }
    public void onUserLeaveHint(){
       super.onUserLeaveHint();
       Iterator iterator = this.onUserLeaveHintListeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().run();
       }
       return;
    }
    public Context peekAvailableContext(){
       return this.contextAwareHelper.peekAvailableContext();
    }
    public final ActivityResultLauncher registerForActivityResult(ActivityResultContract p0,ActivityResultCallback p1){
       ckf.g(p0, "contract");
       ckf.g(p1, "callback");
       return this.registerForActivityResult(p0, this.activityResultRegistry, p1);
    }
    public final ActivityResultLauncher registerForActivityResult(ActivityResultContract p0,ActivityResultRegistry p1,ActivityResultCallback p2){
       ckf.g(p0, "contract");
       ckf.g(p1, "registry");
       ckf.g(p2, "callback");
       return p1.register("activity_rq#"+this.nextLocalRequestCode.getAndIncrement(), this, p0, p2);
    }
    public void removeMenuProvider(MenuProvider p0){
       ckf.g(p0, "provider");
       this.menuHostHelper.removeMenuProvider(p0);
    }
    public final void removeOnConfigurationChangedListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onConfigurationChangedListeners.remove(p0);
    }
    public final void removeOnContextAvailableListener(OnContextAvailableListener p0){
       ckf.g(p0, "listener");
       this.contextAwareHelper.removeOnContextAvailableListener(p0);
    }
    public final void removeOnMultiWindowModeChangedListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onMultiWindowModeChangedListeners.remove(p0);
    }
    public final void removeOnNewIntentListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onNewIntentListeners.remove(p0);
    }
    public final void removeOnPictureInPictureModeChangedListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onPictureInPictureModeChangedListeners.remove(p0);
    }
    public final void removeOnTrimMemoryListener(Consumer p0){
       ckf.g(p0, "listener");
       this.onTrimMemoryListeners.remove(p0);
    }
    public final void removeOnUserLeaveHintListener(Runnable p0){
       ckf.g(p0, "listener");
       this.onUserLeaveHintListeners.remove(p0);
    }
    public void reportFullyDrawn(){
       if (r8u.d()) {
          r8u.a("reportFullyDrawn\(\) for ComponentActivity");
       }
       super.reportFullyDrawn();
       this.getFullyDrawnReporter().fullyDrawnReported();
       r8u.b();
       return;
    }
    public void setContentView(int p0){
       this.initializeViewTreeOwners();
       View decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       this.reportFullyDrawnExecutor.viewCreated(decorView);
       super.setContentView(p0);
    }
    public void setContentView(View p0){
       this.initializeViewTreeOwners();
       View decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       this.reportFullyDrawnExecutor.viewCreated(decorView);
       super.setContentView(p0);
    }
    public void setContentView(View p0,ViewGroup$LayoutParams p1){
       this.initializeViewTreeOwners();
       View decorView = this.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       this.reportFullyDrawnExecutor.viewCreated(decorView);
       super.setContentView(p0, p1);
    }
    public void startActivityForResult(Intent p0,int p1){
       ckf.g(p0, "intent");
       super.startActivityForResult(p0, p1);
    }
    public void startActivityForResult(Intent p0,int p1,Bundle p2){
       ckf.g(p0, "intent");
       super.startActivityForResult(p0, p1, p2);
    }
    public void startIntentSenderForResult(IntentSender p0,int p1,Intent p2,int p3,int p4,int p5){
       ckf.g(p0, "intent");
       super.startIntentSenderForResult(p0, p1, p2, p3, p4, p5);
    }
    public void startIntentSenderForResult(IntentSender p0,int p1,Intent p2,int p3,int p4,int p5,Bundle p6){
       ckf.g(p0, "intent");
       super.startIntentSenderForResult(p0, p1, p2, p3, p4, p5, p6);
    }
}
