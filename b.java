package b;
import tb.a07;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.Number;
import java.lang.Integer;

public final class b	// class@001d44 from classes2.dex
{
    public static IpChange $ipChange;
    public static final b$a Companion;
    public static final int a;
    public static final int b;
    public static final int c;
    public static final int d;
    public static final int e;
    public static final int f;
    public static final int g;

    static {
       b.Companion = new b$a(null);
       b.c(0);
       b.c(1);
       b.c(2);
       b.c(3);
       b.c(4);
       b.a = b.c(5);
       b.c(6);
       b.c(7);
       b.c(8);
       b.c = b.c(9);
       b.d = b.c(16);
       b.c(0x12000001);
       b.c(0x12000002);
       b.c(0x12000003);
       b.c(0x12000004);
       b.c(0x12000005);
       b.c(0x12000006);
       b.c(0x12000007);
       b.c(0x11000001);
       b.c(0x11000002);
       b.c(0x11000003);
       b.c(0x11000004);
       b.c(0x11000005);
       b.c(0x11000006);
       b.c(0x11000007);
       b.c(0x10000001);
       b.b = b.c(0x13000001);
       b.c(0x13000002);
       b.c(0x13000003);
       b.c(0x13000004);
       b.e = b.c(0x13000005);
       b.f = b.c(0x13000006);
       b.c(0x20000001);
       b.c(0x31000001);
       b.c(0x31000002);
       b.c(0x30000002);
       b.c(0x30000003);
       b.c(0x30000004);
       b.g = b.c(0x30000001);
    }
    public static final int a(){
       IpChange $ipChange = b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.a;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("3ac1ee66", objArray).intValue();
    }
    public static final int b(){
       IpChange $ipChange = b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.b;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("c541a42c", objArray).intValue();
    }
    public static int c(int p0){
       Object[] objArray;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[]{new Integer(p0)};
          objArray = $ipChange.ipc$dispatch("f7c3fb1c", objArray).intValue();
       }
       return objArray;
    }
    public static final int d(){
       IpChange $ipChange = b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.c;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("85bb9893", objArray).intValue();
    }
    public static final int e(){
       IpChange $ipChange = b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.d;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("ed9cc572", objArray).intValue();
    }
    public static final int f(){
       IpChange $ipChange = b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.f;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("592d3f2a", objArray).intValue();
    }
    public static final int g(){
       IpChange $ipChange = b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.e;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("355d5a68", objArray).intValue();
    }
    public static final int h(){
       IpChange $ipChange = b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return b.g;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("59b91ec5", objArray).intValue();
    }
}
