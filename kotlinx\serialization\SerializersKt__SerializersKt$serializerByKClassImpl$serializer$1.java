package kotlinx.serialization.SerializersKt__SerializersKt$serializerByKClassImpl$serializer$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.v530;
import kotlinx.serialization.SerializationException;
import java.lang.String;

public final class SerializersKt__SerializersKt$serializerByKClassImpl$serializer$1 extends Lambda implements d1a	// class@000722 from classes11.dex
{
    public static final SerializersKt__SerializersKt$serializerByKClassImpl$serializer$1 INSTANCE;

    static {
       SerializersKt__SerializersKt$serializerByKClassImpl$serializer$1.INSTANCE = new SerializersKt__SerializersKt$serializerByKClassImpl$serializer$1();
    }
    public void SerializersKt__SerializersKt$serializerByKClassImpl$serializer$1(){
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final v530 invoke(){
       throw new SerializationException("It is not possible to retrieve an array serializer using KClass alone, use KType instead or ArraySerializer factory");
    }
}
