package kotlinx.serialization.SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.wyf;
import java.util.List;
import tb.x530;
import java.lang.String;
import tb.ckf;
import tb.tb40;
import tb.ub40;
import tb.sb40;
import kotlinx.serialization.SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1$1;
import tb.d1a;

public final class SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1 extends Lambda implements u1a	// class@00071c from classes11.dex
{
    public static final SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1 INSTANCE;

    static {
       SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1.INSTANCE = new SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1();
    }
    public void SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1(){
       super(2);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final x530 invoke(wyf p0,List p1){
       ckf.g(p0, "clazz");
       ckf.g(p1, "types");
       List list = sb40.g(ub40.a(), p1, true);
       ckf.d(list);
       return sb40.a(p0, list, new SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1$1(p1));
    }
}
