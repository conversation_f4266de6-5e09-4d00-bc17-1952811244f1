package kotlinx.coroutines.JobSupport$e;
import tb.ruf;
import kotlinx.coroutines.JobSupport;
import tb.k9p;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;

public final class JobSupport$e extends ruf	// class@0004a7 from classes11.dex
{
    public final k9p h;
    public final JobSupport i;

    public void JobSupport$e(JobSupport p0,k9p p1){
       this.i = p0;
       super();
       this.h = p1;
    }
    public Object invoke(Object p0){
       this.p(p0);
       return xhv.INSTANCE;
    }
    public void p(Throwable p0){
       this.h.e(this.i, xhv.INSTANCE);
    }
}
