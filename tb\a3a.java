package tb.a3a;
import tb.bmi;
import tb.t2o;
import tb.a3a$a;
import tb.a07;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import tb.bmi$a;

public final class a3a extends bmi	// class@001752 from classes9.dex
{
    public static final a3a$a Companion;
    public static final String HIGHLIGHT_INTERACT_SWITCH;
    public static final String INTERACT_SWITCH_STATE_CHANGE_ARGS_STATE;
    public static final String INTERACT_SWITCH_STATE_CHANGE_ARGS_STATE_OFF;
    public static final String INTERACT_SWITCH_STATE_CHANGE_ARGS_STATE_ON;
    public static final String INTERACT_SWITCH_STATE_CHANGE_NAME;
    public static final String MSG_INIT_URL;
    public static final String MSG_URL_REFRESH;
    public static final String RECEIVER_GG;
    public static final String SENDER_GG;

    static {
       t2o.a(0x2ba000ca);
       a3a.Companion = new a3a$a(null);
    }
    public void a3a(String p0,JSONObject p1,long p2,String p3,String p4,bmi$a p5){
       super(p0, p1, p2, p3, p4, p5);
    }
    public void a3a(String p0,JSONObject p1,long p2,String p3,String p4,bmi$a p5,a07 p6){
       super(p0, p1, p2, p3, p4, p5);
    }
}
