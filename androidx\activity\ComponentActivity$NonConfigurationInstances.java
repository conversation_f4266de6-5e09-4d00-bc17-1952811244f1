package androidx.activity.ComponentActivity$NonConfigurationInstances;
import java.lang.Object;
import androidx.lifecycle.ViewModelStore;

public final class ComponentActivity$NonConfigurationInstances	// class@000438 from classes.dex
{
    private Object custom;
    private ViewModelStore viewModelStore;

    public void ComponentActivity$NonConfigurationInstances(){
       super();
    }
    public final Object getCustom(){
       return this.custom;
    }
    public final ViewModelStore getViewModelStore(){
       return this.viewModelStore;
    }
    public final void setCustom(Object p0){
       this.custom = p0;
    }
    public final void setViewModelStore(ViewModelStore p0){
       this.viewModelStore = p0;
    }
}
