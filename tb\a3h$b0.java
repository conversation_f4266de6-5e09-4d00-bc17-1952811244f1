package tb.a3h$b0;
import java.lang.Runnable;
import tb.a3h;
import java.util.ArrayList;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a3h$s0;
import java.lang.Boolean;

public class a3h$b0 implements Runnable	// class@001e66 from classes10.dex
{
    public final boolean a;
    public final ArrayList b;
    public final a3h c;
    public static IpChange $ipChange;

    public void a3h$b0(a3h p0,boolean p1,ArrayList p2){
       this.c = p0;
       this.a = p1;
       this.b = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$b0.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.C(this.c) != null){
          a3h.C(this.c).d(Boolean.valueOf(this.a), this.b);
       }
       return;
    }
}
