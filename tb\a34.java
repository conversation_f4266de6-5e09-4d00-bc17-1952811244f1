package tb.a34;
import tb.t2o;
import java.lang.Object;
import android.util.SparseArray;
import tb.a34$e;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.wnb;
import java.lang.Integer;
import com.taobao.tao.messagekit.core.utils.MsgLog;
import tb.j8l;
import com.taobao.tao.messagekit.base.MsgRouter;
import tb.h3m;
import tb.ub50;
import tb.a34$b;
import tb.vl50;
import tb.a34$a;
import tb.cc50;
import tb.a950;
import tb.sa50;
import tb.a34$d;
import tb.a34$c;
import tb.an50;
import tb.cn50;

public class a34	// class@001750 from classes9.dex
{
    public final SparseArray a;
    public final a950 b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x2780001b);
    }
    public void a34(){
       super();
       this.a = new SparseArray();
       this.b = new a34$e(this);
    }
    public static SparseArray a(a34 p0){
       IpChange $ipChange = a34.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.a;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("108b22ef", objArray);
    }
    public wnb b(int p0){
       IpChange $ipChange = a34.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a.get(p0);
       }
       Object[] objArray = new Object[]{this,new Integer(p0)};
       return $ipChange.ipc$dispatch("eba39ac2", objArray);
    }
    public void c(){
       Object[] objArray;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a34.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("7900fd98", objArray);
          return;
       }else {
          objArray = new Object[i1];
          objArray[i] = "inject";
          MsgLog.i("CommandManager", objArray);
          if (j8l.O()) {
             MsgRouter.e().c().a().b(new a34$b(this)).c(new a34$a(this)).g(this.b);
          }else {
             MsgRouter.e().c().a().b(new a34$d(this)).c(new a34$c(this)).l(cn50.a()).g(this.b);
          }
          return;
       }
    }
    public void d(int p0,wnb p1){
       IpChange $ipChange = a34.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("491db24b", objArray);
          return;
       }else {
          this.a.put(p0, p1);
          return;
       }
    }
}
