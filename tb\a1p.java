package tb.a1p;
import tb.t2o;
import tb.a1p$a;
import tb.a07;
import java.lang.String;
import java.lang.Object;
import tb.ckf;
import org.json.JSONObject;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;

public final class a1p	// class@001ad5 from classes8.dex
{
    public final String a;
    public final int b;
    public final int c;
    public final long d;
    public final long e;
    public static IpChange $ipChange;
    public static final a1p$a Companion;

    static {
       t2o.a(0x332000d2);
       a1p.Companion = new a1p$a(null);
    }
    public void a1p(String p0,int p1,int p2,long p3,long p4){
       ckf.g(p0, "url");
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
       this.d = p3;
       this.e = p4;
    }
    public static final a1p a(JSONObject p0){
       IpChange $ipChange = a1p.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a1p.Companion.a(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("bc4dea3e", objArray);
    }
    public final long b(){
       IpChange $ipChange = a1p.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.e;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("250d18d", objArray).longValue();
    }
    public final int c(){
       IpChange $ipChange = a1p.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("3c0db5c1", objArray).intValue();
    }
    public final long d(){
       IpChange $ipChange = a1p.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.d;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("490f0b94", objArray).longValue();
    }
    public final int e(){
       IpChange $ipChange = a1p.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6ffb341b", objArray).intValue();
    }
    public final String f(){
       IpChange $ipChange = a1p.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("de8f0660", objArray);
    }
}
