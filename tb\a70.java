package tb.a70;
import tb.t2o;
import android.app.Application;
import com.taobao.tao.Globals;
import android.content.Context;
import com.taobao.tao.util.DensityUtil;
import tb.a70$a;
import java.lang.Object;
import android.view.ViewConfiguration;
import android.view.MotionEvent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.z60;
import java.lang.Boolean;
import androidx.core.view.MotionEventCompat;
import java.lang.Math;
import tb.uqa;
import tb.c4b;
import com.taobao.tao.topmultitab.service.controller.IHomeControllerService;
import java.lang.Class;
import com.taobao.infoflow.protocol.subservice.ISubService;
import com.taobao.tao.topmultitab.protocol.IHomeSubTabController;
import java.lang.Float;
import tb.r5a;
import tb.bqa;

public class a70	// class@00176b from classes9.dex
{
    public float a;
    public boolean b;
    public boolean c;
    public int d;
    public final int e;
    public boolean f;
    public boolean g;
    public final a70$a h;
    public float i;
    public static IpChange $ipChange;
    public static final int MAX_DISTANCE_Y;

    static {
       t2o.a(0x2e0004d7);
       a70.MAX_DISTANCE_Y = DensityUtil.dip2px(Globals.getApplication(), 42.00f);
    }
    public void a70(a70$a p0){
       super();
       this.b = false;
       this.c = false;
       this.d = -1;
       this.i = 0;
       this.h = p0;
       this.e = ViewConfiguration.get(Globals.getApplication()).getScaledPagingTouchSlop();
    }
    public final void a(MotionEvent p0){
       int action;
       int i = 2;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          objArray[1] = p0;
          $ipChange.ipc$dispatch("fa9f2464", objArray);
          return;
       }else if(this.b != null){
          return;
       }else if(action = p0.getAction()){
          if (action != 1) {
             if (action == i) {
                this.o(p0);
             }
          }else {
             this.p(p0);
          }
       }else {
          this.n(p0);
       }
       return;
    }
    public void b(MotionEvent p0){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7bb68bd1", objArray);
          return;
       }else if(!z60.c().e()){
          return;
       }else {
          this.a(p0);
          return;
       }
    }
    public final boolean c(MotionEvent p0){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ae7056f3", objArray).booleanValue();
       }else if(this.b == null && (this.c != null && (this.i() && this.d(p0)))){
          return 1;
       }else {
          return 0;
       }
    }
    public final boolean d(MotionEvent p0){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("12743e7d", objArray).booleanValue();
       }else {
          float f = MotionEventCompat.getX(p0, MotionEventCompat.findPointerIndex(p0, this.d)) - this.a;
          float f1 = Math.abs(f);
          if (this.g(f)) {
             return 0;
          }
          if ((((float)this.e * z60.c().a()) - f1) > 0) {
             return 1;
          }
          return 0;
       }
    }
    public final boolean e(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("39b3fdd6", objArray).booleanValue();
       }else if(!this.f()){
          uqa.a("Scene.ACCIDENT_SLIDE", "Conditional judgment", "Not eligible for rolling ");
          return i;
       }else {
          return i1;
       }
    }
    public final boolean f(){
       IHomeControllerService iHomeControl;
       IHomeSubTabController currentSubTa;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("f2a2257a", objArray).booleanValue();
       }else if((iHomeControl = c4b.i().h(IHomeControllerService.class)) == null){
          return i;
       }else if((currentSubTa = iHomeControl.getCurrentSubTabController()) == null){
          return i;
       }else if(currentSubTa.getSubContainerScrollY() <= z60.c().b()){
          i = true;
       }
       return i;
    }
    public final boolean g(float p0){
       IHomeControllerService iHomeControl;
       int i = 1;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Float(p0)};
          return $ipChange.ipc$dispatch("ccd7876c", objArray).booleanValue();
       }else if((iHomeControl = c4b.i().h(IHomeControllerService.class)) == null){
          return 0;
       }else if((p0) < 0 && iHomeControl.isAtRecommendTab()){
          i = false;
       }
       return i;
    }
    public final boolean h(MotionEvent p0){
       a70 td;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("dbfd3702", objArray).booleanValue();
       }else if((td = this.d) == -1){
          return 1;
       }else if(p0.findPointerIndex(td) == -1){
          return 1;
       }else {
          return 0;
       }
    }
    public final boolean i(){
       IHomeControllerService iHomeControl;
       IHomeSubTabController currentSubTa;
       int i = 0;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("9e09a573", objArray).booleanValue();
       }else if((iHomeControl = c4b.i().h(IHomeControllerService.class)) == null){
          return i;
       }else if((currentSubTa = iHomeControl.getCurrentSubTabController()) == null){
          return i;
       }else {
          return currentSubTa.isHandleUpAndDownScrollingEvent();
       }
    }
    public final boolean j(){
       IHomeControllerService iHomeControl;
       int i = 0;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("e9b94240", objArray).booleanValue();
       }else if((iHomeControl = c4b.i().h(IHomeControllerService.class)) != null){
          i = iHomeControl.isSubscribeTab();
       }
       return i;
    }
    public final void k(MotionEvent p0){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6fc13b3", objArray);
          return;
       }else if(this.f != null){
          return;
       }else {
          this.f = true;
          p0.getX();
          p0.getY();
          this.r(MotionEvent.obtain(p0.getDownTime(), p0.getEventTime(), 0, p0.getX(), p0.getY(), p0.getMetaState()));
          return;
       }
    }
    public boolean l(MotionEvent p0,boolean p1){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          return $ipChange.ipc$dispatch("efc2a1e", objArray).booleanValue();
       }else {
          this.b = p1;
          return p1;
       }
    }
    public void m(){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("90abdd0", objArray);
          return;
       }else if(this.j() && this.g != null){
          r5a.i("Page_Home", 2101, "Page_Home_Accident_Slideto-tabdingyue", "spm=a2141.1.searchbar.dingyue");
          String[] stringArray = new String[]{"accident slide"};
          bqa.d("AccidentSlideFeature", stringArray);
       }
       this.g = false;
       return;
    }
    public final void n(MotionEvent p0){
       int i = 0;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("cfd2272e", objArray);
          return;
       }else {
          boolean b = this.e();
          this.c = b;
          if (b) {
             this.d = MotionEventCompat.getPointerId(p0, i);
             this.a = p0.getX();
             p0.getY();
          }
          return;
       }
    }
    public final void o(MotionEvent p0){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b190c21d", objArray);
          return;
       }else if(this.h(p0)){
          return;
       }else if(this.c(p0)){
          this.g = true;
          this.k(p0);
          this.r(this.s(p0));
       }
       return;
    }
    public final void p(MotionEvent p0){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c717e367", objArray);
          return;
       }else if(this.c != null){
          if (this.h(p0)) {
             p0 = MotionEvent.obtain(p0.getDownTime(), p0.getEventTime(), 1, p0.getX(), p0.getY(), p0.getMetaState());
          }
          this.r(p0);
          this.q();
       }
       return;
    }
    public final void q(){
       int i = 0;
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("788e6256", objArray);
          return;
       }else {
          this.d = -1;
          this.f = i;
          this.a = 0;
          this.i = 0;
          return;
       }
    }
    public final void r(MotionEvent p0){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("59ee4da0", objArray);
          return;
       }else {
          this.h.onTouchEvent(p0);
          return;
       }
    }
    public final MotionEvent s(MotionEvent p0){
       IpChange $ipChange = a70.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("12e4a201", objArray);
       }else {
          int i = p0.findPointerIndex(this.d);
          float y = p0.getY(i);
          if (!(0 - this.i)) {
             this.i = y;
          }
          p0.setLocation(p0.getX(i), this.i);
          return p0;
       }
    }
}
