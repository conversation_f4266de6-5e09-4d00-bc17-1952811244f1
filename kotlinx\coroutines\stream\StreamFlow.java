package kotlinx.coroutines.stream.StreamFlow;
import tb.qp9;
import java.lang.Class;
import java.lang.String;
import java.util.concurrent.atomic.AtomicIntegerFieldUpdater;
import java.util.stream.Stream;
import java.lang.Object;
import tb.sp9;
import tb.ar4;
import kotlinx.coroutines.stream.StreamFlow$collect$1;
import tb.dkf;
import java.util.Iterator;
import kotlin.b;
import java.lang.IllegalStateException;
import tb.fpq;
import tb.gpq;
import tb.xhv;

public final class StreamFlow implements qp9	// class@0006bf from classes11.dex
{
    private final Stream a;
    private int b;
    private static final AtomicIntegerFieldUpdater c;

    static {
       StreamFlow.c = AtomicIntegerFieldUpdater.newUpdater(StreamFlow.class, "b");
    }
    public void StreamFlow(Stream p0){
       super();
       this.a = p0;
       this.b = 0;
    }
    private final int d(){
       return this.b;
    }
    private static final AtomicIntegerFieldUpdater f(){
       return StreamFlow.c;
    }
    private final void g(int p0){
       this.b = p0;
    }
    public Object a(sp9 p0,ar4 p1){
       StreamFlow$collect$1 uocollect$1;
       int i1;
       StreamFlow$collect$1 label1;
       StreamFlow$collect$1 l$2;
       StreamFlow$collect$1 l$0;
       if (p1 instanceof StreamFlow$collect$1) {
          uocollect$1 = p1;
          StreamFlow$collect$1 label = uocollect$1.label;
          int i = Integer.MIN_VALUE;
          if (i1 = label & i) {
             int i2 = label - i;
             uocollect$1.label = i2;
          label_0018 :
             StreamFlow$collect$1 result = uocollect$1.result;
             Object obj = dkf.d();
             if ((label1 = uocollect$1.label) != null) {
                if (label1 == 1) {
                   l$2 = uocollect$1.L$2;
                   l$0 = uocollect$1.L$0;
                   b.b(result);
                   result = uocollect$1.L$1;
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                if (StreamFlow.f().compareAndSet(this, 0, 1)) {
                   l$0 = this;
                   result = p0;
                   l$2 = fpq.a(this.a);
                }else {
                   throw new IllegalStateException("Stream.consumeAsFlow can be collected only once");
                }
             }
             while (true) {
                if (l$2.hasNext()) {
                   uocollect$1.L$0 = l$0;
                   uocollect$1.L$1 = result;
                   uocollect$1.L$2 = l$2;
                   uocollect$1.label = 1;
                   if (result.emit(l$2.next(), uocollect$1) == obj) {
                      break ;
                   }
                }else {
                   gpq.a(l$0.a);
                   return xhv.INSTANCE;
                }
             }
             return obj;
          }
       }
       uocollect$1 = new StreamFlow$collect$1(this, p1);
       goto label_0018 ;
    }
}
