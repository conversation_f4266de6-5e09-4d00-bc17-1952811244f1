package kotlinx.coroutines.TimeoutKt;
import kotlinx.coroutines.h;
import kotlinx.coroutines.m;
import kotlinx.coroutines.TimeoutCancellationException;
import tb.ka7;
import tb.s08;
import kotlin.time.DurationUnit;
import tb.w08;
import java.lang.String;
import java.lang.StringBuilder;
import kotlinx.coroutines.p;
import tb.u1a;
import java.lang.Object;
import tb.wuo;
import kotlin.coroutines.d;
import tb.ar4;
import kotlinx.coroutines.DelayKt;
import kotlinx.coroutines.a;
import java.lang.Runnable;
import tb.rr7;
import tb.quf;
import tb.pgv;
import kotlinx.coroutines.TimeoutKt$withTimeoutOrNull$1;
import tb.dkf;
import kotlin.jvm.internal.Ref$ObjectRef;
import kotlin.b;
import java.lang.IllegalStateException;
import tb.jv6;

public final class TimeoutKt	// class@0004b0 from classes11.dex
{

    public static final TimeoutCancellationException a(long p0,h p1,m p2){
       String str;
       if (p1 instanceof ka7) {
       }else {
          p1 = null;
       }
       if (p1 == null || (str = p1.w(w08.p(p0, DurationUnit.MILLISECONDS))) == null) {
          str = "Timed out waiting for "+p0+" ms";
       }
       return new TimeoutCancellationException(str, p2);
    }
    public static final Object b(p p0,u1a p1){
       quf.e(p0, DelayKt.c(p0.f.getContext()).b(p0.g, p0, p0.getContext()));
       return pgv.d(p0, p0, p1);
    }
    public static final Object c(long p0,u1a p1,ar4 p2){
       TimeoutKt$withTimeoutOrNull$1 owithTimeout;
       TimeoutKt$withTimeoutOrNull$1 label1;
       Ref$ObjectRef objectRef;
       Ref$ObjectRef obj1;
       if (p2 instanceof TimeoutKt$withTimeoutOrNull$1) {
          owithTimeout = p2;
          TimeoutKt$withTimeoutOrNull$1 label = owithTimeout.label;
          int i = Integer.MIN_VALUE;
          if ((label & i)) {
             owithTimeout.label = label - i;
             try{
             label_0018 :
                TimeoutKt$withTimeoutOrNull$1 result = owithTimeout.result;
                Object obj = dkf.d();
                if ((label1 = owithTimeout.label) != null) {
                   if (label1 == 1) {
                      try{
                         b.b(result);
                      }catch(kotlinx.coroutines.TimeoutCancellationException e8){
                      }
                      if (e8.coroutine == owithTimeout.L$1.element) {
                         return null;
                      }else {
                         throw e8;
                      }
                   }else {
                      throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                   }
                }else {
                   b.b(result);
                   if ((p0) <= 0) {
                      return null;
                   }else {
                      objectRef = new Ref$ObjectRef();
                      owithTimeout.L$0 = p1;
                      owithTimeout.L$1 = objectRef;
                      owithTimeout.J$0 = p0;
                      owithTimeout.label = 1;
                      p op = new p(p0, owithTimeout);
                      objectRef.element = op;
                      if ((obj1 = TimeoutKt.b(op, p1)) == dkf.d()) {
                         jv6.c(owithTimeout);
                      }
                      if (obj1 == obj) {
                         return obj;
                      }else {
                         result = obj1;
                      }
                   }
                }
                return result;
             }catch(kotlinx.coroutines.TimeoutCancellationException e8){
                obj1 = objectRef;
             }
          }
       }
       owithTimeout = new TimeoutKt$withTimeoutOrNull$1(p2);
       goto label_0018 ;
    }
    public static final Object d(long p0,u1a p1,ar4 p2){
       Object obj;
       if ((p0) <= 0) {
          throw new TimeoutCancellationException("Timed out immediately");
       }
       if ((obj = TimeoutKt.b(new p(p0, p2), p1)) == dkf.d()) {
          jv6.c(p2);
       }
       return obj;
    }
}
