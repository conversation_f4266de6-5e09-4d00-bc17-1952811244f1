package tb.aa0;
import tb.t2o;
import com.taobao.search.searchdoor.activate.data.ActivateBean;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class aa0	// class@001778 from classes9.dex
{
    public final ActivateBean a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x332003ce);
    }
    public void aa0(ActivateBean p0){
       super();
       this.a = p0;
    }
    public static aa0 a(ActivateBean p0){
       IpChange $ipChange = aa0.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new aa0(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("4fd0a496", objArray);
    }
}
