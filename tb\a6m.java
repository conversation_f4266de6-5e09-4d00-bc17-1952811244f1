package tb.a6m;
import tb.t2o;
import java.lang.Object;
import com.taobao.kmp.nexus.arch.openArch.service.playcontrol.state.PlayControlPlayerState;
import tb.j9d;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ckf;

public final class a6m	// class@001b04 from classes8.dex
{
    public PlayControlPlayerState a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3f10032e);
    }
    public void a6m(){
       super();
       this.a = PlayControlPlayerState.Undefined;
    }
    public final j9d a(){
       IpChange $ipChange = a6m.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7da3fd3", objArray);
    }
    public final PlayControlPlayerState b(){
       IpChange $ipChange = a6m.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("143778b3", objArray);
    }
    public final void c(PlayControlPlayerState p0){
       IpChange $ipChange = a6m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("66d323ad", objArray);
          return;
       }else {
          ckf.g(p0, "<set-?>");
          this.a = p0;
          return;
       }
    }
}
