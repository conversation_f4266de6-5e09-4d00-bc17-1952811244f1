package androidx.activity.ViewTreeOnBackPressedDispatcherOwner;
import android.view.View;
import androidx.activity.OnBackPressedDispatcherOwner;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$1;
import tb.g1a;
import tb.sbp;
import tb.acp;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2;
import tb.dcp;
import com.taobao.taobao.R$id;

public final class ViewTreeOnBackPressedDispatcherOwner	// class@000475 from classes.dex
{

    public static final OnBackPressedDispatcherOwner get(View p0){
       ckf.g(p0, "<this>");
       return dcp.r(dcp.v(acp.f(p0, ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$1.INSTANCE), ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2.INSTANCE));
    }
    public static final void set(View p0,OnBackPressedDispatcherOwner p1){
       ckf.g(p0, "<this>");
       ckf.g(p1, "onBackPressedDispatcherOwner");
       p0.setTag(R$id.view_tree_on_back_pressed_dispatcher_owner, p1);
    }
}
