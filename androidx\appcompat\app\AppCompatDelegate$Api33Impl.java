package androidx.appcompat.app.AppCompatDelegate$Api33Impl;
import java.lang.Object;
import android.os.LocaleList;
import android.app.LocaleManager;

public class AppCompatDelegate$Api33Impl	// class@000558 from classes.dex
{

    private void AppCompatDelegate$Api33Impl(){
       super();
    }
    public static LocaleList localeManagerGetApplicationLocales(Object p0){
       return p0.getApplicationLocales();
    }
    public static void localeManagerSetApplicationLocales(Object p0,LocaleList p1){
       p0.setApplicationLocales(p1);
    }
}
