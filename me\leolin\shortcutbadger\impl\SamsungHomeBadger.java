package me.leolin.shortcutbadger.impl.SamsungHomeBadger;
import tb.po1;
import java.lang.String;
import java.lang.Object;
import me.leolin.shortcutbadger.impl.DefaultBadger;
import java.util.List;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.net.Uri;
import android.content.ContentResolver;
import android.database.Cursor;
import android.content.ContentValues;
import tb.cw3;
import java.lang.Integer;

public class SamsungHomeBadger implements po1	// class@000766 from classes11.dex
{
    public final DefaultBadger a;
    public static final String[] b;

    static {
       String[] stringArray = new String[]{"_id","class"};
       SamsungHomeBadger.b = stringArray;
    }
    public void SamsungHomeBadger(){
       super();
       this.a = new DefaultBadger();
    }
    public List a(){
       String[] stringArray = new String[]{"com.sec.android.app.launcher","com.sec.android.app.twlauncher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       SamsungHomeBadger ta;
       Cursor uCursor;
       if ((ta = this.a) != null && ta.c(p0)) {
          ta.b(p0, p1, p2);
       }else {
          Uri uri = Uri.parse("content://com.sec.badge/apps?notify=true");
          ContentResolver contentResol = p0.getContentResolver();
          String[] stringArray = new String[]{p1.getPackageName()};
          if ((uCursor = contentResol.query(uri, SamsungHomeBadger.b, "package=?", stringArray, null)) != null) {
             String className = p1.getClassName();
             int i = 0;
             while (uCursor.moveToNext()) {
                String[] stringArray1 = new String[]{String.valueOf(uCursor.getInt(0))};
                contentResol.update(uri, this.c(p1, p2, 0), "_id=?", stringArray1);
                if (className.equals(uCursor.getString(uCursor.getColumnIndex("class")))) {
                   i = 1;
                }
             }
             if (!i) {
                contentResol.insert(uri, this.c(p1, p2, true));
             }
          }
          cw3.a(uCursor);
       }
       return;
    }
    public final ContentValues c(ComponentName p0,int p1,boolean p2){
       ContentValues uContentValu = new ContentValues();
       if (p2) {
          uContentValu.put("package", p0.getPackageName());
          uContentValu.put("class", p0.getClassName());
       }
       uContentValu.put("badgecount", Integer.valueOf(p1));
       return uContentValu;
    }
}
