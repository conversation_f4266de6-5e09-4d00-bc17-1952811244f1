package tb.a3h$s;
import java.lang.Runnable;
import tb.a3h;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.a3h$s0;

public class a3h$s implements Runnable	// class@001e89 from classes10.dex
{
    public final String a;
    public final String b;
    public final String c;
    public final a3h d;
    public static IpChange $ipChange;

    public void a3h$s(a3h p0,String p1,String p2,String p3){
       this.d = p0;
       this.a = p1;
       this.b = p2;
       this.c = p3;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$s.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.C(this.d) != null){
          a3h.C(this.d).h(this.a, this.b, this.c);
       }
       return;
    }
}
