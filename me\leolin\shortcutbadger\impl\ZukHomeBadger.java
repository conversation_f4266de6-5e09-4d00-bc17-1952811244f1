package me.leolin.shortcutbadger.impl.ZukHomeBadger;
import tb.po1;
import java.lang.Object;
import java.lang.String;
import android.net.Uri;
import java.util.List;
import java.util.Collections;
import android.content.Context;
import android.content.ComponentName;
import android.os.Bundle;
import android.os.BaseBundle;
import android.content.ContentResolver;

public class ZukHomeBadger implements po1	// class@00076c from classes11.dex
{
    public final Uri a;

    public void ZukHomeBadger(){
       super();
       this.a = Uri.parse("content://com.android.badge/badge");
    }
    public List a(){
       return Collections.singletonList("com.zui.launcher");
    }
    public void b(Context p0,ComponentName p1,int p2){
       Bundle uBundle = new Bundle();
       uBundle.putInt("app_badge_count", p2);
       p0.getContentResolver().call(this.a, "setAppBadgeCount", null, uBundle);
    }
}
