package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$drop$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;
import java.lang.StringBuilder;
import java.lang.IllegalArgumentException;

public final class ChannelsKt__DeprecatedKt$drop$1 extends SuspendLambda implements u1a	// class@0004e1 from classes11.dex
{
    public final int $n;
    public final ReceiveChannel $this_drop;
    public int I$0;
    private Object L$0;
    public Object L$1;
    public int label;

    public void ChannelsKt__DeprecatedKt$drop$1(int p0,ReceiveChannel p1,ar4 p2){
       this.$n = p0;
       this.$this_drop = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$drop$1 uodrop$1 = new ChannelsKt__DeprecatedKt$drop$1(this.$n, this.$this_drop, p1);
       uodrop$1.L$0 = p0;
       return uodrop$1;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$drop$1 tL$0;
       Object obj1;
       ChannelsKt__DeprecatedKt$drop$1 tL$1;
       ChannelsKt__DeprecatedKt$drop$1 tL$01;
       int i1;
       Object obj = dkf.d();
       ChannelsKt__DeprecatedKt$drop$1 tlabel = this.label;
       int i = 1;
       if (tlabel != null) {
          if (tlabel != i) {
             if (tlabel != 2) {
                if (tlabel == 3) {
                   tlabel = this.L$1;
                   tL$0 = this.L$0;
                   b.b(p0);
                label_001c :
                   p0 = tL$0;
                label_0079 :
                   this.L$0 = p0;
                   this.L$1 = tlabel;
                   this.label = 2;
                   if ((obj1 = tlabel.a(this)) == obj) {
                      return obj;
                   }else {
                      tL$0 = p0;
                      p0 = obj1;
                   label_0089 :
                      if (p0.booleanValue()) {
                         this.L$0 = tL$0;
                         this.L$1 = tlabel;
                         this.label = 3;
                         if (tL$0.d(tlabel.next(), this) == obj) {
                            return obj;
                         }else {
                            goto label_001c ;
                         }
                      }else {
                         return xhv.INSTANCE;
                      }
                   }
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                tlabel = this.L$1;
                tL$0 = this.L$0;
                b.b(p0);
                goto label_0089 ;
             }
          }else {
             tlabel = this.I$0;
             tL$1 = this.L$1;
             tL$01 = this.L$0;
             b.b(p0);
          label_0063 :
             if (p0.booleanValue()) {
                tL$1.next();
                if (i1 = tlabel - 1) {
                label_0054 :
                   this.L$0 = tL$01;
                   this.L$1 = tL$1;
                   this.I$0 = i1;
                   this.label = i;
                   if ((p0 = tL$1.a(this)) == obj) {
                      return obj;
                   }else {
                      goto label_0063 ;
                   }
                }
             }
             p0 = tL$01;
          label_0073 :
             ChannelIterator uChannelIter = this.$this_drop.iterator();
             goto label_0079 ;
          }
       }else {
          b.b(p0);
          p0 = this.L$0;
          if ((i1 = this.$n) >= null) {
             if (i1 > null) {
                tL$1 = this.$this_drop.iterator();
                tL$01 = p0;
                goto label_0054 ;
             }else {
                goto label_0073 ;
             }
          }else {
             throw new IllegalArgumentException("Requested element count "+i1+" is less than zero.".toString());
          }
       }
    }
}
