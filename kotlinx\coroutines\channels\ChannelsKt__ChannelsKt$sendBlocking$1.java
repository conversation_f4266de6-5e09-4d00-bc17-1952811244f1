package kotlinx.coroutines.channels.ChannelsKt__ChannelsKt$sendBlocking$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.i;
import java.lang.Object;
import tb.ar4;
import tb.uu4;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;

public final class ChannelsKt__ChannelsKt$sendBlocking$1 extends SuspendLambda implements u1a	// class@0004d5 from classes11.dex
{
    public final Object $element;
    public final i $this_sendBlocking;
    public int label;

    public void ChannelsKt__ChannelsKt$sendBlocking$1(i p0,Object p1,ar4 p2){
       this.$this_sendBlocking = p0;
       this.$element = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       return new ChannelsKt__ChannelsKt$sendBlocking$1(this.$this_sendBlocking, this.$element, p1);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(uu4 p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__ChannelsKt$sendBlocking$1 tlabel;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel == 1) {
             b.b(p0);
          }else {
             throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
          }
       }else {
          b.b(p0);
          this.label = 1;
          if (this.$this_sendBlocking.d(this.$element, this) == obj) {
             return obj;
          }
       }
       return xhv.INSTANCE;
    }
}
