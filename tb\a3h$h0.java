package tb.a3h$h0;
import java.lang.Runnable;
import tb.a3h;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.trtc.utils.TrtcLog;
import tb.a3h$v0;

public class a3h$h0 implements Runnable	// class@001e72 from classes10.dex
{
    public final a3h a;
    public static IpChange $ipChange;

    public void a3h$h0(a3h p0){
       this.a = p0;
       super();
    }
    public void run(){
       int i = 1;
       IpChange $ipChange = a3h$h0.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "artc so load onReady");
          if (a3h.y(this.a) != null) {
             a3h.y(this.a).o(i, 0, "success");
          }
          return;
       }
    }
}
