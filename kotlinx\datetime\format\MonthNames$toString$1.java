package kotlinx.datetime.format.MonthNames$toString$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.String;
import java.lang.Class;
import java.lang.Object;
import tb.ckf;

public final class MonthNames$toString$1 extends FunctionReferenceImpl implements g1a	// class@0006e7 from classes11.dex
{
    public static final MonthNames$toString$1 INSTANCE;

    static {
       MonthNames$toString$1.INSTANCE = new MonthNames$toString$1();
    }
    public void MonthNames$toString$1(){
       super(1, String.class, "toString", "toString\(\)Ljava/lang/String;", 0);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final String invoke(String p0){
       ckf.g(p0, "p0");
       return p0;
    }
}
