package kotlinx.datetime.format.DateFields$dayOfMonth$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.jf20;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class DateFields$dayOfMonth$1 extends MutablePropertyReference1Impl	// class@0006d5 from classes11.dex
{
    public static final DateFields$dayOfMonth$1 INSTANCE;

    static {
       DateFields$dayOfMonth$1.INSTANCE = new DateFields$dayOfMonth$1();
    }
    public void DateFields$dayOfMonth$1(){
       super(jf20.class, "dayOfMonth", "getDayOfMonth\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.o();
    }
    public void set(Object p0,Object p1){
       p0.w(p1);
    }
}
