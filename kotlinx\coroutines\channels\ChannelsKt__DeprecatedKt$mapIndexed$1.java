package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$mapIndexed$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.w1a;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import java.lang.Integer;
import tb.gk2;
import kotlinx.coroutines.channels.i;

public final class ChannelsKt__DeprecatedKt$mapIndexed$1 extends SuspendLambda implements u1a	// class@0004f3 from classes11.dex
{
    public final ReceiveChannel $this_mapIndexed;
    public final w1a $transform;
    public int I$0;
    private Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;

    public void ChannelsKt__DeprecatedKt$mapIndexed$1(ReceiveChannel p0,w1a p1,ar4 p2){
       this.$this_mapIndexed = p0;
       this.$transform = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$mapIndexed$1 omapIndexed$ = new ChannelsKt__DeprecatedKt$mapIndexed$1(this.$this_mapIndexed, this.$transform, p1);
       omapIndexed$.L$0 = p0;
       return omapIndexed$;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$mapIndexed$1 tlabel;
       ChannelsKt__DeprecatedKt$mapIndexed$1 tL$1;
       ChannelsKt__DeprecatedKt$mapIndexed$1 obj1;
       ChannelsKt__DeprecatedKt$mapIndexed$1 omapIndexed$;
       int i1;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel != 2) {
                if (tlabel == 3) {
                   tlabel = this.I$0;
                   tL$1 = this.L$1;
                   b.b(p0);
                   p0 = this.L$0;
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                i1 = this.I$0;
                tL$1 = this.L$2;
                obj1 = this.L$1;
                omapIndexed$ = this.L$0;
                b.b(p0);
             label_0093 :
                this.L$0 = omapIndexed$;
                this.L$1 = obj1;
                this.L$2 = 0;
                this.I$0 = i1;
                this.label = 3;
                if (tL$1.d(p0, this) == obj) {
                   return obj;
                }else {
                   tL$1 = obj1;
                   p0 = omapIndexed$;
                }
             }
          }else {
             i1 = this.I$0;
             tL$1 = this.L$1;
             obj1 = this.L$0;
             b.b(p0);
          label_006a :
             if (p0.booleanValue()) {
                int i = tlabel + 1;
                this.L$0 = obj1;
                this.L$1 = tL$1;
                this.L$2 = obj1;
                this.I$0 = i;
                this.label = 2;
                if ((p0 = this.$transform.invoke(gk2.b(tlabel), tL$1.next(), this)) == obj) {
                   return obj;
                }else {
                   omapIndexed$ = obj1;
                   i1 = i;
                   obj1 = tL$1;
                   tL$1 = omapIndexed$;
                   goto label_0093 ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          p0 = this.L$0;
          tL$1 = this.$this_mapIndexed.iterator();
          i1 = 0;
       }
       this.L$0 = p0;
       this.L$1 = tL$1;
       this.I$0 = tlabel;
       this.label = 1;
       if ((obj1 = tL$1.a(this)) == obj) {
          return obj;
       }else {
          obj1 = p0;
          p0 = obj1;
          goto label_006a ;
       }
    }
}
