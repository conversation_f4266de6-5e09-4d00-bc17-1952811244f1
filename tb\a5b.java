package tb.a5b;
import tb.hdc;
import tb.t2o;
import java.lang.Object;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import android.taobao.windvane.webview.IWVWebView;
import android.content.Context;
import tb.vqa;
import tb.uza;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.ddv;
import java.util.List;
import java.util.Collections;

public class a5b implements hdc	// class@00175e from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2e0001f2);
       t2o.a(0x2e0001df);
    }
    public void a5b(){
       super();
    }
    public final boolean a(WVCallBackContext p0){
       int i = 1;
       IpChange $ipChange = a5b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("16983cb5", objArray).booleanValue();
       }else if(p0 != null && (p0.getWebview() != null && p0.getWebview().getContext() != null)){
          i = false;
       }
       return i;
    }
    public final boolean b(WVCallBackContext p0){
       IpChange $ipChange = a5b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("a500a7b5", objArray).booleanValue();
       }else {
          vqa ovqa = vqa.k().i("Bridge").j("wakeSelectArea").e("homepage.HomePageWVPlugin.wakeSelectArea");
          if (this.a(p0)) {
             ovqa.g("checkCallback", "true");
             uza.a(p0.getWebview().getContext(), 1);
             ovqa.e("success").a();
             return 1;
          }else {
             ovqa.g("checkCallback", "false");
             ddv.h("HomePageWVPlugin", "2.0", "wakeSelectArea Exception", "callback checkError", null, null);
             ovqa.e("fail").a();
             return 0;
          }
       }
    }
    public boolean e(String p0,String p1,WVCallBackContext p2){
       IpChange $ipChange = a5b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b(p2);
       }
       Object[] objArray = new Object[]{this,p0,p1,p2};
       return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
    }
    public List getActions(){
       IpChange $ipChange = a5b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Collections.singletonList("wakeSelectArea");
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("39cd6eb9", objArray);
    }
}
