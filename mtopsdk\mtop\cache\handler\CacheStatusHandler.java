package mtopsdk.mtop.cache.handler.CacheStatusHandler;
import tb.t2o;
import java.lang.Object;
import mtopsdk.mtop.util.MtopStatistics;
import mtopsdk.mtop.domain.MtopResponse;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import java.lang.Throwable;
import java.util.Map;
import mtopsdk.common.util.HeaderHandlerUtil;
import mtopsdk.mtop.domain.ResponseSource;
import android.os.Handler;
import anetwork.network.cache.RpcCache;
import anetwork.network.cache.RpcCache$CacheStatus;
import mtopsdk.mtop.cache.handler.ICacheParser;
import mtopsdk.mtop.cache.handler.CacheParserFactory;
import mtopsdk.mtop.domain.MtopRequest;
import tb.ui9;

public class CacheStatusHandler	// class@000796 from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x253000a9);
    }
    public void CacheStatusHandler(){
       super();
    }
    public static void finishMtopStatisticsOnExpiredCache(MtopStatistics p0,MtopResponse p1){
       MtopStatistics mtopStatisti;
       IpChange $ipChange = CacheStatusHandler.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("60776da7", objArray);
          return;
       }else if(p0 != null && p1 != null){
          try{
             mtopStatisti = p0.clone();
          }catch(java.lang.Exception e0){
             if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.ErrorEnable)) {
                TBSdkLog.e("mtopsdk.CacheStatusHandler", p0.seqNo, "[finishMtopStatisticsOnCache] clone MtopStatistics error.", e0);
             }
             mtopStatisti = null;
          }
          if (mtopStatisti != null) {
             p1.setMtopStat(mtopStatisti);
             mtopStatisti.serverTraceId = HeaderHandlerUtil.getSingleHeaderFieldByKey(p1.getHeaderFields(), "x-s-traceid");
             mtopStatisti.statusCode = p1.getResponseCode();
             mtopStatisti.retCode = p1.getRetCode();
             mtopStatisti.onEndAndCommit();
          }
       }
       return;
    }
    public static void handleCacheStatus(ResponseSource p0,Handler p1){
       ResponseSource rpcCache;
       IpChange $ipChange = CacheStatusHandler.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("ae996e55", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if((rpcCache = p0.rpcCache) != null){
          CacheParserFactory.createCacheParser(rpcCache.cacheStatus).parse(p0, p1);
       }else {
          TBSdkLog.i("mtopsdk.CacheStatusHandler", p0.seqNo, "[handleCacheStatus]Didn\'t  hit local cache ");
       }
       return;
    }
    public static MtopResponse initResponseFromCache(RpcCache p0,MtopRequest p1){
       IpChange $ipChange = CacheStatusHandler.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("168ee586", objArray);
       }else {
          MtopResponse mtopResponse = new MtopResponse();
          mtopResponse.setApi(p1.getApiName());
          mtopResponse.setV(p1.getVersion());
          mtopResponse.setBytedata(p0.body);
          mtopResponse.setHeaderFields(p0.header);
          mtopResponse.setResponseCode(200);
          ui9.c(mtopResponse);
          return mtopResponse;
       }
    }
}
