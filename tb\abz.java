package tb.abz;
import tb.waz;
import com.taobao.android.nanocompose.runtime.node.MeasureAndLayoutDelegate;
import java.util.Map;
import java.util.Set;
import tb.bgz;
import tb.pq10;
import com.taobao.android.nanocompose.runtime.pipeline.parser.Parsing;

public interface abstract abz	// class@0016e8 from classes6.dex
{
    public static final AtomicReference $ipChange;

    void forceMeasureTheSubtree(waz p0);
    waz getLayoutTreeRevision();
    MeasureAndLayoutDelegate getMeasureAndLayoutDelegate();
    Map getObservedAnimLayoutNodes();
    Set getReParsingSet();
    waz getRootLayoutNode();
    boolean hasAnimation();
    void observeAnimState(bgz p0,pq10 p1);
    void onRequestMeasure(waz p0);
    void onRequestParse(Parsing p0);
    void requestAnimation(bgz p0);
    void requestParse();
    void updateLayoutTreeRevision(waz p0);
    void updateRootLayoutNode(waz p0);
}
