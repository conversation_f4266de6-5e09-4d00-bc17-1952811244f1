package androidx.activity.OnBackPressedCallback;
import java.lang.Object;
import java.util.concurrent.CopyOnWriteArrayList;
import androidx.activity.Cancellable;
import java.lang.String;
import tb.ckf;
import tb.d1a;
import androidx.activity.BackEventCompat;
import java.util.Iterator;
import java.lang.Iterable;

public abstract class OnBackPressedCallback	// class@000455 from classes.dex
{
    private final CopyOnWriteArrayList cancellables;
    private d1a enabledChangedCallback;
    private boolean isEnabled;

    public void OnBackPressedCallback(boolean p0){
       super();
       this.isEnabled = p0;
       this.cancellables = new CopyOnWriteArrayList();
    }
    public final void addCancellable(Cancellable p0){
       ckf.g(p0, "cancellable");
       this.cancellables.add(p0);
    }
    public final d1a getEnabledChangedCallback$activity_release(){
       return this.enabledChangedCallback;
    }
    public void handleOnBackCancelled(){
    }
    public abstract void handleOnBackPressed();
    public void handleOnBackProgressed(BackEventCompat p0){
       ckf.g(p0, "backEvent");
    }
    public void handleOnBackStarted(BackEventCompat p0){
       ckf.g(p0, "backEvent");
    }
    public final boolean isEnabled(){
       return this.isEnabled;
    }
    public final void remove(){
       Iterator iterator = this.cancellables.iterator();
       while (iterator.hasNext()) {
          iterator.next().cancel();
       }
       return;
    }
    public final void removeCancellable(Cancellable p0){
       ckf.g(p0, "cancellable");
       this.cancellables.remove(p0);
    }
    public final void setEnabled(boolean p0){
       OnBackPressedCallback tenabledChan;
       this.isEnabled = p0;
       if ((tenabledChan = this.enabledChangedCallback) != null) {
          tenabledChan.invoke();
       }
       return;
    }
    public final void setEnabledChangedCallback$activity_release(d1a p0){
       this.enabledChangedCallback = p0;
    }
}
