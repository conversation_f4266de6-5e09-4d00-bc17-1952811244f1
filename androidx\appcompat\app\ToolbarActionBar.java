package androidx.appcompat.app.ToolbarActionBar;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;
import java.lang.CharSequence;
import android.view.Window$Callback;
import java.util.ArrayList;
import androidx.appcompat.app.ToolbarActionBar$1;
import androidx.appcompat.app.ToolbarActionBar$2;
import java.lang.Object;
import androidx.core.util.Preconditions;
import androidx.appcompat.widget.ToolbarWidgetWrapper;
import androidx.appcompat.widget.DecorToolbar;
import androidx.appcompat.widget.Toolbar$OnMenuItemClickListener;
import androidx.appcompat.app.ToolbarActionBar$ToolbarMenuCallback;
import android.view.Menu;
import androidx.appcompat.app.ToolbarActionBar$ActionMenuPresenterCallback;
import androidx.appcompat.app.ToolbarActionBar$MenuBuilderCallback;
import androidx.appcompat.view.menu.MenuPresenter$Callback;
import androidx.appcompat.view.menu.MenuBuilder$Callback;
import androidx.appcompat.app.ActionBar$OnMenuVisibilityListener;
import androidx.appcompat.app.ActionBar$Tab;
import java.lang.UnsupportedOperationException;
import java.lang.String;
import android.view.View;
import android.view.ViewGroup;
import androidx.core.view.ViewCompat;
import android.content.Context;
import java.lang.Runnable;
import android.content.res.Configuration;
import android.view.KeyEvent;
import android.view.KeyCharacterMap;
import androidx.appcompat.view.menu.MenuBuilder;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import androidx.appcompat.app.ActionBar$LayoutParams;
import android.view.ViewGroup$LayoutParams;
import android.widget.SpinnerAdapter;
import androidx.appcompat.app.ActionBar$OnNavigationListener;
import androidx.appcompat.app.NavItemSelectedListener;
import android.widget.AdapterView$OnItemSelectedListener;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;

public class ToolbarActionBar extends ActionBar	// class@000588 from classes.dex
{
    public final DecorToolbar mDecorToolbar;
    private boolean mLastMenuVisibility;
    public final AppCompatDelegateImpl$ActionBarMenuCallback mMenuCallback;
    private boolean mMenuCallbackSet;
    private final Toolbar$OnMenuItemClickListener mMenuClicker;
    private final Runnable mMenuInvalidator;
    private ArrayList mMenuVisibilityListeners;
    public boolean mToolbarMenuPrepared;
    public final Window$Callback mWindowCallback;

    public void ToolbarActionBar(Toolbar p0,CharSequence p1,Window$Callback p2){
       super();
       this.mMenuVisibilityListeners = new ArrayList();
       this.mMenuInvalidator = new ToolbarActionBar$1(this);
       ToolbarActionBar$2 u2 = new ToolbarActionBar$2(this);
       this.mMenuClicker = u2;
       Preconditions.checkNotNull(p0);
       ToolbarWidgetWrapper toolbarWidge = new ToolbarWidgetWrapper(p0, false);
       this.mDecorToolbar = toolbarWidge;
       this.mWindowCallback = Preconditions.checkNotNull(p2);
       toolbarWidge.setWindowCallback(p2);
       p0.setOnMenuItemClickListener(u2);
       toolbarWidge.setWindowTitle(p1);
       this.mMenuCallback = new ToolbarActionBar$ToolbarMenuCallback(this);
    }
    private Menu getMenu(){
       if (this.mMenuCallbackSet == null) {
          this.mDecorToolbar.setMenuCallbacks(new ToolbarActionBar$ActionMenuPresenterCallback(this), new ToolbarActionBar$MenuBuilderCallback(this));
          this.mMenuCallbackSet = true;
       }
       return this.mDecorToolbar.getMenu();
    }
    public void addOnMenuVisibilityListener(ActionBar$OnMenuVisibilityListener p0){
       this.mMenuVisibilityListeners.add(p0);
    }
    public void addTab(ActionBar$Tab p0){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public void addTab(ActionBar$Tab p0,int p1){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public void addTab(ActionBar$Tab p0,int p1,boolean p2){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public void addTab(ActionBar$Tab p0,boolean p1){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public boolean closeOptionsMenu(){
       return this.mDecorToolbar.hideOverflowMenu();
    }
    public boolean collapseActionView(){
       if (!this.mDecorToolbar.hasExpandedActionView()) {
          return false;
       }
       this.mDecorToolbar.collapseActionView();
       return true;
    }
    public void dispatchMenuVisibilityChanged(boolean p0){
       if (p0 == this.mLastMenuVisibility) {
          return;
       }
       this.mLastMenuVisibility = p0;
       int i = this.mMenuVisibilityListeners.size();
       for (int i1 = 0; i1 < i; i1 = i1 + 1) {
          this.mMenuVisibilityListeners.get(i1).onMenuVisibilityChanged(p0);
       }
       return;
    }
    public View getCustomView(){
       return this.mDecorToolbar.getCustomView();
    }
    public int getDisplayOptions(){
       return this.mDecorToolbar.getDisplayOptions();
    }
    public float getElevation(){
       return ViewCompat.getElevation(this.mDecorToolbar.getViewGroup());
    }
    public int getHeight(){
       return this.mDecorToolbar.getHeight();
    }
    public int getNavigationItemCount(){
       return 0;
    }
    public int getNavigationMode(){
       return 0;
    }
    public int getSelectedNavigationIndex(){
       return -1;
    }
    public ActionBar$Tab getSelectedTab(){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public CharSequence getSubtitle(){
       return this.mDecorToolbar.getSubtitle();
    }
    public ActionBar$Tab getTabAt(int p0){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public int getTabCount(){
       return 0;
    }
    public Context getThemedContext(){
       return this.mDecorToolbar.getContext();
    }
    public CharSequence getTitle(){
       return this.mDecorToolbar.getTitle();
    }
    public void hide(){
       this.mDecorToolbar.setVisibility(8);
    }
    public boolean invalidateOptionsMenu(){
       this.mDecorToolbar.getViewGroup().removeCallbacks(this.mMenuInvalidator);
       ViewCompat.postOnAnimation(this.mDecorToolbar.getViewGroup(), this.mMenuInvalidator);
       return true;
    }
    public boolean isShowing(){
       boolean b = (!this.mDecorToolbar.getVisibility())? true: false;
       return b;
    }
    public boolean isTitleTruncated(){
       return super.isTitleTruncated();
    }
    public ActionBar$Tab newTab(){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public void onConfigurationChanged(Configuration p0){
       super.onConfigurationChanged(p0);
    }
    public void onDestroy(){
       this.mDecorToolbar.getViewGroup().removeCallbacks(this.mMenuInvalidator);
    }
    public boolean onKeyShortcut(int p0,KeyEvent p1){
       Menu menu;
       if ((menu = this.getMenu()) == null) {
          return 0;
       }
       int deviceId = (p1 != null)? p1.getDeviceId(): -1;
       boolean b = true;
       if (KeyCharacterMap.load(deviceId).getKeyboardType() == b) {
          b = false;
       }
       menu.setQwertyMode(b);
       return menu.performShortcut(p0, p1, 0);
    }
    public boolean onMenuKeyEvent(KeyEvent p0){
       if (p0.getAction() == 1) {
          this.openOptionsMenu();
       }
       return true;
    }
    public boolean openOptionsMenu(){
       return this.mDecorToolbar.showOverflowMenu();
    }
    public void populateOptionsMenu(){
       Menu menu = this.getMenu();
       View view = null;
       Menu menu1 = (menu instanceof MenuBuilder)? menu: view;
       if (menu1 != null) {
          menu1.stopDispatchingItemsChanged();
       }
       menu.clear();
       if (!this.mWindowCallback.onCreatePanelMenu(0, menu) || !this.mWindowCallback.onPreparePanel(0, view, menu)) {
          menu.clear();
       }
       if (menu1 != null) {
          menu1.startDispatchingItemsChanged();
       }
       return;
    }
    public void removeAllTabs(){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public void removeOnMenuVisibilityListener(ActionBar$OnMenuVisibilityListener p0){
       this.mMenuVisibilityListeners.remove(p0);
    }
    public void removeTab(ActionBar$Tab p0){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public void removeTabAt(int p0){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public boolean requestFocus(){
       ViewGroup viewGroup;
       if ((viewGroup = this.mDecorToolbar.getViewGroup()) == null || viewGroup.hasFocus()) {
          return false;
       }
       viewGroup.requestFocus();
       return true;
    }
    public void selectTab(ActionBar$Tab p0){
       throw new UnsupportedOperationException("Tabs are not supported in toolbar action bars");
    }
    public void setBackgroundDrawable(Drawable p0){
       this.mDecorToolbar.setBackgroundDrawable(p0);
    }
    public void setCustomView(int p0){
       this.setCustomView(LayoutInflater.from(this.mDecorToolbar.getContext()).inflate(p0, this.mDecorToolbar.getViewGroup(), false));
    }
    public void setCustomView(View p0){
       this.setCustomView(p0, new ActionBar$LayoutParams(-2, -2));
    }
    public void setCustomView(View p0,ActionBar$LayoutParams p1){
       if (p0 != null) {
          p0.setLayoutParams(p1);
       }
       this.mDecorToolbar.setCustomView(p0);
       return;
    }
    public void setDefaultDisplayHomeAsUpEnabled(boolean p0){
    }
    public void setDisplayHomeAsUpEnabled(boolean p0){
       int i = (p0)? 4: 0;
       this.setDisplayOptions(i, 4);
       return;
    }
    public void setDisplayOptions(int p0){
       this.setDisplayOptions(p0, -1);
    }
    public void setDisplayOptions(int p0,int p1){
       this.mDecorToolbar.setDisplayOptions(((p0 & p1) | ((~ p1) & this.mDecorToolbar.getDisplayOptions())));
    }
    public void setDisplayShowCustomEnabled(boolean p0){
       int i = (p0)? 16: 0;
       this.setDisplayOptions(i, 16);
       return;
    }
    public void setDisplayShowHomeEnabled(boolean p0){
       int i = (p0)? 2: 0;
       this.setDisplayOptions(i, 2);
       return;
    }
    public void setDisplayShowTitleEnabled(boolean p0){
       int i = (p0)? 8: 0;
       this.setDisplayOptions(i, 8);
       return;
    }
    public void setDisplayUseLogoEnabled(boolean p0){
       this.setDisplayOptions(p0, 1);
    }
    public void setElevation(float p0){
       ViewCompat.setElevation(this.mDecorToolbar.getViewGroup(), p0);
    }
    public void setHomeActionContentDescription(int p0){
       this.mDecorToolbar.setNavigationContentDescription(p0);
    }
    public void setHomeActionContentDescription(CharSequence p0){
       this.mDecorToolbar.setNavigationContentDescription(p0);
    }
    public void setHomeAsUpIndicator(int p0){
       this.mDecorToolbar.setNavigationIcon(p0);
    }
    public void setHomeAsUpIndicator(Drawable p0){
       this.mDecorToolbar.setNavigationIcon(p0);
    }
    public void setHomeButtonEnabled(boolean p0){
    }
    public void setIcon(int p0){
       this.mDecorToolbar.setIcon(p0);
    }
    public void setIcon(Drawable p0){
       this.mDecorToolbar.setIcon(p0);
    }
    public void setListNavigationCallbacks(SpinnerAdapter p0,ActionBar$OnNavigationListener p1){
       this.mDecorToolbar.setDropdownParams(p0, new NavItemSelectedListener(p1));
    }
    public void setLogo(int p0){
       this.mDecorToolbar.setLogo(p0);
    }
    public void setLogo(Drawable p0){
       this.mDecorToolbar.setLogo(p0);
    }
    public void setNavigationMode(int p0){
       if (p0 == 2) {
          throw new IllegalArgumentException("Tabs not supported in this configuration");
       }
       this.mDecorToolbar.setNavigationMode(p0);
       return;
    }
    public void setSelectedNavigationItem(int p0){
       if (this.mDecorToolbar.getNavigationMode() != 1) {
          throw new IllegalStateException("setSelectedNavigationIndex not valid for current navigation mode");
       }
       this.mDecorToolbar.setDropdownSelectedPosition(p0);
       return;
    }
    public void setShowHideAnimationEnabled(boolean p0){
    }
    public void setSplitBackgroundDrawable(Drawable p0){
    }
    public void setStackedBackgroundDrawable(Drawable p0){
    }
    public void setSubtitle(int p0){
       ToolbarActionBar tmDecorToolb = this.mDecorToolbar;
       CharSequence text = (p0)? tmDecorToolb.getContext().getText(p0): null;
       tmDecorToolb.setSubtitle(text);
       return;
    }
    public void setSubtitle(CharSequence p0){
       this.mDecorToolbar.setSubtitle(p0);
    }
    public void setTitle(int p0){
       ToolbarActionBar tmDecorToolb = this.mDecorToolbar;
       CharSequence text = (p0)? tmDecorToolb.getContext().getText(p0): null;
       tmDecorToolb.setTitle(text);
       return;
    }
    public void setTitle(CharSequence p0){
       this.mDecorToolbar.setTitle(p0);
    }
    public void setWindowTitle(CharSequence p0){
       this.mDecorToolbar.setWindowTitle(p0);
    }
    public void show(){
       this.mDecorToolbar.setVisibility(0);
    }
}
