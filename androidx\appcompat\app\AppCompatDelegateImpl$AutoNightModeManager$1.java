package androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager$1;
import android.content.BroadcastReceiver;
import androidx.appcompat.app.AppCompatDelegateImpl$AutoNightModeManager;
import android.content.Context;
import android.content.Intent;

public class AppCompatDelegateImpl$AutoNightModeManager$1 extends BroadcastReceiver	// class@000570 from classes.dex
{
    public final AppCompatDelegateImpl$AutoNightModeManager this$1;

    public void AppCompatDelegateImpl$AutoNightModeManager$1(AppCompatDelegateImpl$AutoNightModeManager p0){
       this.this$1 = p0;
       super();
    }
    public void onReceive(Context p0,Intent p1){
       this.this$1.onChange();
    }
}
