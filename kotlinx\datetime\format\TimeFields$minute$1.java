package kotlinx.datetime.format.TimeFields$minute$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.fw40;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class TimeFields$minute$1 extends MutablePropertyReference1Impl	// class@0006f1 from classes11.dex
{
    public static final TimeFields$minute$1 INSTANCE;

    static {
       TimeFields$minute$1.INSTANCE = new TimeFields$minute$1();
    }
    public void TimeFields$minute$1(){
       super(fw40.class, "minute", "getMinute\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.a();
    }
    public void set(Object p0,Object p1){
       p0.C(p1);
    }
}
