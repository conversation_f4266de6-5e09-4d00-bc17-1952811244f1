package tb.a6k;
import tb.zp7;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.Boolean;
import android.content.Context;
import java.lang.Number;
import tb.ud8;
import tb.t9o;

public class a6k implements zp7	// class@001b03 from classes8.dex
{
    public final int a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x27100035);
       t2o.a(0x2710002c);
    }
    public void a6k(int p0){
       super();
       this.a = p0;
    }
    public boolean a(String p0,int p1,byte[] p2,int p3,int p4){
       int i = 0;
       IpChange $ipChange = a6k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return i;
       }
       Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,new Integer(p3),new Integer(p4)};
       return $ipChange.ipc$dispatch("1f618c8e", objArray).booleanValue();
    }
    public boolean b(Context p0){
       int i = 0;
       IpChange $ipChange = a6k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return i;
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("c5f931b", objArray).booleanValue();
    }
    public void c(int p0){
       IpChange $ipChange = a6k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("c5f0c2f7", objArray);
       }
       return;
    }
    public void clear(){
       IpChange $ipChange = a6k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b42d4c54", objArray);
       }
       return;
    }
    public boolean d(){
       int i = 0;
       IpChange $ipChange = a6k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("1c1d36ea", objArray).booleanValue();
       }
       return i;
    }
    public long e(String p0,int p1){
       IpChange $ipChange = a6k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return -1;
       }
       Object[] objArray = new Object[]{this,p0,new Integer(p1)};
       return $ipChange.ipc$dispatch("4a05552a", objArray).longValue();
    }
    public int[] f(String p0){
       int i = 0;
       IpChange $ipChange = a6k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("99f2983", objArray);
       }else {
          int[] ointArray = new int[i];
          return ointArray;
       }
    }
    public ud8 g(String p0,int p1){
       IpChange $ipChange = a6k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this,p0,new Integer(p1)};
       return $ipChange.ipc$dispatch("33b83a71", objArray);
    }
    public t9o get(String p0,int p1){
       return this.g(p0, p1);
    }
    public int getPriority(){
       IpChange $ipChange = a6k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("49b31e94", objArray).intValue();
    }
}
