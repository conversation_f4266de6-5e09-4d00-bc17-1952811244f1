package androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import android.view.View;
import tb.ar4;
import tb.ozm;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.graphics.Rect;
import androidx.activity.PipHintTrackerKt;
import kotlinx.coroutines.channels.i;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import tb.f3m;
import tb.g3m;
import androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1;
import android.view.ViewTreeObserver$OnScrollChangedListener;
import android.view.View$OnLayoutChangeListener;
import android.view.ViewTreeObserver;
import android.view.View$OnAttachStateChangeListener;
import androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1$1;
import tb.d1a;
import kotlinx.coroutines.channels.ProduceKt;

public final class PipHintTrackerKt$trackPipAnimationHintView$flow$1 extends SuspendLambda implements u1a	// class@000469 from classes.dex
{
    public final View $view;
    private Object L$0;
    public int label;

    public void PipHintTrackerKt$trackPipAnimationHintView$flow$1(View p0,ar4 p1){
       this.$view = p0;
       super(2, p1);
    }
    public static void a(ozm p0,View p1,int p2,int p3,int p4,int p5,int p6,int p7,int p8,int p9){
       PipHintTrackerKt$trackPipAnimationHintView$flow$1.invokeSuspend$lambda$0(p0, p1, p2, p3, p4, p5, p6, p7, p8, p9);
    }
    public static void b(ozm p0,View p1){
       PipHintTrackerKt$trackPipAnimationHintView$flow$1.invokeSuspend$lambda$1(p0, p1);
    }
    private static final void invokeSuspend$lambda$0(ozm p0,View p1,int p2,int p3,int p4,int p5,int p6,int p7,int p8,int p9){
       if (p2 != p6 || (p4 != p8 || (p3 != p7 || p5 != p9))) {
          ckf.f(p1, "v");
          p0.m(PipHintTrackerKt.access$trackPipAnimationHintView$positionInWindow(p1));
       }
       return;
    }
    private static final void invokeSuspend$lambda$1(ozm p0,View p1){
       p0.m(PipHintTrackerKt.access$trackPipAnimationHintView$positionInWindow(p1));
    }
    public final ar4 create(Object p0,ar4 p1){
       PipHintTrackerKt$trackPipAnimationHintView$flow$1 otrackPipAni = new PipHintTrackerKt$trackPipAnimationHintView$flow$1(this.$view, p1);
       otrackPipAni.L$0 = p0;
       return otrackPipAni;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       PipHintTrackerKt$trackPipAnimationHintView$flow$1 tlabel;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel == 1) {
             b.b(p0);
          }else {
             throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
          }
       }else {
          b.b(p0);
          p0 = this.L$0;
          f3m uof3m = new f3m(p0);
          PipHintTrackerKt$trackPipAnimationHintView$flow$1 t$view = this.$view;
          g3m og3m = new g3m(p0, t$view);
          PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1 otrackPipAni = new PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1(p0, t$view, og3m, uof3m);
          if (this.$view.isAttachedToWindow()) {
             p0.m(PipHintTrackerKt.access$trackPipAnimationHintView$positionInWindow(this.$view));
             this.$view.getViewTreeObserver().addOnScrollChangedListener(og3m);
             this.$view.addOnLayoutChangeListener(uof3m);
          }
          this.$view.addOnAttachStateChangeListener(otrackPipAni);
          this.label = 1;
          if (ProduceKt.a(p0, new PipHintTrackerKt$trackPipAnimationHintView$flow$1$1(this.$view, og3m, uof3m, otrackPipAni), this) == obj) {
             return obj;
          }
       }
       return xhv.INSTANCE;
    }
}
