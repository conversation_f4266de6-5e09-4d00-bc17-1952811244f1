package tb.a5w$b;
import java.lang.Runnable;
import tb.a5w;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.io.File;
import java.lang.StringBuilder;
import tb.fve;

public class a5w$b implements Runnable	// class@001afa from classes8.dex
{
    public final String a;
    public final boolean b;
    public final a5w c;
    public static IpChange $ipChange;

    public void a5w$b(a5w p0,String p1,boolean p2){
       this.c = p0;
       this.a = p1;
       this.b = p2;
       super();
    }
    public void run(){
       int i = 1;
       IpChange $ipChange = a5w$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          String str = a5w.a(this.c);
          if (TextUtils.isEmpty(str)) {
             return;
          }
          File uFile = new File(str);
          if (!uFile.exists()) {
             return;
          }
          boolean b = a5w.b(this.c, this.a, ".mp4");
          File[] uFileArray = uFile.listFiles();
          String str1 = this.c.i(this.a);
          int len = uFileArray.length;
          int i1 = 0;
          while (i1 < len) {
             object oobject = uFileArray[i1];
             int i2 = (b && (a5w.b(this.c, oobject.getAbsolutePath(), ".mp4") && !TextUtils.equals(str1, oobject.getAbsolutePath())))? 1: 0;
             int i3 = (!b && (!a5w.b(this.c, oobject.getAbsolutePath(), ".mp4") && !TextUtils.equals(str1, oobject.getAbsolutePath())))? 1: 0;
             if (this.b != null || (i2 || i3)) {
                String[] stringArray = new String[]{"delete file:".append(oobject.getAbsolutePath()).append(",delete:").append(oobject.delete()).toString()};
                fve.e("VideoAnimationCardHelper", stringArray);
             }
             i1 = i1 + i;
          }
          return;
       }
    }
}
