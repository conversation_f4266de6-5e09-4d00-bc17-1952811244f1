package kotlinx.serialization.SerializersCacheKt;
import kotlinx.serialization.SerializersCacheKt$SERIALIZERS_CACHE$1;
import tb.g1a;
import tb.rb40;
import tb.s020;
import kotlinx.serialization.SerializersCacheKt$SERIALIZERS_CACHE_NULLABLE$1;
import kotlinx.serialization.SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1;
import tb.u1a;
import tb.bv30;
import kotlinx.serialization.SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE_NULLABLE$1;
import tb.wyf;
import tb.x530;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import java.util.List;

public final class SerializersCacheKt	// class@000721 from classes11.dex
{
    public static final rb40 a;
    public static final rb40 b;
    public static final bv30 c;
    public static final bv30 d;

    static {
       SerializersCacheKt.a = s020.a(SerializersCacheKt$SERIALIZERS_CACHE$1.INSTANCE);
       SerializersCacheKt.b = s020.a(SerializersCacheKt$SERIALIZERS_CACHE_NULLABLE$1.INSTANCE);
       SerializersCacheKt.c = s020.b(SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE$1.INSTANCE);
       SerializersCacheKt.d = s020.b(SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE_NULLABLE$1.INSTANCE);
    }
    public static final x530 a(wyf p0,boolean p1){
       x530 ox530;
       ckf.g(p0, "clazz");
       if (!p1) {
          if ((ox530 = SerializersCacheKt.a.a(p0)) == null) {
             ox530 = null;
          }
       }else {
          ox530 = SerializersCacheKt.b.a(p0);
       }
       return ox530;
    }
    public static final Object b(wyf p0,List p1,boolean p2){
       ckf.g(p0, "clazz");
       ckf.g(p1, "types");
       p0 = (!p2)? SerializersCacheKt.c.a(p0, p1): SerializersCacheKt.d.a(p0, p1);
       return p0;
    }
}
