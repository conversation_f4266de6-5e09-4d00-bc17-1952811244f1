package tb.aes$a;
import java.lang.Runnable;
import android.content.Context;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.themis.taobao.impl.TBTMSSDK;

public final class aes$a implements Runnable	// class@001ecd from classes10.dex
{
    public final Context a;
    public static IpChange $ipChange;

    public void aes$a(Context p0){
       this.a = p0;
       super();
    }
    public final void run(){
       IpChange $ipChange = aes$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          TBTMSSDK.initTBTMS(this.a);
          return;
       }
    }
}
