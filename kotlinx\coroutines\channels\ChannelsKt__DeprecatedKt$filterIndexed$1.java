package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$filterIndexed$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.w1a;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import java.lang.Integer;
import tb.gk2;
import kotlinx.coroutines.channels.i;

public final class ChannelsKt__DeprecatedKt$filterIndexed$1 extends SuspendLambda implements u1a	// class@0004e6 from classes11.dex
{
    public final w1a $predicate;
    public final ReceiveChannel $this_filterIndexed;
    public int I$0;
    private Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;

    public void ChannelsKt__DeprecatedKt$filterIndexed$1(ReceiveChannel p0,w1a p1,ar4 p2){
       this.$this_filterIndexed = p0;
       this.$predicate = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$filterIndexed$1 uofilterInde = new ChannelsKt__DeprecatedKt$filterIndexed$1(this.$this_filterIndexed, this.$predicate, p1);
       uofilterInde.L$0 = p0;
       return uofilterInde;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$filterIndexed$1 tlabel;
       ChannelsKt__DeprecatedKt$filterIndexed$1 tL$1;
       ChannelsKt__DeprecatedKt$filterIndexed$1 tL$0;
       Object obj1;
       ChannelsKt__DeprecatedKt$filterIndexed$1 uofilterInde;
       int i1;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel != 2) {
                if (tlabel == 3) {
                   tlabel = this.I$0;
                   tL$1 = this.L$1;
                   tL$0 = this.L$0;
                   b.b(p0);
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                i1 = this.I$0;
                uofilterInde = this.L$0;
                b.b(p0);
                tL$0 = this.L$2;
                tL$1 = this.L$1;
             label_0094 :
                if (p0.booleanValue()) {
                   this.L$0 = uofilterInde;
                   this.L$1 = tL$1;
                   this.L$2 = 0;
                   this.I$0 = i1;
                   this.label = 3;
                   if (uofilterInde.d(tL$0, this) == obj) {
                      return obj;
                   }
                }
                tL$0 = uofilterInde;
             }
          }else {
             i1 = this.I$0;
             tL$1 = this.L$1;
             tL$0 = this.L$0;
             b.b(p0);
          label_006b :
             if (p0.booleanValue()) {
                p0 = tL$1.next();
                int i = tlabel + 1;
                this.L$0 = tL$0;
                this.L$1 = tL$1;
                this.L$2 = p0;
                this.I$0 = i;
                this.label = 2;
                if ((obj1 = this.$predicate.invoke(gk2.b(tlabel), p0, this)) == obj) {
                   return obj;
                }else {
                   uofilterInde = tL$0;
                   tL$0 = p0;
                   p0 = obj1;
                   i1 = i;
                   goto label_0094 ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          tL$0 = this.L$0;
          tL$1 = this.$this_filterIndexed.iterator();
          i1 = 0;
       }
       this.L$0 = tL$0;
       this.L$1 = tL$1;
       this.L$2 = 0;
       this.I$0 = tlabel;
       this.label = 1;
       if ((p0 = tL$1.a(this)) == obj) {
          return obj;
       }else {
          goto label_006b ;
       }
    }
}
