package kotlinx.coroutines.channels.BufferedChannel$a;
import kotlinx.coroutines.channels.ChannelIterator;
import tb.qww;
import kotlinx.coroutines.channels.BufferedChannel;
import java.lang.Object;
import tb.u1r;
import kotlinx.coroutines.channels.BufferedChannelKt;
import kotlinx.coroutines.c;
import tb.ar4;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import tb.zi3;
import java.lang.Boolean;
import tb.gk2;
import java.util.concurrent.atomic.AtomicLongFieldUpdater;
import tb.v8p;
import tb.te4;
import java.lang.IllegalStateException;
import java.lang.String;
import kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt;
import tb.s23;
import kotlin.coroutines.d;
import tb.g1a;
import kotlinx.coroutines.internal.OnUndeliveredElementKt;
import tb.dkf;
import tb.jv6;
import java.lang.Throwable;
import tb.rgq;
import tb.ckf;
import kotlin.Result;
import tb.dv6;
import tb.vu4;
import kotlin.b;
import tb.q23;

public final class BufferedChannel$a implements ChannelIterator, qww	// class@0004bf from classes11.dex
{
    public Object a;
    public c b;
    public final BufferedChannel c;

    public void BufferedChannel$a(BufferedChannel p0){
       super();
       this.c = p0;
       this.a = BufferedChannelKt.m();
    }
    public static final void b(BufferedChannel$a p0){
       p0.h();
    }
    public static final void d(BufferedChannel$a p0,c p1){
       p0.b = p1;
    }
    public static final void e(BufferedChannel$a p0,Object p1){
       p0.a = p1;
    }
    public Object a(ar4 p0){
       Boolean uBoolean;
       zi3 ozi31;
       zi3 ozi32;
       Object obj;
       BufferedChannel$a tc = this.c;
       zi3 ozi3 = BufferedChannel.l().get(tc);
       while (true) {
          if (tc.u0()) {
             this.g();
             uBoolean = gk2.a(false);
             break ;
          }else {
             long andIncrement = BufferedChannel.p().getAndIncrement(tc);
             long l = (long)BufferedChannelKt.SEGMENT_SIZE;
             long l1 = andIncrement / l;
             l = andIncrement % l;
             int i = (int)l;
             if (ozi3.e - l1) {
                if ((ozi31 = BufferedChannel.e(tc, l1, ozi3)) == null) {
                   continue ;
                }else {
                   ozi32 = ozi31;
                }
             }else {
                ozi32 = ozi3;
             }
             if ((obj = BufferedChannel.J(tc, ozi32, i, andIncrement, null)) != BufferedChannelKt.r()) {
                if (obj == BufferedChannelKt.h()) {
                   if ((andIncrement - tc.n0()) < 0) {
                      ozi32.b();
                   }
                   ozi3 = ozi32;
                }else if(obj == BufferedChannelKt.s()){
                   return this.f(ozi32, i, andIncrement, p0);
                }else {
                   ozi32.b();
                   this.a = obj;
                   uBoolean = gk2.a(true);
                   break ;
                }
             }else {
                throw new IllegalStateException("unreachable");
             }
          }
       }
       return uBoolean;
    }
    public void c(v8p p0,int p1){
       BufferedChannel$a tb;
       if ((tb = this.b) != null) {
          tb.c(p0, p1);
       }
       return;
    }
    public final Object f(zi3 p0,int p1,long p2,ar4 p3){
       zi3 obj;
       Object obj1;
       Boolean uBoolean;
       BufferedChannel b;
       BufferedChannel$a tc = this.c;
       c uoc = s23.b(IntrinsicsKt__IntrinsicsJvmKt.c(p3));
       BufferedChannel$a.d(this, uoc);
       if ((obj = BufferedChannel.J(tc, p0, p1, p2, this)) == BufferedChannelKt.r()) {
          BufferedChannel.z(tc, this, p0, p1);
       }else {
          c uoc1 = null;
          if (obj == BufferedChannelKt.h()) {
             if ((p2 - tc.n0()) < 0) {
                p0.b();
             }
             p0 = BufferedChannel.l().get(tc);
             while (true) {
                if (tc.u0()) {
                   BufferedChannel$a.b(this);
                }else {
                   long andIncrement = BufferedChannel.p().getAndIncrement(tc);
                   long l = (long)BufferedChannelKt.SEGMENT_SIZE;
                   long l1 = andIncrement / l;
                   l = andIncrement % l;
                   int i = (int)l;
                   if (p0.e - l1) {
                      if ((obj = BufferedChannel.e(tc, l1, p0)) == null) {
                         continue ;
                      }else {
                         p0 = obj;
                      }
                   }
                   if ((obj1 = BufferedChannel.J(tc, p0, i, andIncrement, this)) == BufferedChannelKt.r()) {
                      BufferedChannel.z(tc, this, p0, i);
                   }else if(obj1 == BufferedChannelKt.h()){
                      if ((andIncrement - tc.n0()) < 0) {
                         p0.b();
                      }
                   }else if(obj1 != BufferedChannelKt.s()){
                      p0.b();
                      BufferedChannel$a.e(this, obj1);
                      BufferedChannel$a.d(this, uoc1);
                      uBoolean = gk2.a(true);
                      if ((b = tc.b) != null) {
                         uoc1 = OnUndeliveredElementKt.a(b, obj1, uoc.getContext());
                      }
                   }else {
                      throw new IllegalStateException("unexpected");
                   }
                }
             }
          }else {
             p0.b();
             BufferedChannel$a.e(this, obj);
             BufferedChannel$a.d(this, uoc1);
             uBoolean = gk2.a(true);
             if ((b = tc.b) != null) {
                uoc1 = OnUndeliveredElementKt.a(b, obj, uoc.getContext());
             }
          }
          uoc.l(uBoolean, uoc1);
       }
       if ((p0 = uoc.A()) == dkf.d()) {
          jv6.c(p3);
       }
       return p0;
    }
    public final boolean g(){
       Throwable throwable;
       this.a = BufferedChannelKt.z();
       if ((throwable = this.c.d0()) == null) {
          return false;
       }
       throw rgq.j(throwable);
    }
    public final void h(){
       Throwable throwable;
       BufferedChannel$a tb = this.b;
       ckf.d(tb);
       this.b = null;
       this.a = BufferedChannelKt.z();
       if ((throwable = this.c.d0()) == null) {
          tb.resumeWith(Result.constructor-impl(Boolean.FALSE));
       }else if(dv6.c()){
          throwable = rgq.a(throwable, tb);
       }
       tb.resumeWith(Result.constructor-impl(b.a(throwable)));
       return;
    }
    public final boolean i(Object p0){
       BufferedChannel b;
       BufferedChannel$a tb = this.b;
       ckf.d(tb);
       c uoc = null;
       this.b = uoc;
       this.a = p0;
       Boolean tRUE = Boolean.TRUE;
       if ((b = this.c.b) != null) {
          uoc = OnUndeliveredElementKt.a(b, p0, tb.getContext());
       }
       return BufferedChannelKt.u(tb, tRUE, uoc);
    }
    public final void j(){
       Throwable throwable;
       BufferedChannel$a tb = this.b;
       ckf.d(tb);
       this.b = null;
       this.a = BufferedChannelKt.z();
       if ((throwable = this.c.d0()) == null) {
          tb.resumeWith(Result.constructor-impl(Boolean.FALSE));
       }else if(dv6.c()){
          throwable = rgq.a(throwable, tb);
       }
       tb.resumeWith(Result.constructor-impl(b.a(throwable)));
       return;
    }
    public Object next(){
       BufferedChannel$a ta;
       if ((ta = this.a) == BufferedChannelKt.m()) {
          throw new IllegalStateException("`hasNext\(\)` has not been invoked");
       }
       this.a = BufferedChannelKt.m();
       if (ta != BufferedChannelKt.z()) {
          return ta;
       }
       throw rgq.j(BufferedChannel.h(this.c));
    }
}
