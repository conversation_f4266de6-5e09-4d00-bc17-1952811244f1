package tb.a29$a;
import tb.kmc;
import tb.a29;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import com.taobao.bootimage.arch.flow.fatigue.RemoteFatigueDataModel;
import java.lang.Class;
import com.alibaba.fastjson.JSON;
import java.lang.StringBuilder;
import tb.tm1;
import java.lang.Throwable;

public class a29$a implements kmc	// class@001845 from classes7.dex
{
    public final a29 a;
    public static IpChange $ipChange;

    public void a29$a(a29 p0){
       super();
       this.a = p0;
    }
    public void a(String p0){
       String str = "FatigueWorkFlow";
       String str1 = "loadFinish: ";
       IpChange $ipChange = a29$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7839eaea", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          a29.e(this.a);
          return;
       }else {
          try{
             a29.g(this.a, JSON.parseObject(p0, RemoteFatigueDataModel.class));
             tm1.a(str, str1+a29.f(this.a).toString());
          }catch(java.lang.Exception e5){
             tm1.b(str, "loadDataFromCache error ", e5);
          }
          a29.e(this.a);
          return;
       }
    }
}
