package tb.a25$c;
import tb.qqt;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicInteger;
import tb.a25$a;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;

public class a25$c implements qqt	// class@001842 from classes7.dex
{
    public final AtomicInteger a;
    public static IpChange $ipChange;

    public void a25$c(){
       super();
       this.a = new AtomicInteger();
    }
    public void a25$c(a25$a p0){
       super();
    }
    public String newThreadName(){
       IpChange $ipChange = a25$c.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "Download_V_"+this.a.getAndIncrement();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("57dab4a4", objArray);
    }
}
