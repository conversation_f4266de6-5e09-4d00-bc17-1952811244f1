package androidx.appcompat.app.AppCompatDialog;
import androidx.appcompat.app.AppCompatCallback;
import androidx.activity.ComponentDialog;
import android.content.Context;
import tb.v31;
import androidx.appcompat.app.AppCompatDelegate;
import android.os.Bundle;
import android.content.DialogInterface$OnCancelListener;
import android.app.Dialog;
import android.util.TypedValue;
import android.content.res.Resources$Theme;
import com.taobao.taobao.R$attr;
import android.view.Window;
import android.view.View;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.savedstate.SavedStateRegistryOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;
import androidx.activity.OnBackPressedDispatcherOwner;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner;
import android.view.ViewGroup$LayoutParams;
import android.view.KeyEvent;
import androidx.core.view.KeyEventDispatcher$Component;
import android.view.Window$Callback;
import androidx.core.view.KeyEventDispatcher;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.view.ActionMode;
import androidx.appcompat.view.ActionMode$Callback;
import java.lang.String;
import java.lang.CharSequence;

public class AppCompatDialog extends ComponentDialog implements AppCompatCallback	// class@000579 from classes.dex
{
    private AppCompatDelegate mDelegate;
    private final KeyEventDispatcher$Component mKeyDispatcher;

    public void AppCompatDialog(Context p0){
       super(p0, 0);
    }
    public void AppCompatDialog(Context p0,int p1){
       super(p0, AppCompatDialog.getThemeResId(p0, p1));
       this.mKeyDispatcher = new v31(this);
       AppCompatDelegate delegate = this.getDelegate();
       delegate.setTheme(AppCompatDialog.getThemeResId(p0, p1));
       delegate.onCreate(null);
    }
    public void AppCompatDialog(Context p0,boolean p1,DialogInterface$OnCancelListener p2){
       super(p0);
       this.mKeyDispatcher = new v31(this);
       this.setCancelable(p1);
       this.setOnCancelListener(p2);
    }
    private static int getThemeResId(Context p0,int p1){
       TypedValue typedValue;
       if (!p1) {
          typedValue = new TypedValue();
          p0.getTheme().resolveAttribute(R$attr.dialogTheme, typedValue, true);
          typedValue = typedValue.resourceId;
       }
       return typedValue;
    }
    private void initViewTreeOwners(){
       ViewTreeLifecycleOwner.set(this.getWindow().getDecorView(), this);
       ViewTreeSavedStateRegistryOwner.set(this.getWindow().getDecorView(), this);
       ViewTreeOnBackPressedDispatcherOwner.set(this.getWindow().getDecorView(), this);
    }
    public void addContentView(View p0,ViewGroup$LayoutParams p1){
       this.getDelegate().addContentView(p0, p1);
    }
    public void dismiss(){
       super.dismiss();
       this.getDelegate().onDestroy();
    }
    public boolean dispatchKeyEvent(KeyEvent p0){
       return KeyEventDispatcher.dispatchKeyEvent(this.mKeyDispatcher, this.getWindow().getDecorView(), this, p0);
    }
    public View findViewById(int p0){
       return this.getDelegate().findViewById(p0);
    }
    public AppCompatDelegate getDelegate(){
       if (this.mDelegate == null) {
          this.mDelegate = AppCompatDelegate.create(this, this);
       }
       return this.mDelegate;
    }
    public ActionBar getSupportActionBar(){
       return this.getDelegate().getSupportActionBar();
    }
    public void invalidateOptionsMenu(){
       this.getDelegate().invalidateOptionsMenu();
    }
    public void onCreate(Bundle p0){
       this.getDelegate().installViewFactory();
       super.onCreate(p0);
       this.getDelegate().onCreate(p0);
    }
    public void onStop(){
       super.onStop();
       this.getDelegate().onStop();
    }
    public void onSupportActionModeFinished(ActionMode p0){
    }
    public void onSupportActionModeStarted(ActionMode p0){
    }
    public ActionMode onWindowStartingSupportActionMode(ActionMode$Callback p0){
       return null;
    }
    public void setContentView(int p0){
       this.initViewTreeOwners();
       this.getDelegate().setContentView(p0);
    }
    public void setContentView(View p0){
       this.initViewTreeOwners();
       this.getDelegate().setContentView(p0);
    }
    public void setContentView(View p0,ViewGroup$LayoutParams p1){
       this.initViewTreeOwners();
       this.getDelegate().setContentView(p0, p1);
    }
    public void setTitle(int p0){
       super.setTitle(p0);
       this.getDelegate().setTitle(this.getContext().getString(p0));
    }
    public void setTitle(CharSequence p0){
       super.setTitle(p0);
       this.getDelegate().setTitle(p0);
    }
    public boolean superDispatchKeyEvent(KeyEvent p0){
       return super.dispatchKeyEvent(p0);
    }
    public boolean supportRequestWindowFeature(int p0){
       return this.getDelegate().requestWindowFeature(p0);
    }
}
