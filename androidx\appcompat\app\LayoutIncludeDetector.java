package androidx.appcompat.app.LayoutIncludeDetector;
import java.lang.Object;
import java.util.ArrayDeque;
import org.xmlpull.v1.XmlPullParser;
import java.util.Deque;
import java.util.Collection;
import java.lang.ref.WeakReference;
import java.lang.ref.Reference;
import java.lang.String;
import android.util.AttributeSet;

public class LayoutIncludeDetector	// class@00057f from classes.dex
{
    private final Deque mXmlParserStack;

    public void LayoutIncludeDetector(){
       super();
       this.mXmlParserStack = new ArrayDeque();
    }
    private static boolean isParserOutdated(XmlPullParser p0){
       try{
          boolean b = true;
          if (p0 != null && (p0.getEventType() != 3 && p0.getEventType() != b)) {
             b = false;
          }
          return e0;
       }catch(org.xmlpull.v1.XmlPullParserException e0){
       }
    }
    private static XmlPullParser popOutdatedAttrHolders(Deque p0){
       XmlPullParser xmlPullParse;
       while (true) {
          if (p0.isEmpty()) {
             return null;
          }
          xmlPullParse = p0.peek().get();
          if (LayoutIncludeDetector.isParserOutdated(xmlPullParse)) {
             p0.pop();
          }else {
             break ;
          }
       }
       return xmlPullParse;
    }
    private static boolean shouldInheritContext(XmlPullParser p0,XmlPullParser p1){
       try{
          if (p1 != null && (p0 != p1 && p1.getEventType() == 2)) {
             return "include".equals(p1.getName());
          }
          return false;
       }catch(org.xmlpull.v1.XmlPullParserException e0){
       }
    }
    public boolean detect(AttributeSet p0){
       if (p0 instanceof XmlPullParser && p0.getDepth() == 1) {
          this.mXmlParserStack.push(new WeakReference(p0));
          if (LayoutIncludeDetector.shouldInheritContext(p0, LayoutIncludeDetector.popOutdatedAttrHolders(this.mXmlParserStack))) {
             return true;
          }
       }
       return false;
    }
}
