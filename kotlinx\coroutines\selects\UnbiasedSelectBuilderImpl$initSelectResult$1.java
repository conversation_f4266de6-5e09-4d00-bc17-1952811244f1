package kotlinx.coroutines.selects.UnbiasedSelectBuilderImpl$initSelectResult$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.selects.b;
import tb.ar4;
import java.lang.Object;
import tb.uu4;
import tb.xhv;
import tb.dkf;
import java.lang.IllegalStateException;
import java.lang.String;
import kotlin.b;
import kotlinx.coroutines.c;

public final class UnbiasedSelectBuilderImpl$initSelectResult$1 extends SuspendLambda implements u1a	// class@0006b8 from classes11.dex
{
    public int label;
    public final b this$0;

    public void UnbiasedSelectBuilderImpl$initSelectResult$1(b p0,ar4 p1){
       super(2, p1);
    }
    public final ar4 create(Object p0,ar4 p1){
       return new UnbiasedSelectBuilderImpl$initSelectResult$1(null, p1);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(uu4 p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       UnbiasedSelectBuilderImpl$initSelectResult$1 tlabel;
       dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
          }
          b.b(p0);
          b.B(null);
          throw null;
       }else {
          b.b(p0);
          this.label = 1;
          throw null;
       }
    }
}
