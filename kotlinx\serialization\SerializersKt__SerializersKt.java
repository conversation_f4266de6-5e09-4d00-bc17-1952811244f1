package kotlinx.serialization.SerializersKt__SerializersKt;
import tb.wyf;
import java.util.List;
import tb.d1a;
import tb.x530;
import java.util.Collection;
import java.lang.Class;
import tb.dun;
import java.lang.Object;
import tb.ckf;
import java.util.ArrayList;
import tb.yt10;
import java.util.HashSet;
import tb.xx20;
import java.util.Set;
import java.util.LinkedHashSet;
import tb.wd30;
import java.util.HashMap;
import tb.vx20;
import java.util.Map;
import java.util.LinkedHashMap;
import tb.ud30;
import java.util.Map$Entry;
import tb.cz10;
import kotlin.Pair;
import kotlin.Triple;
import tb.cy30;
import java.lang.String;
import java.util.Arrays;
import tb.sb40;
import java.lang.Void;
import tb.wy30;
import tb.tb40;
import tb.e1g;
import java.lang.Iterable;
import tb.zz3;
import java.util.Iterator;
import tb.y530;
import kotlinx.serialization.SerializersCacheKt;
import kotlin.Result;
import kotlinx.serialization.SerializersKt__SerializersKt$serializerByKTypeImpl$contextualSerializer$1;
import tb.k140;
import tb.ub40;

public final class SerializersKt__SerializersKt	// class@000724 from classes11.dex
{

    public static final x530 a(wyf p0,List p1,d1a p2){
       yt10 oyt10;
       List list = List.class;
       boolean i = (ckf.b(p0, dun.b(Collection.class)))? 1: ckf.b(p0, dun.b(list));
       i = (i)? 1: ckf.b(p0, dun.b(list));
       i = (i)? 1: ckf.b(p0, dun.b(ArrayList.class));
       if (i) {
          oyt10 = new yt10(p1.get(0));
       }else if(ckf.b(p0, dun.b(HashSet.class))){
          oyt10 = new xx20(p1.get(0));
       }else {
          Set set = Set.class;
          set = (ckf.b(p0, dun.b(set)))? 1: ckf.b(p0, dun.b(set));
          set = (set)? 1: ckf.b(p0, dun.b(LinkedHashSet.class));
          if (set) {
             oyt10 = new wd30(p1.get(0));
          }else if(ckf.b(p0, dun.b(HashMap.class))){
             oyt10 = new vx20(p1.get(0), p1.get(1));
          }else {
             Map map = Map.class;
             map = (ckf.b(p0, dun.b(map)))? 1: ckf.b(p0, dun.b(map));
             map = (map)? 1: ckf.b(p0, dun.b(LinkedHashMap.class));
             if (map) {
                oyt10 = new ud30(p1.get(0), p1.get(1));
             }else if(ckf.b(p0, dun.b(Map$Entry.class))){
                oyt10 = cz10.i(p1.get(0), p1.get(1));
             }else if(ckf.b(p0, dun.b(Pair.class))){
                oyt10 = cz10.k(p1.get(0), p1.get(1));
             }else if(ckf.b(p0, dun.b(Triple.class))){
                oyt10 = cz10.m(p1.get(0), p1.get(1), p1.get(2));
             }else if(cy30.n(p0)){
                p0 = p2.invoke();
                ckf.e(p0, "null cannot be cast to non-null type kotlin.reflect.KClass<kotlin.Any>");
                oyt10 = cz10.a(p0, p1.get(0));
             }else {
                oyt10 = null;
             }
          }
       }
       return oyt10;
    }
    public static final x530 b(wyf p0,List p1){
       x530[] ox530Array = new x530[0];
       x530[] ox530Array1 = p1.toArray(ox530Array);
       return cy30.d(p0, Arrays.copyOf(ox530Array1, ox530Array1.length));
    }
    public static final x530 c(x530 p0,boolean p1){
       if (p1) {
          return cz10.r(p0);
       }
       ckf.e(p0, "null cannot be cast to non-null type kotlinx.serialization.KSerializer<T of kotlinx.serialization.SerializersKt__SerializersKt.nullable?>");
       return p0;
    }
    public static final x530 d(wyf p0,List p1,d1a p2){
       x530 ox530;
       ckf.g(p0, "<this>");
       ckf.g(p1, "serializers");
       ckf.g(p2, "elementClassifierIfArray");
       if ((ox530 = SerializersKt__SerializersKt.a(p0, p1, p2)) == null) {
          ox530 = SerializersKt__SerializersKt.b(p0, p1);
       }
       return ox530;
    }
    public static final x530 e(wyf p0){
       x530 ox530;
       ckf.g(p0, "<this>");
       if ((ox530 = sb40.d(p0)) != null) {
          return ox530;
       }
       wy30.f(p0);
       throw null;
    }
    public static final x530 f(tb40 p0,e1g p1){
       x530 ox530;
       ckf.g(p0, "<this>");
       ckf.g(p1, "type");
       if ((ox530 = SerializersKt__SerializersKt.g(p0, p1, true)) != null) {
          return ox530;
       }
       cy30.o(wy30.c(p1));
       throw null;
    }
    public static final x530 g(tb40 p0,e1g p1,boolean p2){
       x530 ox530;
       x530 ox5301;
       List list1;
       x530 ox5302;
       wyf owyf = wy30.c(p1);
       boolean b = p1.a();
       Iterable iterable = p1.h();
       ArrayList uArrayList = new ArrayList(zz3.q(iterable, 10));
       Iterator iterator = iterable.iterator();
       while (iterator.hasNext()) {
          uArrayList.add(wy30.g(iterator.next()));
       }
       List list = null;
       if (uArrayList.isEmpty()) {
          ox530 = SerializersCacheKt.a(owyf, b);
       }else {
          ox530 = SerializersCacheKt.b(owyf, uArrayList, b);
          if (Result.isFailure-impl(ox530)) {
             list1 = list;
          }
       }
       if (ox530 != null) {
          return ox530;
       }else if(uArrayList.isEmpty()){
          ox5301 = tb40.b(p0, owyf, list, 2, list);
       }else if((list1 = sb40.g(p0, uArrayList, p2)) == null){
          return list;
       }else if((ox5302 = sb40.a(owyf, list1, new SerializersKt__SerializersKt$serializerByKTypeImpl$contextualSerializer$1(uArrayList))) == null){
          ox5301 = p0.a(owyf, list1);
       }else {
          ox5301 = ox5302;
       }
       if (ox5301 != null) {
          list = SerializersKt__SerializersKt.c(ox5301, b);
       }
       return list;
    }
    public static final x530 h(wyf p0){
       x530 ox530;
       ckf.g(p0, "<this>");
       if ((ox530 = cy30.b(p0)) == null) {
          ox530 = k140.b(p0);
       }
       return ox530;
    }
    public static final x530 i(e1g p0){
       ckf.g(p0, "type");
       return sb40.f(ub40.a(), p0);
    }
    public static final x530 j(tb40 p0,e1g p1){
       ckf.g(p0, "<this>");
       ckf.g(p1, "type");
       return SerializersKt__SerializersKt.g(p0, p1, false);
    }
    public static final List k(tb40 p0,List p1,boolean p2){
       ArrayList uArrayList;
       Iterator iterator;
       x530 ox530;
       ckf.g(p0, "<this>");
       ckf.g(p1, "typeArguments");
       int i = 10;
       if (p2) {
          uArrayList = new ArrayList(zz3.q(p1, i));
          iterator = p1.iterator();
          while (iterator.hasNext()) {
             uArrayList.add(sb40.c(p0, iterator.next()));
          }
       }else {
          uArrayList = new ArrayList(zz3.q(p1, i));
          iterator = p1.iterator();
          while (iterator.hasNext()) {
             if ((ox530 = sb40.f(p0, iterator.next())) == null) {
                return null;
             }
             uArrayList.add(ox530);
          }
       }
       return uArrayList;
    }
}
