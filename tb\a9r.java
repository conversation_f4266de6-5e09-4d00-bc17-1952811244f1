package tb.a9r;
import tb.qs;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import tb.lo;
import java.util.Map;
import java.lang.Class;
import tb.fk;
import tb.nh$c;
import java.lang.Integer;
import android.content.Context;
import tb.xh;
import android.view.View$MeasureSpec;
import tb.rbb;
import tb.ck;
import tb.sbb;

public final class a9r extends qs	// class@00185a from classes5.dex
{
    public int b;
    public int c;
    public static IpChange $ipChange;

    static {
       t2o.a(0x2cc00074);
    }
    public void a9r(){
       super();
       this.b = 0;
       this.c = 0;
    }
    public static Object ipc$super(a9r p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/purchase/aura/extension/autosize/TBBuyPopupWindowDxComponentAutoSizeExtension");
    }
    public final boolean B(){
       IpChange $ipChange = a9r.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return fk.b(this.r().i(), "autoSize", Boolean.class, Boolean.FALSE).booleanValue();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a9e9ebfe", objArray).booleanValue();
    }
    public nh$c x(int p0,int p1){
       int i = 2;
       IpChange $ipChange = a9r.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),new Integer(p1)};
          return $ipChange.ipc$dispatch("1da9f9d", objArray);
       }else if(!this.B()){
          return null;
       }else {
          nh$c uoc = new nh$c();
          uoc.b = p1;
          uoc.a = p0;
          Context uContext = this.r().f();
          if (xh.c(this.r().f())) {
             if (this.b == null) {
                this.b = View$MeasureSpec.makeMeasureSpec(xh.a(uContext), 0x40000000);
                ck.g().d("float updateRenderParam cacheWidthSpec = "+this.b);
             }
             uoc.a = this.b;
          }else if(this.c == null){
             this.c = View$MeasureSpec.makeMeasureSpec((xh.a(uContext) / i), 0x40000000);
             ck.g().d("float updateRenderParam cacheHeightSpec = "+this.c);
          }
          uoc.a = this.c;
          return uoc;
       }
    }
}
