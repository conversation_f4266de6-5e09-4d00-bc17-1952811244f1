package kotlinx.coroutines.channels.BufferedChannel$receiveCatching$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import kotlinx.coroutines.channels.BufferedChannel;
import tb.ar4;
import java.lang.Object;
import tb.dkf;
import kotlinx.coroutines.channels.e;

public final class BufferedChannel$receiveCatching$1 extends ContinuationImpl	// class@0004cb from classes11.dex
{
    public int label;
    public Object result;
    public final BufferedChannel this$0;

    public void BufferedChannel$receiveCatching$1(BufferedChannel p0,ar4 p1){
       this.this$0 = p0;
       super(p1);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       if ((p0 = BufferedChannel.c1(this.this$0, this)) == dkf.d()) {
          return p0;
       }
       return e.a(p0);
    }
}
