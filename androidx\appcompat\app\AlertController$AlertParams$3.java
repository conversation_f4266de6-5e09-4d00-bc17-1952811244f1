package androidx.appcompat.app.AlertController$AlertParams$3;
import android.widget.AdapterView$OnItemClickListener;
import androidx.appcompat.app.AlertController$AlertParams;
import androidx.appcompat.app.AlertController;
import java.lang.Object;
import android.widget.AdapterView;
import android.view.View;
import android.content.DialogInterface;
import android.content.DialogInterface$OnClickListener;
import androidx.appcompat.app.AppCompatDialog;

public class AlertController$AlertParams$3 implements AdapterView$OnItemClickListener	// class@000549 from classes.dex
{
    public final AlertController$AlertParams this$0;
    public final AlertController val$dialog;

    public void AlertController$AlertParams$3(AlertController$AlertParams p0,AlertController p1){
       this.this$0 = p0;
       this.val$dialog = p1;
       super();
    }
    public void onItemClick(AdapterView p0,View p1,int p2,long p3){
       this.this$0.mOnClickListener.onClick(this.val$dialog.mDialog, p2);
       if (this.this$0.mIsSingleChoice == null) {
          this.val$dialog.mDialog.dismiss();
       }
       return;
    }
}
