package tb.a2i;
import android.os.Handler;
import tb.t2o;
import android.os.Looper;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.Runnable;

public class a2i extends Handler	// class@001823 from classes5.dex
{
    public static IpChange $ipChange;
    public static a2i a;

    static {
       t2o.a(0x19700061);
    }
    public void a2i(){
       super(Looper.getMainLooper());
    }
    public static a2i a(){
       IpChange $ipChange = a2i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("80dc28a9", objArray);
       }else if(a2i.a == null){
          a2i uoa2i = a2i.class;
          _monitor_enter(uoa2i);
          if (a2i.a == null) {
             a2i.a = new a2i();
          }
          _monitor_exit(uoa2i);
       }
       return a2i.a;
    }
    public static Object ipc$super(a2i p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/artry/thread/MainThreadHandler");
    }
    public void b(Runnable p0){
       IpChange $ipChange = a2i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("87b65512", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if(Looper.myLooper() == Looper.getMainLooper()){
          p0.run();
       }else {
          this.post(p0);
       }
       return;
    }
    public void c(){
       IpChange $ipChange = a2i.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("6623bb89", objArray);
          return;
       }else {
          a2i.a.removeCallbacksAndMessages(null);
          return;
       }
    }
}
