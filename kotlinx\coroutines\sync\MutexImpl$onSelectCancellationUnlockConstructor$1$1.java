package kotlinx.coroutines.sync.MutexImpl$onSelectCancellationUnlockConstructor$1$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.coroutines.sync.MutexImpl;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;

public final class MutexImpl$onSelectCancellationUnlockConstructor$1$1 extends Lambda implements g1a	// class@0006c6 from classes11.dex
{
    public final Object $owner;
    public final MutexImpl this$0;

    public void MutexImpl$onSelectCancellationUnlockConstructor$1$1(MutexImpl p0,Object p1){
       this.this$0 = p0;
       this.$owner = p1;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(Throwable p0){
       this.this$0.unlock(this.$owner);
    }
}
