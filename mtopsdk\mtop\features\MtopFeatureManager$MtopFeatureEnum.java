package mtopsdk.mtop.features.MtopFeatureManager$MtopFeatureEnum;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;
import java.lang.Number;

public final class MtopFeatureManager$MtopFeatureEnum extends Enum	// class@0007c4 from classes11.dex
{
    public long feature;
    private static final MtopFeatureManager$MtopFeatureEnum[] $VALUES;
    public static IpChange $ipChange;
    public static final MtopFeatureManager$MtopFeatureEnum DISABLE_WHITEBOX_SIGN;
    public static final MtopFeatureManager$MtopFeatureEnum DISABLE_X_COMMAND;
    public static final MtopFeatureManager$MtopFeatureEnum SUPPORT_OPEN_ACCOUNT;
    public static final MtopFeatureManager$MtopFeatureEnum SUPPORT_RELATIVE_URL;
    public static final MtopFeatureManager$MtopFeatureEnum SUPPORT_UTDID_UNIT;
    public static final MtopFeatureManager$MtopFeatureEnum UNIT_INFO_FEATURE;

    static {
       MtopFeatureManager$MtopFeatureEnum mtopFeatureE = new MtopFeatureManager$MtopFeatureEnum("SUPPORT_RELATIVE_URL", 0, 1);
       MtopFeatureManager$MtopFeatureEnum.SUPPORT_RELATIVE_URL = mtopFeatureE;
       MtopFeatureManager$MtopFeatureEnum mtopFeatureE1 = new MtopFeatureManager$MtopFeatureEnum("UNIT_INFO_FEATURE", 1, 2);
       MtopFeatureManager$MtopFeatureEnum.UNIT_INFO_FEATURE = mtopFeatureE1;
       MtopFeatureManager$MtopFeatureEnum mtopFeatureE2 = new MtopFeatureManager$MtopFeatureEnum("DISABLE_WHITEBOX_SIGN", 2, 3);
       MtopFeatureManager$MtopFeatureEnum.DISABLE_WHITEBOX_SIGN = mtopFeatureE2;
       MtopFeatureManager$MtopFeatureEnum mtopFeatureE3 = new MtopFeatureManager$MtopFeatureEnum("SUPPORT_UTDID_UNIT", 3, 4);
       MtopFeatureManager$MtopFeatureEnum.SUPPORT_UTDID_UNIT = mtopFeatureE3;
       MtopFeatureManager$MtopFeatureEnum mtopFeatureE4 = new MtopFeatureManager$MtopFeatureEnum("DISABLE_X_COMMAND", 4, 5);
       MtopFeatureManager$MtopFeatureEnum.DISABLE_X_COMMAND = mtopFeatureE4;
       MtopFeatureManager$MtopFeatureEnum mtopFeatureE5 = new MtopFeatureManager$MtopFeatureEnum("SUPPORT_OPEN_ACCOUNT", 5, 6);
       MtopFeatureManager$MtopFeatureEnum.SUPPORT_OPEN_ACCOUNT = mtopFeatureE5;
       MtopFeatureManager$MtopFeatureEnum[] mtopFeatureE6 = new MtopFeatureManager$MtopFeatureEnum[]{mtopFeatureE,mtopFeatureE1,mtopFeatureE2,mtopFeatureE3,mtopFeatureE4,mtopFeatureE5};
       MtopFeatureManager$MtopFeatureEnum.$VALUES = mtopFeatureE6;
    }
    private void MtopFeatureManager$MtopFeatureEnum(String p0,int p1,long p2){
       super(p0, p1);
       this.feature = p2;
    }
    public static Object ipc$super(MtopFeatureManager$MtopFeatureEnum p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/features/MtopFeatureManager$MtopFeatureEnum");
    }
    public static MtopFeatureManager$MtopFeatureEnum valueOf(String p0){
       IpChange $ipChange = MtopFeatureManager$MtopFeatureEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(MtopFeatureManager$MtopFeatureEnum.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("f4c23a1a", objArray);
    }
    public static MtopFeatureManager$MtopFeatureEnum[] values(){
       IpChange $ipChange = MtopFeatureManager$MtopFeatureEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return MtopFeatureManager$MtopFeatureEnum.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("e2d5434b", objArray);
    }
    public long getFeature(){
       IpChange $ipChange = MtopFeatureManager$MtopFeatureEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.feature;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c34bdb3b", objArray).longValue();
    }
}
