package kotlinx.coroutines.channels.BufferedChannel$onSend$2;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.channels.BufferedChannel;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;

public final class BufferedChannel$onSend$2 extends FunctionReferenceImpl implements w1a	// class@0004c8 from classes11.dex
{
    public static final BufferedChannel$onSend$2 INSTANCE;

    static {
       BufferedChannel$onSend$2.INSTANCE = new BufferedChannel$onSend$2();
    }
    public void BufferedChannel$onSend$2(){
       super(3, BufferedChannel.class, "processResultSelectSend", "processResultSelectSend\(Ljava/lang/Object;Ljava/lang/Object;\)Ljava/lang/Object;", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final Object invoke(BufferedChannel p0,Object p1,Object p2){
       BufferedChannel.E(p0, p1, p2);
       return p0;
    }
}
