package androidx.appcompat.app.TwilightManager;
import android.content.Context;
import android.location.LocationManager;
import java.lang.Object;
import androidx.appcompat.app.TwilightManager$TwilightState;
import java.lang.String;
import android.location.Location;
import androidx.core.content.PermissionChecker;
import java.lang.System;
import androidx.appcompat.app.TwilightCalculator;
import java.util.Calendar;

public class TwilightManager	// class@00058b from classes.dex
{
    private final Context mContext;
    private final LocationManager mLocationManager;
    private final TwilightManager$TwilightState mTwilightState;
    private static final int SUNRISE = 6;
    private static final int SUNSET = 22;
    private static final String TAG = "TwilightManager";
    private static TwilightManager sInstance;

    public void TwilightManager(Context p0,LocationManager p1){
       super();
       this.mTwilightState = new TwilightManager$TwilightState();
       this.mContext = p0;
       this.mLocationManager = p1;
    }
    public static TwilightManager getInstance(Context p0){
       if (TwilightManager.sInstance == null) {
          p0 = p0.getApplicationContext();
          TwilightManager.sInstance = new TwilightManager(p0, p0.getSystemService("location"));
       }
       return TwilightManager.sInstance;
    }
    private Location getLastKnownLocation(){
       Location location = null;
       Location lastKnownLoc = (!PermissionChecker.checkSelfPermission(this.mContext, "android.permission.ACCESS_COARSE_LOCATION"))? this.getLastKnownLocationForProvider("network"): location;
       if (!PermissionChecker.checkSelfPermission(this.mContext, "android.permission.ACCESS_FINE_LOCATION")) {
          location = this.getLastKnownLocationForProvider("gps");
       }
       if (location != null && lastKnownLoc != null) {
          if ((location.getTime() - lastKnownLoc.getTime()) > 0) {
             lastKnownLoc = location;
          }
          return lastKnownLoc;
       }else if(location != null){
          lastKnownLoc = location;
       }
       return lastKnownLoc;
    }
    private Location getLastKnownLocationForProvider(String p0){
       try{
          if (this.mLocationManager.isProviderEnabled(p0)) {
             return this.mLocationManager.getLastKnownLocation(p0);
          }
          return null;
       }catch(java.lang.Exception e0){
       }
    }
    private boolean isStateValid(){
       boolean b = ((this.mTwilightState.nextUpdate - System.currentTimeMillis()) > 0)? true: false;
       return b;
    }
    public static void setInstance(TwilightManager p0){
       TwilightManager.sInstance = p0;
    }
    private void updateState(Location p0){
       long l2;
       TwilightManager mTwilightSta = this.mTwilightState;
       long l = System.currentTimeMillis();
       TwilightCalculator instance = TwilightCalculator.getInstance();
       long l1 = 0x5265c00;
       TwilightCalculator twilightCalc = instance;
       twilightCalc.calculateTwilight((l - l1), p0.getLatitude(), p0.getLongitude());
       twilightCalc.calculateTwilight(l, p0.getLatitude(), p0.getLongitude());
       boolean b = (instance.state == 1)? true: false;
       TwilightCalculator twilightCalc1 = instance.sunset;
       TwilightCalculator twilightCalc2 = instance.sunrise;
       instance.calculateTwilight((l + l1), p0.getLatitude(), p0.getLongitude());
       TwilightCalculator sunrise = instance.sunrise;
       twilightCalc = -1;
       if ((twilightCalc2 - twilightCalc) && (twilightCalc1 - twilightCalc)) {
          if ((l - twilightCalc1) <= 0) {
             sunrise = ((l - twilightCalc2) > 0)? twilightCalc1: twilightCalc2;
          }
          l2 = sunrise + 0xea60;
       }else {
          l2 = l + 0x2932e00;
       }
       mTwilightSta.isNight = b;
       mTwilightSta.nextUpdate = l2;
       return;
    }
    public boolean isNight(){
       Location lastKnownLoc;
       boolean i;
       TwilightManager tmTwilightSt = this.mTwilightState;
       if (this.isStateValid()) {
          return tmTwilightSt.isNight;
       }
       if ((lastKnownLoc = this.getLastKnownLocation()) != null) {
          this.updateState(lastKnownLoc);
          return tmTwilightSt.isNight;
       }else if((i = Calendar.getInstance().get(11)) >= 6 && i < 22){
          i = false;
       }else {
          i = true;
       }
       return i;
    }
}
