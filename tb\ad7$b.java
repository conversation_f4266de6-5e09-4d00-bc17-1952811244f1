package tb.ad7$b;
import tb.vc7;
import tb.ad7;
import java.lang.Object;
import mtopsdk.mtop.domain.MtopResponse;
import tb.gf7;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import java.util.HashMap;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.DescNativeController$b;

public class ad7$b implements vc7	// class@001875 from classes5.dex
{
    public final ad7 a;
    public static IpChange $ipChange;

    public void ad7$b(ad7 p0){
       super();
       this.a = p0;
    }
    public void a(Object p0){
       this.d(p0);
    }
    public void b(int p0,Object p1){
       this.e(p0, p1);
    }
    public void c(int p0,MtopResponse p1){
       IpChange $ipChange = ad7$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("c1a69716", objArray);
          return;
       }else {
          ad7.b(this.a).remove(Integer.valueOf(p0));
          return;
       }
    }
    public void d(MtopResponse p0){
       IpChange $ipChange = ad7$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("cb2ff8f7", objArray);
       }
       return;
    }
    public void e(int p0,gf7 p1){
       IpChange $ipChange = ad7$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("c03b8fb4", objArray);
          return;
       }else {
          ad7.b(this.a).remove(Integer.valueOf(p0));
          this.a.b.a(p1);
          return;
       }
    }
    public void f(gf7 p0){
       IpChange $ipChange = ad7$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fda5cda7", objArray);
       }
       return;
    }
    public void onSuccess(Object p0){
       this.f(p0);
    }
}
