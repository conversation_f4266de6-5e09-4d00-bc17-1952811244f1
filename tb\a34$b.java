package tb.a34$b;
import tb.vl50;
import tb.a34;
import java.lang.Object;
import tb.ikl;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import com.taobao.tao.messagekit.core.model.Command;

public class a34$b implements vl50	// class@00174c from classes9.dex
{
    public static IpChange $ipChange;

    public void a34$b(a34 p0){
       super();
    }
    public boolean a(ikl p0){
       IpChange $ipChange = a34$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.a instanceof Command;
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("a95860dc", objArray).booleanValue();
    }
    public boolean test(Object p0){
       return this.a(p0);
    }
}
