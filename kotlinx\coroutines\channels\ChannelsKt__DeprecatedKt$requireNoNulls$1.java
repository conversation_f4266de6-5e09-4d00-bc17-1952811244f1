package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$requireNoNulls$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalArgumentException;
import java.lang.StringBuilder;
import java.lang.String;
import java.lang.IllegalStateException;

public final class ChannelsKt__DeprecatedKt$requireNoNulls$1 extends SuspendLambda implements u1a	// class@0004f7 from classes11.dex
{
    public final ReceiveChannel $this_requireNoNulls;
    public Object L$0;
    public int label;

    public void ChannelsKt__DeprecatedKt$requireNoNulls$1(ReceiveChannel p0,ar4 p1){
       this.$this_requireNoNulls = p0;
       super(2, p1);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$requireNoNulls$1 orequireNoNu = new ChannelsKt__DeprecatedKt$requireNoNulls$1(this.$this_requireNoNulls, p1);
       orequireNoNu.L$0 = p0;
       return orequireNoNu;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(Object p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       dkf.d();
       if (this.label != null) {
          throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
       }
       b.b(p0);
       if ((p0 = this.L$0) != null) {
          return p0;
       }
       throw new IllegalArgumentException("null element found in "+this.$this_requireNoNulls+'.');
    }
}
