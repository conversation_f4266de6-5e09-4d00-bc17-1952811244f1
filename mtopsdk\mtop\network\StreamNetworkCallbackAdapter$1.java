package mtopsdk.mtop.network.StreamNetworkCallbackAdapter$1;
import java.lang.Runnable;
import mtopsdk.mtop.network.StreamNetworkCallbackAdapter;
import tb.o9o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.mtop.common.MtopFinishEvent;
import mtopsdk.mtop.domain.MtopResponse;
import tb.w4j;
import mtopsdk.common.util.TBSdkLog;
import mtopsdk.mtop.util.MtopStatistics;
import java.lang.System;
import tb.jpq;
import mtopsdk.mtop.common.MtopNetworkProp;
import mtopsdk.mtop.common.MtopCallback$MtopStreamListener;

public class StreamNetworkCallbackAdapter$1 implements Runnable	// class@0007ee from classes11.dex
{
    public final StreamNetworkCallbackAdapter this$0;
    public final o9o val$response;
    public static IpChange $ipChange;

    public void StreamNetworkCallbackAdapter$1(StreamNetworkCallbackAdapter p0,o9o p1){
       this.this$0 = p0;
       this.val$response = p1;
       super();
    }
    public void run(){
       o9o g;
       o9o h1;
       IpChange $ipChange = StreamNetworkCallbackAdapter$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          MtopFinishEvent mtopFinishEv = new MtopFinishEvent(StreamNetworkCallbackAdapter.access$000(this.this$0, this.val$response));
          w4j h = this.this$0.mtopContext.h;
          mtopFinishEv.seqNo = h;
          TBSdkLog.e("mtopsdk.StreamNetworkCallbackAdapter", h, "[onReceiveData----] callback");
          h = this.this$0.mtopContext.g;
          if ((h.streamFirstResponseSize) <= 0 && (g = this.val$response.g) != null) {
             h.streamFirstResponseSize = (long)g.length;
          }
          if ((h.streamFirstDataCallbackTime) <= 0 && this.val$response.g != null) {
             h.streamFirstDataCallbackTime = h.currentTimeMillis();
             this.this$0.mtopContext.g.bizFirstChunkTime = System.currentTimeMillis();
          }
          if ((h1 = this.val$response.h) != null) {
             w4j g1 = this.this$0.mtopContext.g;
             if ((g1.parseStreamFirstDataDuration) <= 0) {
                jpq d = h1.d;
                if ((d) > 0) {
                   g1.parseStreamFirstDataDuration = d;
                }
             }
             if ((g1.parseSSEDataDuration) <= 0) {
                g1.parseSSEDataDuration = h1.e;
             }
             if (g1.expansionCount <= null) {
                g1.expansionCount = h1.f;
             }
             if ((g1.copyDataDuration) <= 0) {
                g1.copyDataDuration = h1.g;
             }
             if (g1.receivedCount <= null) {
                g1.receivedCount = h1.h;
             }
             if ((g1.findEndDuration) <= 0) {
                g1.findEndDuration = h1.i;
             }
          }
          StreamNetworkCallbackAdapter$1 tthis$0 = this.this$0;
          tthis$0.streamListener.onReceiveData(mtopFinishEv, tthis$0.mtopContext.d.reqContext);
          return;
       }
    }
}
