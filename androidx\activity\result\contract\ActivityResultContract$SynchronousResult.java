package androidx.activity.result.contract.ActivityResultContract$SynchronousResult;
import java.lang.Object;

public final class ActivityResultContract$SynchronousResult	// class@0004c4 from classes.dex
{
    private final Object value;

    public void ActivityResultContract$SynchronousResult(Object p0){
       super();
       this.value = p0;
    }
    public final Object getValue(){
       return this.value;
    }
}
