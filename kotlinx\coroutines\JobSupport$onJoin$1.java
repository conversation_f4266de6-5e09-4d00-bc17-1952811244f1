package kotlinx.coroutines.JobSupport$onJoin$1;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.JobSupport;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import tb.k9p;
import tb.xhv;

public final class JobSupport$onJoin$1 extends FunctionReferenceImpl implements w1a	// class@0004ab from classes11.dex
{
    public static final JobSupport$onJoin$1 INSTANCE;

    static {
       JobSupport$onJoin$1.INSTANCE = new JobSupport$onJoin$1();
    }
    public void JobSupport$onJoin$1(){
       super(3, JobSupport.class, "registerSelectForOnJoin", "registerSelectForOnJoin\(Lkotlinx/coroutines/selects/SelectInstance;Ljava/lang/Object;\)V", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       this.invoke(p0, p1, p2);
       return xhv.INSTANCE;
    }
    public final void invoke(JobSupport p0,k9p p1,Object p2){
       JobSupport.B(p0, p1, p2);
    }
}
