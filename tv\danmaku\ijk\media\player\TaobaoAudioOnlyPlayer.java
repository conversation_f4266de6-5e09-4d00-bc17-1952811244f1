package tv.danmaku.ijk.media.player.TaobaoAudioOnlyPlayer;
import com.taobao.mediaplay.player.a$a;
import tv.danmaku.ijk.media.player.MonitorMediaPlayer;
import tb.t2o;
import java.util.concurrent.ConcurrentHashMap;
import tb.c15;
import android.content.Context;
import tb.gf4;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;
import tb.cq;
import tb.feh;
import java.lang.ref.Reference;
import com.taobao.taobaoavsdk.AVSDKLog;
import tb.r7t;
import android.os.Looper;
import tv.danmaku.ijk.media.player.TaobaoAudioOnlyPlayer$AudioEventHandler;
import com.taobao.mediaplay.player.a;
import android.app.Application;
import java.lang.ref.WeakReference;
import java.lang.Integer;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.Boolean;
import tv.danmaku.ijk.media.player.TaobaoMediaPlayer;
import java.lang.System;
import java.lang.Throwable;
import java.lang.StackTraceElement;
import java.lang.Long;
import android.os.Message;
import android.os.Handler;
import tv.danmaku.ijk.media.player.EventData;
import java.util.Set;
import java.util.Iterator;
import java.util.Map;
import android.app.Activity;
import android.os.Bundle;
import tb.x3r;
import android.media.AudioManager$OnAudioFocusChangeListener;
import java.util.Map$Entry;
import tv.danmaku.ijk.media.player.AbstractMediaPlayer;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioPauseListener;
import java.util.List;
import tv.danmaku.ijk.media.player.IMediaPlayer$OnAudioStartListener;
import com.taobao.orange.OrangeConfig;
import tb.ew0;
import java.util.concurrent.ExecutorService;
import tb.dq;
import tv.danmaku.ijk.media.player.TaobaoAudioOnlyPlayer$1;
import java.lang.Runnable;
import java.util.concurrent.Executor;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.ja1;
import java.lang.Math;
import android.view.SurfaceHolder;
import java.lang.Float;
import android.view.Surface;

public final class TaobaoAudioOnlyPlayer extends MonitorMediaPlayer implements a$a	// class@0010fa from classes11.dex
{
    private a mActivityLifecycleCallbacks;
    private TaobaoAudioOnlyPlayer$AudioEventHandler mEventHandler;
    public boolean mFetchFFMpegSoReadyInit;
    private feh mLogContext;
    private ConcurrentHashMap mLoopmap;
    private long mNativeMediaPlayer;
    private boolean mPauseInBackground;
    private ConcurrentHashMap mPausemap;
    private ConcurrentHashMap mPlayingmap;
    private boolean mRequestAudioFocus;
    public static IpChange $ipChange;
    private static final int AUDIO_EDIA_BUFFERING_UPDATE;
    public static final int AUDIO_ENABLE_ACCURATE_SEEK;
    public static final int AUDIO_INT64_COMPLETE_DONE_SEEK;
    public static final int AUDIO_INT64_ENABLE_SEEK_IN_PAUSE;
    public static final int AUDIO_INT64_ENABLE_SHORT_MUSIC;
    public static final int AUDIO_INT64_START_ON_PREPARED;
    public static final int AUDIO_INT64_START_WHEN_SEEK;
    public static final int AUDIO_INT64_STATE_NOTIFY_GAP;
    private static final int AUDIO_MEDIA_ERROR;
    private static final int AUDIO_MEDIA_INFO;
    private static final int AUDIO_MEDIA_OUT_OF_BUFFERING;
    private static final int AUDIO_MEDIA_PAUSE;
    private static final int AUDIO_MEDIA_PLAYBACK_COMPLETE;
    private static final int AUDIO_MEDIA_PREPARED;
    private static final int AUDIO_MEDIA_RESUME_BUFFERING;
    private static final int AUDIO_MEDIA_SEEK_COMPLETE;
    private static final int AUDIO_MEDIA_SEEK_START;
    private static final int AUDIO_MEDIA_SET_VIDEO_SIZE;
    private static final int AUDIO_MEDIA_START;
    private static final int AUDIO_MEDIA_TIMED_TEXT;
    private static final int AUDIO_MEDIA_WARMUP;
    public static final int FFP_PROP_FLOAT_VOLUME;
    private static boolean mIsAudioLibLoaded;

    static {
       t2o.a(0x30b0017a);
       t2o.a(0x30b0007a);
       TaobaoAudioOnlyPlayer.mIsAudioLibLoaded = false;
    }
    public void TaobaoAudioOnlyPlayer(){
       super();
       this.mFetchFFMpegSoReadyInit = false;
       this.mLoopmap = new ConcurrentHashMap();
       this.mPausemap = new ConcurrentHashMap();
       this.mPlayingmap = new ConcurrentHashMap();
       this.mRequestAudioFocus = false;
       this.mPauseInBackground = false;
       this.initPlayer(null);
    }
    public void TaobaoAudioOnlyPlayer(Context p0){
       super(p0);
       this.mFetchFFMpegSoReadyInit = false;
       this.mLoopmap = new ConcurrentHashMap();
       this.mPausemap = new ConcurrentHashMap();
       this.mPlayingmap = new ConcurrentHashMap();
       this.mRequestAudioFocus = false;
       this.mPauseInBackground = false;
       this.initPlayer(null);
    }
    public void TaobaoAudioOnlyPlayer(Context p0,gf4 p1){
       super(p0);
       this.mFetchFFMpegSoReadyInit = false;
       this.mLoopmap = new ConcurrentHashMap();
       this.mPausemap = new ConcurrentHashMap();
       this.mPlayingmap = new ConcurrentHashMap();
       this.mRequestAudioFocus = false;
       this.mPauseInBackground = false;
       this.initPlayer(null);
    }
    private native float _audioGetCurCachePosition(String p0);
    private native long _audioGetCurrentPosition(String p0);
    private native long _audioGetDuration(String p0);
    private native boolean _audioIsPlaying(String p0);
    private native void _audioPause(String p0);
    private native void _audioPrepareAsync(String p0);
    private native void _audioSeekTo(String p0,long p1);
    private native void _audioSeekToPause(String p0,long p1,boolean p2);
    private native void _audioStart(String p0);
    private native void _audioStop(String p0);
    private native void _audio_native_setup(Object p0);
    private native void _audiorRelease();
    private native void _setAudioDataSource(String p0,String p1,String[] p2,String[] p3);
    public static void access$000(TaobaoAudioOnlyPlayer p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("c98d92a6", objArray);
          return;
       }else {
          p0._audiorRelease();
          return;
       }
    }
    public static long access$100(TaobaoAudioOnlyPlayer p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mNativeMediaPlayer;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("567aa9b9", objArray).longValue();
    }
    public static ConcurrentHashMap access$200(TaobaoAudioOnlyPlayer p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mPlayingmap;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("aaa8b682", objArray);
    }
    public static ConcurrentHashMap access$300(TaobaoAudioOnlyPlayer p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mPausemap;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("da5fea83", objArray);
    }
    public static ConcurrentHashMap access$400(TaobaoAudioOnlyPlayer p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mLoopmap;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("a171e84", objArray);
    }
    private void initFetchSo(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("bc7cfd0d", objArray);
          return;
       }else {
          this.mFetchFFMpegSoReadyInit = cq.b();
          return;
       }
    }
    private void initPlayer(c15 p0){
       MonitorMediaPlayer tmContextRef;
       Looper mainLooper;
       int i = 1;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f13836b3", objArray);
          return;
       }else {
          this.mLogContext = new feh(this.toString(), "");
          Context uContext = ((tmContextRef = this.mContextRef) == null)? null: tmContextRef.get();
          AVSDKLog.e(this.mLogContext, "AUDIO initPlayer");
          TaobaoAudioOnlyPlayer.loadAudioLibrariesOnce(p0);
          r7t or7t = new r7t("AudioOnly");
          or7t = new r7t("AudioOnly");
          or7t.Z = i;
          or7t.l0 = i;
          this.setConfig(or7t);
          this.mEventHandler = ((mainLooper = Looper.getMainLooper()) != null)? new TaobaoAudioOnlyPlayer$AudioEventHandler(this, mainLooper): null;
          if (uContext != null && this.mActivityLifecycleCallbacks == null) {
             this.mActivityLifecycleCallbacks = new a(this, uContext.getApplicationContext());
          }
          this._audio_native_setup(new WeakReference(this));
          return;
       }
    }
    public static Object ipc$super(TaobaoAudioOnlyPlayer p0,String p1,Object[] p2){
       if (p1.hashCode() == 0x39039f0e) {
          return new Integer(super.setConfig(p2[0]));
       }
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in tv/danmaku/ijk/media/player/TaobaoAudioOnlyPlayer");
    }
    public static boolean isAudioLibLoaded(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return TaobaoAudioOnlyPlayer.mIsAudioLibLoaded;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("90b59909", objArray).booleanValue();
    }
    public static void loadAudioLibrariesOnce(c15 p0){
       int i = 1;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          $ipChange.ipc$dispatch("9dd1cdac", objArray);
          return;
       }else {
          AVSDKLog.e("AVSDK", "AUDIO loadAudioLibrariesOnce");
          _monitor_enter(TaobaoMediaPlayer.class);
          if (!TaobaoAudioOnlyPlayer.mIsAudioLibLoaded) {
             if (p0 != null) {
                p0.loadLibrary("c++_shared");
                p0.loadLibrary("tbffmpeg");
                p0.loadLibrary("taobaoplayer");
             }else {
                System.loadLibrary("c++_shared");
                System.loadLibrary("tbffmpeg");
                System.loadLibrary("taobaoplayer");
             }
             TaobaoAudioOnlyPlayer.mIsAudioLibLoaded = i;
          }
          _monitor_exit(TaobaoMediaPlayer.class);
          return;
       }
    }
    private static void postAudioEventFromNative(Object p0,int p1,long p2,long p3,long p4,Object p5){
       TaobaoAudioOnlyPlayer mEventHandle;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,new Integer(p1),new Long(p2),new Long(p3),new Long(p4),p5};
          $ipChange.ipc$dispatch("6c8cd785", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if((p0 = p0.get()) == null){
          return;
       }else {
          _monitor_enter(TaobaoAudioOnlyPlayer.class);
          if ((mEventHandle = p0.mEventHandler) != null) {
             Message message = mEventHandle.obtainMessage(p1);
             EventData uEventData = new EventData();
             uEventData.arg1 = p2;
             uEventData.arg2 = p3;
             uEventData.arg3 = p4;
             uEventData.obj = p5;
             message.obj = uEventData;
             if (p1 == 1) {
                p0.mEventHandler.sendMessageAtFrontOfQueue(message);
             }else {
                p0.mEventHandler.sendMessage(message);
             }
          }
          _monitor_exit(TaobaoAudioOnlyPlayer.class);
          return;
       }
    }
    public native float _audioGetPropertyFloat(String p0,int p1,float p2);
    public native long _audioGetPropertyLong(String p0,int p1,long p2);
    public native String _audioGetPropertyString(String p0,int p1);
    public native void _audioSetPropertyFloat(String p0,int p1,float p2);
    public native void _audioSetPropertyLong(String p0,int p1,long p2);
    public native void _audioSetPropertyString(String p0,int p1,String p2);
    public float getCurCachePosition(String p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6cae73a2", objArray).floatValue();
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO getCurCachePosition token is "+p0);
          return this._audioGetCurCachePosition(p0);
       }
    }
    public long getCurrentPosition(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return 0;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("b656e207", objArray).longValue();
    }
    public long getCurrentPosition(String p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("3d8ccd11", objArray).longValue();
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO getCurrentPosition token is "+p0);
          return this._audioGetCurrentPosition(p0);
       }
    }
    public long getDuration(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return 0;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ed837a85", objArray).longValue();
    }
    public long getDuration(String p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("e59f820f", objArray).longValue();
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO getDuration token is "+p0);
          return this._audioGetDuration(p0);
       }
    }
    public String getPausePlayerToken(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("7df8dac1", objArray);
       }else if(this.mPausemap.isEmpty()){
          AVSDKLog.e(this.mLogContext, "No paused players available.");
          return null;
       }else {
          try{
             Iterator iterator = this.mPausemap.keySet().iterator();
             if (iterator.hasNext()) {
                return iterator.next();
             }
          }catch(java.lang.Exception e0){
             AVSDKLog.e(this.mLogContext, "Failed to retrieve the first paused player token."+e0);
          }
          return null;
       }
    }
    public Map getQos(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("206bab9b", objArray);
    }
    public int getVideoHeight(){
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("867fcec6", objArray).intValue();
       }
       return i;
    }
    public int getVideoSarDen(){
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("1a935228", objArray).intValue();
       }
       return i;
    }
    public int getVideoSarNum(){
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("46d11521", objArray).intValue();
       }
       return i;
    }
    public int getVideoWidth(){
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("fe5511fb", objArray).intValue();
       }
       return i;
    }
    public void instantSeekTo(long p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("8f691749", objArray);
       }
       return;
    }
    public boolean isPlaying(){
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("9a3f2a2f", objArray).booleanValue();
       }
       return i;
    }
    public boolean isPlaying(String p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("2aec3f39", objArray).booleanValue();
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO isPlaying token is "+p0);
          return this._audioIsPlaying(p0);
       }
    }
    public void onActivityCreated(Activity p0,Bundle p1){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("cce650e1", objArray);
       }
       return;
    }
    public void onActivityDestroyed(Activity p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4148cc84", objArray);
       }
       return;
    }
    public void onActivityPaused(Activity p0){
       AbstractMediaPlayer tmOnAudioPau;
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a4658a75", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO onActivityPaused");
          if (this.mPauseInBackground == null) {
             return;
          }
          if (!this.mPlayingmap.isEmpty()) {
             if (this.mRequestAudioFocus != null) {
                MonitorMediaPlayer tmContextRef = this.mContextRef;
                AudioManager$OnAudioFocusChangeListener onAudioFocus = null;
                Context uContext = (tmContextRef == null)? onAudioFocus: tmContextRef.get();
                x3r.j(uContext).k(onAudioFocus);
                this.mRequestAudioFocus = i;
             }
             Iterator iterator = this.mPlayingmap.entrySet().iterator();
             String str = "";
             while (iterator.hasNext()) {
                AVSDKLog.e(this.mLogContext, "AUDIO onActivityPaused".append(str).toString());
                str = iterator.next().getKey();
                if ((tmOnAudioPau = this.mOnAudioPauseListener) != null) {
                   tmOnAudioPau.onPause(str);
                }
                if ((tmOnAudioPau = this.mOnAudioPauseListeners) != null) {
                   Iterator iterator1 = tmOnAudioPau.iterator();
                   while (iterator1.hasNext()) {
                      iterator1.next().onPause(str);
                   }
                }
                this._audioPause(str);
             }
          }
          return;
       }
    }
    public void onActivityResumed(Activity p0){
       AbstractMediaPlayer tmOnAudioSta;
       int i = 1;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3e8abf42", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO onActivityResumed");
          if (this.mPauseInBackground == null) {
             return;
          }
          if (!this.mPlayingmap.isEmpty()) {
             if (this.mRequestAudioFocus == null) {
                MonitorMediaPlayer tmContextRef = this.mContextRef;
                AudioManager$OnAudioFocusChangeListener onAudioFocus = null;
                Context uContext = (tmContextRef == null)? onAudioFocus: tmContextRef.get();
                x3r.j(uContext).h(onAudioFocus, i);
                this.mRequestAudioFocus = i;
             }
             Iterator iterator = this.mPlayingmap.entrySet().iterator();
             String str = "";
             while (iterator.hasNext()) {
                AVSDKLog.e(this.mLogContext, "AUDIO onActivityResumed".append(str).toString());
                str = iterator.next().getKey();
                if ((tmOnAudioSta = this.mOnAudioStartListener) != null) {
                   tmOnAudioSta.onStart(str);
                }
                if ((tmOnAudioSta = this.mOnAudioStartListeners) != null) {
                   Iterator iterator1 = tmOnAudioSta.iterator();
                   while (iterator1.hasNext()) {
                      iterator1.next().onStart(str);
                   }
                }
                this._audioStart(str);
             }
          }
          return;
       }
    }
    public void onActivitySaveInstanceState(Activity p0,Bundle p1){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("2c9c12a", objArray);
       }
       return;
    }
    public void onActivityStarted(Activity p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5e01616c", objArray);
       }
       return;
    }
    public void onActivityStopped(Activity p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("dc236bb8", objArray);
       }
       return;
    }
    public void pause(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("315dbf7d", objArray);
       }
       return;
    }
    public void pause(String p0){
       AbstractMediaPlayer tmOnAudioPau;
       MonitorMediaPlayer tmContextRef;
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8202e407", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO pause token is "+p0);
          boolean b = ew0.s(OrangeConfig.getInstance().getConfig("DWInteractive", "fixAudioOnlyStartWhenSeek", "false"));
          if (!this.mPausemap.containsKey(p0)) {
             if ((tmOnAudioPau = this.mOnAudioPauseListener) != null) {
                tmOnAudioPau.onPause(p0);
             }
             if ((tmOnAudioPau = this.mOnAudioPauseListeners) != null) {
                Iterator iterator = tmOnAudioPau.iterator();
                while (iterator.hasNext()) {
                   iterator.next().onPause(p0);
                }
             }
             if (b) {
                this.mPausemap.put(p0, Boolean.TRUE);
                this.mPlayingmap.remove(p0);
                this._audioPause(p0);
             }
          }
          if (!b) {
             this.mPausemap.put(p0, Boolean.TRUE);
             this.mPlayingmap.remove(p0);
             this._audioPause(p0);
          }
          if (this.mRequestAudioFocus != null && this.mPlayingmap.isEmpty()) {
             Context uContext = ((tmContextRef = this.mContextRef) == null)? null: tmContextRef.get();
             x3r.j(uContext).k(null);
             this.mRequestAudioFocus = i;
          }
          return;
       }
    }
    public int playingCount(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("1cf9597b", objArray).intValue();
       }else {
          int i = this.mPlayingmap.size();
          AVSDKLog.e(this.mLogContext, "AUDIO isPlaying count is: "+i);
          return i;
       }
    }
    public void prepareAsync(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d411b3bc", objArray);
       }
       return;
    }
    public void prepareAsync(String p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fe686", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO prepareAsync token is "+p0);
          this._audioSetPropertyLong(p0, 0x4ea3, 1);
          this._audioSetPropertyLong(p0, 0x2aff, 1);
          this._audioSetPropertyLong(p0, 0xea64, 1);
          this._audioSetPropertyLong(p0, 0x13886, 1);
          if (ew0.s(OrangeConfig.getInstance().getConfig("DWInteractive", "fixAudioOnlyStartWhenSeek", "false"))) {
             this._audioSetPropertyLong(p0, 0xc353, 1);
          }
          if (ew0.s(OrangeConfig.getInstance().getConfig("DWInteractive", "enableAudioOnlyShortMusic", "false"))) {
             this._audioSetPropertyLong(p0, 0xc354, 1);
          }
          this._audioPrepareAsync(p0);
          return;
       }
    }
    public void release(){
       MonitorMediaPlayer tmContextRef;
       TaobaoAudioOnlyPlayer tmActivityLi;
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("ca5510e", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO release ");
          this.mLoopmap.clear();
          this.mPausemap.clear();
          this.mPlayingmap.clear();
          if (Looper.myLooper() == Looper.getMainLooper()) {
             dq.b().execute(new TaobaoAudioOnlyPlayer$1(this));
          }else {
             this._audiorRelease();
          }
          Context uContext = ((tmContextRef = this.mContextRef) == null)? null: tmContextRef.get();
          if (this.mRequestAudioFocus != null && this.mPlayingmap.isEmpty()) {
             x3r.j(uContext).k(null);
             this.mRequestAudioFocus = i;
          }
          if (uContext != null && (tmActivityLi = this.mActivityLifecycleCallbacks) != null) {
             tmActivityLi.b(uContext.getApplicationContext());
             this.mActivityLifecycleCallbacks = null;
          }
          return;
       }
    }
    public void releasePrefixInUIThread(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5a4562c3", objArray);
       }
       return;
    }
    public void reset(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("788e6256", objArray);
       }
       return;
    }
    public void seekTo(long p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("49645bea", objArray);
       }
       return;
    }
    public void seekTo(long p0,boolean p1,boolean p2){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0),new Boolean(p1),new Boolean(p2)};
          $ipChange.ipc$dispatch("81d363ea", objArray);
       }
       return;
    }
    public void seekTo(String p0,long p1){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("9d3fade0", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO seekTo token is "+p0+"msec is"+p1);
          this._audioSeekTo(p0, p1);
          return;
       }
    }
    public void seekTo(String p0,long p1,boolean p2,boolean p3){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1),new Boolean(p2),new Boolean(p3)};
          $ipChange.ipc$dispatch("4c221060", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO seekTo token is "+p0+"msec is"+p1);
          this._audioSeekToPause(p0, p1, p2);
          return;
       }
    }
    public int setConfig(r7t p0){
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("39039f0e", objArray).intValue();
       }else {
          super.setConfig(p0);
          this.initFetchSo();
          return i;
       }
    }
    public String setDataSource(String p0,String p1){
       TaobaoAudioOnlyPlayer tmLogContext;
       MonitorMediaPlayer tmContextRef;
       Context uContext;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("dfe2f75e", objArray);
       }else if(!TextUtils.isEmpty(p0) && (tmLogContext = this.mLogContext) != null){
          tmLogContext.c(p0);
       }
       AVSDKLog.e(this.mLogContext, "AUDIO setDataSource token is "+p0+", pathis "+p1);
       if (TextUtils.isEmpty(p1)) {
          return "";
       }else if((tmContextRef = this.mContextRef) == null){
          uContext = null;
       }else {
          uContext = tmContextRef.get();
       }
       if (ew0.s(ja1.K(uContext, "DWInteractive", "fixAudioHttps", "false"))) {
          String str = "https://";
          if (p1.startsWith(str)) {
             p1 = p1.replace(str, "http://");
          }
       }
       AVSDKLog.e(this.mLogContext, "TaobaoAudioOnlyPlayer proxyUrl : "+p1);
       if (p0.isEmpty()) {
          p0 = System.currentTimeMillis()+"_"+(Math.random() * 100000.00f);
       }
       this._setAudioDataSource(p0, p1, null, null);
       return p0;
    }
    public void setDataSource(String p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("99f7c5b8", objArray);
       }
       return;
    }
    public void setDisplay(SurfaceHolder p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("83fedb87", objArray);
       }
       return;
    }
    public void setLooping(String p0,boolean p1){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("4f7f66e7", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO setLooping token is "+p0+",looping is "+p1);
          if (p1) {
             this.mLoopmap.put(p0, Boolean.valueOf(p1));
          }else {
             this.mLoopmap.remove(p0);
          }
          return;
       }
    }
    public void setMuted(String p0,boolean p1){
       long l;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("7481ba1a", objArray);
          return;
       }else if(p1){
          l = 1;
       }else {
          l = 0;
       }
       this._audioSetPropertyLong(p0, 0x5210, l);
       return;
    }
    public void setMuted(boolean p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("810efea4", objArray);
       }
       return;
    }
    public void setPauseInBackground(boolean p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("7b7aa86", objArray);
          return;
       }else {
          this.mPauseInBackground = p0;
          return;
       }
    }
    public void setPlayRate(float p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Float(p0)};
          $ipChange.ipc$dispatch("4b5e4523", objArray);
       }
       return;
    }
    public void setScreenOnWhilePlaying(boolean p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("acaef323", objArray);
       }
       return;
    }
    public void setStateChangeGap(String p0,long p1){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("6accaffc", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO setStateChangeGap token is "+p0+"gap is "+p1);
          this._audioSetPropertyLong(p0, 0x5976, p1);
          return;
       }
    }
    public void setSurface(Surface p0){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f72113e", objArray);
       }
       return;
    }
    public void setSurfaceSize(int p0,int p1){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),new Integer(p1)};
          $ipChange.ipc$dispatch("514522b3", objArray);
       }
       return;
    }
    public void setVolume(float p0,float p1){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Float(p0),new Float(p1)};
          $ipChange.ipc$dispatch("ef12afe3", objArray);
       }
       return;
    }
    public void setVolume(String p0,float p1,float p2){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Float(p1),new Float(p2)};
          $ipChange.ipc$dispatch("27e97c6d", objArray);
          return;
       }else {
          this._audioSetPropertyFloat(p0, 0x2ee1, p1);
          return;
       }
    }
    public void start(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("810347e9", objArray);
       }
       return;
    }
    public void start(String p0){
       MonitorMediaPlayer tmContextRef;
       AbstractMediaPlayer tmOnAudioSta;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5a37a973", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO start token is "+p0);
          Context uContext = ((tmContextRef = this.mContextRef) == null)? null: tmContextRef.get();
          if (!this.mPlayingmap.containsKey(p0)) {
             if ((tmOnAudioSta = this.mOnAudioStartListener) != null) {
                tmOnAudioSta.onStart(p0);
             }
             if ((tmOnAudioSta = this.mOnAudioStartListeners) != null) {
                Iterator iterator = tmOnAudioSta.iterator();
                while (iterator.hasNext()) {
                   iterator.next().onStart(p0);
                }
             }
          }
          this.mPlayingmap.put(p0, Boolean.TRUE);
          this.mPausemap.remove(p0);
          if (this.mRequestAudioFocus == null && !this.mPlayingmap.isEmpty()) {
             x3r.j(uContext).h(null, 1);
             this.mRequestAudioFocus = true;
          }
          this._audioStart(p0);
          return;
       }
    }
    public void stop(){
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("6623bb89", objArray);
       }
       return;
    }
    public void stop(String p0){
       MonitorMediaPlayer tmContextRef;
       int i = 0;
       IpChange $ipChange = TaobaoAudioOnlyPlayer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4bbe7513", objArray);
          return;
       }else {
          AVSDKLog.e(this.mLogContext, "AUDIO stop token is "+p0);
          this._audioPause(p0);
          this._audioStop(p0);
          this.mLoopmap.remove(p0);
          this.mPausemap.remove(p0);
          this.mPlayingmap.remove(p0);
          if (this.mRequestAudioFocus != null && this.mPlayingmap.isEmpty()) {
             Context uContext = ((tmContextRef = this.mContextRef) == null)? null: tmContextRef.get();
             x3r.j(uContext).k(null);
             this.mRequestAudioFocus = i;
          }
          return;
       }
    }
}
