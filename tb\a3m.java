package tb.a3m;
import tb.md7;
import tb.t2o;
import com.taobao.android.detail.ttdetail.skeleton.desc.natives.structure.ComponentModel;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import com.alibaba.fastjson.JSONObject;

public class a3m extends md7	// class@00182f from classes5.dex
{
    public String k;
    public String l;
    public String m;
    public static IpChange $ipChange;

    static {
       t2o.a(0x38e00617);
    }
    public void a3m(ComponentModel p0){
       super(p0);
    }
    public static Object ipc$super(a3m p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/detail/ttdetail/skeleton/desc/natives/viewmodel/PictureJumperViewModel");
    }
    public boolean g(){
       int i = 0;
       IpChange $ipChange = a3m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("ea845f58", objArray).booleanValue();
       }
       return i;
    }
    public void j(JSONObject p0){
       IpChange $ipChange = a3m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a37e7e28", objArray);
          return;
       }else {
          this.k = p0.getString("actPicUrl");
          p0.getString("jumpUrl");
          this.m = p0.getString("widthRatio");
          this.l = p0.getString("heightRatio");
          return;
       }
    }
}
