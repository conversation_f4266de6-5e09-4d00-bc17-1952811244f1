package androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import android.view.View;
import android.view.ViewTreeObserver$OnScrollChangedListener;
import android.view.View$OnLayoutChangeListener;
import androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1;
import java.lang.Object;
import tb.xhv;
import android.view.ViewTreeObserver;
import android.view.View$OnAttachStateChangeListener;

public final class PipHintTrackerKt$trackPipAnimationHintView$flow$1$1 extends Lambda implements d1a	// class@000467 from classes.dex
{
    public final PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1 $attachStateChangeListener;
    public final View$OnLayoutChangeListener $layoutChangeListener;
    public final ViewTreeObserver$OnScrollChangedListener $scrollChangeListener;
    public final View $view;

    public void PipHintTrackerKt$trackPipAnimationHintView$flow$1$1(View p0,ViewTreeObserver$OnScrollChangedListener p1,View$OnLayoutChangeListener p2,PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1 p3){
       this.$view = p0;
       this.$scrollChangeListener = p1;
       this.$layoutChangeListener = p2;
       this.$attachStateChangeListener = p3;
       super(0);
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       this.$view.getViewTreeObserver().removeOnScrollChangedListener(this.$scrollChangeListener);
       this.$view.removeOnLayoutChangeListener(this.$layoutChangeListener);
       this.$view.removeOnAttachStateChangeListener(this.$attachStateChangeListener);
    }
}
