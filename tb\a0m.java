package tb.a0m;
import tb.t2o;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import android.app.Application;
import com.ut.share.ShareEnv;
import android.content.pm.ApplicationInfo;
import android.content.Context;
import android.os.Build$VERSION;
import tb.a0m$a;
import java.lang.Boolean;
import java.util.ArrayList;

public class a0m	// class@00173c from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x29c000c1);
    }
    public static String[] a(){
       Object[] objArray;
       String[] stringArray;
       IpChange $ipChange = a0m.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[0];
          return $ipChange.ipc$dispatch("37607bb6", objArray);
       }else {
          objArray = 33;
          if (ShareEnv.getApplication().getApplicationInfo().targetSdkVersion >= objArray && Build$VERSION.SDK_INT >= objArray) {
             stringArray = new String[]{"android.permission.READ_MEDIA_IMAGES","android.permission.READ_MEDIA_VIDEO","android.permission.READ_MEDIA_AUDIO"};
             return stringArray;
          }else {
             stringArray = new String[]{"android.permission.WRITE_EXTERNAL_STORAGE","android.permission.READ_EXTERNAL_STORAGE"};
             return stringArray;
          }
       }
    }
    public static boolean b(a0m$a p0){
       boolean b;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a0m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("34698f61", objArray).booleanValue();
       }else {
          boolean i2 = 33;
          if (ShareEnv.getApplication().getApplicationInfo().targetSdkVersion >= i2 && Build$VERSION.SDK_INT >= i2) {
             i2 = p0.a("android.permission.READ_MEDIA_VIDEO");
             b = p0.a("android.permission.READ_MEDIA_AUDIO");
             if (p0.a("android.permission.READ_MEDIA_IMAGES") && (i2 && b)) {
                i = true;
             }
             return i;
          }else {
             b = p0.a("android.permission.WRITE_EXTERNAL_STORAGE");
             if (p0.a("android.permission.READ_EXTERNAL_STORAGE") && b) {
                i = true;
             }
             return i;
          }
       }
    }
    public static boolean c(){
       IpChange $ipChange = a0m.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("4b7cd6f8", objArray).booleanValue();
       }else if(ShareEnv.getApplication().getApplicationInfo().targetSdkVersion >= 33 && Build$VERSION.SDK_INT >= 33){
          return true;
       }else {
          return i;
       }
    }
    public static String[] d(boolean p0,boolean p1,boolean p2){
       String[] stringArray;
       int i = 0;
       IpChange $ipChange = a0m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Boolean(p0),new Boolean(p1),new Boolean(p2)};
          return $ipChange.ipc$dispatch("3b9689dc", objArray);
       }else if(ShareEnv.getApplication().getApplicationInfo().targetSdkVersion >= 33 && Build$VERSION.SDK_INT >= 33){
          ArrayList uArrayList = new ArrayList();
          if (p0) {
             uArrayList.add("android.permission.READ_MEDIA_AUDIO");
          }
          if (p2) {
             uArrayList.add("android.permission.READ_MEDIA_IMAGES");
          }
          if (p1) {
             uArrayList.add("android.permission.READ_MEDIA_VIDEO");
          }
          stringArray = new String[i];
          return uArrayList.toArray(stringArray);
       }else {
          stringArray = new String[]{"android.permission.WRITE_EXTERNAL_STORAGE","android.permission.READ_EXTERNAL_STORAGE"};
          return stringArray;
       }
    }
}
