package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$take$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.ar4;
import java.lang.Object;
import tb.ozm;
import tb.xhv;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import java.lang.Boolean;
import kotlinx.coroutines.channels.i;
import java.lang.StringBuilder;
import java.lang.IllegalArgumentException;

public final class ChannelsKt__DeprecatedKt$take$1 extends SuspendLambda implements u1a	// class@0004fa from classes11.dex
{
    public final int $n;
    public final ReceiveChannel $this_take;
    public int I$0;
    private Object L$0;
    public Object L$1;
    public int label;

    public void ChannelsKt__DeprecatedKt$take$1(int p0,ReceiveChannel p1,ar4 p2){
       this.$n = p0;
       this.$this_take = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$take$1 otake$1 = new ChannelsKt__DeprecatedKt$take$1(this.$n, this.$this_take, p1);
       otake$1.L$0 = p0;
       return otake$1;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(ozm p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ChannelsKt__DeprecatedKt$take$1 tlabel;
       ChannelsKt__DeprecatedKt$take$1 tL$1;
       ChannelsKt__DeprecatedKt$take$1 tL$0;
       int i;
       Object obj1;
       Object obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel != 1) {
             if (tlabel == 2) {
                tlabel = this.I$0;
                tL$1 = this.L$1;
                tL$0 = this.L$0;
                b.b(p0);
             label_001b :
                p0 = tL$0;
                if (!(i = tlabel - 1)) {
                   return xhv.INSTANCE;
                }
             }else {
                throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
             }
          }else {
             i = this.I$0;
             tL$1 = this.L$1;
             tL$0 = this.L$0;
             b.b(p0);
          label_005b :
             if (p0.booleanValue()) {
                this.L$0 = tL$0;
                this.L$1 = tL$1;
                this.I$0 = i;
                this.label = 2;
                if (tL$0.d(tL$1.next(), this) == obj) {
                   return obj;
                }else {
                   goto label_001b ;
                }
             }else {
                return xhv.INSTANCE;
             }
          }
       }else {
          b.b(p0);
          p0 = this.L$0;
          if ((i = this.$n) == null) {
             return xhv.INSTANCE;
          }else if(i >= null){
             tL$1 = this.$this_take.iterator();
          }else {
             throw new IllegalArgumentException("Requested element count "+i+" is less than zero.".toString());
          }
       }
       this.L$0 = p0;
       this.L$1 = tL$1;
       this.I$0 = i;
       this.label = 1;
       if ((obj1 = tL$1.a(this)) == obj) {
          return obj;
       }else {
          tL$0 = p0;
          p0 = obj1;
          goto label_005b ;
       }
    }
}
