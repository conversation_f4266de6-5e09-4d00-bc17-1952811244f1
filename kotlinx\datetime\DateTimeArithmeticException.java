package kotlinx.datetime.DateTimeArithmeticException;
import java.lang.RuntimeException;
import java.lang.String;
import java.lang.Object;
import tb.ckf;
import java.lang.Throwable;

public final class DateTimeArithmeticException extends RuntimeException	// class@0006d1 from classes11.dex
{

    public void DateTimeArithmeticException(){
       super();
    }
    public void DateTimeArithmeticException(String p0){
       ckf.g(p0, "message");
       super(p0);
    }
    public void DateTimeArithmeticException(String p0,Throwable p1){
       ckf.g(p0, "message");
       ckf.g(p1, "cause");
       super(p0, p1);
    }
    public void DateTimeArithmeticException(Throwable p0){
       ckf.g(p0, "cause");
       super(p0);
    }
}
