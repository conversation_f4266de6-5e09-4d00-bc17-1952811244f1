package me.ele.bridge.PizzaApi;
import android.app.Application;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ariver.engine.api.bridge.extension.BridgeCallback;

public interface abstract PizzaApi	// class@000759 from classes11.dex
{

    void sendPizza(Application p0,String p1,String p2,String p3,JSONObject p4,JSONObject p5,JSONObject p6,int p7,JSONObject p8,JSONObject p9,String p10,BridgeCallback p11);
}
