package kotlinx.serialization.internal.ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.av30;

public final class ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1 extends Lambda implements d1a	// class@000738 from classes11.dex
{

    public void ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1(){
       super(0);
    }
    public final Object invoke(){
       return new av30();
    }
}
