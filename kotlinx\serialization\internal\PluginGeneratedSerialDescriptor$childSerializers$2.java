package kotlinx.serialization.internal.PluginGeneratedSerialDescriptor$childSerializers$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor;
import java.lang.Object;
import tb.x530;
import tb.eu20;
import tb.yy30;

public final class PluginGeneratedSerialDescriptor$childSerializers$2 extends Lambda implements d1a	// class@000749 from classes11.dex
{
    public final PluginGeneratedSerialDescriptor this$0;

    public void PluginGeneratedSerialDescriptor$childSerializers$2(PluginGeneratedSerialDescriptor p0){
       this.this$0 = p0;
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final x530[] invoke(){
       eu20 uoeu20;
       x530[] ox530Array = ((uoeu20 = PluginGeneratedSerialDescriptor.i(this.this$0)) != null)? uoeu20.b(): yy30.EMPTY_SERIALIZER_ARRAY;
       return ox530Array;
    }
}
