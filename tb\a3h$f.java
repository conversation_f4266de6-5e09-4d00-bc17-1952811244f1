package tb.a3h$f;
import java.lang.Runnable;
import tb.a3h;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.a3h$r0;

public class a3h$f implements Runnable	// class@001e6f from classes10.dex
{
    public final String a;
    public final String b;
    public final a3h c;
    public static IpChange $ipChange;

    public void a3h$f(a3h p0,String p1,String p2){
       this.c = p0;
       this.a = p1;
       this.b = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$f.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.z(this.c) != null){
          a3h.z(this.c).d(this.a, this.b);
       }
       return;
    }
}
