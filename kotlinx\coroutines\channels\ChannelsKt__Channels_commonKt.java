package kotlinx.coroutines.channels.ChannelsKt__Channels_commonKt;
import kotlinx.coroutines.channels.ReceiveChannel;
import java.lang.Throwable;
import java.util.concurrent.CancellationException;
import java.lang.String;
import tb.qm8;
import tb.g1a;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.channels.ChannelsKt__Channels_commonKt$consumeEach$1;
import tb.dkf;
import kotlinx.coroutines.channels.ChannelIterator;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.Boolean;
import tb.xhv;
import tb.bj3;
import kotlinx.coroutines.channels.ChannelsKt__Channels_commonKt$toList$1;
import java.util.List;
import tb.xz3;

public final class ChannelsKt__Channels_commonKt	// class@0004d9 from classes11.dex
{

    public static final void a(ReceiveChannel p0,Throwable p1){
       CancellationException uCancellatio = null;
       if (p1 != null) {
          if (p1 instanceof CancellationException) {
             uCancellatio = p1;
          }
          if (!uCancellatio) {
             uCancellatio = qm8.a("Channel was consumed, consumer had failed", p1);
          }
       }
       p0.a(uCancellatio);
       return;
    }
    public static final Object b(ReceiveChannel p0,g1a p1,ar4 p2){
       ChannelsKt__Channels_commonKt$consumeEach$1 uoconsumeEac;
       int i1;
       ChannelsKt__Channels_commonKt$consumeEach$1 label1;
       ChannelsKt__Channels_commonKt$consumeEach$1 l$2;
       ChannelsKt__Channels_commonKt$consumeEach$1 l$1;
       Object obj1;
       if (p2 instanceof ChannelsKt__Channels_commonKt$consumeEach$1) {
          uoconsumeEac = p2;
          ChannelsKt__Channels_commonKt$consumeEach$1 label = uoconsumeEac.label;
          int i = Integer.MIN_VALUE;
          if (i1 = label & i) {
             int i2 = label - i;
             uoconsumeEac.label = i2;
          label_0018 :
             ChannelsKt__Channels_commonKt$consumeEach$1 result = uoconsumeEac.result;
             Object obj = dkf.d();
             if ((label1 = uoconsumeEac.label) != null) {
                if (label1 == 1) {
                   l$2 = uoconsumeEac.L$2;
                   l$1 = uoconsumeEac.L$1;
                   label1 = uoconsumeEac.L$0;
                   b.b(result);
                label_005c :
                   if (result.booleanValue()) {
                      label1.invoke(l$2.next());
                      result = label1;
                   }else {
                      bj3.b(l$1, null);
                      return xhv.INSTANCE;
                   }
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                l$1 = p0;
                l$2 = p0.iterator();
                result = p1;
             }
             uoconsumeEac.L$0 = result;
             uoconsumeEac.L$1 = l$1;
             uoconsumeEac.L$2 = l$2;
             uoconsumeEac.label = 1;
             if ((obj1 = l$2.a(uoconsumeEac)) == obj) {
                return obj;
             }else {
                label1 = result;
                result = obj1;
                goto label_005c ;
             }
          }
       }
       uoconsumeEac = new ChannelsKt__Channels_commonKt$consumeEach$1(p2);
       goto label_0018 ;
    }
    public static final Object d(ReceiveChannel p0,ar4 p1){
       ChannelsKt__Channels_commonKt$toList$1 otoList$1;
       int i1;
       ChannelsKt__Channels_commonKt$toList$1 label1;
       ChannelsKt__Channels_commonKt$toList$1 l$3;
       ChannelsKt__Channels_commonKt$toList$1 l$1;
       ChannelsKt__Channels_commonKt$toList$1 l$0;
       Object obj1;
       if (p1 instanceof ChannelsKt__Channels_commonKt$toList$1) {
          otoList$1 = p1;
          ChannelsKt__Channels_commonKt$toList$1 label = otoList$1.label;
          int i = Integer.MIN_VALUE;
          if (i1 = label & i) {
             int i2 = label - i;
             otoList$1.label = i2;
          label_0018 :
             ChannelsKt__Channels_commonKt$toList$1 result = otoList$1.result;
             Object obj = dkf.d();
             if ((label1 = otoList$1.label) != null) {
                if (label1 == 1) {
                   l$3 = otoList$1.L$3;
                   label1 = otoList$1.L$2;
                   l$1 = otoList$1.L$1;
                   l$0 = otoList$1.L$0;
                   b.b(result);
                label_0067 :
                   if (result.booleanValue()) {
                      l$1.add(l$3.next());
                      result = label1;
                   }else {
                      bj3.b(label1, null);
                      return xz3.a(l$0);
                   }
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
                l$1 = xz3.c();
                l$0 = l$1;
                result = p0;
                l$3 = p0.iterator();
             }
             otoList$1.L$0 = l$0;
             otoList$1.L$1 = l$1;
             otoList$1.L$2 = result;
             otoList$1.L$3 = l$3;
             otoList$1.label = 1;
             if ((obj1 = l$3.a(otoList$1)) == obj) {
                return obj;
             }else {
                label1 = result;
                result = obj1;
                goto label_0067 ;
             }
          }
       }
       otoList$1 = new ChannelsKt__Channels_commonKt$toList$1(p1);
       goto label_0018 ;
    }
}
