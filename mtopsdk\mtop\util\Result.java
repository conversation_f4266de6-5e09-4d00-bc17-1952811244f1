package mtopsdk.mtop.util.Result;
import java.io.Serializable;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;
import java.lang.Boolean;
import java.lang.Integer;

public class Result implements Serializable	// class@00082b from classes11.dex
{
    public String errCode;
    public String errInfo;
    public String errType;
    public Object model;
    public int statusCode;
    public boolean success;
    public static IpChange $ipChange;
    private static final long serialVersionUID;

    static {
       t2o.a(0x25300139);
    }
    public void Result(){
       super();
       this.success = true;
    }
    public void Result(Object p0){
       super();
       this.success = true;
       this.model = p0;
    }
    public void Result(boolean p0,String p1,String p2){
       super(p0, null, p1, p2);
    }
    public void Result(boolean p0,String p1,String p2,String p3){
       super();
       this.success = p0;
       this.errType = p1;
       this.errCode = p2;
       this.errInfo = p3;
    }
    public String getErrCode(){
       IpChange $ipChange = Result.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.errCode;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("49f20fbd", objArray);
    }
    public String getErrInfo(){
       IpChange $ipChange = Result.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.errInfo;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("3caecb9c", objArray);
    }
    public String getErrType(){
       IpChange $ipChange = Result.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.errType;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6389f090", objArray);
    }
    public Object getModel(){
       IpChange $ipChange = Result.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.model;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("b4c825f8", objArray);
    }
    public int getStatusCode(){
       IpChange $ipChange = Result.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.statusCode;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("eae362ef", objArray).intValue();
    }
    public boolean isSuccess(){
       IpChange $ipChange = Result.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.success;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6049a784", objArray).booleanValue();
    }
    public void setErrCode(String p0){
       IpChange $ipChange = Result.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fb5370c1", objArray);
          return;
       }else {
          this.errCode = p0;
          return;
       }
    }
    public void setErrInfo(String p0){
       IpChange $ipChange = Result.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("602e30c2", objArray);
          return;
       }else {
          this.errInfo = p0;
          return;
       }
    }
    public void setErrType(String p0){
       IpChange $ipChange = Result.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("14b7aa4e", objArray);
          return;
       }else {
          this.errType = p0;
          return;
       }
    }
    public void setModel(Object p0){
       IpChange $ipChange = Result.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a92cc54a", objArray);
          return;
       }else {
          this.model = p0;
          return;
       }
    }
    public void setStatusCode(int p0){
       IpChange $ipChange = Result.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("a86008fb", objArray);
          return;
       }else {
          this.statusCode = p0;
          return;
       }
    }
    public void setSuccess(boolean p0){
       IpChange $ipChange = Result.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("6d88968c", objArray);
          return;
       }else {
          this.success = p0;
          return;
       }
    }
}
