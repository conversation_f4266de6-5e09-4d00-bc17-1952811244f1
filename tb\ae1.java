package tb.ae1;
import tb.k04;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.RuntimeException;
import tb.zd1;
import android.os.Handler;

public abstract class ae1 extends k04	// class@001b26 from classes8.dex
{
    public static IpChange $ipChange;

    public void ae1(){
       super();
    }
    public static Object ipc$super(ae1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/metrickit/collector/AsyncCollector");
    }
    public final Object a(int p0,Map p1){
       IpChange $ipChange = ae1.$ipChange;
       if (!$ipChange instanceof IpChange) {
          throw new RuntimeException("AsyncCollector: Incorrect usage.");
       }
       Object[] objArray = new Object[]{this,new Integer(p0),p1};
       return $ipChange.ipc$dispatch("3c7192ea", objArray);
    }
    public void c(){
       IpChange $ipChange = ae1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("51db1d5f", objArray);
       }
       return;
    }
    public abstract void e(int p0,Map p1,zd1 p2);
    public abstract Handler f();
}
