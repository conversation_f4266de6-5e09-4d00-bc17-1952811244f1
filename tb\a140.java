package tb.a140;
import tb.mz20;
import tb.t2o;
import tb.a140$b;
import tb.a07;
import java.lang.Object;
import java.util.LinkedHashSet;
import java.util.ArrayList;
import tb.a140$a;
import android.os.Looper;
import java.util.List;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.Set;
import tb.qpu;
import tb.ckf;
import java.lang.Integer;
import androidx.recyclerview.widget.RecyclerView;
import tb.aef;
import java.util.Iterator;
import java.lang.Iterable;
import java.lang.Number;
import java.util.Collection;
import tb.yz3;
import android.os.Handler;

public class a140 implements mz20	// class@001838 from classes7.dex
{
    public qpu a;
    public RecyclerView b;
    public final Set c;
    public final List d;
    public final int e;
    public final a140$a f;
    public static IpChange $ipChange;
    public static final a140$b Companion;
    public static final String TAG;

    static {
       t2o.a(0x39400168);
       t2o.a(0x39400167);
       a140.Companion = new a140$b(null);
    }
    public void a140(){
       super();
       this.c = new LinkedHashSet();
       this.d = new ArrayList();
       this.e = 6;
       this.f = new a140$a(this, Looper.getMainLooper());
    }
    public static final List p(a140 p0){
       IpChange $ipChange = a140.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.d;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("c1b9dab9", objArray);
    }
    public static final Set w(a140 p0){
       IpChange $ipChange = a140.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.c;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("ec6b82dd", objArray);
    }
    public final qpu A(){
       a140 ta;
       IpChange $ipChange = a140.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("3d61b71e", objArray);
       }else if((ta = this.a) != null){
          return ta;
       }else {
          ckf.y("turboEngineContext");
          throw null;
       }
    }
    public void B(int p0){
       IpChange $ipChange = a140.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("43ac181f", objArray);
       }
       return;
    }
    public void a(int p0,RecyclerView p1){
       int i = 0;
       IpChange $ipChange = a140.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("575676c5", objArray);
          return;
       }else {
          ckf.g(p1, "recyclerView");
          this.b = p1;
          this.c.add(Integer.valueOf(p0));
          ArrayList uArrayList = new ArrayList();
          Iterator iterator = new aef((p0 + 1), (p0 + this.e)).iterator();
          int i1 = 0;
          while (true) {
             if (iterator.hasNext()) {
                Object obj = iterator.next();
                int i2 = i1 + 1;
                if (i1 >= 0) {
                   if (!this.c.contains(Integer.valueOf(obj.intValue()))) {
                      uArrayList.add(obj);
                   }
                   i1 = i2;
                }else {
                   break ;
                }
             }else {
                Iterator iterator1 = uArrayList.iterator();
                while (iterator1.hasNext()) {
                   int i3 = iterator1.next().intValue();
                   if (this.d.size() >= this.e) {
                      this.d.remove(i);
                   }
                   this.d.add(Integer.valueOf(i3));
                   if (!this.f.hasMessages(1)) {
                      this.f.sendEmptyMessage(1);
                   }else {
                      continue ;
                   }
                }
                return;
             }
          }
          yz3.p();
          throw null;
       }
    }
    public void e(){
       IpChange $ipChange = a140.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("e362cf6d", objArray);
          return;
       }else {
          this.c.clear();
          this.f.removeCallbacksAndMessages(null);
          return;
       }
    }
    public void onCreateService(qpu p0){
       IpChange $ipChange = a140.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f9b2eb95", objArray);
          return;
       }else {
          ckf.g(p0, "context");
          this.a = p0;
          return;
       }
    }
    public void onDestroyService(qpu p0){
       IpChange $ipChange = a140.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("673e3e35", objArray);
          return;
       }else {
          ckf.g(p0, "context");
          this.e();
          return;
       }
    }
    public final RecyclerView x(){
       IpChange $ipChange = a140.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("23bc5268", objArray);
    }
}
