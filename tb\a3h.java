package tb.a3h;
import com.taobao.living.api.TBLiveMediaSDKEngine$k;
import com.taobao.living.api.TBLiveMediaSDKEngine$h;
import com.taobao.living.api.TBLiveMediaSDKEngine$f;
import com.taobao.living.api.TBLiveMediaSDKEngine$c;
import com.taobao.living.api.TBLiveMediaSDKEngine$e;
import com.taobao.living.api.TBLiveMediaSDKEngine$g;
import com.taobao.living.api.TBLiveMediaSDKEngine$b;
import tb.t2o;
import android.app.Activity;
import tb.gir;
import java.lang.Object;
import java.util.LinkedList;
import java.util.HashSet;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import tb.a3h$w0;
import tb.a3h$s0;
import com.taobao.living.api.TBLiveMediaSDKEngine$a;
import tb.a3h$v0;
import tb.a3h$r0;
import com.taobao.living.api.TBLiveMediaSDKEngine;
import com.taobao.living.api.TBConstants$BeautyType;
import java.lang.Boolean;
import java.lang.Float;
import java.lang.Integer;
import android.opengl.EGLSurface;
import java.lang.StringBuilder;
import com.taobao.trtc.utils.TrtcLog;
import java.util.ArrayList;
import tb.a3h$t0;
import com.taobao.living.api.TBLiveMediaSDKEngine$i;
import com.taobao.living.api.TBLiveMediaSDKEngine$j;
import com.taobao.living.api.TBLiveMediaSDKEngine$d;
import tb.a3h$u0;
import tb.xkr;
import android.widget.RelativeLayout;
import tb.gz2;
import java.util.Map;
import android.graphics.Bitmap;
import com.taobao.artc.api.ArtcVideoLayout;
import com.taobao.living.api.TBConstants$VideoDefinition;
import com.taobao.artc.api.ArtcVideoLayout$ArtcVideoRect;
import android.util.Pair;
import tb.a3h$b;
import java.lang.Runnable;
import tb.qur;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Throwable;
import com.taobao.living.utils.TBLSLog;
import com.taobao.artc.utils.ArtcLog;
import android.content.Context;
import tb.a3h$j0;
import com.taobao.living.api.TBConstants$TBMediaSDKNetworkStauts;
import tb.a3h$k0;
import java.lang.Enum;
import tb.e3e;
import com.taobao.trtc.api.TrtcDefines$i;
import tb.a3h$e;
import tb.a3h$d;
import tb.a3h$l0;
import tb.a3h$j;
import tb.a3h$i0;
import tb.a3h$g0;
import tb.a3h$h0;
import tb.a3h$c;
import java.lang.Exception;
import com.taobao.artc.api.ArtcStats;
import tb.a3h$u;
import java.lang.Number;
import tb.a3h$a;
import com.taobao.living.api.TBConstants$TBMediaSDKState;
import tb.a3h$q0;
import tb.a3h$p0;
import tb.a3h$o0;
import tb.a3h$n0;
import com.taobao.living.api.TBLiveMediaSDKEngine$VCLinkMultiEvent;
import tb.a3h$p;
import tb.a3h$q;
import tb.a3h$r;
import tb.a3h$s;
import tb.a3h$t;
import tb.a3h$v;
import tb.a3h$w;
import tb.a3h$x;
import tb.a3h$y;
import tb.a3h$z;
import tb.a3h$a0;
import tb.a3h$b0;
import tb.a3h$c0;
import tb.a3h$d0;
import tb.a3h$e0;
import tb.a3h$f0;
import tb.a3h$m0;
import com.taobao.living.api.TBConstants$VCLinkLiveEvent;
import tb.a3h$f;
import tb.a3h$g;
import tb.a3h$h;
import tb.a3h$i;
import tb.a3h$k;
import tb.a3h$l;
import tb.a3h$m;
import tb.a3h$n;
import tb.a3h$o;

public class a3h implements TBLiveMediaSDKEngine$k, TBLiveMediaSDKEngine$h, TBLiveMediaSDKEngine$f, TBLiveMediaSDKEngine$c, TBLiveMediaSDKEngine$e, TBLiveMediaSDKEngine$g, TBLiveMediaSDKEngine$b	// class@001e95 from classes10.dex
{
    public final Activity a;
    public final gir b;
    public RelativeLayout c;
    public a3h$w0 d;
    public a3h$v0 e;
    public a3h$u0 f;
    public a3h$r0 g;
    public a3h$s0 h;
    public a3h$t0 i;
    public TBLiveMediaSDKEngine$c j;
    public TBLiveMediaSDKEngine$e k;
    public TBLiveMediaSDKEngine l;
    public final LinkedList m;
    public TBLiveMediaSDKEngine$a n;
    public boolean o;
    public String p;
    public String q;
    public boolean r;
    public static IpChange $ipChange;
    public static final int CAMERA_BACK;
    public static final int CAMERA_CLOSE;
    public static final int CAMERA_FRONT;
    public static final int LIVE_STATUS_END;
    public static final int LIVE_STATUS_LIVE;
    public static final int LIVE_STATUS_PAUSE;
    public static final int LIVE_STATUS_UNSTART;
    public static final int MAX_EVENT_NUM;
    public static int s;

    static {
       t2o.a(0x32b0014c);
       t2o.a(0x32b00082);
       t2o.a(0x32b0007e);
       t2o.a(0x32b0007c);
       t2o.a(0x32b00079);
       t2o.a(0x32b0007b);
       t2o.a(0x32b0007d);
       t2o.a(0x32b00078);
       a3h.s = 1;
    }
    public void a3h(Activity p0,gir p1,boolean p2){
       super();
       this.m = new LinkedList();
       HashSet hashSet = new HashSet();
       this.a = p0;
       this.b = p1;
       this.b0(p2);
       this.i0("init");
    }
    public static void A(a3h p0,String p1){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("2adf6997", objArray);
          return;
       }else {
          p0.i0(p1);
          return;
       }
    }
    public static a3h$w0 B(a3h p0){
       IpChange $ipChange = a3h.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.d;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("a744328a", objArray);
    }
    public static a3h$s0 C(a3h p0){
       IpChange $ipChange = a3h.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.h;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("2c4366db", objArray);
    }
    public static TBLiveMediaSDKEngine$a D(a3h p0){
       IpChange $ipChange = a3h.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.n;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("d2a67eef", objArray);
    }
    public static a3h$v0 y(a3h p0){
       IpChange $ipChange = a3h.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.e;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("4a01321a", objArray);
    }
    public static a3h$r0 z(a3h p0){
       IpChange $ipChange = a3h.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.g;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("fa1a50ca", objArray);
    }
    public void A0(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("9d436264", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.S(p0);
       }
       return;
    }
    public void B0(TBConstants$BeautyType p0,boolean p1,float p2){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1),new Float(p2)};
          $ipChange.ipc$dispatch("1c99a482", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.U(p0, p1, p2);
       }
       return;
    }
    public void C0(String p0,boolean p1,float p2){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1),new Float(p2)};
          $ipChange.ipc$dispatch("93a0be37", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.V(p0, p1, p2);
       }
       return;
    }
    public void D0(boolean p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("90804def", objArray);
       }
       return;
    }
    public void E(String p0,int p1,String p2,int p3,int p4){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,new Integer(p3),new Integer(p4)};
          $ipChange.ipc$dispatch("d0dbf731", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.i(p0, p1, p2, p3, p4);
       }
       return;
    }
    public void E0(a3h$r0 p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("45da9265", objArray);
          return;
       }else {
          this.g = p0;
          return;
       }
    }
    public void F(String p0,int p1,String p2,int p3,int p4,String p5){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,new Integer(p3),new Integer(p4),p5};
          $ipChange.ipc$dispatch("b4ce16bb", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.j(p0, p1, p2, p3, p4, p5);
       }
       return;
    }
    public void F0(a3h$s0 p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("925a9312", objArray);
          return;
       }else {
          this.h = p0;
          return;
       }
    }
    public void G(EGLSurface p0,int p1,int p2){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),new Integer(p2)};
          $ipChange.ipc$dispatch("cbaf29cf", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "attachEGLSurface, eglSurface: "+p0+" ,width: "+p1+" ,height: "+p2);
          if ((tl = this.l) != null) {
             tl.k(p0, p1, p2);
          }
          return;
       }
    }
    public void G0(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("605e0763", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.Y(p0);
       }
       return;
    }
    public void H(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("758834fb", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "attachSurfaceHolder");
          if ((tl = this.l) != null) {
             tl.l();
          }
          return;
       }
    }
    public void H0(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fe4b297a", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.a0(p0);
       }
       return;
    }
    public void I(ArrayList p0,String p1,int p2,int p3,int p4){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),new Integer(p3),new Integer(p4)};
          $ipChange.ipc$dispatch("db11a041", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.m(p0, p1, p2, p3, p4);
       }
       return;
    }
    public void I0(String p0,boolean p1,float p2,float p3){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1),new Float(p2),new Float(p3)};
          $ipChange.ipc$dispatch("8a0be7bf", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.b0(p0, p1, p2, p3);
       }
       return;
    }
    public void J(ArrayList p0,String p1,int p2,int p3,int p4,String p5){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),new Integer(p3),new Integer(p4),p5};
          $ipChange.ipc$dispatch("d0f19bcb", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.n(p0, p1, p2, p3, p4, p5);
       }
       return;
    }
    public void J0(boolean p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("9b5bb484", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.c0(p0);
       }
       return;
    }
    public void K(ArrayList p0,String p1,int p2,int p3,int p4,String p5,String p6,String p7){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[9];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[2] = p1;
          objArray[3] = new Integer(p2);
          objArray[4] = new Integer(p3);
          objArray[5] = new Integer(p4);
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = p7;
          $ipChange.ipc$dispatch("8da465bf", objArray);
       }
       return;
    }
    public void K0(a3h$t0 p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e2c43874", objArray);
          return;
       }else {
          this.i = p0;
          return;
       }
    }
    public void L(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("439b4c99", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.o(p0, null, null);
       }
       return;
    }
    public void L0(TBLiveMediaSDKEngine$c p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("676501e", objArray);
          return;
       }else {
          this.j = p0;
          return;
       }
    }
    public void M(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("4f0676f2", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "deInitPush");
          if ((tl = this.l) != null) {
             tl.s();
          }
          return;
       }
    }
    public void M0(TBLiveMediaSDKEngine$i p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("cf48551e", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.e0(p0);
       }
       return;
    }
    public void N(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("89c49781", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.r();
          this.l = null;
       }
       this.e = null;
       this.g = null;
       this.h = null;
       this.m.clear();
       return;
    }
    public void N0(TBLiveMediaSDKEngine$j p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fa30eebe", objArray);
       }
       return;
    }
    public void O(TBLiveMediaSDKEngine$d p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("849b3394", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "detachEGLSurface, detachListener: "+p0);
          if ((tl = this.l) != null) {
             tl.t(p0);
          }
          return;
       }
    }
    public void O0(TBLiveMediaSDKEngine$e p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b71db4d6", objArray);
          return;
       }else {
          this.k = p0;
          return;
       }
    }
    public void P(boolean p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("20499eae", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.T(p0);
       }
       return;
    }
    public void P0(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a956471c", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.g0();
       }
       return;
    }
    public void Q(boolean p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("e3502f26", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.X(p0);
       }
       return;
    }
    public void Q0(a3h$u0 p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("83bf6f8", objArray);
          return;
       }else {
          this.f = p0;
          return;
       }
    }
    public void R(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b82cd63a", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.u(p0, null, null);
       }
       return;
    }
    public void R0(a3h$v0 p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("28978e78", objArray);
          return;
       }else {
          this.e = p0;
          return;
       }
    }
    public xkr S(String p0){
       a3h tl;
       xkr oxkr;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("b6af1512", objArray);
       }else if((tl = this.l) != null){
          oxkr = tl.v(p0);
       }else {
          oxkr = null;
       }
       return oxkr;
    }
    public void S0(RelativeLayout p0,String p1){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("98131ae0", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.h0(p0, p1);
       }
       return;
    }
    public boolean T(){
       a3h tl;
       int i = 0;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("79d01f72", objArray).booleanValue();
       }else {
          TrtcLog.j("LivePushInstance", "getArtcSoIsReady\(\)");
          if ((tl = this.l) != null) {
             i = tl.w();
          }
          return i;
       }
    }
    public void T0(ArrayList p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8d636039", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.i0(p0);
       }
       return;
    }
    public gz2 U(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("83743d35", objArray);
       }else if((tl = this.l) != null){
          return tl.x();
       }else {
          return null;
       }
    }
    public void U0(Map p0,boolean p1){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("f5580955", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.j0(p0, p1);
       }
       return;
    }
    public Bitmap V(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("ca05fa96", objArray);
       }else if((tl = this.l) != null){
          try{
             return tl.y();
          }catch(java.lang.Exception e0){
             return v1;
          }
       }else {
       }
    }
    public void V0(a3h$w0 p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("39e15a18", objArray);
          return;
       }else {
          this.d = p0;
          return;
       }
    }
    public String W(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("cc46a04f", objArray);
       }else {
          StringBuilder str = "";
          for (; i < this.m.size(); i = i + i1) {
             if (i) {
                str = str.append("_");
             }
             str = str.append(this.m.get(i));
          }
          return str;
       }
    }
    public void W0(ArtcVideoLayout p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1a606e43", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.k0(p0);
       }
       return;
    }
    public String X(){
       IpChange $ipChange = a3h.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.q;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8feb009a", objArray);
    }
    public void X0(String p0,String p1,boolean p2,int p3){
       ArtcVideoLayout uArtcVideoLa;
       ArtcVideoLayout uArtcVideoLa1;
       ArtcVideoLayout$ArtcVideoRect b1;
       ArtcVideoLayout uArtcVideoLa2;
       ArtcVideoLayout$ArtcVideoRect b2;
       object oobject = this;
       int b = p2;
       int i = p3;
       int i1 = 1;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,p0,p1,new Boolean(b),new Integer(i)};
          $ipChange.ipc$dispatch("58cf8c0d", objArray);
          return;
       }else if(oobject.l != null){
          if (oobject.b.d0() == TBConstants$VideoDefinition.SuperHighDefinition) {
             uArtcVideoLa = new ArtcVideoLayout();
             ArrayList uArrayList = new ArrayList();
             if (b) {
                uArtcVideoLa1 = uArtcVideoLa;
                b1 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa1, 0, 288, 0, 540, 768, p0);
                uArrayList.add(b);
                b1 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa1, 540, 288, 1, 540, 768, p1);
                uArrayList.add(b);
                uArtcVideoLa.sub_width = 540;
                uArtcVideoLa.sub_height = 960;
                uArtcVideoLa.desc = uArrayList;
                uArtcVideoLa.bg_color = 0;
                uArtcVideoLa.bg_width = 1080;
                uArtcVideoLa.bg_height = 1920;
                oobject.l.k0(uArtcVideoLa);
             }else {
                b = 1080;
                if (!i) {
                   uArtcVideoLa1 = uArtcVideoLa;
                   b1 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa1, 0, 0, 0, 1080, 1920, p0);
                   uArrayList.add(i);
                   b1 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa1, 756, 1305, 1, 270, 480, p1);
                   uArrayList.add(i);
                }else if(i == i1){
                   uArtcVideoLa1 = uArtcVideoLa;
                   i1 = 960;
                   b1 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa1, 756, 1305, 1, 270, 480, p0);
                   uArrayList.add(i);
                   b1 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa1, 0, 0, 0, 1080, 1920, p1);
                   uArrayList.add(i);
                }else {
                   i1 = 960;
                }
                uArtcVideoLa.sub_width = 540;
                uArtcVideoLa.sub_height = 960;
                uArtcVideoLa.desc = uArrayList;
                uArtcVideoLa.bg_color = 0;
                uArtcVideoLa.bg_width = b;
                uArtcVideoLa.bg_height = 1920;
                oobject.l.k0(uArtcVideoLa);
             }
          }else {
             uArtcVideoLa = new ArtcVideoLayout();
             ArrayList uArrayList1 = new ArrayList();
             if (b) {
                uArtcVideoLa2 = uArtcVideoLa;
                b2 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa2, 0, 192, 0, 360, 512, p0);
                uArrayList1.add(b);
                b2 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa2, 360, 192, 1, 360, 512, p1);
                uArrayList1.add(b);
                uArtcVideoLa.sub_width = 360;
                uArtcVideoLa.sub_height = 640;
                uArtcVideoLa.desc = uArrayList1;
                uArtcVideoLa.bg_color = 0;
                uArtcVideoLa.bg_width = 720;
                uArtcVideoLa.bg_height = 1280;
                oobject.l.k0(uArtcVideoLa);
             }else {
                b = 720;
                if (!i) {
                   uArtcVideoLa2 = uArtcVideoLa;
                   b2 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa2, 0, 0, 0, 720, 1280, p0);
                   uArrayList1.add(i);
                   b2 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa2, 504, 870, 1, 180, 320, p1);
                   uArrayList1.add(i);
                }else if(i == i1){
                   uArtcVideoLa2 = uArtcVideoLa;
                   i1 = 640;
                   b2 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa2, 504, 870, 1, 180, 320, p0);
                   uArrayList1.add(i);
                   b2 = new ArtcVideoLayout$ArtcVideoRect(uArtcVideoLa2, 0, 0, 0, 720, 1280, p1);
                   uArrayList1.add(i);
                }else {
                   i1 = 640;
                }
                uArtcVideoLa.sub_width = 360;
                uArtcVideoLa.sub_height = 640;
                uArtcVideoLa.desc = uArrayList1;
                uArtcVideoLa.bg_color = 0;
                uArtcVideoLa.bg_width = b;
                uArtcVideoLa.bg_height = 1280;
                oobject.l.k0(uArtcVideoLa);
             }
          }
       }
       return;
    }
    public Pair Y(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("3ab99a56", objArray);
       }else if((tl = this.l) != null){
          return tl.z();
       }else {
          return null;
       }
    }
    public void Y0(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("86c839c1", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.m0();
       }
       return;
    }
    public RelativeLayout Z(){
       IpChange $ipChange = a3h.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("bb5d7b2", objArray);
    }
    public void Z0(String p0,int p1,String p2,int p3,int p4){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,new Integer(p3),new Integer(p4)};
          $ipChange.ipc$dispatch("7d9b694c", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.n0(p0, p1, null, p2, p3, p4);
       }
       return;
    }
    public void a(){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("1580a46b", objArray);
          return;
       }else if((ta = this.a) != null && this.e != null){
          ta.runOnUiThread(new a3h$b(this));
       }
       return;
    }
    public void a0(qur p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("70b1c530", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "initPushEngine");
          if ((tl = this.l) != null) {
             tl.A(p0);
          }
          return;
       }
    }
    public void a1(String p0,String p1){
       int i = 1;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("8063a489", objArray);
          return;
       }else if(this.o == null && (!TextUtils.isEmpty(p0) && !TextUtils.isEmpty(p1))){
          this.p = p0;
          this.q = p1;
          if (this.l != null) {
             try{
                this.i0("starting");
                this.l.o0(this.p, this.q);
                this.o = i;
             }catch(com.taobao.living.api.TBMediaSDKException e6){
                Object[] objArray1 = new Object[0];
                TBLSLog.b("LivePushInstance", "error", e6, objArray1);
             }
          }
       }
       return;
    }
    public void b(int p0,String p1){
       a3h te;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("d3617597", objArray);
          return;
       }else if((te = this.e) != null){
          te.b(p0, p1);
       }
       return;
    }
    public final void b0(boolean p0){
       a3h tl;
       int i = 1;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("31f858cd", objArray);
          return;
       }else {
          ArtcLog.setPrintLog(i);
          ArtcLog.setUseTlog(i);
          this.l = (p0)? TBLiveMediaSDKEngine.q(this.a, this.b, this, this, this, this, this, this, null): TBLiveMediaSDKEngine.p(this.a, this.b, this, this, this, this, this);
          if ((tl = this.l) != null) {
             tl.d0(new a3h$j0(this));
          }
          return;
       }
    }
    public void b1(RelativeLayout p0){
       a3h tl;
       int i = 1;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("dee6ad73", objArray);
          return;
       }else if((tl = this.l) != null && this.r == null){
          try{
             this.c = p0;
             tl.p0(p0);
             this.i0("preview");
             this.r = i;
          }catch(com.taobao.living.api.TBMediaSDKException e6){
             Object[] objArray1 = new Object[0];
             TBLSLog.b("LivePushInstance", "error", e6, objArray1);
          }
       }
       return;
    }
    public void c(TBConstants$TBMediaSDKNetworkStauts p0){
       int i2;
       a3h ti;
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          $ipChange.ipc$dispatch("1361d806", objArray);
          return;
       }else if((i2 = a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKNetworkStauts[p0.ordinal()]) != i){
          if (i2 != i1) {
             if (i2 == 3) {
                a3h.s = i1;
             }
          }else {
             a3h.s = i;
          }
       }else {
          a3h.s = 0;
       }
       if ((ti = this.i) != null) {
          ti.c(a3h.s);
       }
       return;
    }
    public boolean c0(){
       a3h tl;
       int i = 0;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("24080cad", objArray).booleanValue();
       }else if((tl = this.l) != null){
          i = tl.B();
       }
       return i;
    }
    public boolean c1(String p0,e3e p1){
       Object[] objArray;
       int i = 0;
       String str = "LivePushInstance";
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("6d63e60e", objArray).booleanValue();
       }else if(TextUtils.isEmpty(p0)){
          return i;
       }else if(this.l != null){
          try{
             objArray = new Object[i];
             TBLSLog.c(str, "PurityStream startPurityStreamProcess", objArray);
             return this.l.a(p0, p1);
          }catch(com.taobao.living.api.TBMediaSDKException e5){
             Object[] objArray1 = new Object[i];
             TBLSLog.b(str, "PurityStream startPurityStreamProcess error", e5, objArray1);
          }
          return i;
       }else {
       }
    }
    public void d(String p0,String p1,String p2,String p3){
       a3h te;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("b5e4711c", objArray);
          return;
       }else if((te = this.e) != null){
          te.d(p0, p1, p2, p3);
       }
       return;
    }
    public boolean d0(){
       a3h tl;
       int i = 0;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("c08a01e9", objArray).booleanValue();
       }else if((tl = this.l) != null){
          i = tl.C();
       }
       return i;
    }
    public void d1(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("adc5d561", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.r0();
       }
       return;
    }
    public void e(ArrayList p0){
       a3h te;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c2744305", objArray);
          return;
       }else if((te = this.e) != null){
          te.e(p0);
       }
       return;
    }
    public void e0(String p0,String p1,int p2,int p3){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),new Integer(p3)};
          $ipChange.ipc$dispatch("9fadb0a8", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.D(p0, p1, p2, p3);
       }
       return;
    }
    public void e1(){
       a3h tl;
       int i = 0;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("e7f2995", objArray);
          return;
       }else if((tl = this.l) != null && this.o != null){
          try{
             tl.s0();
             this.i0("ended");
             this.o = i;
          }catch(com.taobao.living.api.TBMediaSDKException e0){
             e0.printStackTrace();
          }
       }
       return;
    }
    public void eglSurfaceChanged(EGLSurface p0,int p1,int p2,int p3){
       a3h tk;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),new Integer(p2),new Integer(p3)};
          $ipChange.ipc$dispatch("f169435d", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "eglSurfaceChanged, eglSurface: "+p0+" ,formate: "+p1+" ,width: "+p2+" ,height: "+p3);
          if ((tk = this.k) != null) {
             tk.eglSurfaceChanged(p0, p1, p2, p3);
          }else {
             this.G(p0, p2, p3);
          }
          return;
       }
    }
    public void eglSurfaceCreated(EGLSurface p0){
       a3h tk;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f1da02d2", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "eglSurfaceCreated, eglSurface: "+p0);
          if ((tk = this.k) != null) {
             tk.eglSurfaceCreated(p0);
          }
          return;
       }
    }
    public void eglSurfaceDestroyed(EGLSurface p0){
       a3h tk;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c9cb08c1", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "eglSurfaceDestroyed, eglSurface: "+p0);
          if ((tk = this.k) != null) {
             tk.eglSurfaceDestroyed(p0);
          }else {
             this.O(null);
          }
          return;
       }
    }
    public void f(TrtcDefines$i p0,float p1){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Float(p1)};
          $ipChange.ipc$dispatch("a25bf7c2", objArray);
          return;
       }else if((ta = this.a) != null && this.e != null){
          ta.runOnUiThread(new a3h$e(this, p0, p1));
       }
       return;
    }
    public void f0(String p0,String p1,int p2,int p3,String p4){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),new Integer(p3),p4};
          $ipChange.ipc$dispatch("8c370072", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.E(p0, p1, p2, p3, p4);
       }
       return;
    }
    public void f1(){
       int i = 0;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("1cdc6cf3", objArray);
          return;
       }else if(this.l != null){
          try{
             Object[] objArray1 = new Object[i];
             TBLSLog.c("LivePushInstance", "PurityStream stopPurityStreamProcess", objArray1);
             this.l.u0();
          }catch(com.taobao.living.api.TBMediaSDKException e0){
             e0.printStackTrace();
          }
       }
       return;
    }
    public void g(String p0){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("99487c09", objArray);
          return;
       }else if((ta = this.a) != null){
          ta.runOnUiThread(new a3h$d(this, p0));
       }
       return;
    }
    public void g0(ArrayList p0,String p1){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("e79c630a", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.F(p0, p1);
       }
       return;
    }
    public void g1(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("46484f80", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.v0();
       }
       return;
    }
    public void h(boolean p0){
       a3h te;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("b5f978f1", objArray);
          return;
       }else if((te = this.e) != null){
          te.h(p0);
       }
       return;
    }
    public void h0(ArrayList p0,String p1,String p2){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("27d69e54", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.G(p0, p1, p2);
       }
       return;
    }
    public void h1(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f7cf4520", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.w0(p0);
       }
       return;
    }
    public void i(String p0,int p1,String p2,String p3,String p4){
       a3h te;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,p3,p4};
          $ipChange.ipc$dispatch("6b3c42ec", objArray);
          return;
       }else if((te = this.e) != null){
          te.i(p0, p1, p2, p3, p4);
       }
       return;
    }
    public final void i0(String p0){
       a3h tf;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6dfa0577", objArray);
          return;
       }else if(this.m.size() > 60){
          this.m.poll();
       }
       this.m.offer(p0);
       if ((tf = this.f) != null) {
          tf.onChange();
       }
       return;
    }
    public void j(){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("e3503265", objArray);
          return;
       }else if((ta = this.a) != null && this.e != null){
          ta.runOnUiThread(new a3h$l0(this));
       }
       return;
    }
    public void j0(boolean p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("dc39eee9", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.H(p0);
       }
       return;
    }
    public void k(String p0,String p1){
       a3h te;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("afd6e9c0", objArray);
          return;
       }else if((te = this.e) != null){
          te.k(p0, p1);
       }
       return;
    }
    public void k0(boolean p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("55792b85", objArray);
       }
       return;
    }
    public void l(){
       a3h te;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a1c9050f", objArray);
          return;
       }else if((te = this.e) != null){
          te.l();
       }
       return;
    }
    public void l0(ArrayList p0,boolean p1,boolean p2){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1),new Boolean(p2)};
          $ipChange.ipc$dispatch("c47bfb8c", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.I(p0, p1, p2);
       }
       return;
    }
    public void m(String p0){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a5f5dfe3", objArray);
          return;
       }else if((ta = this.a) != null && this.e != null){
          ta.runOnUiThread(new a3h$j(this, p0));
       }
       return;
    }
    public void m0(int p0,String p1){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("7a671c9d", objArray);
          return;
       }else {
          this.a.runOnUiThread(new a3h$i0(this, p0, p1));
          return;
       }
    }
    public void n(Map p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("703dec82", objArray);
          return;
       }else {
          this.a.runOnUiThread(new a3h$g0(this, p0));
          return;
       }
    }
    public void n0(){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("bd1a118b", objArray);
          return;
       }else {
          this.a.runOnUiThread(new a3h$h0(this));
          return;
       }
    }
    public void o(String p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("2c60dea0", objArray);
       }
       return;
    }
    public void o0(){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b3dde88", objArray);
          return;
       }else if((tl = this.l) != null && this.r != null){
          this.r = false;
          tl.t0();
          this.e1();
       }
       return;
    }
    public void onBlueToothDeviceDisconnected(){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a8c28207", objArray);
          return;
       }else if((ta = this.a) != null && this.e != null){
          ta.runOnUiThread(new a3h$c(this));
       }
       return;
    }
    public void onConfigure(gz2 p0){
       a3h tj;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("805ebdd5", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "onConfigure:"+p0);
          if ((tj = this.j) != null) {
             tj.onConfigure(p0);
          }else {
             this.H();
             this.P0();
          }
          return;
       }
    }
    public void onError(gz2 p0,int p1,Exception p2){
       a3h tj;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("9bf70816", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "onError:"+p0+" ,Exception: "+p2);
          if ((tj = this.j) != null) {
             tj.onError(p0, p1, p2);
          }
          return;
       }
    }
    public void onOpen(gz2 p0){
       a3h tj;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d97aef93", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "onOpen, cameraClient:"+p0);
          if ((tj = this.j) != null) {
             tj.onOpen(p0);
          }
          return;
       }
    }
    public void onPreviewStart(gz2 p0){
       a3h tj;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("86e0b963", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "onOpen, onPreviewStart:"+p0);
          if ((tj = this.j) != null) {
             tj.onPreviewStart(p0);
          }
          return;
       }
    }
    public void onRtcStats(ArtcStats p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a6bb70af", objArray);
       }
       return;
    }
    public void onStop(gz2 p0){
       a3h tj;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("25622adb", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "onStop:"+p0);
          if ((tj = this.j) != null) {
             tj.onStop(p0);
          }
          return;
       }
    }
    public void onSwitchCamera(){
       a3h tj;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("f84b037f", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "onSwitchCamera");
          if ((tj = this.j) != null) {
             tj.onSwitchCamera();
          }
          return;
       }
    }
    public void p(){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("73582d2a", objArray);
          return;
       }else if((ta = this.a) != null && this.e != null){
          ta.runOnUiThread(new a3h$u(this));
       }
       return;
    }
    public int p0(String p0,xkr p1){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("ce53f599", objArray).intValue();
       }else if((tl = this.l) != null){
          return tl.J(p0, p1);
       }else {
          return -1;
       }
    }
    public void q(boolean p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("19c106e3", objArray);
       }
       return;
    }
    public void q0(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6a035a85", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.K(p0);
       }
       return;
    }
    public void r(Map p0){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a0e81d99", objArray);
          return;
       }else if((ta = this.a) != null && this.e != null){
          ta.runOnUiThread(new a3h$a(this, p0));
       }
       return;
    }
    public void r0(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("36adfe1c", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.L(p0);
       }
       return;
    }
    public void s(TBConstants$TBMediaSDKState p0){
       a3h ta;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6a996d0a", objArray);
          return;
       }else {
          switch (a3h$k0.$SwitchMap$com$taobao$living$api$TBConstants$TBMediaSDKState[p0.ordinal()]){
              case 1:
                this.i0("started");
                break;
              case 2:
                this.i0("inited");
                break;
              case 3:
                this.i0("joinchannel");
                break;
              case 4:
                if (this.a != null && this.e != null) {
                   this.i0("error");
                   this.a.runOnUiThread(new a3h$n0(this));
                }
                break;
              case 6:
                if (this.a != null && this.e != null) {
                   this.i0("retry");
                   this.a.runOnUiThread(new a3h$o0(this));
                }
                break;
              case 7:
                if (this.a != null && this.e != null) {
                   this.i0("connected");
                   this.a.runOnUiThread(new a3h$p0(this));
                }
                break;
              case 8:
                if ((ta = this.a) != null && this.e != null) {
                   ta.runOnUiThread(new a3h$q0(this));
                }
                break;
              default:
          }
          return;
       }
    }
    public void s0(String p0,boolean p1,String p2,int p3,int p4){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1),p2,new Integer(p3),new Integer(p4)};
          $ipChange.ipc$dispatch("b965c79", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.M(p0, p1, null, p2, p3, p4);
       }
       return;
    }
    public void t(float p0){
       a3h te;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Float(p0)};
          $ipChange.ipc$dispatch("4d812ef4", objArray);
          return;
       }else if((te = this.e) != null){
          te.q(p0);
       }
       return;
    }
    public void t0(ArtcVideoLayout p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("33fcbdeb", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.O(p0);
       }
       return;
    }
    public void u(TBLiveMediaSDKEngine$VCLinkMultiEvent p0,Map p1){
       a3h$t p1;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("53dc9dc0", objArray);
          return;
       }else {
          TrtcLog.j("LivePushInstance", "handleLinkMultiEvent, event: "+p0+" ,params: "+p1.toString());
          String str = "remoteId";
          String str1 = "extension";
          if (p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiLocalCalling) {
             this.a.runOnUiThread(new a3h$p(this, p1.get(str), p1.get(str1)));
          }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiLocalCallFailed){
             this.a.runOnUiThread(new a3h$q(this, p1.get(str), p1.get("result"), p1.get(str1)));
          }else {
             String str2 = "chatChannelId";
             if (p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiLocalAccept) {
                this.a.runOnUiThread(new a3h$r(this, p1.get(str), p1.get(str1), p1.get(str2)));
             }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiLocalReject){
                this.a.runOnUiThread(new a3h$s(this, p1.get(str), p1.get(str1), p1.get(str2)));
             }else {
                str = "remoteUserId";
                if (p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiRemoteCalled) {
                   p1 = new a3h$t(this, p1.get("callId"), p1.get(str), p1.get("remoteRole"), p1.get(str1));
                   this.a.runOnUiThread(p1);
                }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiRemoteAccept){
                   a3h$v p11 = new a3h$v(this, p1.get(str), p1.get("role"), p1.get(str1), p1.get(str2));
                   this.a.runOnUiThread(p1);
                }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiRemoteCalledTimeOut){
                   this.a.runOnUiThread(new a3h$w(this, p1.get(str)));
                }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiRemoteReject){
                   a3h$x p12 = new a3h$x(this, p1.get(str), p1.get("role"), p1.get(str1), p1.get(str2));
                   this.a.runOnUiThread(p1);
                }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiRemoteEnd){
                   this.a.runOnUiThread(new a3h$y(this, p1.get(str), p1.get("option"), p1.get(str1)));
                }else {
                   str = "peerList";
                   if (p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiJoinChannel) {
                      this.a.runOnUiThread(new a3h$z(this, p1.get(str)));
                   }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiLeftChannel){
                      this.a.runOnUiThread(new a3h$a0(this, p1.get(str)));
                   }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiRemoteMute){
                      this.a.runOnUiThread(new a3h$b0(this, p1.get("mute").booleanValue(), p1.get("remoteStreams")));
                   }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiFirstVideoFrame){
                      this.a.runOnUiThread(new a3h$c0(this, p1.get("uid")));
                   }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiFirstAudioFrame){
                      this.a.runOnUiThread(new a3h$d0(this, p1.get("uid")));
                   }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiVideoTimeOutFrame){
                      this.a.runOnUiThread(new a3h$e0(this, p1.get("uid")));
                   }else if(p0 == TBLiveMediaSDKEngine$VCLinkMultiEvent.VCLinkMultiAudioTimeOutFrame){
                      this.a.runOnUiThread(new a3h$f0(this, p1.get("uid")));
                   }
                }
             }
          }
          return;
       }
    }
    public void u0(String p0,String p1,String p2,String p3){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("2ae743d2", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.P(p0, p1, p2, p3);
       }
       return;
    }
    public void v(Map p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3d8f609d", objArray);
          return;
       }else {
          this.a.runOnUiThread(new a3h$m0(this, p0));
          return;
       }
    }
    public void v0(String p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f3ccf5f6", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.Q(p0);
       }
       return;
    }
    public void w(TBConstants$VCLinkLiveEvent p0,Map p1){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("535c6c13", objArray);
          return;
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveLocalCalled){
          this.i0("lkLocalCalled");
          this.a.runOnUiThread(new a3h$f(this, p1.get("peerId"), p1.get("extension")));
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveLocalCalling){
          this.i0("lkLocalCalling");
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveRemoteAccept){
          this.i0("lkRemoteAccept");
          this.a.runOnUiThread(new a3h$g(this, p1));
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveRemoteReject){
          this.i0("lkRemoteReject");
          this.a.runOnUiThread(new a3h$h(this, p1));
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveLocalEnd){
          this.i0("lkLocalEnd");
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveRemoteEnd){
          this.i0("lkRemoteEnd");
          this.a.runOnUiThread(new a3h$i(this, p1));
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveLocalReject){
          this.i0("lkLocalReject");
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveLocalCallFailed){
          this.i0("lkLocalFailed");
          this.a.runOnUiThread(new a3h$k(this, p1));
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveLocalCallTimeOut){
          this.i0("lkLocalTimeout");
          this.a.runOnUiThread(new a3h$l(this));
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveRemoteCancel){
          this.i0("lkRemoteCancel");
          this.a.runOnUiThread(new a3h$m(this));
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLiveLocalAccept){
          this.i0("lkLocalAccept");
          this.a.runOnUiThread(new a3h$n(this));
       }else if(p0 == TBConstants$VCLinkLiveEvent.VCLinkLivePlayViewStartRendering){
          this.a.runOnUiThread(new a3h$o(this));
       }
       return;
    }
    public void w0(TBLiveMediaSDKEngine$a p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d64287fb", objArray);
          return;
       }else {
          this.n = p0;
          return;
       }
    }
    public void x(){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("907c7bcf", objArray);
       }
       return;
    }
    public void x0(boolean p0){
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("48a3963e", objArray);
       }
       return;
    }
    public void y0(int p0){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("177b8884", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.R(p0);
       }
       return;
    }
    public void z0(String p0,String p1,boolean p2){
       a3h tl;
       IpChange $ipChange = a3h.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Boolean(p2)};
          $ipChange.ipc$dispatch("cd2a9bb5", objArray);
          return;
       }else if((tl = this.l) != null){
          tl.N(p0, p1, p2);
       }
       return;
    }
}
