package tb.a06$a;
import tb.qub;
import tb.t2o;
import java.lang.Object;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a06;

public class a06$a implements qub	// class@001ac5 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1f60003d);
       t2o.a(0x1c500477);
    }
    public void a06$a(){
       super();
    }
    public DXWidgetNode build(Object p0){
       IpChange $ipChange = a06$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new a06();
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("966917b0", objArray);
    }
}
