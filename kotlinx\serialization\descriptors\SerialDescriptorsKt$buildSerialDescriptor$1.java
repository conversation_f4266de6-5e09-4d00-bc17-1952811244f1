package kotlinx.serialization.descriptors.SerialDescriptorsKt$buildSerialDescriptor$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.f520;
import tb.xhv;
import java.lang.String;
import tb.ckf;

public final class SerialDescriptorsKt$buildSerialDescriptor$1 extends Lambda implements g1a	// class@00072d from classes11.dex
{
    public static final SerialDescriptorsKt$buildSerialDescriptor$1 INSTANCE;

    static {
       SerialDescriptorsKt$buildSerialDescriptor$1.INSTANCE = new SerialDescriptorsKt$buildSerialDescriptor$1();
    }
    public void SerialDescriptorsKt$buildSerialDescriptor$1(){
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(f520 p0){
       ckf.g(p0, "$this$null");
    }
}
