package androidx.appcompat.app.WindowDecorActionBar$ActionModeImpl;
import androidx.appcompat.view.menu.MenuBuilder$Callback;
import androidx.appcompat.view.ActionMode;
import androidx.appcompat.app.WindowDecorActionBar;
import android.content.Context;
import androidx.appcompat.view.ActionMode$Callback;
import androidx.appcompat.view.menu.MenuBuilder;
import android.view.Menu;
import androidx.appcompat.widget.ActionBarContextView;
import androidx.appcompat.widget.ActionBarOverlayLayout;
import android.view.View;
import java.lang.Object;
import java.lang.ref.Reference;
import android.view.MenuInflater;
import androidx.appcompat.view.SupportMenuInflater;
import java.lang.CharSequence;
import androidx.appcompat.view.menu.SubMenuBuilder;
import android.view.MenuItem;
import androidx.appcompat.view.menu.MenuPopupHelper;
import java.lang.ref.WeakReference;
import android.content.res.Resources;
import java.lang.String;

public class WindowDecorActionBar$ActionModeImpl extends ActionMode implements MenuBuilder$Callback	// class@00058f from classes.dex
{
    private final Context mActionModeContext;
    private ActionMode$Callback mCallback;
    private WeakReference mCustomView;
    private final MenuBuilder mMenu;
    public final WindowDecorActionBar this$0;

    public void WindowDecorActionBar$ActionModeImpl(WindowDecorActionBar p0,Context p1,ActionMode$Callback p2){
       this.this$0 = p0;
       super();
       this.mActionModeContext = p1;
       this.mCallback = p2;
       MenuBuilder menuBuilder = new MenuBuilder(p1).setDefaultShowAsAction(1);
       this.mMenu = menuBuilder;
       menuBuilder.setCallback(this);
    }
    public boolean dispatchOnCreate(){
       this.mMenu.stopDispatchingItemsChanged();
       this.mMenu.startDispatchingItemsChanged();
       return this.mCallback.onCreateActionMode(this, this.mMenu);
    }
    public void finish(){
       WindowDecorActionBar$ActionModeImpl tthis$0 = this.this$0;
       if (tthis$0.mActionMode != this) {
          return;
       }
       boolean b = false;
       if (!WindowDecorActionBar.checkShowingFlags(tthis$0.mHiddenByApp, tthis$0.mHiddenBySystem, b)) {
          tthis$0 = this.this$0;
          tthis$0.mDeferredDestroyActionMode = this;
          tthis$0.mDeferredModeDestroyCallback = this.mCallback;
       }else {
          this.mCallback.onDestroyActionMode(this);
       }
       this.mCallback = null;
       this.this$0.animateToMode(b);
       this.this$0.mContextView.closeMode();
       WindowDecorActionBar$ActionModeImpl tthis$01 = this.this$0;
       tthis$01.mOverlayLayout.setHideOnContentScrollEnabled(tthis$01.mHideOnContentScroll);
       this.this$0.mActionMode = null;
       return;
    }
    public View getCustomView(){
       WindowDecorActionBar$ActionModeImpl tmCustomView;
       View view = ((tmCustomView = this.mCustomView) != null)? tmCustomView.get(): null;
       return view;
    }
    public Menu getMenu(){
       return this.mMenu;
    }
    public MenuInflater getMenuInflater(){
       return new SupportMenuInflater(this.mActionModeContext);
    }
    public CharSequence getSubtitle(){
       return this.this$0.mContextView.getSubtitle();
    }
    public CharSequence getTitle(){
       return this.this$0.mContextView.getTitle();
    }
    public void invalidate(){
       if (this.this$0.mActionMode != this) {
          return;
       }
       this.mMenu.stopDispatchingItemsChanged();
       this.mCallback.onPrepareActionMode(this, this.mMenu);
       this.mMenu.startDispatchingItemsChanged();
       return;
    }
    public boolean isTitleOptional(){
       return this.this$0.mContextView.isTitleOptional();
    }
    public void onCloseMenu(MenuBuilder p0,boolean p1){
    }
    public void onCloseSubMenu(SubMenuBuilder p0){
    }
    public boolean onMenuItemSelected(MenuBuilder p0,MenuItem p1){
       WindowDecorActionBar$ActionModeImpl tmCallback;
       if ((tmCallback = this.mCallback) != null) {
          return tmCallback.onActionItemClicked(this, p1);
       }
       return false;
    }
    public void onMenuModeChange(MenuBuilder p0){
       if (this.mCallback == null) {
          return;
       }
       this.invalidate();
       this.this$0.mContextView.showOverflowMenu();
       return;
    }
    public boolean onSubMenuSelected(SubMenuBuilder p0){
       if (this.mCallback == null) {
          return false;
       }
       if (!p0.hasVisibleItems()) {
          return true;
       }
       new MenuPopupHelper(this.this$0.getThemedContext(), p0).show();
       return true;
    }
    public void setCustomView(View p0){
       this.this$0.mContextView.setCustomView(p0);
       this.mCustomView = new WeakReference(p0);
    }
    public void setSubtitle(int p0){
       this.setSubtitle(this.this$0.mContext.getResources().getString(p0));
    }
    public void setSubtitle(CharSequence p0){
       this.this$0.mContextView.setSubtitle(p0);
    }
    public void setTitle(int p0){
       this.setTitle(this.this$0.mContext.getResources().getString(p0));
    }
    public void setTitle(CharSequence p0){
       this.this$0.mContextView.setTitle(p0);
    }
    public void setTitleOptionalHint(boolean p0){
       super.setTitleOptionalHint(p0);
       this.this$0.mContextView.setTitleOptional(p0);
    }
}
