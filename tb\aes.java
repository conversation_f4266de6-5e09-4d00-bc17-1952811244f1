package tb.aes;
import tb.xnj;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import android.content.Intent;
import tb.zmj;
import java.lang.Boolean;
import android.net.Uri;
import android.content.Context;
import tb.abs;
import tb.no8;
import tb.h8s;
import com.taobao.themis.kernel.executor.IExecutorService;
import java.lang.Class;
import tb.p8s;
import com.taobao.themis.kernel.executor.ExecutorType;
import java.util.concurrent.Executor;
import tb.aes$a;
import java.lang.Runnable;
import com.taobao.themis.taobao.impl.TBTMSSDK;
import java.lang.Throwable;
import com.taobao.themis.kernel.basic.TMSLogger;
import tb.q9s;
import tb.tsq;
import tb.ges;
import com.taobao.themis.kernel.solution.TMSSolutionType;
import com.taobao.themis.taobao.impl.TMS;

public final class aes implements xnj	// class@001ece from classes10.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x35000060);
       t2o.a(0x24900063);
    }
    public void aes(){
       super();
    }
    public String name(){
       IpChange $ipChange = aes.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "TMSUniAppNavProcess";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("aa84494e", objArray);
    }
    public boolean process(Intent p0,zmj p1){
       Uri data;
       IExecutorService iExecutorSer;
       int i = 2;
       IpChange $ipChange = aes.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("b3ebca67", objArray).booleanValue();
       }else if(p0 == null){
          return 1;
       }else if(p1 == null){
          return 1;
       }else if((data = p0.getData()) == null || data.isHierarchical()){
          Context uContext = ((uContext = p1.d()) == null)? null: uContext.getApplicationContext();
          if (uContext == null) {
             return 1;
          }else {
             abs.c(uContext);
             boolean b = h8s.f(uContext, "enableAsyncInitTask").a();
             if ((iExecutorSer = p8s.b(IExecutorService.class)) != null && b) {
                iExecutorSer.getExecutor(ExecutorType.NORMAL).execute(new aes$a(uContext));
             }else {
                TBTMSSDK.initTBTMS(uContext);
             }
             if (q9s.z1() && tsq.I(String.valueOf(data), "tbopen://", 0, i, null)) {
                return 1;
             }else if(ges.m(data) && !p0.getBooleanExtra("disableHook", 0)){
                return (TMS.startApp(p0, p1, TMSSolutionType.UNIAPP) ^ 1);
             }else {
                return 1;
             }
          }
       }else {
          return 1;
       }
    }
    public boolean skip(){
       int i = 0;
       IpChange $ipChange = aes.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("7fce928a", objArray).booleanValue();
       }
       return i;
    }
}
