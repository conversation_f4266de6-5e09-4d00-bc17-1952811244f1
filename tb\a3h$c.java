package tb.a3h$c;
import java.lang.Runnable;
import tb.a3h;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a3h$v0;

public class a3h$c implements Runnable	// class@001e69 from classes10.dex
{
    public final a3h a;
    public static IpChange $ipChange;

    public void a3h$c(a3h p0){
       this.a = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.y(this.a) != null){
          a3h.y(this.a).onBlueToothDeviceDisconnected();
       }
       return;
    }
}
