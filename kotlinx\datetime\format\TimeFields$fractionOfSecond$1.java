package kotlinx.datetime.format.TimeFields$fractionOfSecond$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.fw40;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import tb.fg20;

public final class TimeFields$fractionOfSecond$1 extends MutablePropertyReference1Impl	// class@0006ee from classes11.dex
{
    public static final TimeFields$fractionOfSecond$1 INSTANCE;

    static {
       TimeFields$fractionOfSecond$1.INSTANCE = new TimeFields$fractionOfSecond$1();
    }
    public void TimeFields$fractionOfSecond$1(){
       super(fw40.class, "fractionOfSecond", "getFractionOfSecond\(\)Lkotlinx/datetime/internal/DecimalFraction;", 0);
    }
    public Object get(Object p0){
       return p0.m();
    }
    public void set(Object p0,Object p1){
       p0.y(p1);
    }
}
