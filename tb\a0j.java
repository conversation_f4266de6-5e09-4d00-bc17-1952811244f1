package tb.a0j;
import tb.t2o;
import java.util.List;
import tb.zzi;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.Object;
import java.lang.String;
import com.taobao.tao.messagekit.core.utils.MsgLog;
import tb.nzn;
import tb.zd0;
import tb.bpn;
import tb.e2o;
import tb.bxe;

public class a0j	// class@00173a from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2780005e);
    }
    public static zzi a(int p0,int p1,List p2){
       int i = 0;
       IpChange $ipChange = a0j.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0),new Integer(p1),p2};
          return $ipChange.ipc$dispatch("f53694a", objArray);
       }else {
          Object[] objArray1 = new Object[]{"createMonitorTask type= ",Integer.valueOf(p0)};
          MsgLog.e("MonitorTaskFactory", objArray1);
          if (p0 == 1) {
             return new bxe(p1);
          }
          if (p0 != 2) {
             if (p0 != 3) {
                if (p0 != 4) {
                   if (p0 != 5) {
                   label_0054 :
                      return null;
                   }
                }else if(p2 != null){
                   return new nzn(p1, p2);
                }
                if (p2 != null) {
                   return new zd0(p2);
                }else {
                   goto label_0054 ;
                }
             }
          }else if(p2 != null){
             return new bpn(p2);
          }
          return new e2o(p1);
       }
    }
}
