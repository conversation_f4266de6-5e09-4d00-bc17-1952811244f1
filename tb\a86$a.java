package tb.a86$a;
import tb.qub;
import tb.t2o;
import java.lang.Object;
import com.taobao.android.dinamicx.widget.DXWidgetNode;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.s32;
import tb.a86;

public class a86$a implements qub	// class@001b0a from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2ef00224);
       t2o.a(0x1c500477);
    }
    public void a86$a(){
       super();
    }
    public DXWidgetNode build(Object p0){
       IpChange $ipChange = a86$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("966917b0", objArray);
       }else {
          s32.c().i("DXPlayControlScrollLayout.build");
          return new a86();
       }
    }
}
