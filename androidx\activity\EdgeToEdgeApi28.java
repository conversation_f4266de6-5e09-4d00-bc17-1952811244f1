package androidx.activity.EdgeToEdgeApi28;
import androidx.activity.EdgeToEdgeApi26;
import android.view.Window;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.view.WindowManager$LayoutParams;
import tb.a78;

public class EdgeToEdgeApi28 extends EdgeToEdgeApi26	// class@000446 from classes.dex
{

    public void EdgeToEdgeApi28(){
       super();
    }
    public void adjustLayoutInDisplayCutoutMode(Window p0){
       ckf.g(p0, "window");
       a78.a(p0.getAttributes(), 1);
    }
}
