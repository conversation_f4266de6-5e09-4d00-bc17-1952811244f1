package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$singleOrNull$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.bj3;

public final class ChannelsKt__DeprecatedKt$singleOrNull$1 extends ContinuationImpl	// class@0004f9 from classes11.dex
{
    public Object L$0;
    public Object L$1;
    public int label;
    public Object result;

    public void ChannelsKt__DeprecatedKt$singleOrNull$1(ar4 p0){
       super(p0);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return bj3.r(null, this);
    }
}
