package tb.a4p$a;
import java.lang.Runnable;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.location.common.TBLocationDTO;
import com.taobao.location.client.TBLocationClient;
import tb.a4p;

public class a4p$a implements Runnable	// class@001aeb from classes8.dex
{
    public static IpChange $ipChange;

    public void a4p$a(){
       super();
    }
    public void run(){
       IpChange $ipChange = a4p$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          a4p.a(TBLocationClient.d());
          return;
       }
    }
}
