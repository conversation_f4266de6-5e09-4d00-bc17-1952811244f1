package tb.a7t;
import tb.r0e;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.tao.timestamp.TimeStampManager;
import java.lang.Number;
import com.taobao.taobaocompat.lifecycle.TimestampSynchronizer;

public class a7t implements r0e	// class@001770 from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x30f001b2);
       t2o.a(0x3290065a);
    }
    public void a7t(){
       super();
    }
    public void a(){
       IpChange $ipChange = a7t.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("9c8cd8a", objArray);
          return;
       }else {
          TimeStampManager.instance().pullTimeStampIfNeeded();
          return;
       }
    }
    public long getServerTime(){
       IpChange $ipChange = a7t.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return TimestampSynchronizer.getServerTime();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7cda1621", objArray).longValue();
    }
}
