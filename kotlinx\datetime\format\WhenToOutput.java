package kotlinx.datetime.format.WhenToOutput;
import java.lang.Enum;
import java.lang.String;
import tb.fg8;
import kotlin.enums.a;
import java.lang.Class;
import java.lang.Object;

public final class WhenToOutput extends Enum	// class@0006f3 from classes11.dex
{
    private static final fg8 $ENTRIES;
    private static final WhenToOutput[] $VALUES;
    public static final WhenToOutput ALWAYS;
    public static final WhenToOutput IF_NONZERO;
    public static final WhenToOutput NEVER;

    private static final WhenToOutput[] $values(){
       WhenToOutput[] whenToOutput = new WhenToOutput[]{WhenToOutput.NEVER,WhenToOutput.IF_NONZERO,WhenToOutput.ALWAYS};
       return whenToOutput;
    }
    static {
       WhenToOutput.NEVER = new WhenToOutput("NEVER", 0);
       WhenToOutput.IF_NONZERO = new WhenToOutput("IF_NONZERO", 1);
       WhenToOutput.ALWAYS = new WhenToOutput("ALWAYS", 2);
       WhenToOutput[] whenToOutput = WhenToOutput.$values();
       WhenToOutput.$VALUES = whenToOutput;
       WhenToOutput.$ENTRIES = a.a(whenToOutput);
    }
    private void WhenToOutput(String p0,int p1){
       super(p0, p1);
    }
    public static fg8 getEntries(){
       return WhenToOutput.$ENTRIES;
    }
    public static WhenToOutput valueOf(String p0){
       return Enum.valueOf(WhenToOutput.class, p0);
    }
    public static WhenToOutput[] values(){
       return WhenToOutput.$VALUES.clone();
    }
}
