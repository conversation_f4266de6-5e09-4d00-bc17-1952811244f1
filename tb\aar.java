package tb.aar;
import tb.t2o;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import tb.aar$a;
import java.lang.Runnable;
import tb.dn;

public class aar	// class@001861 from classes5.dex
{
    public static IpChange $ipChange;
    public static final String a;
    public static final String b;
    public static final String c;
    public static final String d;
    public static final String e;
    public static final String f;

    static {
       t2o.a(0x2cc000c3);
       aar.a = "/sre.buy.init_container";
       aar.b = "/sre.buy.halfbuy_init_container";
       aar.c = "/sre.buy.full_init_container";
       aar.d = "/sre.buy.lightbuy_init_container";
       aar.e = "/sre.buy.lightbuy_close_container";
       aar.f = "/sre.buy.halfbuy_close_container";
    }
    public static void a(String p0){
       IpChange $ipChange = aar.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("5ced3be", objArray);
          return;
       }else {
          dn.h(new aar$a(p0));
          return;
       }
    }
}
