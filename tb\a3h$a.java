package tb.a3h$a;
import java.lang.Runnable;
import tb.a3h;
import java.util.Map;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a3h$v0;
import java.lang.Integer;
import java.lang.StringBuilder;
import com.alibaba.fastjson.JSON;
import android.util.Log;

public class a3h$a implements Runnable	// class@001e65 from classes10.dex
{
    public final Map a;
    public final a3h b;
    public static IpChange $ipChange;

    public void a3h$a(a3h p0,Map p1){
       this.b = p0;
       this.a = p1;
       super();
    }
    public void run(){
       a3h$a ta;
       IpChange $ipChange = a3h$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.y(this.b) != null && (ta = this.a) != null){
          String str = "reason";
          String str1 = "";
          String str2 = (ta.get(str) == null)? str1: this.a.get(str).toString();
          String str3 = "errorMsg";
          if (this.a.get(str3) != null) {
             str1 = this.a.get(str3).toString();
          }
          a3h.y(this.b).m(str2, this.a.get("errorCode").intValue(), str1);
          a3h.A(this.b, "error");
          Log.e("LivePushInstance", "TBMediaSDKStateError error:"+JSON.toJSONString(this.a));
       }
       return;
    }
}
