package tb.a4u;
import android.view.View$OnClickListener;
import tb.dz1;
import tb.t2o;
import android.content.Context;
import com.taobao.taobao.R$style;
import android.view.Window;
import android.app.Dialog;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import android.view.LayoutInflater;
import com.taobao.taobao.R$layout;
import android.view.ViewGroup;
import tb.ux9;
import tb.vx9;
import tb.up6;
import com.taobao.taobao.R$id;
import com.alilive.adapter.uikit.AliUrlImageView;
import com.taobao.schedule.ViewProxy;
import java.util.HashMap;
import tb.rbu;
import android.util.DisplayMetrics;
import android.view.WindowManager$LayoutParams;
import tb.hw0;
import android.os.Build$VERSION;
import tb.a78;
import tb.v2s;
import tb.joc;
import tb.sjr;
import tb.hwy;
import android.app.Activity;
import tb.joc$a;
import java.lang.Throwable;
import tb.pvs;
import tb.voj;

public class a4u extends dz1 implements View$OnClickListener	// class@00175b from classes9.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x30f00428);
    }
    public void a4u(Context p0){
       super(p0, R$style.taolive_top_dialog);
       this.getWindow().setDimAmount(0);
    }
    public static Object ipc$super(a4u p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/taolive/room/ui/view/TopMoreBtnPopupWindow");
    }
    public View a(){
       IpChange $ipChange = a4u.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("a20e10cc", objArray);
       }else {
          View view = LayoutInflater.from(this.a).inflate(R$layout.taolive_top_more_btn_layout, null);
          if (up6.p0(vx9.d())) {
             view.findViewById(R$id.taolive_top_more_report).setVisibility(8);
          }else {
             view.findViewById(R$id.taolive_top_more_report_img).setImageUrl("https://gw.alicdn.com/tfs/TB1OBQVNAL0gK0jSZFtXXXQCXXa-128-128.png");
             ViewProxy.setOnClickListener(view.findViewById(R$id.taolive_top_more_report), this);
          }
          view.findViewById(R$id.taolive_top_more_tobehost_img).setImageUrl("https://gw.alicdn.com/imgextra/i1/O1CN01VDHOlI1gZx6ci3QyD_!!6000000004157-2-tps-128-128.png");
          ViewProxy.setOnClickListener(view.findViewById(R$id.taolive_top_more_close_btn), this);
          ViewProxy.setOnClickListener(view.findViewById(R$id.taolive_top_more_tobehost), this);
          rbu.c0(vx9.d(), "Show-more_anchor", null);
          return view;
       }
    }
    public WindowManager$LayoutParams b(DisplayMetrics p0){
       IpChange $ipChange = a4u.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("c9bf7551", objArray);
       }else {
          WindowManager$LayoutParams attributes = this.getWindow().getAttributes();
          attributes.gravity = 48;
          attributes.width = p0.widthPixels;
          attributes.height = hw0.b(this.getContext(), 171.00f);
          attributes.flags = attributes.flags | 0x0400;
          if (Build$VERSION.SDK_INT >= 28) {
             a78.a(attributes, 1);
          }
          return attributes;
       }
    }
    public void onClick(View p0){
       int i = 0;
       IpChange $ipChange = a4u.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8dfcefe2", objArray);
          return;
       }else if(p0.getId() == R$id.taolive_top_more_report){
          if (v2s.o().u() != null && v2s.o().u().checkSessionValid()) {
             sjr.g().c("com.taobao.taolive.room.start_report_from_btns", null, vx9.e());
          }else if(v2s.o().u() != null){
             try{
                v2s.o().u().a(this.a, null);
             }catch(java.lang.Exception e5){
                e5.printStackTrace();
             }
          }
          this.dismiss();
       }else if(p0.getId() == R$id.taolive_top_more_close_btn){
          this.dismiss();
       }else if(p0.getId() == R$id.taolive_top_more_tobehost){
          dz1 ta = this.a;
          voj.a(ta, pvs.C2(ta));
          this.dismiss();
          String[] stringArray = new String[i];
          rbu.L(vx9.d(), "more_anchor", stringArray);
       }
       return;
    }
}
