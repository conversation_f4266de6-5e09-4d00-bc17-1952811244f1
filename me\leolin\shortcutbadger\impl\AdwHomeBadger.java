package me.leolin.shortcutbadger.impl.AdwHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.content.Intent;
import tb.ol2;
import me.leolin.shortcutbadger.ShortcutBadgeException;
import java.lang.StringBuilder;

public class AdwHomeBadger implements po1	// class@00075c from classes11.dex
{
    public static final String CLASSNAME = "CNAME";
    public static final String COUNT = "COUNT";
    public static final String INTENT_UPDATE_COUNTER = "org.adw.launcher.counter.SEND";
    public static final String PACKAGENAME = "PNAME";

    public void AdwHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"org.adw.launcher","org.adwfreak.launcher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       Intent intent = new Intent("org.adw.launcher.counter.SEND");
       intent.putExtra("PNAME", p1.getPackageName());
       intent.putExtra("CNAME", p1.getClassName());
       intent.putExtra("COUNT", p2);
       if (!ol2.a(p0, intent)) {
          throw new ShortcutBadgeException("unable to resolve intent: "+intent.toString());
       }
       p0.sendBroadcast(intent);
       return;
    }
}
