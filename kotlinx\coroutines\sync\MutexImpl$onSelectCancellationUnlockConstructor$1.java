package kotlinx.coroutines.sync.MutexImpl$onSelectCancellationUnlockConstructor$1;
import tb.w1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.coroutines.sync.MutexImpl;
import java.lang.Object;
import tb.k9p;
import tb.g1a;
import kotlinx.coroutines.sync.MutexImpl$onSelectCancellationUnlockConstructor$1$1;

public final class MutexImpl$onSelectCancellationUnlockConstructor$1 extends Lambda implements w1a	// class@0006c7 from classes11.dex
{
    public final MutexImpl this$0;

    public void MutexImpl$onSelectCancellationUnlockConstructor$1(MutexImpl p0){
       this.this$0 = p0;
       super(3);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final g1a invoke(k9p p0,Object p1,Object p2){
       return new MutexImpl$onSelectCancellationUnlockConstructor$1$1(this.this$0, p1);
    }
}
