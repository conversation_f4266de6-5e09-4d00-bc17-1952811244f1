package mtopsdk.mtop.upload.domain.UploadResult;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;

public class UploadResult	// class@000810 from classes11.dex
{
    public boolean isFinish;
    public String location;
    public String serverRT;
    public static IpChange $ipChange;

    static {
       t2o.a(0x25900014);
    }
    public void UploadResult(boolean p0,String p1){
       super();
       this.isFinish = p0;
       this.location = p1;
    }
    public String toString(){
       IpChange $ipChange = UploadResult.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new StringBuilder(32)+"UploadResult [isFinish="+this.isFinish+"location=locationserverRT=serverRT]";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
