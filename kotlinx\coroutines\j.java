package kotlinx.coroutines.j;
import tb.sct;
import java.lang.Object;
import java.lang.Throwable;
import tb.ar4;
import tb.fa4;
import tb.sm8;
import kotlinx.coroutines.CoroutinesInternalError;
import java.lang.StringBuilder;
import java.lang.String;
import tb.ckf;
import kotlin.coroutines.d;
import tb.tu4;
import tb.dv6;
import tb.uq7;
import kotlinx.coroutines.internal.ThreadContextKt;
import tb.ogv;
import kotlinx.coroutines.CoroutineContextKt;
import tb.wq7;
import kotlinx.coroutines.m;
import kotlin.coroutines.d$c;
import kotlin.coroutines.d$b;
import java.util.concurrent.CancellationException;
import tb.vu4;
import tb.rgq;
import kotlin.b;
import kotlin.Result;
import tb.xhv;
import tb.gdt;

public abstract class j extends sct	// class@000698 from classes11.dex
{
    public int c;

    public void j(int p0){
       super();
       this.c = p0;
    }
    public void b(Object p0,Throwable p1){
    }
    public abstract ar4 d();
    public Throwable e(Object p0){
       Throwable throwable = null;
       if (p0 instanceof fa4) {
       }else {
          p0 = throwable;
       }
       if (p0 != null) {
          throwable = p0.a;
       }
       return throwable;
    }
    public Object f(Object p0){
       return p0;
    }
    public final void g(Throwable p0,Throwable p1){
       if (p0 == null && p1 == null) {
          return;
       }
       if (p0 != null && p1 != null) {
          sm8.a(p0, p1);
       }
       if (p0 == null) {
          p0 = p1;
       }
       ckf.d(p0);
       tu4.a(this.d().getContext(), new CoroutinesInternalError("Fatal exception in coroutines machinery for "+this+". Please read KDoc to \'handleFatalException\' method and report this incident to maintainers", p0));
       return;
    }
    public abstract Object i();
    public final void run(){
       Object obj;
       Throwable throwable;
       sct tb = this.b;
       ar4 uoar4 = this.d();
       ckf.e(uoar4, "null cannot be cast to non-null type kotlinx.coroutines.internal.DispatchedContinuation<T of kotlinx.coroutines.DispatchedTask>");
       uq7 e = uoar4.e;
       d context = e.getContext();
       ogv oogv = ((obj = ThreadContextKt.c(context, uoar4.g)) != ThreadContextKt.NO_THREAD_ELEMENTS)? CoroutineContextKt.g(e, context, obj): null;
       d context1 = e.getContext();
       Object obj1 = this.i();
       m om = ((throwable = this.e(obj1)) == null && wq7.b(this.c))? context1.get(m.Key): null;
       if (om != null && !om.isActive()) {
          CancellationException uCancellatio = om.u0();
          this.b(obj1, uCancellatio);
          if (dv6.c() && e instanceof vu4) {
             uCancellatio = rgq.a(uCancellatio, e);
          }
          e.resumeWith(Result.constructor-impl(b.a(uCancellatio)));
       }else if(throwable != null){
          e.resumeWith(Result.constructor-impl(b.a(throwable)));
       }else {
          e.resumeWith(Result.constructor-impl(this.f(obj1)));
       }
       if (oogv == null || oogv.w1()) {
          ThreadContextKt.a(context, obj);
       }
       tb.a();
       Object obj2 = Result.constructor-impl(xhv.INSTANCE);
       this.g(null, Result.exceptionOrNull-impl(obj2));
       return;
    }
}
