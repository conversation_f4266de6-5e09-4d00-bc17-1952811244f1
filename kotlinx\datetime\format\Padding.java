package kotlinx.datetime.format.Padding;
import java.lang.Enum;
import java.lang.String;
import tb.fg8;
import kotlin.enums.a;
import java.lang.Class;
import java.lang.Object;

public final class Padding extends Enum	// class@0006ec from classes11.dex
{
    private static final fg8 $ENTRIES;
    private static final Padding[] $VALUES;
    public static final Padding NONE;
    public static final Padding SPACE;
    public static final Padding ZERO;

    private static final Padding[] $values(){
       Padding[] paddingArray = new Padding[]{Padding.NONE,Padding.ZERO,Padding.SPACE};
       return paddingArray;
    }
    static {
       Padding.NONE = new Padding("NONE", 0);
       Padding.ZERO = new Padding("ZERO", 1);
       Padding.SPACE = new Padding("SPACE", 2);
       Padding[] paddingArray = Padding.$values();
       Padding.$VALUES = paddingArray;
       Padding.$ENTRIES = a.a(paddingArray);
    }
    private void Padding(String p0,int p1){
       super(p0, p1);
    }
    public static fg8 getEntries(){
       return Padding.$ENTRIES;
    }
    public static Padding valueOf(String p0){
       return Enum.valueOf(Padding.class, p0);
    }
    public static Padding[] values(){
       return Padding.$VALUES.clone();
    }
}
