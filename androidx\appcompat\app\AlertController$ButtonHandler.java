package androidx.appcompat.app.AlertController$ButtonHandler;
import android.os.Handler;
import android.content.DialogInterface;
import java.lang.ref.WeakReference;
import java.lang.Object;
import android.os.Message;
import android.content.DialogInterface$OnClickListener;
import java.lang.ref.Reference;

public final class AlertController$ButtonHandler extends Handler	// class@00054d from classes.dex
{
    private WeakReference mDialog;
    private static final int MSG_DISMISS_DIALOG = 1;

    public void AlertController$ButtonHandler(DialogInterface p0){
       super();
       this.mDialog = new WeakReference(p0);
    }
    public void handleMessage(Message p0){
       Message what;
       if ((what = p0.what) != -3 && (what != -2 && what != -1)) {
          if (what == 1) {
             p0.obj.dismiss();
          }
       }else {
          p0.obj.onClick(this.mDialog.get(), p0.what);
       }
       return;
    }
}
