package kotlinx.coroutines.channels.BufferedChannel$onUndeliveredElementReceiveCancellationConstructor$1$1;
import tb.w1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.coroutines.channels.BufferedChannel;
import java.lang.Object;
import tb.k9p;
import tb.g1a;
import kotlinx.coroutines.channels.BufferedChannel$onUndeliveredElementReceiveCancellationConstructor$1$1$1;

public final class BufferedChannel$onUndeliveredElementReceiveCancellationConstructor$1$1 extends Lambda implements w1a	// class@0004ca from classes11.dex
{
    public final BufferedChannel this$0;

    public void BufferedChannel$onUndeliveredElementReceiveCancellationConstructor$1$1(BufferedChannel p0){
       this.this$0 = p0;
       super(3);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final g1a invoke(k9p p0,Object p1,Object p2){
       return new BufferedChannel$onUndeliveredElementReceiveCancellationConstructor$1$1$1(p2, this.this$0, p0);
    }
}
