package mtopsdk.instanceconfigs.MtopExternalInstanceConfigsGetRequest;
import mtopsdk.mtop.domain.IMTOPDataObject;
import tb.t2o;
import java.lang.Object;

public class MtopExternalInstanceConfigsGetRequest implements IMTOPDataObject	// class@00077f from classes11.dex
{
    public String API_NAME;
    public boolean NEED_ECODE;
    public boolean NEED_SESSION;
    public String VERSION;
    public String innerInstanceConfigs;

    static {
       t2o.a(0x2530008f);
       t2o.a(0x253000cf);
    }
    public void MtopExternalInstanceConfigsGetRequest(){
       super();
       this.API_NAME = "mtop.external.instanceConfigs.get";
       this.VERSION = "1.0";
       this.NEED_ECODE = false;
       this.NEED_SESSION = false;
       this.innerInstanceConfigs = null;
    }
}
