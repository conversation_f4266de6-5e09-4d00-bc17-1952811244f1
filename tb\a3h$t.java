package tb.a3h$t;
import java.lang.Runnable;
import tb.a3h;
import java.lang.String;
import java.lang.Integer;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.a3h$s0;

public class a3h$t implements Runnable	// class@001e8b from classes10.dex
{
    public final String a;
    public final String b;
    public final Integer c;
    public final String d;
    public final a3h e;
    public static IpChange $ipChange;

    public void a3h$t(a3h p0,String p1,String p2,Integer p3,String p4){
       this.e = p0;
       this.a = p1;
       this.b = p2;
       this.c = p3;
       this.d = p4;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$t.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.C(this.e) != null){
          a3h.C(this.e).p(this.a, this.b, this.c.intValue(), this.d);
       }
       return;
    }
}
