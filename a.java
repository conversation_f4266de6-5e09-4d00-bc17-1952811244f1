package a;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class a extends Enum	// class@000006 from classes3.dex
{
    public int a;
    public String b;
    public static IpChange $ipChange;
    public static final a c;
    public static final a d;
    public static final a e;
    public static final a f;
    public static final a g;
    public static final a[] h;

    static {
       a uoa = new a("SUCCESS", 0, 0, "success");
       a.c = uoa;
       a uoa1 = new a("ERROR_NO_PERMISSION", 1, 0x7a19d9, "No permission to call this function.");
       a.d = uoa1;
       a uoa2 = new a("ERROR_EXCEEDED_MAXIMUM", 2, 0x7a19da, "Turning on the notification switch request exceeds the maximum number of times.");
       a.e = uoa2;
       a uoa3 = new a("ERROR_REQUIRES_TOO_MANY", 3, 0x7a19db, "Turning on the notification switch requires too many requests.");
       a.f = uoa3;
       a uoa4 = new a("ERROR_VERSION_NOT_SUPPORT", 4, 0x7a19dc, "The current version does not support this function, please upgrade Honor Push.");
       a.g = uoa4;
       a[] uoaArray = new a[]{uoa,uoa1,uoa2,uoa3,uoa4};
       a.h = uoaArray;
    }
    public void a(String p0,int p1,int p2,String p3){
       super(p0, p1);
       this.a = p2;
       this.b = p3;
    }
    public static Object ipc$super(a p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in a");
    }
    public static a valueOf(String p0){
       a uoa;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          uoa = $ipChange.ipc$dispatch("c937cbe9", objArray);
       }else {
          uoa = Enum.valueOf(a.class, p0);
       }
       return uoa;
    }
    public static a[] values(){
       a[] uoaArray;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          uoaArray = $ipChange.ipc$dispatch("41f481d8", objArray);
       }else {
          uoaArray = a.h.clone();
       }
       return uoaArray;
    }
}
