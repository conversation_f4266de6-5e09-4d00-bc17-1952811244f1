package tb.a2g;
import java.lang.Runnable;
import com.taobao.keepalive.KeepAliveManager$c;
import android.content.Context;
import java.lang.Object;

public final class a2g implements Runnable	// class@001adb from classes8.dex
{
    public final KeepAliveManager$c a;
    public final Context b;

    public void a2g(KeepAliveManager$c p0,Context p1){
       super();
       this.a = p0;
       this.b = p1;
    }
    public final void run(){
       KeepAliveManager$c.a(this.a, this.b);
    }
}
