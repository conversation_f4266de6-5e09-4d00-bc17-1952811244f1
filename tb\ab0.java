package tb.ab0;
import java.lang.Class;
import java.lang.Object;
import java.lang.String;
import java.lang.reflect.Method;
import java.lang.reflect.AccessibleObject;
import java.lang.Throwable;
import com.taobao.tao.log.TLog;
import android.app.Activity;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.StringBuilder;

public class ab0	// class@001863 from classes5.dex
{
    public final Class a;
    public final Object b;
    public static IpChange $ipChange;

    public void ab0(Class p0,Object p1){
       super();
       this.a = p0;
       this.b = p1;
    }
    public static Object a(Class p0,String p1,Object p2,Class[] p3,Object[] p4){
       try{
          Method declaredMeth = p0.getDeclaredMethod(p1, p3);
          declaredMeth.setAccessible(true);
          return declaredMeth.invoke(p2, p4);
       }catch(java.lang.Exception e0){
          TLog.loge("TBAutoSize.EmbeddingComponent", "invokeMethod: ", e0);
          return null;
       }
    }
    public boolean b(Activity p0){
       ab0 tb;
       int i = 1;
       IpChange $ipChange = ab0.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("de4faee0", objArray).booleanValue();
       }else if((tb = this.b) == null){
          return 0;
       }else {
          Class[] uClassArray = new Class[i];
          uClassArray[0] = Activity.class;
          Object[] objArray1 = new Object[i];
          objArray1[0] = p0;
          Object obj = ab0.a(this.a, "isActivityEmbedded", tb, uClassArray, objArray1);
          TLog.loge("TBAutoSize.EmbeddingComponent", "isActivityEmbedded: activity="+p0+" result="+obj);
          if (obj instanceof Boolean) {
             return obj.booleanValue();
          }
          return 0;
       }
    }
}
