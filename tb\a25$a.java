package tb.a25$a;
import java.lang.Runnable;
import tb.a25;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class a25$a implements Runnable	// class@00183f from classes7.dex
{
    public final Runnable a;
    public final a25 b;
    public static IpChange $ipChange;

    public void a25$a(a25 p0,Runnable p1){
       this.b = p0;
       this.a = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = a25$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.a.run();
          this.b.a = null;
          return;
       }
    }
}
