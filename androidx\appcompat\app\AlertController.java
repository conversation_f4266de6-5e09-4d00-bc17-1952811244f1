package androidx.appcompat.app.AlertController;
import android.content.Context;
import androidx.appcompat.app.AppCompatDialog;
import android.view.Window;
import java.lang.Object;
import androidx.appcompat.app.AlertController$1;
import androidx.appcompat.app.AlertController$ButtonHandler;
import android.content.DialogInterface;
import com.taobao.taobao.R$styleable;
import com.taobao.taobao.R$attr;
import android.util.AttributeSet;
import android.content.res.TypedArray;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.view.ViewGroup$LayoutParams;
import android.widget.LinearLayout$LayoutParams;
import android.view.ViewStub;
import android.view.ViewParent;
import com.taobao.taobao.R$id;
import android.os.Build$VERSION;
import androidx.core.view.ViewCompat;
import androidx.appcompat.app.AlertController$2;
import androidx.core.widget.NestedScrollView$OnScrollChangeListener;
import androidx.core.widget.NestedScrollView;
import androidx.appcompat.app.AlertController$3;
import java.lang.Runnable;
import androidx.appcompat.app.AlertController$4;
import android.widget.AbsListView$OnScrollListener;
import android.widget.AbsListView;
import androidx.appcompat.app.AlertController$5;
import android.view.View$OnClickListener;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.widget.TextView;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import androidx.appcompat.widget.LinearLayoutCompat$LayoutParams;
import android.widget.ImageView;
import androidx.appcompat.app.AlertController$RecycleListView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.util.TypedValue;
import android.content.res.Resources$Theme;
import android.view.KeyEvent;
import android.content.DialogInterface$OnClickListener;
import android.os.Message;
import android.os.Handler;
import java.lang.IllegalArgumentException;
import java.lang.String;

public class AlertController	// class@000550 from classes.dex
{
    public ListAdapter mAdapter;
    private int mAlertDialogLayout;
    private final View$OnClickListener mButtonHandler;
    private final int mButtonIconDimen;
    public Button mButtonNegative;
    private Drawable mButtonNegativeIcon;
    public Message mButtonNegativeMessage;
    private CharSequence mButtonNegativeText;
    public Button mButtonNeutral;
    private Drawable mButtonNeutralIcon;
    public Message mButtonNeutralMessage;
    private CharSequence mButtonNeutralText;
    private int mButtonPanelLayoutHint;
    private int mButtonPanelSideLayout;
    public Button mButtonPositive;
    private Drawable mButtonPositiveIcon;
    public Message mButtonPositiveMessage;
    private CharSequence mButtonPositiveText;
    public int mCheckedItem;
    private final Context mContext;
    private View mCustomTitleView;
    public final AppCompatDialog mDialog;
    public Handler mHandler;
    private Drawable mIcon;
    private int mIconId;
    private ImageView mIconView;
    public int mListItemLayout;
    public int mListLayout;
    public ListView mListView;
    private CharSequence mMessage;
    private TextView mMessageView;
    public int mMultiChoiceItemLayout;
    public NestedScrollView mScrollView;
    private boolean mShowTitle;
    public int mSingleChoiceItemLayout;
    private CharSequence mTitle;
    private TextView mTitleView;
    private View mView;
    private int mViewLayoutResId;
    private int mViewSpacingBottom;
    private int mViewSpacingLeft;
    private int mViewSpacingRight;
    private boolean mViewSpacingSpecified;
    private int mViewSpacingTop;
    private final Window mWindow;

    public void AlertController(Context p0,AppCompatDialog p1,Window p2){
       super();
       this.mViewSpacingSpecified = false;
       this.mIconId = 0;
       this.mCheckedItem = -1;
       this.mButtonPanelLayoutHint = 0;
       this.mButtonHandler = new AlertController$1(this);
       this.mContext = p0;
       this.mDialog = p1;
       this.mWindow = p2;
       this.mHandler = new AlertController$ButtonHandler(p1);
       TypedArray typedArray = p0.obtainStyledAttributes(null, R$styleable.AlertDialog, R$attr.alertDialogStyle, false);
       this.mAlertDialogLayout = typedArray.getResourceId(R$styleable.AlertDialog_android_layout, false);
       this.mButtonPanelSideLayout = typedArray.getResourceId(R$styleable.AlertDialog_buttonPanelSideLayout, false);
       this.mListLayout = typedArray.getResourceId(R$styleable.AlertDialog_listLayout, false);
       this.mMultiChoiceItemLayout = typedArray.getResourceId(R$styleable.AlertDialog_multiChoiceItemLayout, false);
       this.mSingleChoiceItemLayout = typedArray.getResourceId(R$styleable.AlertDialog_singleChoiceItemLayout, false);
       this.mListItemLayout = typedArray.getResourceId(R$styleable.AlertDialog_listItemLayout, false);
       this.mShowTitle = typedArray.getBoolean(R$styleable.AlertDialog_showTitle, true);
       this.mButtonIconDimen = typedArray.getDimensionPixelSize(R$styleable.AlertDialog_buttonIconDimen, false);
       typedArray.recycle();
       p1.supportRequestWindowFeature(true);
    }
    public static boolean canTextInput(View p0){
       if (p0.onCheckIsTextEditor()) {
          return true;
       }
       if (!p0 instanceof ViewGroup) {
          return false;
       }
       int childCount = p0.getChildCount();
       while (true) {
          if (childCount <= 0) {
             return false;
          }
          childCount = childCount - 1;
          if (AlertController.canTextInput(p0.getChildAt(childCount))) {
             break ;
          }
       }
       return true;
    }
    private void centerButton(Button p0){
       LinearLayout$LayoutParams layoutParams = p0.getLayoutParams();
       layoutParams.gravity = 1;
       layoutParams.weight = 0.50f;
       p0.setLayoutParams(layoutParams);
    }
    public static void manageScrollIndicators(View p0,View p1,View p2){
       int i = 4;
       if (p1 != null) {
          int i1 = (p0.canScrollVertically(-1))? 0: 4;
          p1.setVisibility(i1);
       }
       if (p2 != null) {
          if (p0.canScrollVertically(1)) {
             i = 0;
          }
          p2.setVisibility(i);
       }
       return;
    }
    private ViewGroup resolvePanel(View p0,View p1){
       ViewGroup viewGroup;
       ViewGroup viewGroup1;
       if (p0 == null) {
          if (p1 instanceof ViewStub) {
             viewGroup = p1.inflate();
          }
          return viewGroup;
       }else if(p1 != null){
          ViewParent parent = p1.getParent();
          if (parent instanceof ViewGroup) {
             parent.removeView(p1);
          }
       }
       if (p0 instanceof ViewStub) {
          viewGroup1 = p0.inflate();
       }
       return viewGroup1;
    }
    private int selectContentView(){
       AlertController tmButtonPane;
       if ((tmButtonPane = this.mButtonPanelSideLayout) == null) {
          return this.mAlertDialogLayout;
       }
       if (this.mButtonPanelLayoutHint == 1) {
          return tmButtonPane;
       }
       return this.mAlertDialogLayout;
    }
    private void setScrollIndicators(ViewGroup p0,View p1,int p2,int p3){
       AlertController tmListView;
       View view = this.mWindow.findViewById(R$id.scrollIndicatorUp);
       View view1 = this.mWindow.findViewById(R$id.scrollIndicatorDown);
       if (Build$VERSION.SDK_INT >= 23) {
          ViewCompat.setScrollIndicators(p1, p2, p3);
          if (view != null) {
             p0.removeView(view);
          }
          if (view1 != null) {
             p0.removeView(view1);
          }
       }else {
          int i = 0;
          if (view != null && !((p2 & 0x01))) {
             p0.removeView(view);
             view = i;
          }
          if (view1 != null && !((p2 & 0x02))) {
             p0.removeView(view1);
             view1 = i;
          }
          if (view != null || view1 != null) {
             if (this.mMessage != null) {
                this.mScrollView.setOnScrollChangeListener(new AlertController$2(this, view, view1));
                this.mScrollView.post(new AlertController$3(this, view, view1));
             }else if((tmListView = this.mListView) != null){
                tmListView.setOnScrollListener(new AlertController$4(this, view, view1));
                this.mListView.post(new AlertController$5(this, view, view1));
             }else if(view != null){
                p0.removeView(view);
             }
             if (view1 != null) {
                p0.removeView(view1);
             }
          }
       }
       return;
    }
    private void setupButtons(ViewGroup p0){
       int i;
       AlertController tmButtonNeut;
       AlertController tmButtonPosi;
       Button uButton = p0.findViewById(0x1020019);
       this.mButtonPositive = uButton;
       uButton.setOnClickListener(this.mButtonHandler);
       Drawable uDrawable = null;
       if (TextUtils.isEmpty(this.mButtonPositiveText) && this.mButtonPositiveIcon == null) {
          this.mButtonPositive.setVisibility(8);
          i = 0;
       }else {
          this.mButtonPositive.setText(this.mButtonPositiveText);
          if ((tmButtonPosi = this.mButtonPositiveIcon) != null) {
             tmButtonPosi.setBounds(0, 0, this.mButtonIconDimen, this.mButtonIconDimen);
             this.mButtonPositive.setCompoundDrawables(this.mButtonPositiveIcon, uDrawable, uDrawable, uDrawable);
          }
          this.mButtonPositive.setVisibility(0);
          i = 1;
       }
       Button uButton1 = p0.findViewById(0x102001a);
       this.mButtonNegative = uButton1;
       uButton1.setOnClickListener(this.mButtonHandler);
       if (TextUtils.isEmpty(this.mButtonNegativeText) && this.mButtonNegativeIcon == null) {
          this.mButtonNegative.setVisibility(8);
       }else {
          this.mButtonNegative.setText(this.mButtonNegativeText);
          if ((tmButtonNeut = this.mButtonNegativeIcon) != null) {
             tmButtonNeut.setBounds(0, 0, this.mButtonIconDimen, this.mButtonIconDimen);
             this.mButtonNegative.setCompoundDrawables(this.mButtonNegativeIcon, uDrawable, uDrawable, uDrawable);
          }
          this.mButtonNegative.setVisibility(0);
          i = i | 0x02;
       }
       uButton1 = p0.findViewById(0x102001b);
       this.mButtonNeutral = uButton1;
       uButton1.setOnClickListener(this.mButtonHandler);
       if (TextUtils.isEmpty(this.mButtonNeutralText) && this.mButtonNeutralIcon == null) {
          this.mButtonNeutral.setVisibility(8);
       }else {
          this.mButtonNeutral.setText(this.mButtonNeutralText);
          if ((tmButtonNeut = this.mButtonNeutralIcon) != null) {
             tmButtonNeut.setBounds(0, 0, this.mButtonIconDimen, this.mButtonIconDimen);
             this.mButtonNeutral.setCompoundDrawables(this.mButtonNeutralIcon, uDrawable, uDrawable, uDrawable);
          }
          this.mButtonNeutral.setVisibility(0);
          i = i | 0x04;
       }
       if (AlertController.shouldCenterSingleButton(this.mContext)) {
          if (i == 1) {
             this.centerButton(this.mButtonPositive);
          }else if(i == 2){
             this.centerButton(this.mButtonNegative);
          }else if(i == 4){
             this.centerButton(this.mButtonNeutral);
          }
       }
       if (!i) {
          p0.setVisibility(8);
       }
       return;
    }
    private void setupContent(ViewGroup p0){
       AlertController tmMessage;
       NestedScrollView nestedScroll = this.mWindow.findViewById(R$id.scrollView);
       this.mScrollView = nestedScroll;
       int b = false;
       nestedScroll.setFocusable(b);
       this.mScrollView.setNestedScrollingEnabled(b);
       TextView textView = p0.findViewById(0x102000b);
       this.mMessageView = textView;
       if (textView == null) {
          return;
       }
       if ((tmMessage = this.mMessage) != null) {
          textView.setText(tmMessage);
       }else {
          b = 8;
          textView.setVisibility(b);
          this.mScrollView.removeView(this.mMessageView);
          if (this.mListView != null) {
             p0 = this.mScrollView.getParent();
             int i = p0.indexOfChild(this.mScrollView);
             p0.removeViewAt(i);
             p0.addView(this.mListView, i, new ViewGroup$LayoutParams(-1, -1));
          }else {
             p0.setVisibility(b);
          }
       }
       return;
    }
    private void setupCustomContent(ViewGroup p0){
       AlertController tmView = this.mView;
       boolean b = false;
       if (tmView == null) {
          tmView = (this.mViewLayoutResId != null)? LayoutInflater.from(this.mContext).inflate(this.mViewLayoutResId, p0, b): null;
       }
       if (tmView != null) {
          b = 1;
       }
       if (!b || !AlertController.canTextInput(tmView)) {
          this.mWindow.setFlags(0x20000, 0x20000);
       }
       if (b) {
          FrameLayout uFrameLayout = this.mWindow.findViewById(R$id.custom);
          uFrameLayout.addView(tmView, new ViewGroup$LayoutParams(-1, -1));
          if (this.mViewSpacingSpecified != null) {
             uFrameLayout.setPadding(this.mViewSpacingLeft, this.mViewSpacingTop, this.mViewSpacingRight, this.mViewSpacingBottom);
          }
          if (this.mListView != null) {
             p0.weight = 0;
          }
       }else {
          p0.setVisibility(8);
       }
       return;
    }
    private void setupTitle(ViewGroup p0){
       AlertController tmIconId;
       if (this.mCustomTitleView != null) {
          p0.addView(this.mCustomTitleView, 0, new ViewGroup$LayoutParams(-1, -2));
          this.mWindow.findViewById(R$id.title_template).setVisibility(8);
       }else {
          this.mIconView = this.mWindow.findViewById(0x1020006);
          if (!TextUtils.isEmpty(this.mTitle) && this.mShowTitle != null) {
             TextView textView = this.mWindow.findViewById(R$id.alertTitle);
             this.mTitleView = textView;
             textView.setText(this.mTitle);
             if ((tmIconId = this.mIconId) != null) {
                this.mIconView.setImageResource(tmIconId);
             }else if((tmIconId = this.mIcon) != null){
                this.mIconView.setImageDrawable(tmIconId);
             }else {
                this.mTitleView.setPadding(this.mIconView.getPaddingLeft(), this.mIconView.getPaddingTop(), this.mIconView.getPaddingRight(), this.mIconView.getPaddingBottom());
                this.mIconView.setVisibility(8);
             }
          }else {
             this.mWindow.findViewById(R$id.title_template).setVisibility(8);
             this.mIconView.setVisibility(8);
             p0.setVisibility(8);
          }
       }
       return;
    }
    private void setupView(){
       AlertController tmScrollView;
       View view4;
       AlertController tmListView1;
       View view = this.mWindow.findViewById(R$id.parentPanel);
       int topPanel = R$id.topPanel;
       View view1 = view.findViewById(topPanel);
       boolean contentPanel = R$id.contentPanel;
       View view2 = view.findViewById(contentPanel);
       int buttonPanel = R$id.buttonPanel;
       View view3 = view.findViewById(buttonPanel);
       ViewGroup viewGroup = view.findViewById(R$id.customPanel);
       this.setupCustomContent(viewGroup);
       ViewGroup viewGroup1 = this.resolvePanel(viewGroup.findViewById(topPanel), view1);
       ViewGroup viewGroup2 = this.resolvePanel(viewGroup.findViewById(contentPanel), view2);
       ViewGroup viewGroup3 = this.resolvePanel(viewGroup.findViewById(buttonPanel), view3);
       this.setupContent(viewGroup2);
       this.setupButtons(viewGroup3);
       this.setupTitle(viewGroup1);
       view2 = 8;
       int i = 0;
       viewGroup = (viewGroup.getVisibility() != view2)? 1: 0;
       boolean b = (viewGroup1 != null && viewGroup1.getVisibility() != view2)? true: false;
       contentPanel = (viewGroup3 != null && viewGroup3.getVisibility() != view2)? true: false;
       if (!contentPanel && (viewGroup2 != null && (view2 = viewGroup2.findViewById(R$id.textSpacerNoButtons)) != null)) {
          view2.setVisibility(i);
       }
       if (b) {
          if ((tmScrollView = this.mScrollView) != null) {
             tmScrollView.setClipToPadding(true);
          }
          view4 = (this.mMessage == null && this.mListView == null)? null: viewGroup1.findViewById(R$id.titleDividerNoCustom);
          if (view4 != null) {
             view4.setVisibility(i);
          }
       }else if(viewGroup2 != null && (view4 = viewGroup2.findViewById(R$id.textSpacerNoTitle)) != null){
          view4.setVisibility(i);
       }
       AlertController tmListView = this.mListView;
       if (tmListView instanceof AlertController$RecycleListView) {
          tmListView.setHasDecor(b, contentPanel);
       }
       if (!viewGroup) {
          if ((tmListView1 = this.mListView) == null) {
             tmListView1 = this.mScrollView;
          }
          if (tmListView1 != null) {
             if (contentPanel) {
                i = 2;
             }
             this.setScrollIndicators(viewGroup2, tmListView1, (b | i), 3);
          }
       }
       if ((tmListView1 = this.mListView) != null && (tmListView = this.mAdapter) != null) {
          tmListView1.setAdapter(tmListView);
          if ((tmListView = this.mCheckedItem) > -1) {
             tmListView1.setItemChecked(tmListView, true);
             tmListView1.setSelection(tmListView);
          }
       }
       return;
    }
    private static boolean shouldCenterSingleButton(Context p0){
       TypedValue typedValue = new TypedValue();
       boolean b = true;
       p0.getTheme().resolveAttribute(R$attr.alertDialogCenterButtons, typedValue, b);
       if (typedValue.data == null) {
          b = false;
       }
       return b;
    }
    public Button getButton(int p0){
       if (p0 == -3) {
          return this.mButtonNeutral;
       }
       if (p0 == -2) {
          return this.mButtonNegative;
       }
       if (p0 != -1) {
          return null;
       }
       return this.mButtonPositive;
    }
    public int getIconAttributeResId(int p0){
       TypedValue typedValue = new TypedValue();
       this.mContext.getTheme().resolveAttribute(p0, typedValue, true);
       return typedValue.resourceId;
    }
    public ListView getListView(){
       return this.mListView;
    }
    public void installContent(){
       this.mDialog.setContentView(this.selectContentView());
       this.setupView();
    }
    public boolean onKeyDown(int p0,KeyEvent p1){
       AlertController tmScrollView;
       boolean b = ((tmScrollView = this.mScrollView) != null && tmScrollView.executeKeyEvent(p1))? true: false;
       return b;
    }
    public boolean onKeyUp(int p0,KeyEvent p1){
       AlertController tmScrollView;
       boolean b = ((tmScrollView = this.mScrollView) != null && tmScrollView.executeKeyEvent(p1))? true: false;
       return b;
    }
    public void setButton(int p0,CharSequence p1,DialogInterface$OnClickListener p2,Message p3,Drawable p4){
       if (p3 == null && p2 != null) {
          p3 = this.mHandler.obtainMessage(p0, p2);
       }
       if (p0 != -3) {
          if (p0 != -2) {
             if (p0 == -1) {
                this.mButtonPositiveText = p1;
                this.mButtonPositiveMessage = p3;
                this.mButtonPositiveIcon = p4;
             }else {
                throw new IllegalArgumentException("Button does not exist");
             }
          }else {
             this.mButtonNegativeText = p1;
             this.mButtonNegativeMessage = p3;
             this.mButtonNegativeIcon = p4;
          }
       }else {
          this.mButtonNeutralText = p1;
          this.mButtonNeutralMessage = p3;
          this.mButtonNeutralIcon = p4;
       }
       return;
    }
    public void setButtonPanelLayoutHint(int p0){
       this.mButtonPanelLayoutHint = p0;
    }
    public void setCustomTitle(View p0){
       this.mCustomTitleView = p0;
    }
    public void setIcon(int p0){
       AlertController tmIconView;
       this.mIcon = null;
       this.mIconId = p0;
       if ((tmIconView = this.mIconView) != null) {
          if (p0) {
             tmIconView.setVisibility(0);
             this.mIconView.setImageResource(this.mIconId);
          }else {
             tmIconView.setVisibility(8);
          }
       }
       return;
    }
    public void setIcon(Drawable p0){
       AlertController tmIconView;
       this.mIcon = p0;
       int i = 0;
       this.mIconId = i;
       if ((tmIconView = this.mIconView) != null) {
          if (p0 != null) {
             tmIconView.setVisibility(i);
             this.mIconView.setImageDrawable(p0);
          }else {
             tmIconView.setVisibility(8);
          }
       }
       return;
    }
    public void setMessage(CharSequence p0){
       AlertController tmMessageVie;
       this.mMessage = p0;
       if ((tmMessageVie = this.mMessageView) != null) {
          tmMessageVie.setText(p0);
       }
       return;
    }
    public void setTitle(CharSequence p0){
       AlertController tmTitleView;
       this.mTitle = p0;
       if ((tmTitleView = this.mTitleView) != null) {
          tmTitleView.setText(p0);
       }
       return;
    }
    public void setView(int p0){
       this.mView = null;
       this.mViewLayoutResId = p0;
       this.mViewSpacingSpecified = false;
    }
    public void setView(View p0){
       this.mView = p0;
       this.mViewLayoutResId = 0;
       this.mViewSpacingSpecified = false;
    }
    public void setView(View p0,int p1,int p2,int p3,int p4){
       this.mView = p0;
       this.mViewLayoutResId = 0;
       this.mViewSpacingSpecified = true;
       this.mViewSpacingLeft = p1;
       this.mViewSpacingTop = p2;
       this.mViewSpacingRight = p3;
       this.mViewSpacingBottom = p4;
    }
}
