package androidx.activity.result.ActivityResultRegistry$CallbackAndContract;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.contract.ActivityResultContract;
import java.lang.Object;
import java.lang.String;
import tb.ckf;

public final class ActivityResultRegistry$CallbackAndContract	// class@0004b4 from classes.dex
{
    private final ActivityResultCallback callback;
    private final ActivityResultContract contract;

    public void ActivityResultRegistry$CallbackAndContract(ActivityResultCallback p0,ActivityResultContract p1){
       ckf.g(p0, "callback");
       ckf.g(p1, "contract");
       super();
       this.callback = p0;
       this.contract = p1;
    }
    public final ActivityResultCallback getCallback(){
       return this.callback;
    }
    public final ActivityResultContract getContract(){
       return this.contract;
    }
}
