package androidx.activity.result.ActivityResultCallerLauncher$resultContract$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.result.ActivityResultCallerLauncher;
import androidx.activity.result.ActivityResultCallerLauncher$resultContract$2$1;
import java.lang.Object;

public final class ActivityResultCallerLauncher$resultContract$2 extends Lambda implements d1a	// class@0004af from classes.dex
{
    public final ActivityResultCallerLauncher this$0;

    public void ActivityResultCallerLauncher$resultContract$2(ActivityResultCallerLauncher p0){
       this.this$0 = p0;
       super(0);
    }
    public final ActivityResultCallerLauncher$resultContract$2$1 invoke(){
       return new ActivityResultCallerLauncher$resultContract$2$1(this.this$0);
    }
    public Object invoke(){
       return this.invoke();
    }
}
