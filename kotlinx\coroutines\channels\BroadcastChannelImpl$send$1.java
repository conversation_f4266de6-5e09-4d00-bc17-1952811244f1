package kotlinx.coroutines.channels.BroadcastChannelImpl$send$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import kotlinx.coroutines.channels.BroadcastChannelImpl;
import tb.ar4;
import java.lang.Object;

public final class BroadcastChannelImpl$send$1 extends ContinuationImpl	// class@0004ba from classes11.dex
{
    public Object L$0;
    public Object L$1;
    public Object L$2;
    public int label;
    public Object result;
    public final BroadcastChannelImpl this$0;

    public void BroadcastChannelImpl$send$1(BroadcastChannelImpl p0,ar4 p1){
       this.this$0 = p0;
       super(p1);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       return this.this$0.d(null, this);
    }
}
