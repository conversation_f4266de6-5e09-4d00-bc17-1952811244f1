package kotlinx.serialization.SerializersKt__SerializersKt$serializerByKTypeImpl$contextualSerializer$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.util.List;
import java.lang.Object;
import tb.v530;
import tb.e1g;

public final class SerializersKt__SerializersKt$serializerByKTypeImpl$contextualSerializer$1 extends Lambda implements d1a	// class@000723 from classes11.dex
{
    public final List $typeArguments;

    public void SerializersKt__SerializersKt$serializerByKTypeImpl$contextualSerializer$1(List p0){
       this.$typeArguments = p0;
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final v530 invoke(){
       return this.$typeArguments.get(0).g();
    }
}
