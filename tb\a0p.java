package tb.a0p;
import tb.t2o;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.System;
import com.alibaba.mtl.appmonitor.model.MeasureValue;
import com.alibaba.mtl.appmonitor.model.MeasureValueSet;
import com.alibaba.mtl.appmonitor.model.MeasureSet;
import com.alibaba.mtl.appmonitor.model.DimensionSet;
import com.alibaba.mtl.appmonitor.AppMonitor;
import com.alibaba.mtl.appmonitor.model.DimensionValueSet;
import java.lang.Long;

public class a0p	// class@001ace from classes8.dex
{
    public static IpChange $ipChange;
    public static final String POINT_NAME;
    public static MeasureValueSet a;
    public static boolean b;
    public static boolean c;

    static {
       t2o.a(0x33200061);
       a0p.c = false;
    }
    public static void a(String p0){
       MeasureValueSet a;
       IpChange $ipChange = a0p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("f003de7a", objArray);
          return;
       }else if(a0p.b && (a = a0p.a) != null){
          a.setValue(p0, MeasureValue.create((double)System.currentTimeMillis()));
       }
       return;
    }
    public static void b(){
       IpChange $ipChange = a0p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("fede197", objArray);
          return;
       }else if(!a0p.c){
          a0p.c = true;
          MeasureSet measureSet = MeasureSet.create();
          measureSet.addMeasure("whole");
          measureSet.addMeasure("network");
          measureSet.addMeasure("parseData");
          measureSet.addMeasure("uiShown");
          DimensionSet uDimensionSe = DimensionSet.create();
          uDimensionSe.addDimension("mtopSource");
          AppMonitor.register("TBSearch", "SearchEnter", measureSet, uDimensionSe);
       }
       DimensionValueSet.create();
       a0p.a = MeasureValueSet.create();
       return;
    }
    public static void c(String p0,long p1){
       MeasureValueSet a;
       IpChange $ipChange = a0p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,new Long(p1)};
          $ipChange.ipc$dispatch("30f003b1", objArray);
          return;
       }else if(a0p.b && (a = a0p.a) != null){
          a.setValue(p0, (double)p1);
       }
       return;
    }
    public static void d(){
       IpChange $ipChange = a0p.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("b5d47e5b", objArray);
          return;
       }else {
          a0p.b = true;
          a0p.b();
          return;
       }
    }
}
