package mtopsdk.instanceconfigs.a$b;
import java.lang.Runnable;
import mtopsdk.instanceconfigs.a;
import java.util.concurrent.FutureTask;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class a$b implements Runnable	// class@000782 from classes11.dex
{
    public final FutureTask a;
    public final a b;
    public static IpChange $ipChange;

    public void a$b(a p0,FutureTask p1){
       this.b = p0;
       this.a = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = a$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.a.run();
          return;
       }
    }
}
