package tb.a7u;
import tb.yb4;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;

public class a7u extends yb4	// class@00184e from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2490008e);
    }
    public void a7u(){
       super();
    }
    public static Object ipc$super(a7u p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/pissarro/pad/TpPadFullscreenNavProcessorNodeComponentFilterItem0");
    }
    public boolean a(String p0){
       int i = 1;
       IpChange $ipChange = a7u.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f15c09d0", objArray).booleanValue();
       }else if(!p0.equals("com.taobao.taopai.business.image.album.ImageGalleryActivity") && !p0.equals("com.taobao.taopai.business.record.fragment.TPRecordActivity")){
          i = false;
       }
       return i;
    }
    public boolean b(String p0){
       IpChange $ipChange = a7u.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.equals("com.taobao.taobao");
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("8e8cdc9e", objArray).booleanValue();
    }
}
