package androidx.appcompat.app.AppCompatDelegateImpl$ActionModeCallbackWrapperV9;
import androidx.appcompat.view.ActionMode$Callback;
import androidx.appcompat.app.AppCompatDelegateImpl;
import java.lang.Object;
import androidx.appcompat.view.ActionMode;
import android.view.MenuItem;
import android.view.Menu;
import android.view.View;
import android.view.Window;
import java.lang.Runnable;
import androidx.core.view.ViewPropertyAnimatorCompat;
import androidx.core.view.ViewCompat;
import androidx.appcompat.app.AppCompatDelegateImpl$ActionModeCallbackWrapperV9$1;
import androidx.core.view.ViewPropertyAnimatorListener;
import androidx.appcompat.app.AppCompatCallback;

public class AppCompatDelegateImpl$ActionModeCallbackWrapperV9 implements ActionMode$Callback	// class@000569 from classes.dex
{
    private ActionMode$Callback mWrapped;
    public final AppCompatDelegateImpl this$0;

    public void AppCompatDelegateImpl$ActionModeCallbackWrapperV9(AppCompatDelegateImpl p0,ActionMode$Callback p1){
       this.this$0 = p0;
       super();
       this.mWrapped = p1;
    }
    public boolean onActionItemClicked(ActionMode p0,MenuItem p1){
       return this.mWrapped.onActionItemClicked(p0, p1);
    }
    public boolean onCreateActionMode(ActionMode p0,Menu p1){
       return this.mWrapped.onCreateActionMode(p0, p1);
    }
    public void onDestroyActionMode(ActionMode p0){
       AppCompatDelegateImpl mAppCompatCa;
       this.mWrapped.onDestroyActionMode(p0);
       AppCompatDelegateImpl$ActionModeCallbackWrapperV9 tthis$0 = this.this$0;
       if (tthis$0.mActionModePopup != null) {
          tthis$0.mWindow.getDecorView().removeCallbacks(this.this$0.mShowActionModePopup);
       }
       tthis$0 = this.this$0;
       if (tthis$0.mActionModeView != null) {
          tthis$0.endOnGoingFadeAnimation();
          tthis$0 = this.this$0;
          tthis$0.mFadeAnim = ViewCompat.animate(tthis$0.mActionModeView).alpha(0);
          this.this$0.mFadeAnim.setListener(new AppCompatDelegateImpl$ActionModeCallbackWrapperV9$1(this));
       }
       tthis$0 = this.this$0;
       if ((mAppCompatCa = tthis$0.mAppCompatCallback) != null) {
          mAppCompatCa.onSupportActionModeFinished(tthis$0.mActionMode);
       }
       tthis$0 = this.this$0;
       tthis$0.mActionMode = null;
       ViewCompat.requestApplyInsets(tthis$0.mSubDecor);
       this.this$0.updateBackInvokedCallbackState();
       return;
    }
    public boolean onPrepareActionMode(ActionMode p0,Menu p1){
       ViewCompat.requestApplyInsets(this.this$0.mSubDecor);
       return this.mWrapped.onPrepareActionMode(p0, p1);
    }
}
