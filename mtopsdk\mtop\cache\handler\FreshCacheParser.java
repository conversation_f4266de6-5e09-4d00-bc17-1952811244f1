package mtopsdk.mtop.cache.handler.FreshCacheParser;
import mtopsdk.mtop.cache.handler.ICacheParser;
import tb.t2o;
import java.lang.Object;
import mtopsdk.mtop.domain.ResponseSource;
import android.os.Handler;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import tb.w4j;
import mtopsdk.mtop.util.MtopStatistics;
import anetwork.network.cache.RpcCache;
import mtopsdk.mtop.domain.MtopRequest;
import mtopsdk.mtop.domain.MtopResponse;
import mtopsdk.mtop.cache.handler.CacheStatusHandler;
import mtopsdk.mtop.domain.MtopResponse$ResponseSource;
import mtopsdk.mtop.common.MtopNetworkProp;
import mtopsdk.mtop.common.MtopCallback$MtopCacheListener;
import mtopsdk.mtop.common.MtopCacheEvent;
import mtopsdk.mtop.common.MtopFinishEvent;
import mtopsdk.mtop.cache.handler.FreshCacheParser$1;
import mtopsdk.mtop.common.MtopListener;
import java.lang.Runnable;
import tb.ui9;

public class FreshCacheParser implements ICacheParser	// class@00079b from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x253000ad);
       t2o.a(0x253000af);
    }
    public void FreshCacheParser(){
       super();
    }
    public void parse(ResponseSource p0,Handler p1){
       FreshCacheParser$1 mtopContext.d.skipCacheCallback;
       int i = 1;
       int i1 = 0;
       int i2 = 3;
       IpChange $ipChange = FreshCacheParser.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i2];
          objArray[i1] = this;
          objArray[i] = p0;
          objArray[2] = p1;
          $ipChange.ipc$dispatch("ec0249bc", objArray);
          return;
       }else {
          ResponseSource seqNo = p0.seqNo;
          if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.InfoEnable)) {
             TBSdkLog.i("mtopsdk.FreshCacheParser", seqNo, "[parse]FreshCacheParser parse called");
          }
          p0.requireConnection = i1;
          ResponseSource mtopContext = p0.mtopContext;
          w4j g = mtopContext.g;
          g.cacheHitType = i;
          g.cacheResponseParseStartTime = g.currentTimeMillis();
          MtopResponse mtopResponse = CacheStatusHandler.initResponseFromCache(p0.rpcCache, mtopContext.b);
          mtopResponse.setSource(MtopResponse$ResponseSource.FRESH_CACHE);
          g.cacheResponseParseEndTime = g.currentTimeMillis();
          mtopResponse.setMtopStat(g);
          p0.cacheResponse = mtopResponse;
          g.cacheReturnTime = g.currentTimeMillis();
          w4j d = mtopContext.d;
          if (d.forceRefreshCache != null) {
             p0.requireConnection = i;
             w4j e = mtopContext.e;
             if (e instanceof MtopCallback$MtopCacheListener) {
                MtopNetworkProp reqContext = d.reqContext;
                MtopCacheEvent mtopCacheEve = new MtopCacheEvent(mtopResponse);
                mtopCacheEve.seqNo = seqNo;
                CacheStatusHandler.finishMtopStatisticsOnExpiredCache(g, mtopResponse);
                if (mtopContext.d.skipCacheCallback == null) {
                   mtopContext.d.skipCacheCallback = new FreshCacheParser$1(this, e, mtopCacheEve, reqContext, seqNo);
                   ui9.d(p1, mtopContext.d.skipCacheCallback, mtopContext.h.hashCode());
                }
                g.cacheHitType = i2;
             }
          }
          return;
       }
    }
}
