package kotlinx.coroutines.channels.c$a;
import kotlinx.coroutines.channels.c;
import java.lang.Object;
import kotlinx.coroutines.channels.i;
import kotlinx.coroutines.channels.i$a;
import kotlinx.coroutines.channels.ReceiveChannel;
import kotlinx.coroutines.channels.ReceiveChannel$DefaultImpls;
import tb.ar4;

public final class c$a	// class@000516 from classes11.dex
{

    public static boolean a(c p0,Object p1){
       return i$a.b(p0, p1);
    }
    public static Object b(c p0){
       return ReceiveChannel$DefaultImpls.b(p0);
    }
    public static Object c(c p0,ar4 p1){
       return ReceiveChannel$DefaultImpls.c(p0, p1);
    }
}
