package androidx.activity.result.IntentSenderRequest$Companion$CREATOR$1;
import android.os.Parcelable$Creator;
import java.lang.Object;
import android.os.Parcel;
import androidx.activity.result.IntentSenderRequest;
import java.lang.String;
import tb.ckf;

public final class IntentSenderRequest$Companion$CREATOR$1 implements Parcelable$Creator	// class@0004be from classes.dex
{

    public void IntentSenderRequest$Companion$CREATOR$1(){
       super();
    }
    public IntentSenderRequest createFromParcel(Parcel p0){
       ckf.g(p0, "inParcel");
       return new IntentSenderRequest(p0);
    }
    public Object createFromParcel(Parcel p0){
       return this.createFromParcel(p0);
    }
    public IntentSenderRequest[] newArray(int p0){
       IntentSenderRequest[] intentSender = new IntentSenderRequest[p0];
       return intentSender;
    }
    public Object[] newArray(int p0){
       return this.newArray(p0);
    }
}
