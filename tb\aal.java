package tb.aal;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import tb.aal$d;
import com.taobao.orange.OrangeConfig;
import tb.z9l;
import tb.obk;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.aqa;
import java.lang.Boolean;
import tb.djn;
import tb.w8l;
import java.util.concurrent.ExecutorService;
import tb.n8b;
import tb.aal$a;
import java.lang.Runnable;
import java.util.concurrent.Executor;
import java.lang.Long;
import java.util.List;
import java.util.Arrays;
import tb.ean;
import android.os.Build$VERSION;
import tb.aal$b;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.JSON;
import tb.aal$c;
import android.app.Application;
import com.taobao.tbhudong.TBHuDongServiceImp;
import android.content.Context;
import tb.yup;
import java.lang.Throwable;

public class aal	// class@001eb6 from classes10.dex
{
    public static IpChange $ipChange;
    public static final String CALENDAR_CLEAR_TITLE;
    public static final String STORAGE_MONITOR_MIN_SIZE_KEY;

    public void aal(){
       super();
    }
    public static void a(aal p0,String p1,Map p2){
       p0.e(p1, p2);
    }
    public static aal d(){
       IpChange $ipChange = aal.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return aal$d.a();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("606ffcb5", objArray);
    }
    public String b(String p0){
       IpChange $ipChange = aal.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return OrangeConfig.getInstance().getConfig("android_reach_flow", p0, "");
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("f41a24ff", objArray);
    }
    public void c(){
       int i = 1;
       IpChange $ipChange = aal.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("8876706b", objArray);
          return;
       }else {
          String[] stringArray = new String[]{"android_reach_flow"};
          OrangeConfig.getInstance().registerListener(stringArray, new z9l(this), i);
          return;
       }
    }
    public final void e(String p0,Map p1){
       Object[] objArray;
       String str2;
       Feature[] uFeatureArra;
       int i = 2;
       int i1 = 3;
       String str = "storage_monitor_min_size";
       String str1 = "configVersion";
       IpChange $ipChange = aal.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i1];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[i] = p1;
          $ipChange.ipc$dispatch("e2e45645", objArray);
          return;
       }else if(p1 != null && p1.containsKey(str1)){
          str2 = p1.get(str1);
          if (!TextUtils.isEmpty(str2)) {
          label_0038 :
             objArray = new Object[i1];
             objArray[0] = "android_reach_flow";
             objArray[1] = p0;
             objArray[i] = str2;
             aqa.b("%s.configGroup=%s.configVersion=%s.", objArray);
             p0 = this.b("closeCalendar");
             if (!TextUtils.isEmpty(p0)) {
                Boolean.parseBoolean(p0);
             }
             p0 = this.b("closeCalendarRepeat");
             if (!TextUtils.isEmpty(p0)) {
                Boolean.parseBoolean(p0);
             }
             p0 = this.b("clearCalendar");
             if (!TextUtils.isEmpty(p0) && !p0.equals(djn.c("calendar_clear_title", ""))) {
                w8l.f = p0;
                n8b.b().execute(new aal$a(this));
             }
             p0 = this.b("enablePermissionReporter");
             if (!TextUtils.isEmpty(p0)) {
                Boolean.parseBoolean(p0);
             }
             p0 = this.b("permissionCheckInterval");
             if (!TextUtils.isEmpty(p0)) {
                Long.parseLong(p0);
             }
             p0 = this.b("PermissionCheckList");
             if (!TextUtils.isEmpty(p0)) {
                w8l.g = Arrays.asList(p0.trim().split(","));
             }
             p0 = this.b("runtimePermissionConfig");
             if (!TextUtils.isEmpty(p0)) {
                ean.h().o(p0);
             }
             if (Build$VERSION.SDK_INT >= 25) {
                p0 = this.b("short_cuts_config");
                if (!TextUtils.isEmpty(p0)) {
                   uFeatureArra = new Feature[0];
                   w8l.d().g(JSON.parseObject(p0, new aal$b(this), uFeatureArra));
                }
                p0 = this.b("short_cuts_control");
                if (!TextUtils.isEmpty(p0)) {
                   uFeatureArra = new Feature[0];
                   w8l.d().h(JSON.parseObject(p0, new aal$c(this), uFeatureArra));
                }
                p0 = this.b("short_cuts_enable");
                if (!TextUtils.isEmpty(p0)) {
                   w8l.d().j(Boolean.parseBoolean(p0));
                }
                p0 = this.b("shortcuts_net_enable");
                if (!TextUtils.isEmpty(p0)) {
                   w8l.d().k(Boolean.parseBoolean(p0));
                }
                p0 = this.b("short_cuts_ab_module");
                if (!TextUtils.isEmpty(p0)) {
                   w8l.d().i(p0);
                }
                p0 = this.b(str);
                if (!TextUtils.isEmpty(p0)) {
                   djn.d(str, Long.parseLong(p0));
                }
                yup.u(TBHuDongServiceImp.getApplication()).I();
             }
             return;
          }
       }
       str2 = "";
       goto label_0038 ;
    }
}
