package tb.a4j;
import tb.t2o;
import tb.a4j$a;
import tb.a07;
import java.lang.Object;
import com.taobao.android.dinamicx.DinamicXEngine;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.alibaba.android.ultron.vfw.instance.a;
import tb.ko5;
import android.app.Activity;
import com.alibaba.android.ultron.vfw.instance.UltronInstanceConfig;
import tb.z7v$b;
import com.taobao.android.dinamicx.DXEngineConfig$b;
import com.taobao.mytaobao.base.MtbGlobalEnv;
import tb.uv6;
import tb.a4j$b;
import tb.z7v$c;
import tb.z7v;
import android.content.Context;
import tb.ckf;
import tb.a4j$c;
import tb.vav$b;
import tb.vav;

public final class a4j	// class@001aea from classes8.dex
{
    public a a;
    public DinamicXEngine b;
    public ko5 c;
    public static IpChange $ipChange;
    public static final a4j$a Companion;

    static {
       t2o.a(0x2ef00204);
       a4j.Companion = new a4j$a(null);
    }
    public void a4j(){
       super();
    }
    public static final DinamicXEngine a(a4j p0){
       IpChange $ipChange = a4j.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.b;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("35beedc", objArray);
    }
    public static final void b(a4j p0,DinamicXEngine p1){
       IpChange $ipChange = a4j.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("65e67670", objArray);
          return;
       }else {
          p0.b = p1;
          return;
       }
    }
    public static final void h(a p0,ko5 p1){
       IpChange $ipChange = a4j.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("b4ea86e9", objArray);
          return;
       }else {
          a4j.Companion.a(p0, p1);
          return;
       }
    }
    public final a c(Activity p0){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = a4j.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("8bf1dda6", objArray);
       }else {
          UltronInstanceConfig ultronInstan = new UltronInstanceConfig().z("mytaobao").K(i1);
          ultronInstan.a(i);
          ultronInstan.I(i);
          z7v$b uob = new z7v$b().p(0x2766).q("mytaobao").m(i);
          DXEngineConfig$b uob1 = new DXEngineConfig$b("mytaobao").H(i, i).g0(i);
          if (MtbGlobalEnv.p() && !uv6.b) {
             uob1.R(i1);
          }
          ultronInstan.G(uob.l(uob1).n(new a4j$b(this, p0)).i());
          a uoa = a.q(ultronInstan, p0);
          ckf.c(uoa, "UltronInstance.createUltronInstance\(config, aty\)");
          return uoa;
       }
    }
    public final void d(Activity p0){
       a4j ta;
       IpChange $ipChange = a4j.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("42a35114", objArray);
          return;
       }else {
          ckf.h(p0, "aty");
          if (this.a != null) {
             return;
          }
          vav.h("mytaobao", new a4j$c());
          this.a = this.c(p0);
          ko5 oko5 = new ko5();
          this.c = oko5;
          a4j$a companion = a4j.Companion;
          if ((ta = this.a) != null) {
             companion.a(ta, oko5);
             return;
          }else {
             ckf.s();
             throw null;
          }
       }
    }
    public final a e(){
       IpChange $ipChange = a4j.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("53e15d68", objArray);
       }else {
          a4j ta = this.a;
          this.a = null;
          return ta;
       }
    }
    public final DinamicXEngine f(){
       IpChange $ipChange = a4j.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5bafc634", objArray);
    }
    public final ko5 g(){
       IpChange $ipChange = a4j.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("20dfd948", objArray);
    }
}
