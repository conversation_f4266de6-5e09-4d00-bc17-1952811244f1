package androidx.activity.OnBackPressedDispatcher;
import java.lang.Runnable;
import tb.a07;
import androidx.core.util.Consumer;
import java.lang.Object;
import tb.ob1;
import android.os.Build$VERSION;
import androidx.activity.OnBackPressedDispatcher$Api34Impl;
import androidx.activity.OnBackPressedDispatcher$1;
import androidx.activity.OnBackPressedDispatcher$2;
import androidx.activity.OnBackPressedDispatcher$3;
import androidx.activity.OnBackPressedDispatcher$4;
import tb.g1a;
import tb.d1a;
import android.window.OnBackInvokedCallback;
import androidx.activity.OnBackPressedDispatcher$Api33Impl;
import androidx.activity.OnBackPressedDispatcher$5;
import androidx.activity.OnBackPressedCallback;
import androidx.activity.BackEventCompat;
import java.util.List;
import java.util.ListIterator;
import java.util.Collection;
import java.util.Iterator;
import java.util.AbstractList;
import java.lang.Boolean;
import java.lang.String;
import tb.ckf;
import androidx.activity.Cancellable;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.Lifecycle$State;
import androidx.activity.OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;
import androidx.activity.OnBackPressedDispatcher$addCallback$1;
import androidx.activity.OnBackPressedDispatcher$OnBackPressedCancellable;
import androidx.activity.OnBackPressedDispatcher$addCancellableCallback$1;
import android.window.OnBackInvokedDispatcher;

public final class OnBackPressedDispatcher	// class@000462 from classes.dex
{
    private boolean backInvokedCallbackRegistered;
    private final Runnable fallbackOnBackPressed;
    private boolean hasEnabledCallbacks;
    private OnBackPressedCallback inProgressCallback;
    private OnBackInvokedDispatcher invokedDispatcher;
    private OnBackInvokedCallback onBackInvokedCallback;
    private final ob1 onBackPressedCallbacks;
    private final Consumer onHasEnabledCallbacksChanged;

    public void OnBackPressedDispatcher(){
       super(null, 1, null);
    }
    public void OnBackPressedDispatcher(Runnable p0){
       super(p0, null);
    }
    public void OnBackPressedDispatcher(Runnable p0,int p1,a07 p2){
       if ((p1 & 0x01)) {
          p0 = null;
       }
       super(p0);
       return;
    }
    public void OnBackPressedDispatcher(Runnable p0,Consumer p1){
       int sDK_INT;
       super();
       this.fallbackOnBackPressed = p0;
       this.onHasEnabledCallbacksChanged = p1;
       this.onBackPressedCallbacks = new ob1();
       if ((sDK_INT = Build$VERSION.SDK_INT) >= 33) {
          OnBackInvokedCallback onBackInvoke = (sDK_INT >= 34)? OnBackPressedDispatcher$Api34Impl.INSTANCE.createOnBackAnimationCallback(new OnBackPressedDispatcher$1(this), new OnBackPressedDispatcher$2(this), new OnBackPressedDispatcher$3(this), new OnBackPressedDispatcher$4(this)): OnBackPressedDispatcher$Api33Impl.INSTANCE.createOnBackInvokedCallback(new OnBackPressedDispatcher$5(this));
          this.onBackInvokedCallback = onBackInvoke;
       }
       return;
    }
    public static final OnBackPressedCallback access$getInProgressCallback$p(OnBackPressedDispatcher p0){
       return p0.inProgressCallback;
    }
    public static final ob1 access$getOnBackPressedCallbacks$p(OnBackPressedDispatcher p0){
       return p0.onBackPressedCallbacks;
    }
    public static final void access$onBackCancelled(OnBackPressedDispatcher p0){
       p0.onBackCancelled();
    }
    public static final void access$onBackProgressed(OnBackPressedDispatcher p0,BackEventCompat p1){
       p0.onBackProgressed(p1);
    }
    public static final void access$onBackStarted(OnBackPressedDispatcher p0,BackEventCompat p1){
       p0.onBackStarted(p1);
    }
    public static final void access$setInProgressCallback$p(OnBackPressedDispatcher p0,OnBackPressedCallback p1){
       p0.inProgressCallback = p1;
    }
    public static final void access$updateEnabledCallbacks(OnBackPressedDispatcher p0){
       p0.updateEnabledCallbacks();
    }
    private final void onBackCancelled(){
       OnBackPressedDispatcher tinProgressC;
       OnBackPressedCallback onBackPresse;
       if ((tinProgressC = this.inProgressCallback) == null) {
          tinProgressC = this.onBackPressedCallbacks;
          ListIterator listIterator = tinProgressC.listIterator(tinProgressC.size());
          while (true) {
             if (listIterator.hasPrevious()) {
                onBackPresse = listIterator.previous();
                if (!onBackPresse.isEnabled()) {
                   continue ;
                }
             }else {
                onBackPresse = null;
             }
             tinProgressC = onBackPresse;
          }
       }
       this.inProgressCallback = null;
       if (tinProgressC != null) {
          tinProgressC.handleOnBackCancelled();
       }
       return;
    }
    private final void onBackProgressed(BackEventCompat p0){
       OnBackPressedDispatcher tinProgressC;
       Object obj;
       if ((tinProgressC = this.inProgressCallback) == null) {
          tinProgressC = this.onBackPressedCallbacks;
          ListIterator listIterator = tinProgressC.listIterator(tinProgressC.size());
          while (true) {
             if (listIterator.hasPrevious()) {
                obj = listIterator.previous();
                if (!obj.isEnabled()) {
                   continue ;
                }
             }else {
                obj = 0;
             }
             tinProgressC = obj;
          }
       }
       if (tinProgressC != null) {
          tinProgressC.handleOnBackProgressed(p0);
       }
       return;
    }
    private final void onBackStarted(BackEventCompat p0){
       OnBackPressedCallback onBackPresse;
       OnBackPressedDispatcher tonBackPress = this.onBackPressedCallbacks;
       ListIterator listIterator = tonBackPress.listIterator(tonBackPress.size());
       while (true) {
          if (listIterator.hasPrevious()) {
             onBackPresse = listIterator.previous();
             if (!onBackPresse.isEnabled()) {
                continue ;
             }
          }else {
             onBackPresse = null;
          }
          if (this.inProgressCallback != null) {
             this.onBackCancelled();
          }
          this.inProgressCallback = onBackPresse;
          if (onBackPresse != null) {
             onBackPresse.handleOnBackStarted(p0);
             break ;
          }
          break ;
       }
       return;
    }
    private final void updateBackInvokedCallbackState(boolean p0){
       OnBackPressedDispatcher tinvokedDisp = this.invokedDispatcher;
       OnBackPressedDispatcher tonBackInvok = this.onBackInvokedCallback;
       if (tinvokedDisp != null && tonBackInvok != null) {
          if (p0 && this.backInvokedCallbackRegistered == null) {
             OnBackPressedDispatcher$Api33Impl.INSTANCE.registerOnBackInvokedCallback(tinvokedDisp, 0, tonBackInvok);
             this.backInvokedCallbackRegistered = true;
          }else if(!p0 && this.backInvokedCallbackRegistered != null){
             OnBackPressedDispatcher$Api33Impl.INSTANCE.unregisterOnBackInvokedCallback(tinvokedDisp, tonBackInvok);
             this.backInvokedCallbackRegistered = false;
          }
       }
       return;
    }
    private final void updateEnabledCallbacks(){
       OnBackPressedDispatcher thasEnabledC = this.hasEnabledCallbacks;
       OnBackPressedDispatcher tonBackPress = this.onBackPressedCallbacks;
       boolean b = false;
       if (!tonBackPress instanceof Collection || !tonBackPress.isEmpty()) {
          Iterator iterator = tonBackPress.iterator();
          while (iterator.hasNext()) {
             if (iterator.next().isEnabled()) {
                b = true;
                break ;
             }
          }
       }
       this.hasEnabledCallbacks = b;
       if (b != thasEnabledC) {
          if ((thasEnabledC = this.onHasEnabledCallbacksChanged) != null) {
             thasEnabledC.accept(Boolean.valueOf(b));
          }
          if (Build$VERSION.SDK_INT >= 33) {
             this.updateBackInvokedCallbackState(b);
          }
       }
       return;
    }
    public final void addCallback(OnBackPressedCallback p0){
       ckf.g(p0, "onBackPressedCallback");
       this.addCancellableCallback$activity_release(p0);
    }
    public final void addCallback(LifecycleOwner p0,OnBackPressedCallback p1){
       ckf.g(p0, "owner");
       ckf.g(p1, "onBackPressedCallback");
       Lifecycle lifecycle = p0.getLifecycle();
       if (lifecycle.getCurrentState() == Lifecycle$State.DESTROYED) {
          return;
       }
       p1.addCancellable(new OnBackPressedDispatcher$LifecycleOnBackPressedCancellable(this, lifecycle, p1));
       this.updateEnabledCallbacks();
       p1.setEnabledChangedCallback$activity_release(new OnBackPressedDispatcher$addCallback$1(this));
       return;
    }
    public final Cancellable addCancellableCallback$activity_release(OnBackPressedCallback p0){
       ckf.g(p0, "onBackPressedCallback");
       this.onBackPressedCallbacks.add(p0);
       OnBackPressedDispatcher$OnBackPressedCancellable onBackPresse = new OnBackPressedDispatcher$OnBackPressedCancellable(this, p0);
       p0.addCancellable(onBackPresse);
       this.updateEnabledCallbacks();
       p0.setEnabledChangedCallback$activity_release(new OnBackPressedDispatcher$addCancellableCallback$1(this));
       return onBackPresse;
    }
    public final void dispatchOnBackCancelled(){
       this.onBackCancelled();
    }
    public final void dispatchOnBackProgressed(BackEventCompat p0){
       ckf.g(p0, "backEvent");
       this.onBackProgressed(p0);
    }
    public final void dispatchOnBackStarted(BackEventCompat p0){
       ckf.g(p0, "backEvent");
       this.onBackStarted(p0);
    }
    public final boolean hasEnabledCallbacks(){
       return this.hasEnabledCallbacks;
    }
    public final void onBackPressed(){
       OnBackPressedDispatcher tinProgressC;
       OnBackPressedCallback onBackPresse;
       if ((tinProgressC = this.inProgressCallback) == null) {
          tinProgressC = this.onBackPressedCallbacks;
          ListIterator listIterator = tinProgressC.listIterator(tinProgressC.size());
          while (true) {
             if (listIterator.hasPrevious()) {
                onBackPresse = listIterator.previous();
                if (!onBackPresse.isEnabled()) {
                   continue ;
                }
             }else {
                onBackPresse = null;
             }
             tinProgressC = onBackPresse;
          }
       }
       this.inProgressCallback = null;
       if (tinProgressC != null) {
          tinProgressC.handleOnBackPressed();
          return;
       }else if((tinProgressC = this.fallbackOnBackPressed) != null){
          tinProgressC.run();
       }
       return;
    }
    public final void setOnBackInvokedDispatcher(OnBackInvokedDispatcher p0){
       ckf.g(p0, "invoker");
       this.invokedDispatcher = p0;
       this.updateBackInvokedCallbackState(this.hasEnabledCallbacks);
    }
}
