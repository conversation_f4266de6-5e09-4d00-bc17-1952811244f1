package tb.a;
import tb.t2o;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;
import java.util.Map;
import java.lang.Object;
import java.lang.ref.WeakReference;
import java.lang.System;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import java.lang.ref.Reference;

public class a	// class@00185b from classes5.dex
{
    public final WeakReference a;
    public final long b;
    public final int c;
    public final Map d;
    public static IpChange $ipChange;

    static {
       t2o.a(0x15c00009);
    }
    public void a(View p0,AccessibilityEvent p1,Map p2){
       super();
       this.c = -1;
       this.a = new WeakReference(p0);
       this.b = System.currentTimeMillis();
       this.c = p1.getEventType();
       this.d = p2;
    }
    public int a(){
       IpChange $ipChange = a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.c;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9ac77340", objArray).intValue();
    }
    public Map b(){
       IpChange $ipChange = a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.d;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("46c4490f", objArray);
    }
    public long c(){
       IpChange $ipChange = a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c0aad42f", objArray).longValue();
    }
    public View d(){
       a ta;
       View view;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("b9599910", objArray);
       }else if((ta = this.a) == null){
          view = null;
       }else {
          view = ta.get();
       }
       return view;
    }
}
