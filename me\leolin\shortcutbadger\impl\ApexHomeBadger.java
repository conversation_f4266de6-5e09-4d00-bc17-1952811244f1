package me.leolin.shortcutbadger.impl.ApexHomeBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.util.Arrays;
import android.content.Context;
import android.content.ComponentName;
import android.content.Intent;
import tb.ol2;
import me.leolin.shortcutbadger.ShortcutBadgeException;
import java.lang.StringBuilder;

public class ApexHomeBadger implements po1	// class@00075d from classes11.dex
{

    public void ApexHomeBadger(){
       super();
    }
    public List a(){
       String[] stringArray = new String[]{"com.anddoes.launcher"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       Intent intent = new Intent("com.anddoes.launcher.COUNTER_CHANGED");
       intent.putExtra("package", p1.getPackageName());
       intent.putExtra("count", p2);
       intent.putExtra("class", p1.getClassName());
       if (!ol2.a(p0, intent)) {
          throw new ShortcutBadgeException("unable to resolve intent: "+intent.toString());
       }
       p0.sendBroadcast(intent);
       return;
    }
}
