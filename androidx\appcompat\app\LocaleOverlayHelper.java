package androidx.appcompat.app.LocaleOverlayHelper;
import java.lang.Object;
import androidx.core.os.LocaleListCompat;
import java.util.LinkedHashSet;
import java.util.Locale;
import java.util.Set;
import android.os.LocaleList;
import tb.kah;

public final class LocaleOverlayHelper	// class@000580 from classes.dex
{

    private void LocaleOverlayHelper(){
       super();
    }
    private static LocaleListCompat combineLocales(LocaleListCompat p0,LocaleListCompat p1){
       Locale locale;
       LinkedHashSet linkedHashSe = new LinkedHashSet();
       int i = 0;
       while (true) {
          int i1 = p0.size() + p1.size();
          if (i < i1) {
             if (i < p0.size()) {
                locale = p0.get(i);
             }else {
                i1 = i - p0.size();
                locale = p1.get(i1);
             }
             if (locale != null) {
                linkedHashSe.add(locale);
             }
             i = i + 1;
          }else {
             break ;
          }
       }
       Locale[] localeArray = new Locale[linkedHashSe.size()];
       return LocaleListCompat.create(linkedHashSe.toArray(localeArray));
    }
    public static LocaleListCompat combineLocalesIfOverlayExists(LocaleList p0,LocaleList p1){
       if (p0 != null && !kah.a(p0)) {
          return LocaleOverlayHelper.combineLocales(LocaleListCompat.wrap(p0), LocaleListCompat.wrap(p1));
       }
       return LocaleListCompat.getEmptyLocaleList();
    }
    public static LocaleListCompat combineLocalesIfOverlayExists(LocaleListCompat p0,LocaleListCompat p1){
       if (p0 != null && !p0.isEmpty()) {
          return LocaleOverlayHelper.combineLocales(p0, p1);
       }
       return LocaleListCompat.getEmptyLocaleList();
    }
}
