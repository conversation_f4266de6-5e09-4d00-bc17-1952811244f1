package tb.a51;
import tb.kti;
import com.taobao.metrickit.context.MetricContext;
import tb.c0c;
import tb.i91;
import tb.k04;
import tb.c0c$a;
import java.lang.String;
import java.lang.System;
import android.os.SystemClock;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.h91;
import com.android.alibaba.ip.runtime.IpChange;

public class a51 extends kti	// class@001af3 from classes8.dex
{
    public static IpChange $ipChange;

    public void a51(MetricContext p0,c0c p1,i91 p2){
       super(p0, p1, p2);
       p1.c().putLong("processStartTime", p0.getProcessStartTime()).putLong("appLauncherStartTime", p0.getLauncherStartTime()).putLong("startupTimestampInterval", (System.currentTimeMillis() - SystemClock.uptimeMillis())).commit();
    }
    public static Object ipc$super(a51 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/metrickit/processor/time/AppFgTimeMetricProcessor");
    }
    public void a(Object p0){
       this.h(p0);
    }
    public int[] c(){
       int i = 1;
       IpChange $ipChange = a51.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          return $ipChange.ipc$dispatch("df81d3cb", objArray);
       }else {
          int[] ointArray = new int[]{i};
          return ointArray;
       }
    }
    public void h(h91 p0){
       IpChange $ipChange = a51.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("895fd39d", objArray);
          return;
       }else if(!(p0.a())){
          return;
       }else {
          c0c$a uoa = this.b.c();
          uoa.putLong("cumulativeForegroundTime", (p0.a() + this.b.getLong("cumulativeForegroundTime", 0)));
          uoa.commit();
          return;
       }
    }
}
