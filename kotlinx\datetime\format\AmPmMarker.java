package kotlinx.datetime.format.AmPmMarker;
import java.lang.Enum;
import java.lang.String;
import tb.fg8;
import kotlin.enums.a;
import java.lang.Class;
import java.lang.Object;

public final class AmPmMarker extends Enum	// class@0006d4 from classes11.dex
{
    private static final fg8 $ENTRIES;
    private static final AmPmMarker[] $VALUES;
    public static final AmPmMarker AM;
    public static final AmPmMarker PM;

    private static final AmPmMarker[] $values(){
       AmPmMarker[] uAmPmMarkerA = new AmPmMarker[]{AmPmMarker.AM,AmPmMarker.PM};
       return uAmPmMarkerA;
    }
    static {
       AmPmMarker.AM = new AmPmMarker("AM", 0);
       AmPmMarker.PM = new AmPmMarker("PM", 1);
       AmPmMarker[] uAmPmMarkerA = AmPmMarker.$values();
       AmPmMarker.$VALUES = uAmPmMarkerA;
       AmPmMarker.$ENTRIES = a.a(uAmPmMarkerA);
    }
    private void AmPmMarker(String p0,int p1){
       super(p0, p1);
    }
    public static fg8 getEntries(){
       return AmPmMarker.$ENTRIES;
    }
    public static AmPmMarker valueOf(String p0){
       return Enum.valueOf(AmPmMarker.class, p0);
    }
    public static AmPmMarker[] values(){
       return AmPmMarker.$VALUES.clone();
    }
}
