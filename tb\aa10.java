package tb.aa10;
import tb.t2o;
import java.lang.Object;
import tb.g1a;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public final class aa10	// class@001b13 from classes8.dex
{
    public g1a a;
    public g1a b;
    public static IpChange $ipChange;

    static {
       t2o.a(0x403000f1);
    }
    public void aa10(){
       super();
    }
    public final g1a a(){
       IpChange $ipChange = aa10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("aa60c0e2", objArray);
    }
    public final g1a b(){
       IpChange $ipChange = aa10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("b178fc7", objArray);
    }
    public final void c(g1a p0){
       IpChange $ipChange = aa10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8bb9f74", objArray);
          return;
       }else {
          this.b = p0;
          return;
       }
    }
    public final void d(g1a p0){
       IpChange $ipChange = aa10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c2c3a02f", objArray);
          return;
       }else {
          this.a = p0;
          return;
       }
    }
}
