package me.leolin.shortcutbadger.impl.OPPOHomeBader;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.util.Collections;
import android.content.Context;
import android.content.ComponentName;
import android.content.Intent;
import java.lang.String;
import tb.ol2;
import android.os.Bundle;
import android.os.BaseBundle;
import android.content.ContentResolver;
import android.net.Uri;
import me.leolin.shortcutbadger.ShortcutBadgeException;
import java.lang.StringBuilder;
import java.lang.Class;
import java.lang.reflect.Method;
import java.lang.reflect.AccessibleObject;
import java.lang.Throwable;
import java.lang.Integer;
import java.lang.Runtime;
import java.lang.Process;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.InputStream;
import java.io.Reader;
import java.io.Closeable;
import tb.cw3;

public class OPPOHomeBader implements po1	// class@000765 from classes11.dex
{
    public static int a = 255;

    public void OPPOHomeBader(){
       super();
    }
    public List a(){
       return Collections.singletonList("com.oppo.launcher");
    }
    public void b(Context p0,ComponentName p1,int p2){
       if (!p2) {
          p2 = -1;
       }
       Intent intent = new Intent("com.oppo.unsettledevent");
       intent.putExtra("pakeageName", p1.getPackageName());
       intent.putExtra("number", p2);
       intent.putExtra("upgradeNumber", p2);
       if (ol2.a(p0, intent)) {
          p0.sendBroadcast(intent);
       }else if(this.g() == 6){
          Bundle uBundle = new Bundle();
          uBundle.putInt("app_badge_count", p2);
          p0.getContentResolver().call(Uri.parse("content://com.android.badge/badge"), "setAppBadgeCount", null, uBundle);
       }
       return;
    }
    public final boolean c(Object p0){
       boolean b = (p0 != null && (!p0.toString().equals("") && !p0.toString().trim().equals("null")))? false: true;
       return b;
    }
    public final Object d(Class p0,String p1,Class[] p2,Object[] p3){
       Method method;
       Object obj = null;
       if (p0 != null && (!this.c(p1) && (method = this.f(p0, p1, p2)) != null)) {
          try{
             method.setAccessible(true);
             obj = method.invoke(obj, p3);
          }catch(java.lang.IllegalAccessException e3){
             e3.printStackTrace();
          }catch(java.lang.reflect.InvocationTargetException e3){
             e3.printStackTrace();
          }
       }
       return obj;
    }
    public final Class e(String p0){
       Class uClass;
       try{
          uClass = Class.forName(p0);
       }catch(java.lang.ClassNotFoundException e0){
          uClass = null;
       }
       return uClass;
    }
    public final Method f(Class p0,String p1,Class[] p2){
       if (p0 != null && !this.c(p1)) {
          try{
             p0.getMethods();
             p0.getDeclaredMethods();
             return p0.getDeclaredMethod(p1, p2);
          }catch(java.lang.Exception e0){
             try{
                return p0.getMethod(p1, p2);
             }catch(java.lang.Exception e0){
                if (p0.getSuperclass() != null) {
                   Method method = this.f(p0.getSuperclass(), p1, p2);
                }
             }
          label_0028 :
             return null;
          }
       }else {
          goto label_0028 ;
       }
    }
    public final int g(){
       int a;
       if ((a = OPPOHomeBader.a) >= 0) {
          return a;
       }
       try{
          a = this.d(this.e("com.color.os.ColorBuild"), "getColorOSVERSION", null, null).intValue();
       }catch(java.lang.Exception e0){
          a = 0;
       }
       if (!a) {
          try{
             String str = this.h("ro.build.version.opporom");
             if (str.startsWith("V1.4")) {
                return 3;
             }else if(str.startsWith("V2.0")){
                return 4;
             }else if(str.startsWith("V2.1")){
                return 5;
             }
          }catch(java.lang.Exception e0){
          }
       }
       OPPOHomeBader.a = e0;
       return e0;
    }
    public final String h(String p0){
       Closeable uCloseable;
       String str = "getprop ";
       String str1 = null;
       try{
          InputStreamReader inputStreamR = new InputStreamReader(Runtime.getRuntime().exec(str+p0).getInputStream());
          int i = 1024;
          try{
             BufferedReader str2 = new BufferedReader(inputStreamR, i);
             str2.close();
             cw3.b(str2);
             return str2.readLine();
          }catch(java.io.IOException e0){
             cw3.b(uCloseable);
             return str1;
          }
       }catch(java.io.IOException e0){
          uCloseable = str1;
       }
    }
}
