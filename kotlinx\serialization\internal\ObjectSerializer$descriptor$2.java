package kotlinx.serialization.internal.ObjectSerializer$descriptor$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.lang.String;
import kotlinx.serialization.internal.ObjectSerializer;
import java.lang.Object;
import kotlinx.serialization.descriptors.a;
import kotlinx.serialization.descriptors.b$d;
import kotlinx.serialization.internal.ObjectSerializer$descriptor$2$1;
import tb.ob40;
import tb.g1a;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;

public final class ObjectSerializer$descriptor$2 extends Lambda implements d1a	// class@000744 from classes11.dex
{
    public final String $serialName;
    public final ObjectSerializer this$0;

    public void ObjectSerializer$descriptor$2(String p0,ObjectSerializer p1){
       this.$serialName = p0;
       this.this$0 = p1;
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final a invoke(){
       a[] uoaArray = new a[0];
       return SerialDescriptorsKt.c(this.$serialName, b$d.INSTANCE, uoaArray, new ObjectSerializer$descriptor$2$1(this.this$0));
    }
}
