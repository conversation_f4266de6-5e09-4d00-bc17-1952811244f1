package tb.a3h$s0;
import java.lang.String;
import java.lang.Boolean;
import java.util.ArrayList;

public interface abstract a3h$s0	// class@001e88 from classes10.dex
{

    void a(String p0,String p1,String p2);
    void b(String p0);
    void c(String p0);
    void d(Boolean p0,ArrayList p1);
    void e(String p0);
    void f(String p0,int p1,String p2);
    void g(String p0);
    void h(String p0,String p1,String p2);
    void i(String p0,String p1);
    void j(ArrayList p0);
    void k(String p0);
    void l(String p0);
    void m(ArrayList p0);
    void n(String p0,int p1,String p2,String p3);
    void o(String p0,int p1,String p2,String p3);
    void p(String p0,String p1,int p2,String p3);
    void q(String p0,String p1,String p2);
}
