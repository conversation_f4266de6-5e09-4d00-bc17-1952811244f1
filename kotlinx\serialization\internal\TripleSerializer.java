package kotlinx.serialization.internal.TripleSerializer;
import tb.x530;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import kotlinx.serialization.descriptors.a;
import kotlinx.serialization.internal.TripleSerializer$descriptor$1;
import tb.g1a;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;
import tb.gn20;
import kotlin.Triple;
import tb.ha20;
import tb.g43;
import tb.qb40;

public final class TripleSerializer implements x530	// class@000751 from classes11.dex
{
    public final x530 a;
    public final x530 b;
    public final x530 c;
    public final a d;

    public void TripleSerializer(x530 p0,x530 p1,x530 p2){
       ckf.g(p0, "aSerializer");
       ckf.g(p1, "bSerializer");
       ckf.g(p2, "cSerializer");
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
       a[] uoaArray = new a[0];
       this.d = SerialDescriptorsKt.b("kotlin.Triple", uoaArray, new TripleSerializer$descriptor$1(this));
    }
    public static final x530 c(TripleSerializer p0){
       return p0.a;
    }
    public static final x530 d(TripleSerializer p0){
       return p0.b;
    }
    public static final x530 e(TripleSerializer p0){
       return p0.c;
    }
    public void f(gn20 p0,Triple p1){
       ckf.g(p0, "encoder");
       ckf.g(p1, "value");
       ha20 oha20 = p0.b(this.getDescriptor());
       oha20.z(this.getDescriptor(), 0, this.a, p1.getFirst());
       oha20.z(this.getDescriptor(), 1, this.b, p1.getSecond());
       oha20.z(this.getDescriptor(), 2, this.c, p1.getThird());
       oha20.s(this.getDescriptor());
    }
    public a getDescriptor(){
       return this.d;
    }
    public void serialize(gn20 p0,Object p1){
       this.f(p0, p1);
    }
}
