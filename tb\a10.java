package tb.a10;
import tb.h4c;
import tb.t2o;
import java.lang.Object;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.lang.String;
import java.lang.Runnable;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import tb.a10$a;
import android.os.Looper;
import com.taobao.android.dinamicx.DinamicXEngine;
import java.lang.StringBuilder;
import tb.h36;
import java.util.Set;
import java.util.Map;
import tb.a10$b;
import android.util.LruCache;
import java.lang.Long;
import android.os.Handler;
import java.lang.Number;
import java.util.Iterator;
import java.util.Map$Entry;
import java.util.List;
import java.util.ArrayList;
import android.util.Pair;

public abstract class a10 implements h4c	// class@00181f from classes5.dex
{
    public Handler a;
    public final Map b;
    public LruCache c;
    public final Map d;
    public final Map e;
    public final Set f;
    public final Set g;
    public String h;
    public static IpChange $ipChange;
    public static final int DEFAULT_CACHE_SIZE;
    public static final long DEFAULT_EXPOSE_DELAY;

    static {
       t2o.a(0x1c500322);
       t2o.a(0x1c500328);
    }
    public void a10(){
       super();
       this.b = new HashMap();
       this.d = new LinkedHashMap();
       this.e = new HashMap();
       this.f = new HashSet();
       this.g = new LinkedHashSet();
    }
    public Runnable A(Object p0,Object p1,boolean p2,String p3){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Boolean(p2),p3};
          return $ipChange.ipc$dispatch("a4d97639", objArray);
       }else {
          a10$a v6 = new a10$a(this, p2, p0, p1, p3);
          return v6;
       }
    }
    public Looper B(){
       IpChange $ipChange = a10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Looper.getMainLooper();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c7f5376b", objArray);
    }
    public abstract void C(Object p0,Object p1,String p2);
    public void D(Object p0,Object p1,boolean p2,String p3){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Boolean(p2),p3};
          $ipChange.ipc$dispatch("a0135812", objArray);
          return;
       }else if(p2){
          if (DinamicXEngine.j0()) {
             p1 = new String[]{"batch onDataExpose success at key: "+p0};
             h36.g("DXExposure", p1);
          }
          this.g.add(p0);
          return;
       }else {
          this.C(p0, p1, p3);
          return;
       }
    }
    public void E(){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a6532022", objArray);
       }
       return;
    }
    public void F(){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("ce3e3eaf", objArray);
       }
       return;
    }
    public void G(Runnable p0,Object p1,Object p2,boolean p3,String p4){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,new Boolean(p3),p4};
          $ipChange.ipc$dispatch("59fc62cd", objArray);
          return;
       }else {
          this.b.remove(p1);
          if (this.z(p1, p2, p4)) {
             return;
          }
          if (!this.H(p1, p2, p4, this.n())) {
             this.c.put(p1, new a10$b(p2, p4, p3, p0));
             return;
          }else {
             this.D(p1, p2, p3, p4);
             this.J(p1, p4);
             return;
          }
       }
    }
    public abstract boolean H(Object p0,Object p1,String p2,Map p3);
    public final void I(Object p0,Object p1,String p2,boolean p3,long p4){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,new Boolean(p3),new Long(p4)};
          $ipChange.ipc$dispatch("5914f2a2", objArray);
          return;
       }else {
          Runnable runnable = this.A(p0, p1, p3, p2);
          a10$b uob = new a10$b(p1, p2, p3, runnable);
          this.b.put(p0, uob);
          this.a.postDelayed(runnable, p4);
          if (p3) {
             if (DinamicXEngine.j0()) {
                p1 = new String[]{"batch postExposeTask at key: "+p0};
                h36.g("DXExposure", p1);
             }
             this.e.put(p0, uob);
          }
          return;
       }
    }
    public Object J(Object p0,String p1){
       a10$b uob;
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("5fb8387", objArray);
       }else if(!this.v()){
          return null;
       }else if((uob = this.c.remove(p0)) != null){
          this.K(p0, uob);
          return uob.a;
       }else {
          return null;
       }
    }
    public final void K(Object p0,a10$b p1){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("679d9b72", objArray);
          return;
       }else if(p1 == null){
          return;
       }else {
          this.a.removeCallbacks(p1.d);
          if (p1.c != null && (this.u() && p0 != null)) {
             this.f.add(p0);
             this.y();
          }
          return;
       }
    }
    public void a(){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b1afb60e", objArray);
          return;
       }else if(!this.v()){
          this.a = this.p();
          this.c = this.o();
          this.F();
       }
       return;
    }
    public int b(){
       IpChange $ipChange = a10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return 8;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ae85ed9d", objArray).intValue();
    }
    public void c(String p0){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("861a892d", objArray);
          return;
       }else {
          Iterator iterator = this.d.entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             Object key = uEntry.getKey();
             this.l(key, uEntry.getValue(), p0);
          }
          return;
       }
    }
    public void d(){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("9cf7aba5", objArray);
          return;
       }else if(this.v() && this.c.size()){
          Iterator iterator = this.c.snapshot().entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             Object key = uEntry.getKey();
             this.q(key, uEntry.getValue().a, uEntry.getValue().b);
          }
       }
       return;
    }
    public void destroy(){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("89c49781", objArray);
          return;
       }else if(this.v()){
          this.a = null;
          this.E();
       }
       return;
    }
    public void e(Object p0,String p1){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("6a349c8b", objArray);
          return;
       }else {
          this.f(p0, p1, 1);
          return;
       }
    }
    public void f(Object p0,String p1,boolean p2){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Boolean(p2)};
          $ipChange.ipc$dispatch("dc5fa7a9", objArray);
          return;
       }else {
          Object obj = this.J(p0, p1);
          this.K(p0, this.b.remove(p0));
          if (p2) {
             this.w(p0, obj, p1);
          }
          return;
       }
    }
    public Map g(){
       IpChange $ipChange = a10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new HashMap(this.d);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c7c9e86e", objArray);
    }
    public Map h(){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("22643da7", objArray);
       }else {
          this.d.clear();
          return new HashMap(this.d);
       }
    }
    public void i(String p0){
       int i = 0;
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("74000ea1", objArray);
          return;
       }else if(!this.v()){
          return;
       }else {
          Iterator iterator = this.d.entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             Object key = uEntry.getKey();
             this.A(key, uEntry.getValue(), i, p0).run();
          }
          return;
       }
    }
    public void j(Object p0,Object p1){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("3ed8580e", objArray);
          return;
       }else {
          this.d.put(p0, p1);
          return;
       }
    }
    public void k(String p0,List p1){
       String[] stringArray;
       Iterator iterator;
       Object obj1;
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("cdeca7f3", objArray);
          return;
       }else if(this.u()){
          if (DinamicXEngine.j0()) {
             stringArray = new String[]{"isBatchExposing skip"};
             h36.g("DXExposure", stringArray);
          }
          return;
       }else {
          this.h = p0;
          if (DinamicXEngine.j0()) {
             String[] stringArray1 = new String[]{"start batchTriggerExpose"};
             h36.g("DXExposure", stringArray1);
          }
          if (p1 != null && !p1.isEmpty()) {
             iterator = p1.iterator();
             while (iterator.hasNext()) {
                Object obj = iterator.next();
                if ((obj1 = this.d.get(obj)) == null) {
                   continue ;
                }
                this.s(obj, obj1, p0, true, this.r());
             }
          }else {
             iterator = this.d.entrySet().iterator();
             while (iterator.hasNext()) {
                Map$Entry uEntry = iterator.next();
                this.s(uEntry.getKey(), uEntry.getValue(), p0, true, this.r());
             }
          }
          if (this.e.isEmpty()) {
             this.h = null;
             if (DinamicXEngine.j0()) {
                stringArray = new String[]{"end batchTriggerExpose pendingTasks.isEmpty"};
                h36.g("DXExposure", stringArray);
             }
          }
          return;
       }
    }
    public void l(Object p0,Object p1,String p2){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("cc791c8d", objArray);
          return;
       }else {
          this.s(p0, p1, p2, false, this.r());
          return;
       }
    }
    public void m(Object p0,String p1){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("d48fc2df", objArray);
          return;
       }else {
          this.d.remove(p0);
          return;
       }
    }
    public final Map n(){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("d5f9dd13", objArray);
       }else {
          HashMap hashMap = new HashMap();
          Iterator iterator = this.c.snapshot().entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             Object key = uEntry.getKey();
             hashMap.put(key, uEntry.getValue().a);
          }
          return hashMap;
       }
    }
    public LruCache o(){
       IpChange $ipChange = a10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new LruCache(this.b());
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("cd040c5e", objArray);
    }
    public Handler p(){
       IpChange $ipChange = a10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new Handler(this.B());
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("93331871", objArray);
    }
    public void q(Object p0,Object p1,String p2){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("a980ba01", objArray);
          return;
       }else {
          this.s(p0, p1, p2, false, 0);
          return;
       }
    }
    public long r(){
       IpChange $ipChange = a10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return 0;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a200cd1a", objArray).longValue();
    }
    public final void s(Object p0,Object p1,String p2,boolean p3,long p4){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,new Boolean(p3),new Long(p4)};
          $ipChange.ipc$dispatch("c395011a", objArray);
          return;
       }else if(!this.v()){
          return;
       }else {
          this.K(p0, this.b.remove(p0));
          this.I(p0, p1, p2, p3, p4);
          return;
       }
    }
    public boolean t(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("2aee3231", objArray).booleanValue();
       }else if(this.f.equals(this.e.keySet()) && !this.e.isEmpty()){
          i = true;
       }
       return i;
    }
    public boolean u(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("7d53bdc", objArray).booleanValue();
       }else if(this.h != null){
          i = true;
       }
       return i;
    }
    public final boolean v(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("59e1a492", objArray).booleanValue();
       }else if(this.a != null){
          i = true;
       }
       return i;
    }
    public void w(Object p0,Object p1,String p2){
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("c0576d0e", objArray);
       }
       return;
    }
    public abstract void x(List p0,String p1);
    public void y(){
       a10$b uob;
       IpChange $ipChange = a10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("42376d1f", objArray);
          return;
       }else if(!this.t()){
          return;
       }else {
          ArrayList uArrayList = new ArrayList();
          Iterator iterator = this.g.iterator();
          while (iterator.hasNext()) {
             Object obj = iterator.next();
             if ((uob = this.e.get(obj)) != null) {
                uArrayList.add(new Pair(obj, uob.a));
             }
          }
          if (DinamicXEngine.j0()) {
             String[] stringArray = new String[]{"finished onBatchDataExpose batchSize: "+uArrayList.size()};
             h36.g("DXExposure", stringArray);
          }
          this.x(uArrayList, this.h);
          this.e.clear();
          this.f.clear();
          this.g.clear();
          this.h = null;
          return;
       }
    }
    public boolean z(Object p0,Object p1,String p2){
       int i = 0;
       IpChange $ipChange = a10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return i;
       }
       Object[] objArray = new Object[]{this,p0,p1,p2};
       return $ipChange.ipc$dispatch("8d7a8bb9", objArray).booleanValue();
    }
}
