package kotlinx.serialization.SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE_NULLABLE$1$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.util.List;
import java.lang.Object;
import tb.v530;
import tb.e1g;

public final class SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE_NULLABLE$1$1 extends Lambda implements d1a	// class@00071d from classes11.dex
{
    public final List $types;

    public void SerializersCacheKt$PARAMETRIZED_SERIALIZERS_CACHE_NULLABLE$1$1(List p0){
       this.$types = p0;
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final v530 invoke(){
       return this.$types.get(0).g();
    }
}
