package tb.a3h$j0;
import com.taobao.living.api.TBLiveMediaSDKEngine$a;
import tb.a3h;
import java.lang.Object;
import com.taobao.artc.api.ArtcExternalAudioProcess$AudioFrame;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class a3h$j0 implements TBLiveMediaSDKEngine$a	// class@001e76 from classes10.dex
{
    public final a3h a;
    public static IpChange $ipChange;

    public void a3h$j0(a3h p0){
       super();
       this.a = p0;
    }
    public void a(ArtcExternalAudioProcess$AudioFrame p0){
       IpChange $ipChange = a3h$j0.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("9951794d", objArray);
          return;
       }else if(a3h.D(this.a) != null){
          a3h.D(this.a).a(p0);
       }
       return;
    }
}
