package tb.abt$a;
import com.taobao.taolive.room.openarchitecture.gateway.command.type.TaoliveGatewayEnum;
import java.lang.Enum;

public class abt$a	// class@00177e from classes9.dex
{
    public static final int[] $SwitchMap$com$taobao$taolive$room$openarchitecture$gateway$command$type$TaoliveGatewayEnum;

    static {
       int[] ointArray = new int[TaoliveGatewayEnum.values().length];
       try{
          abt$a.$SwitchMap$com$taobao$taolive$room$openarchitecture$gateway$command$type$TaoliveGatewayEnum = ointArray;
          ointArray[TaoliveGatewayEnum.TaoliveGateway_Create_OpenLiveRoom.ordinal()] = 1;
          try{
             abt$a.$SwitchMap$com$taobao$taolive$room$openarchitecture$gateway$command$type$TaoliveGatewayEnum[TaoliveGatewayEnum.TaoliveGateway_Create_OpenLiveRoomView.ordinal()] = 2;
             try{
                abt$a.$SwitchMap$com$taobao$taolive$room$openarchitecture$gateway$command$type$TaoliveGatewayEnum[TaoliveGatewayEnum.TaoliveGateway_Create_OpenLiveCompontent.ordinal()] = 3;
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
