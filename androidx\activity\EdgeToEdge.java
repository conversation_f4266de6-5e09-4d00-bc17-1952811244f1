package androidx.activity.EdgeToEdge;
import android.graphics.Color;
import androidx.activity.ComponentActivity;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.activity.SystemBarStyle;
import android.view.Window;
import android.app.Activity;
import android.view.View;
import tb.g1a;
import android.content.res.Resources;
import java.lang.Boolean;
import android.os.Build$VERSION;
import androidx.activity.EdgeToEdgeApi30;
import androidx.activity.EdgeToEdgeApi29;
import androidx.activity.EdgeToEdgeApi28;
import androidx.activity.EdgeToEdgeApi26;
import androidx.activity.EdgeToEdgeApi23;
import androidx.activity.EdgeToEdgeApi21;
import androidx.activity.EdgeToEdgeImpl;
import androidx.activity.SystemBarStyle$Companion;

public final class EdgeToEdge	// class@000442 from classes.dex
{
    private static final int DefaultDarkScrim;
    private static final int DefaultLightScrim;
    private static EdgeToEdgeImpl Impl;

    static {
       EdgeToEdge.DefaultLightScrim = Color.argb(230, 255, 255, 255);
       EdgeToEdge.DefaultDarkScrim = Color.argb(128, 27, 27, 27);
    }
    public static final void enable(ComponentActivity p0){
       ckf.g(p0, "<this>");
       EdgeToEdge.enable$default(p0, null, null, 3, null);
    }
    public static final void enable(ComponentActivity p0,SystemBarStyle p1){
       ckf.g(p0, "<this>");
       ckf.g(p1, "statusBarStyle");
       EdgeToEdge.enable$default(p0, p1, null, 2, null);
    }
    public static final void enable(ComponentActivity p0,SystemBarStyle p1,SystemBarStyle p2){
       EdgeToEdgeImpl impl;
       int sDK_INT;
       Window window;
       ckf.g(p0, "<this>");
       ckf.g(p1, "statusBarStyle");
       ckf.g(p2, "navigationBarStyle");
       View decorView = p0.getWindow().getDecorView();
       ckf.f(decorView, "window.decorView");
       Resources resources = decorView.getResources();
       String str = "view.resources";
       ckf.f(resources, str);
       boolean b = p1.getDetectDarkMode$activity_release().invoke(resources).booleanValue();
       resources = decorView.getResources();
       ckf.f(resources, str);
       boolean b1 = p2.getDetectDarkMode$activity_release().invoke(resources).booleanValue();
       if ((impl = EdgeToEdge.Impl) == null) {
          if ((sDK_INT = Build$VERSION.SDK_INT) >= 30) {
             impl = new EdgeToEdgeApi30();
          }else if(sDK_INT >= 29){
             impl = new EdgeToEdgeApi29();
          }else if(sDK_INT >= 28){
             impl = new EdgeToEdgeApi28();
          }else if(sDK_INT >= 26){
             impl = new EdgeToEdgeApi26();
          }else if(sDK_INT >= 23){
             impl = new EdgeToEdgeApi23();
          }else {
             impl = new EdgeToEdgeApi21();
             EdgeToEdge.Impl = impl;
          }
       }
       window = p0.getWindow();
       ckf.f(window, "window");
       impl.setUp(p1, p2, window, decorView, b, b1);
       Window window1 = p0.getWindow();
       ckf.f(window1, "window");
       impl.adjustLayoutInDisplayCutoutMode(window1);
       return;
    }
    public static void enable$default(ComponentActivity p0,SystemBarStyle p1,SystemBarStyle p2,int p3,Object p4){
       if ((p3 & 0x01)) {
          p1 = SystemBarStyle$Companion.auto$default(SystemBarStyle.Companion, 0, 0, null, 4, null);
       }
       if ((p3 & 0x02)) {
          p2 = SystemBarStyle$Companion.auto$default(SystemBarStyle.Companion, EdgeToEdge.DefaultLightScrim, EdgeToEdge.DefaultDarkScrim, null, 4, null);
       }
       EdgeToEdge.enable(p0, p1, p2);
       return;
    }
    public static final int getDefaultDarkScrim(){
       return EdgeToEdge.DefaultDarkScrim;
    }
    public static void getDefaultDarkScrim$annotations(){
    }
    public static final int getDefaultLightScrim(){
       return EdgeToEdge.DefaultLightScrim;
    }
    public static void getDefaultLightScrim$annotations(){
    }
}
