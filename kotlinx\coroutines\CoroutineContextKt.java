package kotlinx.coroutines.CoroutineContextKt;
import kotlin.coroutines.d;
import kotlin.jvm.internal.Ref$ObjectRef;
import kotlin.coroutines.EmptyCoroutineContext;
import kotlinx.coroutines.CoroutineContextKt$foldCopies$folded$1;
import java.lang.Object;
import tb.u1a;
import kotlinx.coroutines.CoroutineContextKt$foldCopies$1;
import java.lang.String;
import tb.dv6;
import kotlinx.coroutines.d;
import kotlin.coroutines.d$c;
import kotlin.coroutines.d$b;
import kotlinx.coroutines.e;
import java.lang.StringBuilder;
import java.lang.Boolean;
import kotlinx.coroutines.CoroutineContextKt$hasCopyableElements$1;
import tb.uu4;
import java.util.concurrent.atomic.AtomicLong;
import kotlinx.coroutines.CoroutineDispatcher;
import tb.zq7;
import kotlin.coroutines.c;
import tb.vu4;
import tb.ogv;
import kotlinx.coroutines.i;
import tb.ar4;
import tb.qgv;

public final class CoroutineContextKt	// class@000490 from classes11.dex
{

    public static final d a(d p0,d p1,boolean p2){
       boolean b = CoroutineContextKt.c(p1);
       if (!CoroutineContextKt.c(p0) && !b) {
          return p0.plus(p1);
       }
       Ref$ObjectRef objectRef = new Ref$ObjectRef();
       objectRef.element = p1;
       EmptyCoroutineContext iNSTANCE = EmptyCoroutineContext.INSTANCE;
       p0 = p0.fold(iNSTANCE, new CoroutineContextKt$foldCopies$folded$1(objectRef, p2));
       if (b) {
          objectRef.element = objectRef.element.fold(iNSTANCE, CoroutineContextKt$foldCopies$1.INSTANCE);
       }
       return p0.plus(objectRef.element);
    }
    public static final String b(d p0){
       d uod;
       String str = null;
       if (!dv6.b()) {
          return str;
       }
       if ((uod = p0.get(d.Key)) == null) {
          return str;
       }
       e uoe = p0.get(e.Key);
       return "coroutine#"+uod.N0();
    }
    public static final boolean c(d p0){
       return p0.fold(Boolean.FALSE, CoroutineContextKt$hasCopyableElements$1.INSTANCE).booleanValue();
    }
    public static final d d(d p0,d p1){
       if (!CoroutineContextKt.c(p1)) {
          return p0.plus(p1);
       }
       return CoroutineContextKt.a(p0, p1, false);
    }
    public static final d e(uu4 p0,d p1){
       d uod = CoroutineContextKt.a(p0.getCoroutineContext(), p1, true);
       p1 = (dv6.b())? uod.plus(new d(dv6.a().incrementAndGet())): uod;
       if (uod != zq7.a() && uod.get(c.Key) == null) {
          p1 = p1.plus(zq7.a());
       }
       return p1;
    }
    public static final ogv f(vu4 p0){
       while (true) {
          if (p0 instanceof i) {
             return null;
          }
          if ((p0 = p0.getCallerFrame()) == null) {
             return null;
          }
          if (p0 instanceof ogv) {
             break ;
          }
       }
       return p0;
    }
    public static final ogv g(ar4 p0,d p1,Object p2){
       ogv oogv;
       if (!p0 instanceof vu4) {
          return null;
       }
       if (p1.get(qgv.INSTANCE) == null) {
          return null;
       }
       if ((oogv = CoroutineContextKt.f(p0)) != null) {
          oogv.x1(p1, p2);
       }
       return oogv;
    }
}
