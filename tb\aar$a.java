package tb.aar$a;
import java.lang.Runnable;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import com.ut.mini.internal.UTOriginalCustomHitBuilder;
import java.util.Map;
import com.ut.mini.UTAnalytics;
import com.ut.mini.UTTracker;
import tb.g1v;

public final class aar$a implements Runnable	// class@001860 from classes5.dex
{
    public final String a;
    public static IpChange $ipChange;

    public void aar$a(String p0){
       this.a = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = aar$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          UTOriginalCustomHitBuilder $ipChange1 = new UTOriginalCustomHitBuilder("Page_Order", 0x4e1d, this.a, null, null, null);
          UTAnalytics.getInstance().getDefaultTracker().send($ipChange.build());
          return;
       }
    }
}
