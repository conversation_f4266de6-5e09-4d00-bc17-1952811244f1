package mtopsdk.mtop.upload.DefaultFileUploadListener;
import mtopsdk.mtop.upload.FileUploadBaseListener;
import tb.t2o;
import java.lang.Object;
import mtopsdk.mtop.upload.FileUploadListener;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;
import java.lang.StringBuilder;
import java.lang.Thread;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import java.lang.Integer;

public class DefaultFileUploadListener implements FileUploadBaseListener	// class@0007fd from classes11.dex
{
    private FileUploadListener oldListener;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x25900001);
       t2o.a(0x25900003);
    }
    public void DefaultFileUploadListener(){
       super();
    }
    public void DefaultFileUploadListener(FileUploadListener p0){
       super();
       this.oldListener = p0;
    }
    public void onError(String p0,String p1){
       IpChange $ipChange = DefaultFileUploadListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("97d08c84", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)){
          TBSdkLog.d("mtopsdk.DefaultFileUploadListener", "[onError]onError errCode="+p0+", errMsg="+p1+" , ThreadName:"+Thread.currentThread().getName());
       }
       return;
    }
    public void onError(String p0,String p1,String p2){
       DefaultFileUploadListener toldListener;
       IpChange $ipChange = DefaultFileUploadListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("ffe51d4e", objArray);
          return;
       }else if((toldListener = this.oldListener) != null){
          toldListener.onError(p1, p2);
          return;
       }else {
          this.onError(p1, p2);
          return;
       }
    }
    public void onFinish(String p0){
       IpChange $ipChange = DefaultFileUploadListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("99807463", objArray);
          return;
       }else if(TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)){
          TBSdkLog.d("mtopsdk.DefaultFileUploadListener", "[onFinish]onFinish url="+p0+", ThreadName:"+Thread.currentThread().getName());
       }
       return;
    }
    public void onFinish(UploadFileInfo p0,String p1){
       DefaultFileUploadListener toldListener;
       IpChange $ipChange = DefaultFileUploadListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("57d514e0", objArray);
          return;
       }else if((toldListener = this.oldListener) != null){
          toldListener.onFinish(p1);
          return;
       }else {
          this.onFinish(p1);
          return;
       }
    }
    public void onProgress(int p0){
       DefaultFileUploadListener toldListener;
       IpChange $ipChange = DefaultFileUploadListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("5ec8f5b0", objArray);
          return;
       }else if((toldListener = this.oldListener) != null){
          toldListener.onProgress(p0);
       }
       if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)) {
          TBSdkLog.d("mtopsdk.DefaultFileUploadListener", "[onProgress]onProgress \(percentage="+p0+"\), ThreadName:"+Thread.currentThread().getName());
       }
       return;
    }
    public void onStart(){
       DefaultFileUploadListener toldListener;
       IpChange $ipChange = DefaultFileUploadListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("7f2d84ca", objArray);
          return;
       }else if((toldListener = this.oldListener) != null){
          toldListener.onStart();
       }
       if (TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable)) {
          TBSdkLog.d("mtopsdk.DefaultFileUploadListener", "[onStart]onStart called., ThreadName:"+Thread.currentThread().getName());
       }
       return;
    }
}
