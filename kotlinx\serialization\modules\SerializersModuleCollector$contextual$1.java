package kotlinx.serialization.modules.SerializersModuleCollector$contextual$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import tb.x530;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import tb.ckf;

public final class SerializersModuleCollector$contextual$1 extends Lambda implements g1a	// class@000754 from classes11.dex
{
    public final x530 $serializer;

    public void SerializersModuleCollector$contextual$1(x530 p0){
       this.$serializer = p0;
       super(1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final x530 invoke(List p0){
       ckf.g(p0, "it");
       return this.$serializer;
    }
}
