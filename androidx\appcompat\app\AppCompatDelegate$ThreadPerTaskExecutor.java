package androidx.appcompat.app.AppCompatDelegate$ThreadPerTaskExecutor;
import java.util.concurrent.Executor;
import java.lang.Object;
import java.lang.Runnable;
import java.lang.Thread;

public class AppCompatDelegate$ThreadPerTaskExecutor implements Executor	// class@00055b from classes.dex
{

    public void AppCompatDelegate$ThreadPerTaskExecutor(){
       super();
    }
    public void execute(Runnable p0){
       new Thread(p0).start();
    }
}
