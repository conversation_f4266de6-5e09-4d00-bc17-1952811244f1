package tb.ae9;
import tb.t2o;
import java.lang.String;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import android.content.res.Resources;
import android.content.res.AssetManager;
import java.io.InputStream;
import com.taobao.codetrack.sdk.assets.AssetsDelegate;
import java.io.ByteArrayOutputStream;
import java.lang.Throwable;
import tb.s3a;
import java.io.File;
import java.io.FileInputStream;
import java.lang.Math;
import java.util.Arrays;
import java.lang.OutOfMemoryError;
import java.lang.Boolean;
import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;

public class ae9	// class@0016fb from classes6.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x1e400059);
    }
    public static byte[] a(String p0,Context p1){
       int i1;
       int i = 0;
       IpChange $ipChange = ae9.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("e0859aa0", objArray);
       }else {
          InputStream inputStream = AssetsDelegate.proxy_open(p1.getResources().getAssets(), p0);
          ByteArrayOutputStream uByteArrayOu = new ByteArrayOutputStream();
          byte[] uobyteArray = new byte[8192];
          while ((i1 = inputStream.read(uobyteArray)) != -1) {
             uByteArrayOu.write(uobyteArray, i, i1);
          }
          if (uByteArrayOu.size() > 0) {
             inputStream.close();
             uByteArrayOu.close();
             return uByteArrayOu.toByteArray();
          }else {
             inputStream.close();
             uByteArrayOu.close();
             return null;
          }
       }
    }
    public static byte[] b(File p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = ae9.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("6ee147df", objArray);
       }else {
          long l = p0.length();
          if ((l - 0x7ffffff7) > 0) {
             throw new OutOfMemoryError("Required array size too large");
          }
          FileInputStream uFileInputSt = new FileInputStream(p0);
          int i2 = (int)l;
          byte[] uobyteArray = new byte[i2];
          while (true) {
             int i3 = i2 - i;
             if ((i3 = uFileInputSt.read(uobyteArray, i, i3)) > 0) {
                i = i + i3;
             }else if(i3 >= 0 && (i3 = uFileInputSt.read()) >= 0){
                int i4 = 0x7ffffff7;
                int i5 = i4 - i2;
                if (i2 <= i5) {
                   i2 = i2 << i1;
                   i2 = Math.max(i2, 8192);
                }else if(i2 != i4){
                   i2 = 0x7ffffff7;
                }else {
                   throw new OutOfMemoryError("Required array size too large");
                }
                uobyteArray = Arrays.copyOf(uobyteArray, i2);
                i4 = i + 1;
                uobyteArray[i] = (byte)i3;
                i = i4;
             }else {
                uFileInputSt.close();
                if (i2 != i) {
                   uobyteArray = Arrays.copyOf(uobyteArray, i);
                }
                return uobyteArray;
             }
          }
       }
    }
    public static byte[] c(File p0){
       byte[] uobyteArray;
       IpChange $ipChange = ae9.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("f594f96d", objArray);
       }else {
          try{
             uobyteArray = ae9.b(p0);
          }catch(java.io.IOException e4){
             s3a.b("Read all bytes exception", e4);
             uobyteArray = null;
          }
          return uobyteArray;
       }
    }
    public static boolean d(File p0,byte[] p1){
       File parentFile;
       IpChange $ipChange = ae9.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("7dd98184", objArray).booleanValue();
       }else if(p1 != null && p0 != null){
          if ((parentFile = p0.getParentFile()) != null && !parentFile.exists()) {
             parentFile.mkdirs();
          }
          BufferedOutputStream uBufferedOut = new BufferedOutputStream(new FileOutputStream(p0));
          uBufferedOut.write(p1);
          uBufferedOut.close();
          return 1;
       }else {
          return 0;
       }
    }
}
