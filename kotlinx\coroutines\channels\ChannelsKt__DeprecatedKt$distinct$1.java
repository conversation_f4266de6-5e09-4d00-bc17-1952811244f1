package kotlinx.coroutines.channels.ChannelsKt__DeprecatedKt$distinct$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import tb.ar4;
import java.lang.Object;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;

public final class ChannelsKt__DeprecatedKt$distinct$1 extends SuspendLambda implements u1a	// class@0004df from classes11.dex
{
    public Object L$0;
    public int label;

    public void ChannelsKt__DeprecatedKt$distinct$1(ar4 p0){
       super(2, p0);
    }
    public final ar4 create(Object p0,ar4 p1){
       ChannelsKt__DeprecatedKt$distinct$1 uodistinct$1 = new ChannelsKt__DeprecatedKt$distinct$1(p1);
       uodistinct$1.L$0 = p0;
       return uodistinct$1;
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(Object p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       dkf.d();
       if (this.label != null) {
          throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
       }
       b.b(p0);
       return this.L$0;
    }
}
