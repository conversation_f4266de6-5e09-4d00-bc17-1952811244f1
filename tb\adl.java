package tb.adl;
import tb.env;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;

public class adl extends env	// class@001b25 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x249000d4);
    }
    public void adl(){
       super();
    }
    public static Object ipc$super(adl p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/order/downgrade/detail/OrderDetailNavProcessorNodeUriFilterItem5");
    }
    public boolean hostFilter(String p0){
       IpChange $ipChange = adl.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.equals("h5.m.taobao.com");
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("c0d619d5", objArray).booleanValue();
    }
    public boolean pathFilter(String p0){
       IpChange $ipChange = adl.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.equals("/mlapp/odetail.html");
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("ee8b1ad2", objArray).booleanValue();
    }
    public boolean schemeFilter(String p0){
       int i = 1;
       IpChange $ipChange = adl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("9fb6ac52", objArray).booleanValue();
       }else if(!p0.equals("https") && (!p0.equals("taobao") && !p0.equals("http"))){
          i = false;
       }
       return i;
    }
}
