package androidx.activity.SystemBarStyle$Companion$light$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import android.content.res.Resources;
import java.lang.Boolean;
import java.lang.Object;
import java.lang.String;
import tb.ckf;

public final class SystemBarStyle$Companion$light$1 extends Lambda implements g1a	// class@00046d from classes.dex
{
    public static final SystemBarStyle$Companion$light$1 INSTANCE;

    static {
       SystemBarStyle$Companion$light$1.INSTANCE = new SystemBarStyle$Companion$light$1();
    }
    public void SystemBarStyle$Companion$light$1(){
       super(1);
    }
    public final Boolean invoke(Resources p0){
       ckf.g(p0, "<anonymous parameter 0>");
       return Boolean.FALSE;
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
}
