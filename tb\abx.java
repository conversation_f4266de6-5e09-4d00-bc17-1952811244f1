package tb.abx;
import tb.ude;
import tb.nde;
import tb.t2o;
import android.app.Activity;
import java.lang.Object;
import java.util.HashSet;
import tb.yko;
import tb.abx$a;
import java.lang.Runnable;
import java.lang.String;
import java.util.Set;
import java.util.Collection;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.Iterator;
import com.taobao.android.searchbaseframe.list.WidgetViewHolder;
import tb.hj8;
import com.alibaba.fastjson.JSONObject;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Class;
import android.view.View;
import java.lang.Integer;
import tb.c4p;
import tb.agg;
import java.lang.Boolean;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.nde$a;

public abstract class abx implements ude, nde	// class@001897 from classes7.dex
{
    public final Activity mActivity;
    private final Set mChildren;
    private yko mCore;
    private hj8 mEventBus;
    private ude mHolder;
    private final ude mParent;
    private final Set mScopes;
    private final Set mSubscribers;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3db00224);
       t2o.a(0x3db0021d);
       t2o.a(0x3db0021b);
    }
    public void abx(Activity p0,ude p1){
       String scopeTag;
       super();
       this.mChildren = new HashSet();
       this.mEventBus = null;
       this.mSubscribers = new HashSet();
       this.mActivity = p0;
       this.mParent = p1;
       this.mCore = p1.getCore();
       int i = 1;
       if (p1 instanceof abx) {
          this.mHolder = p1.mHolder;
          p0.runOnUiThread(new abx$a(this, p1));
          scopeTag = this.getScopeTag();
          int i1 = p1.getScopes().size();
          if (scopeTag == null) {
             i = 0;
          }
          HashSet hashSet = new HashSet((i1 + i));
          this.mScopes = hashSet;
          hashSet.addAll(p1.getScopes());
          if (scopeTag != null) {
             hashSet.add(scopeTag);
          }
       }else {
          this.mHolder = p1;
          if ((scopeTag = this.getScopeTag()) == null) {
             i = 0;
          }
          HashSet hashSet1 = new HashSet(i);
          this.mScopes = hashSet1;
          if (scopeTag != null) {
             hashSet1.add(scopeTag);
          }
       }
       return;
    }
    public final void addChild(nde p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a88698af", objArray);
          return;
       }else {
          this.mChildren.add(p0);
          this.onChildAdded(p0);
          return;
       }
    }
    public final yko c(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mCore;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("827434f7", objArray);
    }
    public void destroyAndRemoveFromParent(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("120cd83c", objArray);
          return;
       }else {
          this.destroyComponent();
          abx tmParent = this.mParent;
          if (tmParent instanceof abx) {
             tmParent.removeChild(this);
          }
          return;
       }
    }
    public final void destroyComponent(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("3a20b12a", objArray);
          return;
       }else if(this.mChildren.size() > 0){
          Iterator iterator = this.mChildren.iterator();
          while (iterator.hasNext()) {
             nde onde = iterator.next();
             if (onde instanceof abx) {
                onde.destroyComponent();
             }else if(onde instanceof WidgetViewHolder){
                onde.e0();
             }
          }
       }
       hj8 ohj8 = this.getRoot().obtainScopeEventBus();
       Iterator iterator1 = this.mSubscribers.iterator();
       while (iterator1.hasNext()) {
          ohj8.s(iterator1.next());
       }
       this.onComponentDestroy();
       return;
    }
    public JSONObject dumpDebugInfo(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new JSONObject();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("980ff734", objArray);
    }
    public nde findComponentOfScope(String p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d707514b", objArray);
       }else {
          nde onde = this;
          while (!TextUtils.equals(onde.getScopeTag(), p0)) {
             onde = onde.getParent();
             if (onde instanceof nde) {
             }else {
                onde = null;
             }
             if (onde == null) {
                break ;
             }
          }
          return onde;
       }
    }
    public final Object findParentOfClass(Class p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("a801e27a", objArray);
       }else {
          nde onde = this;
          while (true) {
             ude parent = onde.getParent();
             if (p0.isInstance(parent)) {
                return p0.cast(parent);
             }
             if (parent instanceof nde) {
             }else {
                parent = null;
             }
             if (parent == null) {
                break ;
             }
          }
          return null;
       }
    }
    public View findView(int p0){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mActivity.findViewById(p0);
       }
       Object[] objArray = new Object[]{this,new Integer(p0)};
       return $ipChange.ipc$dispatch("86c73ae0", objArray);
    }
    public final Activity getActivity(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mActivity;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("81223f9c", objArray);
    }
    public final yko getCore(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mCore;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ce8ff685", objArray);
    }
    public ude getHolder(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mHolder;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("bf2fa37e", objArray);
    }
    public abstract String getLogTag();
    public final ude getParent(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mParent;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("65261d7c", objArray);
    }
    public nde getRoot(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("ce1f4f40", objArray);
       }else {
          nde onde = this;
          while (true) {
             ude parent = onde.getParent();
             if (parent instanceof nde) {
                onde = parent;
             }else {
                break ;
             }
          }
          return onde;
       }
    }
    public String getScopeTag(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("53eba25d", objArray);
    }
    public final Set getScopes(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mScopes;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("65c13c8b", objArray);
    }
    public final void logError(String p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5f57b0b5", objArray);
          return;
       }else {
          this.c().l().d(this.getLogTag(), p0);
          return;
       }
    }
    public final void logWarn(String p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8ff976fb", objArray);
          return;
       }else {
          this.c().l().A(this.getLogTag(), p0);
          return;
       }
    }
    public final hj8 obtainScopeEventBus(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("60092b94", objArray);
       }else if(this.mEventBus == null){
          this.mEventBus = agg.a();
       }
       return this.mEventBus;
    }
    public void onChildAdded(nde p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6a40dc27", objArray);
       }
       return;
    }
    public void onChildRemoved(nde p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("18e50587", objArray);
       }
       return;
    }
    public void onComponentDestroy(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("f360aec3", objArray);
       }
       return;
    }
    public void onCtxDestroy(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("23655699", objArray);
       }
       return;
    }
    public final void onCtxDestroyInternal(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("f92298f6", objArray);
          return;
       }else {
          Iterator iterator = this.mChildren.iterator();
          while (iterator.hasNext()) {
             iterator.next().onCtxDestroyInternal();
          }
          this.onCtxDestroy();
          return;
       }
    }
    public void onCtxPause(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("11d36495", objArray);
       }
       return;
    }
    public final void onCtxPauseInternal(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("690eaf2", objArray);
          return;
       }else {
          Iterator iterator = this.mChildren.iterator();
          while (iterator.hasNext()) {
             iterator.next().onCtxPauseInternal();
          }
          this.onCtxPause();
          return;
       }
    }
    public void onCtxResume(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("cf86c1c", objArray);
       }
       return;
    }
    public final void onCtxResumeInternal(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("1bd57b79", objArray);
          return;
       }else {
          Iterator iterator = this.mChildren.iterator();
          while (iterator.hasNext()) {
             iterator.next().onCtxResumeInternal();
          }
          this.onCtxResume();
          return;
       }
    }
    public void onCtxStop(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("4c591371", objArray);
       }
       return;
    }
    public void onCtxStopInternal(){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("da57fdce", objArray);
          return;
       }else {
          Iterator iterator = this.mChildren.iterator();
          while (iterator.hasNext()) {
             iterator.next().onCtxStopInternal();
          }
          this.onCtxStop();
          return;
       }
    }
    public final void postEvent(Object p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("2e40efd", objArray);
          return;
       }else {
          this.getRoot().obtainScopeEventBus().k(p0);
          return;
       }
    }
    public final boolean postScopeEvent(Object p0,String p1){
       nde onde;
       int i = 1;
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("a4c9cf17", objArray).booleanValue();
       }else if((onde = this.findComponentOfScope(p1)) == null){
          this.logError("scope not found: "+p1+" for: "+p0.toString());
          return 0;
       }else {
          onde.obtainScopeEventBus().k(p0);
          return i;
       }
    }
    public final void printTree(StringBuilder p0,int p1){
       int i = 0;
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1)};
          $ipChange.ipc$dispatch("85bc8aa", objArray);
          return;
       }else {
          for (; i < p1; i = i + 1) {
             p0 = p0.append("  ");
          }
          p0 = p0+this.toString()+":";
          String scopeTag = (this.getScopeTag() != null)? this.getScopeTag(): "";
          p0 = p0+scopeTag+10;
          if (this.mChildren.size()) {
             Iterator iterator = this.mChildren.iterator();
             while (iterator.hasNext()) {
                int i1 = p1 + 1;
                iterator.next().printTree(p0, i1);
             }
          }
          return;
       }
    }
    public final void removeChild(nde p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e63cea12", objArray);
          return;
       }else {
          this.mChildren.remove(p0);
          this.onChildRemoved(p0);
          return;
       }
    }
    public final Object searchWidget(Class p0){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.getRoot().searchWidgetInSubTree(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("35160968", objArray);
    }
    public final Object searchWidgetInSubTree(Class p0){
       Object obj;
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ac60b709", objArray);
       }else if(p0.isInstance(this)){
          return p0.cast(this);
       }else if(this.mChildren.isEmpty()){
          return null;
       }else {
          Iterator iterator = this.mChildren.iterator();
          while (true) {
             if (!iterator.hasNext()) {
                return null;
             }
             if ((obj = iterator.next().searchWidgetInSubTree(p0)) != null) {
                break ;
             }
          }
          return obj;
       }
    }
    public void setHolder(ude p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("355e4350", objArray);
          return;
       }else {
          this.mHolder = p0;
          return;
       }
    }
    public final void subscribeEvent(Object p0){
       int i = 0;
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("204d3013", objArray);
          return;
       }else {
          try{
             this.getRoot().obtainScopeEventBus().o(p0);
             this.mSubscribers.add(p0);
          }catch(de.greenrobot.event.EventBusException e5){
             this.c().l().f(this.getLogTag(), "register event throws exception", e5, i);
          }
          return;
       }
    }
    public final boolean subscribeScopeEvent(Object p0,String p1){
       nde onde;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("5ccdd541", objArray).booleanValue();
       }else if((onde = this.findComponentOfScope(p1)) == null){
          this.logError("scope not found: "+p1+" for consumer: "+p0.toString());
          return i1;
       }else {
          hj8 ohj8 = onde.obtainScopeEventBus();
          if (!ohj8.i(p0)) {
             ohj8.o(p0);
          }
          return i;
       }
    }
    public String toString(){
       IpChange $ipChange = abx.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.getClass().getSimpleName();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
    public final boolean travel(nde$a p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6d20179", objArray).booleanValue();
       }else if(!p0.c(this)){
          return 0;
       }else if(this.mChildren.size()){
          p0.b();
          Iterator iterator = this.mChildren.iterator();
          while (true) {
             if (iterator.hasNext()) {
                if (!iterator.next().travel(p0)) {
                   return 0;
                }
                continue ;
             }else {
                p0.a();
                break ;
             }
          }
       }
       return 1;
    }
    public final void unsubscribeEvent(Object p0){
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d2ba62c", objArray);
          return;
       }else {
          this.getRoot().obtainScopeEventBus().s(p0);
          this.mSubscribers.remove(p0);
          return;
       }
    }
    public final boolean unsubscribeScopeEvent(Object p0,String p1){
       nde onde;
       int i = 1;
       IpChange $ipChange = abx.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("2a319308", objArray).booleanValue();
       }else if((onde = this.findComponentOfScope(p1)) == null){
          this.logError("unregister scope not found:"+p1+"for consumer"+p0.toString());
          return 0;
       }else {
          onde.obtainScopeEventBus().s(p0);
          return i;
       }
    }
}
