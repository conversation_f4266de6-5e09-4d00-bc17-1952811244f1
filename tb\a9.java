package tb.a9;
import tb.h9;
import tb.t2o;
import com.alibaba.fastjson.JSONObject;
import java.lang.String;
import tb.h110;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import java.lang.CharSequence;
import tb.wsq;
import android.content.pm.ActivityInfo;
import java.lang.Boolean;

public final class a9 extends h9	// class@001857 from classes5.dex
{
    public String l;
    public final String m;
    public final boolean n;
    public ActivityInfo o;
    public final boolean p;
    public final boolean q;
    public static IpChange $ipChange;

    static {
       t2o.a(0x2b200005);
    }
    public void a9(JSONObject p0){
       h9 th;
       super(p0);
       boolean b = true;
       this.q = b;
       if ((th = this.h) != null) {
          this.l = h110.g(th, "fragmentClass", null);
          this.m = h110.g(this.h, "droidFragmentTag", null);
          h110.d(this.h, "showNativeWithCode", -1);
          this.p = h110.b(this.h, "recreateOnSysChanged", false);
          this.n = h110.b(this.h, "recoverWindow", false);
          this.q = h110.b(this.h, "enablePadActTransition", b);
       }
       if (this.g == null) {
          this.g = new JSONObject(false);
       }
       return;
    }
    public static Object ipc$super(a9 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/abilitykit/ability/pop/model/AKNativeParams");
    }
    public String a(){
       String str1;
       IpChange $ipChange = a9.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("51290782", objArray);
       }else {
          h9 te = this.e;
          String str = "url";
          ckf.f(te, str);
          if (wsq.a0(te)) {
             str1 = "stdpop://native/"+this.l;
          }else {
             str1 = this.e;
             ckf.f(str1, str);
          }
          return str1;
       }
    }
    public final ActivityInfo f(){
       IpChange $ipChange = a9.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.o;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7b39eb12", objArray);
    }
    public final String g(){
       IpChange $ipChange = a9.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.m;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2bf3eaf5", objArray);
    }
    public final boolean h(){
       IpChange $ipChange = a9.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.q;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("56e00a58", objArray).booleanValue();
    }
    public final String i(){
       IpChange $ipChange = a9.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.l;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("637590e7", objArray);
    }
    public final boolean j(){
       IpChange $ipChange = a9.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.n;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("19810c49", objArray).booleanValue();
    }
    public final boolean k(){
       IpChange $ipChange = a9.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.p;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2a8cc076", objArray).booleanValue();
    }
    public final void l(ActivityInfo p0){
       IpChange $ipChange = a9.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("bcdc58e2", objArray);
          return;
       }else {
          this.o = p0;
          return;
       }
    }
    public final void m(String p0){
       IpChange $ipChange = a9.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3fdc6dd7", objArray);
          return;
       }else {
          this.l = p0;
          return;
       }
    }
}
