package tb.a02;
import tb.ijd;
import tb.t2o;
import com.taobao.aranger.core.entity.Call;
import java.lang.Object;
import com.taobao.aranger.core.entity.Reply;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.ArrayList;
import com.taobao.aranger.core.wrapper.ParameterWrapper;
import java.util.List;
import tb.fql;
import java.lang.System;
import java.util.Iterator;
import java.lang.Integer;

public abstract class a02 implements ijd	// class@00182c from classes7.dex
{
    public final Call a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x19400021);
       t2o.a(0x19400022);
    }
    public void a02(Call p0){
       super();
       this.a = p0;
    }
    public Reply a(){
       IpChange $ipChange = a02.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("f6cc3eda", objArray);
       }else {
          ArrayList uArrayList = new ArrayList();
          Object[] objArray1 = fql.a(this.a.getParameterWrappers(), uArrayList);
          Reply reply = Reply.obtain().setResult(this.b(objArray1)).setInvokeTime((System.currentTimeMillis() - System.currentTimeMillis()));
          if (!uArrayList.isEmpty()) {
             ParameterWrapper[] parameterWra = new ParameterWrapper[uArrayList.size()];
             Iterator iterator = uArrayList.iterator();
             while (iterator.hasNext()) {
                int i = iterator.next().intValue();
                parameterWra[i] = ParameterWrapper.obtain().setData(objArray1[uArrayList.get(i).intValue()]);
             }
             reply.setFlowParameterWrappers(parameterWra);
          }
          return reply;
       }
    }
    public abstract Object b(Object[] p0);
}
