package androidx.activity.SystemBarStyle$Companion$auto$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import android.content.res.Resources;
import java.lang.Boolean;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.content.res.Configuration;

public final class SystemBarStyle$Companion$auto$1 extends Lambda implements g1a	// class@00046b from classes.dex
{
    public static final SystemBarStyle$Companion$auto$1 INSTANCE;

    static {
       SystemBarStyle$Companion$auto$1.INSTANCE = new SystemBarStyle$Companion$auto$1();
    }
    public void SystemBarStyle$Companion$auto$1(){
       super(1);
    }
    public final Boolean invoke(Resources p0){
       ckf.g(p0, "resources");
       boolean b = (((p0.getConfiguration().uiMode & 0x30)) == 32)? true: false;
       return Boolean.valueOf(b);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
}
