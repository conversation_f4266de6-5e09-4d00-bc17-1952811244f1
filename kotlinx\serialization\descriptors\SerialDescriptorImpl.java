package kotlinx.serialization.descriptors.SerialDescriptorImpl;
import kotlinx.serialization.descriptors.a;
import tb.q020;
import java.lang.String;
import tb.ob40;
import java.util.List;
import tb.f520;
import java.lang.Object;
import tb.ckf;
import java.lang.Iterable;
import java.util.HashSet;
import tb.i04;
import java.util.ArrayList;
import tb.wy30;
import java.util.Collection;
import tb.ic1;
import tb.zz3;
import tb.ite;
import java.util.Iterator;
import tb.jte;
import tb.hte;
import java.lang.Integer;
import kotlin.Pair;
import tb.jpu;
import java.util.Map;
import kotlin.collections.a;
import kotlinx.serialization.descriptors.SerialDescriptorImpl$_hashCode$2;
import tb.d1a;
import tb.njg;
import kotlin.a;
import java.util.Set;
import java.util.Arrays;
import java.lang.Number;
import tb.aef;
import tb.hfn;
import java.lang.StringBuilder;
import kotlinx.serialization.descriptors.SerialDescriptorImpl$toString$1;
import java.lang.CharSequence;
import tb.g1a;

public final class SerialDescriptorImpl implements a, q020	// class@00072b from classes11.dex
{
    public final String a;
    public final ob40 b;
    public final int c;
    public final List d;
    public final Set e;
    public final String[] f;
    public final a[] g;
    public final List[] h;
    public final boolean[] i;
    public final Map j;
    public final a[] k;
    public final njg l;

    public void SerialDescriptorImpl(String p0,ob40 p1,int p2,List p3,f520 p4){
       ckf.g(p0, "serialName");
       ckf.g(p1, "kind");
       ckf.g(p3, "typeParameters");
       ckf.g(p4, "builder");
       super();
       this.a = p0;
       this.b = p1;
       this.c = p2;
       this.d = p4.c();
       this.e = i04.z0(p4.f());
       String[] stringArray = new String[0];
       String[] stringArray1 = p4.f().toArray(stringArray);
       this.f = stringArray1;
       this.g = wy30.b(p4.e());
       List[] listArray = new List[0];
       this.h = p4.d().toArray(listArray);
       this.i = i04.S0(p4.g());
       Iterable iterable = ic1.Y0(stringArray1);
       ArrayList uArrayList = new ArrayList(zz3.q(iterable, 10));
       Iterator iterator = iterable.iterator();
       while (true) {
          Iterator iterator1 = iterator;
          if (iterator1.hasNext()) {
             hte ohte = iterator1.next();
             p4 = ohte.d();
             uArrayList.add(jpu.a(p4, Integer.valueOf(ohte.c())));
          }else {
             break ;
          }
       }
       this.j = a.p(uArrayList);
       this.k = wy30.b(p3);
       this.l = a.b(new SerialDescriptorImpl$_hashCode$2(this));
       return;
    }
    public static final a[] i(SerialDescriptorImpl p0){
       return p0.k;
    }
    public Set a(){
       return this.e;
    }
    public boolean b(){
       return false;
    }
    public ob40 c(){
       return this.b;
    }
    public int d(){
       return this.c;
    }
    public String e(int p0){
       return this.f[p0];
    }
    public boolean equals(Object p0){
       boolean b = true;
       if (this != p0) {
          if (p0 instanceof SerialDescriptorImpl) {
             a uoa = p0;
             if (ckf.b(this.f(), uoa.f()) && (Arrays.equals(this.k, p0.k) && this.d() == uoa.d())) {
                int i = this.d();
                int i1 = 0;
                while (i1 < i) {
                   if (ckf.b(this.g(i1).f(), uoa.g(i1).f()) && ckf.b(this.g(i1).c(), uoa.g(i1).c())) {
                      i1 = i1 + 1;
                   }
                }
             }
          }
          b = false;
       }
       return b;
    }
    public String f(){
       return this.a;
    }
    public a g(int p0){
       return this.g[p0];
    }
    public List getAnnotations(){
       return this.d;
    }
    public boolean h(int p0){
       return this.i[p0];
    }
    public int hashCode(){
       return this.j();
    }
    public final int j(){
       return this.l.getValue().intValue();
    }
    public String toString(){
       return i04.j0(hfn.n(0, this.d()), ", ", this.f()+'(', "\)", 0, null, new SerialDescriptorImpl$toString$1(this), 24, null);
    }
}
