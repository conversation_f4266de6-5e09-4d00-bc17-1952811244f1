package mtopsdk.mtop.features.MtopFeatureManager$1;
import mtopsdk.mtop.features.MtopFeatureManager$MtopFeatureEnum;
import java.lang.Enum;

public class MtopFeatureManager$1	// class@0007c3 from classes11.dex
{
    public static final int[] $SwitchMap$mtopsdk$mtop$features$MtopFeatureManager$MtopFeatureEnum;

    static {
       int[] ointArray = new int[MtopFeatureManager$MtopFeatureEnum.values().length];
       try{
          MtopFeatureManager$1.$SwitchMap$mtopsdk$mtop$features$MtopFeatureManager$MtopFeatureEnum = ointArray;
          ointArray[MtopFeatureManager$MtopFeatureEnum.SUPPORT_RELATIVE_URL.ordinal()] = 1;
          try{
             MtopFeatureManager$1.$SwitchMap$mtopsdk$mtop$features$MtopFeatureManager$MtopFeatureEnum[MtopFeatureManager$MtopFeatureEnum.UNIT_INFO_FEATURE.ordinal()] = 2;
             try{
                MtopFeatureManager$1.$SwitchMap$mtopsdk$mtop$features$MtopFeatureManager$MtopFeatureEnum[MtopFeatureManager$MtopFeatureEnum.DISABLE_WHITEBOX_SIGN.ordinal()] = 3;
                try{
                   MtopFeatureManager$1.$SwitchMap$mtopsdk$mtop$features$MtopFeatureManager$MtopFeatureEnum[MtopFeatureManager$MtopFeatureEnum.SUPPORT_UTDID_UNIT.ordinal()] = 4;
                   try{
                      MtopFeatureManager$1.$SwitchMap$mtopsdk$mtop$features$MtopFeatureManager$MtopFeatureEnum[MtopFeatureManager$MtopFeatureEnum.DISABLE_X_COMMAND.ordinal()] = 5;
                      try{
                         MtopFeatureManager$1.$SwitchMap$mtopsdk$mtop$features$MtopFeatureManager$MtopFeatureEnum[MtopFeatureManager$MtopFeatureEnum.SUPPORT_OPEN_ACCOUNT.ordinal()] = 6;
                      }catch(java.lang.NoSuchFieldError e0){
                      }
                   }catch(java.lang.NoSuchFieldError e0){
                   }
                }catch(java.lang.NoSuchFieldError e0){
                }
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
