package tb.a01;
import tb.x0s;
import java.lang.String;
import tb.a01$a;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import tb.lmj;
import java.lang.Boolean;
import java.io.File;
import tb.u9u;
import android.app.Application;
import android.content.Context;
import java.lang.StringBuilder;
import tb.v9a;
import java.util.concurrent.ScheduledExecutorService;
import tb.zz0;
import java.lang.Runnable;
import java.util.concurrent.Executor;

public class a01	// class@00182a from classes7.dex
{
    public final x0s a;
    public final a01$a b;
    public final String c;
    public static IpChange $ipChange;

    public void a01(x0s p0,String p1,a01$a p2){
       super();
       this.a = p0;
       this.b = p2;
       this.c = p1;
    }
    public static void a(a01 p0){
       p0.e();
    }
    public static a01 b(x0s p0,a01$a p1){
       IpChange $ipChange = a01.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new a01(p0, "/data/anr", p1);
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("b5148370", objArray);
    }
    public final void c(String p0){
       a01 tb;
       IpChange $ipChange = a01.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("34cab96e", objArray);
          return;
       }else if((tb = this.b) != null){
          _monitor_enter(tb);
          if (p0.contains("trace") && this.d(p0)) {
             this.b.s(p0);
          }
          _monitor_exit(tb);
       }
       return;
    }
    public final boolean d(String p0){
       int i = 0;
       IpChange $ipChange = a01.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ed14d3c4", objArray).booleanValue();
       }else {
          File uFile = new File(p0);
          if (!uFile.exists()) {
             return i;
          }
          return new u9u(this.a.g(), uFile).a();
       }
    }
    public final void e(){
       IpChange $ipChange = a01.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("daf8f898", objArray);
          return;
       }else {
          this.c(this.c+"/traces.txt");
          return;
       }
    }
    public void f(){
       IpChange $ipChange = a01.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("810347e9", objArray);
          return;
       }else {
          v9a.c().a().execute(new zz0(this));
          return;
       }
    }
}
