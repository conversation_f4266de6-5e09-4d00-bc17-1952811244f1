package me.leolin.shortcutbadger.impl.DefaultBadger;
import tb.po1;
import java.lang.Object;
import java.util.List;
import java.util.Collections;
import android.content.Context;
import android.content.ComponentName;
import android.content.Intent;
import java.lang.String;
import tb.ol2;
import me.leolin.shortcutbadger.ShortcutBadgeException;
import java.lang.StringBuilder;

public class DefaultBadger implements po1	// class@00075f from classes11.dex
{

    public void DefaultBadger(){
       super();
    }
    public List a(){
       return Collections.singletonList("fr.neamar.kiss");
    }
    public void b(Context p0,ComponentName p1,int p2){
       Intent intent = new Intent("android.intent.action.BADGE_COUNT_UPDATE");
       intent.putExtra("badge_count", p2);
       intent.putExtra("badge_count_package_name", p1.getPackageName());
       intent.putExtra("badge_count_class_name", p1.getClassName());
       if (!ol2.a(p0, intent)) {
          throw new ShortcutBadgeException("unable to resolve intent: "+intent.toString());
       }
       p0.sendBroadcast(intent);
       return;
    }
    public boolean c(Context p0){
       return ol2.a(p0, new Intent("android.intent.action.BADGE_COUNT_UPDATE"));
    }
}
