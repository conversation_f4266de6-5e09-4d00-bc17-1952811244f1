package androidx.activity.EdgeToEdgeBase;
import androidx.activity.EdgeToEdgeImpl;
import java.lang.Object;
import android.view.Window;
import java.lang.String;
import tb.ckf;
import androidx.activity.SystemBarStyle;
import android.view.View;

public class EdgeToEdgeBase implements EdgeToEdgeImpl	// class@000449 from classes.dex
{

    public void EdgeToEdgeBase(){
       super();
    }
    public void adjustLayoutInDisplayCutoutMode(Window p0){
       ckf.g(p0, "window");
    }
    public void setUp(SystemBarStyle p0,SystemBarStyle p1,Window p2,View p3,boolean p4,boolean p5){
       ckf.g(p0, "statusBarStyle");
       ckf.g(p1, "navigationBarStyle");
       ckf.g(p2, "window");
       ckf.g(p3, "view");
    }
}
