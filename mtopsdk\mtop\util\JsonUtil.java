package mtopsdk.mtop.util.JsonUtil;
import tb.t2o;
import java.lang.Object;
import com.alibaba.fastjson.JSONObject;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.mtop.global.SwitchConfig;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.JSON;
import java.lang.Throwable;
import mtopsdk.common.util.TBSdkLog;
import java.lang.Class;
import mtopsdk.mtop.domain.BaseOutDo;
import com.alibaba.fastjson2.JSONReader$c;
import com.alibaba.fastjson2.JSONFactory;
import tb.lrf;
import com.alibaba.fastjson2.function.Supplier;
import tb.mrf;
import tb.wqf;
import java.lang.reflect.Type;

public class JsonUtil	// class@000823 from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x2530012a);
    }
    public void JsonUtil(){
       super();
    }
    public static JSONObject parse(byte[] p0){
       int i = 0;
       IpChange $ipChange = JsonUtil.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("975b6321", objArray);
       }else {
          JSONObject jSONObject = null;
          if (p0 != null && p0.length) {
             try{
                if (SwitchConfig.getInstance().isFastJson2Enable()) {
                   jSONObject = JsonUtil.parseWithJson2(p0);
                }else if(SwitchConfig.getInstance().isFastJson1Enable()){
                   Feature[] uFeatureArra = new Feature[i];
                   jSONObject = JSON.parse(p0, uFeatureArra);
                }
             }catch(java.lang.Exception e4){
                TBSdkLog.e("mtopsdk.JsonUtil", "[JsonUtil]parse error", e4);
             }
          }
          return jSONObject;
       }
    }
    public static BaseOutDo parseObject(byte[] p0,Class p1){
       BaseOutDo uBaseOutDo;
       BaseOutDo uBaseOutDo1;
       int i = 0;
       IpChange $ipChange = JsonUtil.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("5f7bae27", objArray);
       }else {
          try{
             uBaseOutDo = null;
             if (p1 != null && (p0 != null && p0.length)) {
                if (SwitchConfig.getInstance().isFastJson2Enable()) {
                   JSONReader$c uoc = JSONFactory.b();
                   uoc.p(new lrf());
                   uoc.o(new mrf());
                   uBaseOutDo1 = wqf.n(p0, p1, uoc);
                }else if(SwitchConfig.getInstance().isFastJson1Enable()){
                   Feature[] uFeatureArra = new Feature[i];
                   uBaseOutDo1 = JSON.parseObject(p0, p1, uFeatureArra);
                }
                uBaseOutDo = uBaseOutDo1;
             }
          }catch(java.lang.Exception e4){
             TBSdkLog.e("mtopsdk.JsonUtil", "[JsonUtil]parseObject error", e4);
          }
          return uBaseOutDo;
       }
    }
    public static JSONObject parseWithJson2(byte[] p0){
       IpChange $ipChange = JsonUtil.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("cb52139f", objArray);
       }else if(p0 != null && p0.length){
          JSONReader$c uoc = JSONFactory.b();
          uoc.p(new lrf());
          uoc.o(new mrf());
          return wqf.b(p0, uoc);
       }
    }
}
