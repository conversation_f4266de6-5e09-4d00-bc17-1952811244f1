package tb.a3h$a0;
import java.lang.Runnable;
import tb.a3h;
import java.util.ArrayList;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a3h$s0;

public class a3h$a0 implements Runnable	// class@001e64 from classes10.dex
{
    public final ArrayList a;
    public final a3h b;
    public static IpChange $ipChange;

    public void a3h$a0(a3h p0,ArrayList p1){
       this.b = p0;
       this.a = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$a0.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.C(this.b) != null){
          a3h.C(this.b).j(this.a);
       }
       return;
    }
}
