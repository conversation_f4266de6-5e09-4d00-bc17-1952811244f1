package androidx.appcompat.app.AppCompatDelegate$SerialExecutor;
import java.util.concurrent.Executor;
import java.lang.Object;
import java.util.ArrayDeque;
import java.lang.Runnable;
import tb.g31;
import java.util.Queue;

public class AppCompatDelegate$SerialExecutor implements Executor	// class@00055a from classes.dex
{
    public Runnable mActive;
    public final Executor mExecutor;
    private final Object mLock;
    public final Queue mTasks;

    public void AppCompatDelegate$SerialExecutor(Executor p0){
       super();
       this.mLock = new Object();
       this.mTasks = new ArrayDeque();
       this.mExecutor = p0;
    }
    public static void a(AppCompatDelegate$SerialExecutor p0,Runnable p1){
       p0.lambda$execute$0(p1);
    }
    private void lambda$execute$0(Runnable p0){
       p0.run();
       this.scheduleNext();
    }
    public void execute(Runnable p0){
       AppCompatDelegate$SerialExecutor tmLock = this.mLock;
       _monitor_enter(tmLock);
       this.mTasks.add(new g31(this, p0));
       if (this.mActive == null) {
          this.scheduleNext();
       }
       _monitor_exit(tmLock);
       return;
    }
    public void scheduleNext(){
       AppCompatDelegate$SerialExecutor tmLock = this.mLock;
       _monitor_enter(tmLock);
       Runnable runnable = this.mTasks.poll();
       this.mActive = runnable;
       if (runnable != null) {
          this.mExecutor.execute(runnable);
       }
       _monitor_exit(tmLock);
       return;
    }
}
