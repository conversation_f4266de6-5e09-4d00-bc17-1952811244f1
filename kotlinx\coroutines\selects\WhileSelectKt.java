package kotlinx.coroutines.selects.WhileSelectKt;
import tb.g1a;
import tb.ar4;
import java.lang.Object;
import kotlinx.coroutines.selects.WhileSelectKt$whileSelect$1;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import kotlinx.coroutines.selects.SelectImplementation;
import kotlin.coroutines.d;
import java.lang.Boolean;
import tb.xhv;

public final class WhileSelectKt	// class@0006ba from classes11.dex
{

    public static final Object a(g1a p0,ar4 p1){
       WhileSelectKt$whileSelect$1 owhileSelect;
       int i1;
       WhileSelectKt$whileSelect$1 label1;
       WhileSelectKt$whileSelect$1 l$0;
       if (p1 instanceof WhileSelectKt$whileSelect$1) {
          owhileSelect = p1;
          WhileSelectKt$whileSelect$1 label = owhileSelect.label;
          int i = Integer.MIN_VALUE;
          if (i1 = label & i) {
             int i2 = label - i;
             owhileSelect.label = i2;
          label_0018 :
             WhileSelectKt$whileSelect$1 result = owhileSelect.result;
             Object obj = dkf.d();
             if ((label1 = owhileSelect.label) != null) {
                if (label1 == 1) {
                   l$0 = owhileSelect.L$0;
                   b.b(result);
                label_004f :
                   if (!result.booleanValue()) {
                      return xhv.INSTANCE;
                   }
                }else {
                   throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
                }
             }else {
                b.b(result);
             }
             SelectImplementation selectImplem = new SelectImplementation(owhileSelect.getContext());
             l$0.invoke(selectImplem);
             owhileSelect.L$0 = l$0;
             owhileSelect.label = 1;
             if ((result = selectImplem.o(owhileSelect)) == obj) {
                return obj;
             }else {
                goto label_004f ;
             }
          }
       }
       owhileSelect = new WhileSelectKt$whileSelect$1(p1);
       goto label_0018 ;
    }
}
