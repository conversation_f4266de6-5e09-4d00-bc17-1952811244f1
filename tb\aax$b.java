package tb.aax$b;
import com.taobao.weex.WXSDKInstance;
import java.lang.String;
import android.view.View;

public interface abstract aax$b	// class@001ce4 from classes3.dex
{

    void onException(WXSDKInstance p0,String p1,String p2);
    void onRefreshSuccess(WXSDKInstance p0,int p1,int p2);
    void onRenderSuccess(WXSDKInstance p0,int p1,int p2);
    void onViewCreated(WXSDKInstance p0,View p1);
}
