package mtopsdk.mtop.cache.handler.CacheParserFactory;
import tb.t2o;
import java.lang.Object;
import anetwork.network.cache.RpcCache$CacheStatus;
import mtopsdk.mtop.cache.handler.ICacheParser;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import mtopsdk.mtop.cache.handler.EmptyCacheParser;
import mtopsdk.mtop.cache.handler.CacheParserFactory$1;
import java.lang.Enum;
import mtopsdk.mtop.cache.handler.ExpiredCacheParser;
import mtopsdk.mtop.cache.handler.FreshCacheParser;

public class CacheParserFactory	// class@000795 from classes11.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000a7);
    }
    public void CacheParserFactory(){
       super();
    }
    public static ICacheParser createCacheParser(RpcCache$CacheStatus p0){
       int i1;
       EmptyCacheParser uEmptyCacheP;
       int i = 1;
       IpChange $ipChange = CacheParserFactory.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("3a4dae35", objArray);
       }else if(p0 == null){
          return new EmptyCacheParser();
       }else if((i1 = CacheParserFactory$1.$SwitchMap$anetwork$network$cache$RpcCache$CacheStatus[p0.ordinal()]) != i){
          uEmptyCacheP = (i1 != 2)? new EmptyCacheParser(): new ExpiredCacheParser();
       }else {
          uEmptyCacheP = new FreshCacheParser();
       }
       return uEmptyCacheP;
    }
}
