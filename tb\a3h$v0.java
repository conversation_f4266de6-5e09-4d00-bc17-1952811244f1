package tb.a3h$v0;
import java.lang.String;
import java.util.ArrayList;
import com.taobao.trtc.api.TrtcDefines$i;

public interface abstract a3h$v0	// class@001e8e from classes10.dex
{

    void a();
    void b(int p0,String p1);
    void c(String p0);
    void d(String p0,String p1,String p2,String p3);
    void e(ArrayList p0);
    void f(TrtcDefines$i p0,float p1);
    void g();
    void h(boolean p0);
    void i(String p0,int p1,String p2,String p3,String p4);
    void j();
    void k(String p0,String p1);
    void l();
    void m(String p0,int p1,String p2);
    void n();
    void o(boolean p0,int p1,String p2);
    void onBlueToothDeviceDisconnected();
    void onConnectionInterrupted();
    void onSuccess();
    void p(String p0,String p1);
    void q(float p0);
    void r();
}
