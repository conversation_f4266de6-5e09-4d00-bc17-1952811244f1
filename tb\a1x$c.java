package tb.a1x$c;
import tb.uq;
import java.lang.Object;
import tb.jr;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.String;

public final class a1x$c implements uq	// class@001e5c from classes10.dex
{
    public static IpChange $ipChange;
    public static final a1x$c INSTANCE;

    static {
       a1x$c.INSTANCE = new a1x$c();
    }
    public void a1x$c(){
       super();
    }
    public final void a(jr p0,boolean p1){
       IpChange $ipChange = a1x$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("3310cd60", objArray);
       }
       return;
    }
}
