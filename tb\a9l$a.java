package tb.a9l$a;
import tb.t2o;
import tb.a9l;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;

public class a9l$a	// class@001eaa from classes10.dex
{
    public static IpChange $ipChange;
    public static final a9l a;

    static {
       t2o.a(0x31a0003e);
       a9l$a.a = new a9l();
    }
    public static a9l a(){
       IpChange $ipChange = a9l$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return a9l$a.a;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("42ca8524", objArray);
    }
}
