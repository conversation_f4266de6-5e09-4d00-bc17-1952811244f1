package androidx.activity.ComponentDialog;
import androidx.lifecycle.LifecycleOwner;
import androidx.activity.OnBackPressedDispatcherOwner;
import androidx.savedstate.SavedStateRegistryOwner;
import android.app.Dialog;
import android.content.Context;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import tb.a07;
import androidx.savedstate.SavedStateRegistryController;
import androidx.savedstate.SavedStateRegistryController$Companion;
import androidx.activity.OnBackPressedDispatcher;
import tb.qb4;
import java.lang.Runnable;
import androidx.lifecycle.LifecycleRegistry;
import android.view.View;
import android.view.ViewGroup$LayoutParams;
import androidx.lifecycle.Lifecycle;
import androidx.savedstate.SavedStateRegistry;
import android.view.Window;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;
import android.os.Bundle;
import android.os.Build$VERSION;
import android.window.OnBackInvokedDispatcher;
import tb.pb4;
import androidx.lifecycle.Lifecycle$Event;

public class ComponentDialog extends Dialog implements LifecycleOwner, OnBackPressedDispatcherOwner, SavedStateRegistryOwner	// class@000441 from classes.dex
{
    private LifecycleRegistry _lifecycleRegistry;
    private final OnBackPressedDispatcher onBackPressedDispatcher;
    private final SavedStateRegistryController savedStateRegistryController;

    public void ComponentDialog(Context p0){
       ckf.g(p0, "context");
       super(p0, 0, 2, null);
    }
    public void ComponentDialog(Context p0,int p1){
       ckf.g(p0, "context");
       super(p0, p1);
       this.savedStateRegistryController = SavedStateRegistryController.Companion.create(this);
       this.onBackPressedDispatcher = new OnBackPressedDispatcher(new qb4(this));
    }
    public void ComponentDialog(Context p0,int p1,int p2,a07 p3){
       if ((p2 & 0x02)) {
          p1 = 0;
       }
       super(p0, p1);
       return;
    }
    public static void a(ComponentDialog p0){
       ComponentDialog.onBackPressedDispatcher$lambda$1(p0);
    }
    private final LifecycleRegistry getLifecycleRegistry(){
       ComponentDialog t_lifecycleR;
       if ((t_lifecycleR = this._lifecycleRegistry) == null) {
          t_lifecycleR = new LifecycleRegistry(this);
          this._lifecycleRegistry = t_lifecycleR;
       }
       return t_lifecycleR;
    }
    public static void getOnBackPressedDispatcher$annotations(){
    }
    private static final void onBackPressedDispatcher$lambda$1(ComponentDialog p0){
       ckf.g(p0, "this$0");
       super.onBackPressed();
    }
    public void addContentView(View p0,ViewGroup$LayoutParams p1){
       ckf.g(p0, "view");
       this.initializeViewTreeOwners();
       super.addContentView(p0, p1);
    }
    public Lifecycle getLifecycle(){
       return this.getLifecycleRegistry();
    }
    public final OnBackPressedDispatcher getOnBackPressedDispatcher(){
       return this.onBackPressedDispatcher;
    }
    public SavedStateRegistry getSavedStateRegistry(){
       return this.savedStateRegistryController.getSavedStateRegistry();
    }
    public void initializeViewTreeOwners(){
       Window window = this.getWindow();
       ckf.d(window);
       View decorView = window.getDecorView();
       ckf.f(decorView, "window!!.decorView");
       ViewTreeLifecycleOwner.set(decorView, this);
       window = this.getWindow();
       ckf.d(window);
       decorView = window.getDecorView();
       ckf.f(decorView, "window!!.decorView");
       ViewTreeOnBackPressedDispatcherOwner.set(decorView, this);
       window = this.getWindow();
       ckf.d(window);
       decorView = window.getDecorView();
       ckf.f(decorView, "window!!.decorView");
       ViewTreeSavedStateRegistryOwner.set(decorView, this);
    }
    public void onBackPressed(){
       this.onBackPressedDispatcher.onBackPressed();
    }
    public void onCreate(Bundle p0){
       super.onCreate(p0);
       if (Build$VERSION.SDK_INT >= 33) {
          OnBackInvokedDispatcher onBackInvoke = pb4.a(this);
          ckf.f(onBackInvoke, "onBackInvokedDispatcher");
          this.onBackPressedDispatcher.setOnBackInvokedDispatcher(onBackInvoke);
       }
       this.savedStateRegistryController.performRestore(p0);
       this.getLifecycleRegistry().handleLifecycleEvent(Lifecycle$Event.ON_CREATE);
       return;
    }
    public Bundle onSaveInstanceState(){
       Bundle uBundle = super.onSaveInstanceState();
       ckf.f(uBundle, "super.onSaveInstanceState\(\)");
       this.savedStateRegistryController.performSave(uBundle);
       return uBundle;
    }
    public void onStart(){
       super.onStart();
       this.getLifecycleRegistry().handleLifecycleEvent(Lifecycle$Event.ON_RESUME);
    }
    public void onStop(){
       this.getLifecycleRegistry().handleLifecycleEvent(Lifecycle$Event.ON_DESTROY);
       this._lifecycleRegistry = null;
       super.onStop();
    }
    public void setContentView(int p0){
       this.initializeViewTreeOwners();
       super.setContentView(p0);
    }
    public void setContentView(View p0){
       ckf.g(p0, "view");
       this.initializeViewTreeOwners();
       super.setContentView(p0);
    }
    public void setContentView(View p0,ViewGroup$LayoutParams p1){
       ckf.g(p0, "view");
       this.initializeViewTreeOwners();
       super.setContentView(p0, p1);
    }
}
