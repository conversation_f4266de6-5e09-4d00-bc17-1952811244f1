package tb.a96;
import tb.ax;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.cfc;
import android.view.View;
import com.taobao.infoflow.protocol.model.datamodel.card.BaseSectionModel;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.infoflow.protocol.subservice.biz.ICardOverlayService;
import java.lang.Class;
import com.taobao.infoflow.protocol.subservice.ISubService;

public class a96 extends ax	// class@001b0f from classes8.dex
{
    public static IpChange $ipChange;
    public static final long DX_EVENT_ROVERLAYGUIDE;

    static {
       t2o.a(0x1f60014c);
    }
    public void a96(){
       super();
    }
    public static Object ipc$super(a96 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/infoflow/taobao/subservice/biz/overlayservice/impl/feedback/dxview/event/DXROverlayGuideEventHandler");
    }
    public void b(cfc p0,View p1,BaseSectionModel p2,BaseSectionModel p3,View p4){
       ISubService iSubService;
       IpChange $ipChange = a96.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4};
          $ipChange.ipc$dispatch("3e7fa716", objArray);
          return;
       }else if((iSubService = p0.a(ICardOverlayService.class)) == null){
          return;
       }else if(p3 != null && p4 != null){
          iSubService.showOverlayWithOverlayData("guide", p1, p2, p3, p4);
       }else {
          iSubService.showOverlay("guide", p1, p2);
       }
       return;
    }
}
