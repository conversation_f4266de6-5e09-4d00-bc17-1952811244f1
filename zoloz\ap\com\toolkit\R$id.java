package zoloz.ap.com.toolkit.R$id;
import com.taobao.taobao.R$id;
import java.lang.Object;

public final class R$id	// class@001100 from classes11.dex
{
    public static int body;
    public static int btn_left;
    public static int btn_right;
    public static int btn_x;
    public static int dialog_btn_cancel;
    public static int dialog_btn_cancel_center;
    public static int dialog_btn_confirm;
    public static int dialog_buttons;
    public static int dialog_content;
    public static int dialog_content_sub_title;
    public static int dialog_content_title;
    public static int dialog_protocol;
    public static int iv_bar;
    public static int iv_left;
    public static int iv_right;
    public static int iv_separate;
    public static int message;
    public static int protocol;
    public static int tv_left;
    public static int tv_right;
    public static int tv_title;

    static {
       R$id.body = R$id.body;
       R$id.btn_left = R$id.btn_left;
       R$id.btn_right = R$id.btn_right;
       R$id.btn_x = R$id.btn_x;
       R$id.dialog_btn_cancel = R$id.dialog_btn_cancel;
       R$id.dialog_btn_cancel_center = R$id.dialog_btn_cancel_center;
       R$id.dialog_btn_confirm = R$id.dialog_btn_confirm;
       R$id.dialog_buttons = R$id.dialog_buttons;
       R$id.dialog_content = R$id.dialog_content;
       R$id.dialog_content_sub_title = R$id.dialog_content_sub_title;
       R$id.dialog_content_title = R$id.dialog_content_title;
       R$id.dialog_protocol = R$id.dialog_protocol;
       R$id.iv_bar = R$id.iv_bar;
       R$id.iv_left = R$id.iv_left;
       R$id.iv_right = R$id.iv_right;
       R$id.iv_separate = R$id.iv_separate;
       R$id.message = R$id.message;
       R$id.protocol = R$id.protocol;
       R$id.tv_left = R$id.tv_left;
       R$id.tv_right = R$id.tv_right;
       R$id.tv_title = R$id.tv_title;
    }
    public void R$id(){
       super();
    }
}
