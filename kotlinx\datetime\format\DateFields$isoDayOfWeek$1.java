package kotlinx.datetime.format.DateFields$isoDayOfWeek$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.jf20;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class DateFields$isoDayOfWeek$1 extends MutablePropertyReference1Impl	// class@0006d6 from classes11.dex
{
    public static final DateFields$isoDayOfWeek$1 INSTANCE;

    static {
       DateFields$isoDayOfWeek$1.INSTANCE = new DateFields$isoDayOfWeek$1();
    }
    public void DateFields$isoDayOfWeek$1(){
       super(jf20.class, "isoDayOfWeek", "getIsoDayOfWeek\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.c();
    }
    public void set(Object p0,Object p1){
       p0.h(p1);
    }
}
