package kotlinx.serialization.internal.ClassValueReferences$getOrSet$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;

public final class ClassValueReferences$getOrSet$2 extends Lambda implements d1a	// class@00073a from classes11.dex
{
    public final d1a $factory;

    public void ClassValueReferences$getOrSet$2(d1a p0){
       this.$factory = p0;
       super(0);
    }
    public final Object invoke(){
       return this.$factory.invoke();
    }
}
