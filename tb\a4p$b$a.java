package tb.a4p$b$a;
import tb.xpo;
import tb.a4p$b;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import tb.a4p$d;
import android.content.Context;
import tb.a4p;

public class a4p$b$a extends xpo	// class@001aec from classes8.dex
{
    public final a4p$b c;
    public static IpChange $ipChange;

    public void a4p$b$a(a4p$b p0){
       this.c = p0;
       super();
    }
    public static Object ipc$super(a4p$b$a p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/search/common/service/SearchLocationService$2$1");
    }
    public void c(){
       IpChange $ipChange = a4p$b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("15c1193f", objArray);
          return;
       }else {
          a4p$b$a tc = this.c;
          a4p.b(tc.a, tc.b, tc.c);
          return;
       }
    }
}
