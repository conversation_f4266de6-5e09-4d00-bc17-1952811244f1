package tb.a240;
import tb.t2o;
import java.util.List;
import tb.a07;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import tb.ckf;
import java.lang.Number;
import java.lang.StringBuilder;

public final class a240	// class@00183e from classes7.dex
{
    public final List a;
    public static IpChange $ipChange;
    public static final int $stable;

    static {
       t2o.a(0x3e20006c);
       a240.$stable = 8;
    }
    public void a240(){
       super(null, 1, null);
    }
    public void a240(List p0){
       super();
       this.a = p0;
    }
    public void a240(List p0,int p1,a07 p2){
       if ((p1 & 0x01)) {
          p0 = null;
       }
       super(p0);
       return;
    }
    public final List a(){
       IpChange $ipChange = a240.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ef354ee9", objArray);
    }
    public boolean equals(Object p0){
       IpChange $ipChange = a240.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6c2a9726", objArray).booleanValue();
       }else if(this == p0){
          return 1;
       }else if(!p0 instanceof a240){
          return 0;
       }else if(!ckf.b(this.a, p0.a)){
          return 0;
       }else {
          return 1;
       }
    }
    public int hashCode(){
       a240 ta;
       int i = 0;
       IpChange $ipChange = a240.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("53a9ab15", objArray).intValue();
       }else if((ta = this.a) == null){
          i = ta.hashCode();
       }
       return i;
    }
    public String toString(){
       IpChange $ipChange = a240.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "QAUiState\(questions="+this.a+"\)";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8126d80d", objArray);
    }
}
