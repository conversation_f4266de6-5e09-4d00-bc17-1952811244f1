package mtopsdk.mtop.cache.handler.FreshCacheParser$1;
import java.lang.Runnable;
import mtopsdk.mtop.cache.handler.FreshCacheParser;
import mtopsdk.mtop.common.MtopListener;
import mtopsdk.mtop.common.MtopCacheEvent;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.mtop.common.MtopCallback$MtopCacheListener;
import java.lang.Throwable;
import mtopsdk.common.util.TBSdkLog;

public class FreshCacheParser$1 implements Runnable	// class@00079a from classes11.dex
{
    public final FreshCacheParser this$0;
    public final MtopCacheEvent val$cacheEvent;
    public final MtopListener val$mtopListener;
    public final Object val$reqContext;
    public final String val$seqNo;
    public static IpChange $ipChange;

    public void FreshCacheParser$1(FreshCacheParser p0,MtopListener p1,MtopCacheEvent p2,Object p3,String p4){
       this.this$0 = p0;
       this.val$mtopListener = p1;
       this.val$cacheEvent = p2;
       this.val$reqContext = p3;
       this.val$seqNo = p4;
       super();
    }
    public void run(){
       IpChange $ipChange = FreshCacheParser$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          try{
             this.val$mtopListener.onCached(this.val$cacheEvent, this.val$reqContext);
          }catch(java.lang.Exception e0){
             TBSdkLog.e("mtopsdk.FreshCacheParser", this.val$seqNo, "do onCached callback error.", e0);
          }
          return;
       }
    }
}
