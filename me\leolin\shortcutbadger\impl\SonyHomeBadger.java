package me.leolin.shortcutbadger.impl.SonyHomeBadger;
import tb.po1;
import java.lang.Object;
import java.lang.String;
import android.net.Uri;
import android.content.Context;
import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ProviderInfo;
import java.util.List;
import java.util.Arrays;
import android.content.ContentValues;
import java.lang.Integer;
import android.os.Looper;
import me.leolin.shortcutbadger.impl.SonyHomeBadger$1;
import android.content.ContentResolver;
import android.content.AsyncQueryHandler;

public class SonyHomeBadger implements po1	// class@000768 from classes11.dex
{
    public final Uri a;
    public AsyncQueryHandler b;

    public void SonyHomeBadger(){
       super();
       this.a = Uri.parse("content://com.sonymobile.home.resourceprovider/badge");
    }
    public static void d(Context p0,ComponentName p1,int p2){
       Intent intent = new Intent("com.sonyericsson.home.action.UPDATE_BADGE");
       intent.putExtra("com.sonyericsson.home.intent.extra.badge.PACKAGE_NAME", p1.getPackageName());
       intent.putExtra("com.sonyericsson.home.intent.extra.badge.ACTIVITY_NAME", p1.getClassName());
       intent.putExtra("com.sonyericsson.home.intent.extra.badge.MESSAGE", String.valueOf(p2));
       boolean b = (p2 > 0)? true: false;
       intent.putExtra("com.sonyericsson.home.intent.extra.badge.SHOW_MESSAGE", b);
       p0.sendBroadcast(intent);
       return;
    }
    public static boolean h(Context p0){
       int i = 0;
       if (p0.getPackageManager().resolveContentProvider("com.sonymobile.home.resourceprovider", i) != null) {
          i = true;
       }
       return i;
    }
    public List a(){
       String[] stringArray = new String[]{"com.sonyericsson.home","com.sonymobile.home"};
       return Arrays.asList(stringArray);
    }
    public void b(Context p0,ComponentName p1,int p2){
       if (SonyHomeBadger.h(p0)) {
          this.e(p0, p1, p2);
       }else {
          SonyHomeBadger.d(p0, p1, p2);
       }
       return;
    }
    public final ContentValues c(int p0,ComponentName p1){
       ContentValues uContentValu = new ContentValues();
       uContentValu.put("badge_count", Integer.valueOf(p0));
       uContentValu.put("package_name", p1.getPackageName());
       uContentValu.put("activity_name", p1.getClassName());
       return uContentValu;
    }
    public final void e(Context p0,ComponentName p1,int p2){
       if (p2 < 0) {
          return;
       }
       ContentValues uContentValu = this.c(p2, p1);
       if (Looper.myLooper() == Looper.getMainLooper()) {
          if (this.b == null) {
             this.b = new SonyHomeBadger$1(this, p0.getApplicationContext().getContentResolver());
          }
          this.f(uContentValu);
       }else {
          this.g(p0, uContentValu);
       }
       return;
    }
    public final void f(ContentValues p0){
       this.b.startInsert(0, null, this.a, p0);
    }
    public final void g(Context p0,ContentValues p1){
       p0.getApplicationContext().getContentResolver().insert(this.a, p1);
    }
}
