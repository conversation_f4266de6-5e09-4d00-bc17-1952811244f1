package tb.a3w;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;

public class a3w	// class@001830 from classes5.dex
{
    public String a;
    public String b;
    public String c;
    public String d;
    public JSONObject e;
    public static IpChange $ipChange;

    static {
       t2o.a(0x1d200038);
    }
    public void a3w(){
       super();
    }
    public static a3w h(){
       IpChange $ipChange = a3w.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new a3w();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("3c68cc02", objArray);
    }
    public static a3w i(JSONObject p0){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("c64eae78", objArray);
       }else if(p0 == null){
          return null;
       }else {
          return a3w.h().k(p0);
       }
    }
    public a3w a(String p0){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("3133b0f3", objArray);
       }else {
          this.d = p0;
          return this;
       }
    }
    public a3w b(JSONObject p0){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("8d057c54", objArray);
       }else {
          this.e = p0;
          return this;
       }
    }
    public a3w c(String p0,Object p1){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("15938240", objArray);
       }else if(this.e == null){
          this.e = new JSONObject();
       }
       this.e.put(p0, p1);
       return this;
    }
    public a3w d(String p0){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("798d4e7", objArray);
       }else {
          this.a = p0;
          return this;
       }
    }
    public a3w e(String p0){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("a25948a9", objArray);
       }else {
          this.b = p0;
          return this;
       }
    }
    public boolean equals(Object p0){
       int i = 1;
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6c2a9726", objArray).booleanValue();
       }else if(!p0 instanceof a3w){
          return 0;
       }else if(TextUtils.equals(p0.a, this.a) && (TextUtils.equals(p0.b, this.b) && TextUtils.equals(p0.c, this.c))){
          i = false;
       }
       return i;
    }
    public a3w f(String p0){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("175ec230", objArray);
       }else {
          this.c = p0;
          return this;
       }
    }
    public JSONObject g(){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("b6a2ff62", objArray);
       }else {
          JSONObject jSONObject = new JSONObject();
          jSONObject.put("dataVersion", this.a);
          jSONObject.put("groupCode", this.b);
          jSONObject.put("versionCode", this.c);
          jSONObject.put("activeType", this.d);
          jSONObject.put("bizParams", this.e);
          return jSONObject;
       }
    }
    public boolean j(String p0){
       IpChange $ipChange = a3w.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return TextUtils.equals(p0, this.b);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("e56c7eca", objArray).booleanValue();
    }
    public a3w k(JSONObject p0){
       IpChange $ipChange = a3w.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("4976740f", objArray);
       }else if(p0 == null){
          return this;
       }else {
          return this.d(p0.getString("dataVersion")).f(p0.getString("versionCode")).e(p0.getString("groupCode")).a(p0.getString("activeType")).b(p0.getJSONObject("bizParams"));
       }
    }
}
