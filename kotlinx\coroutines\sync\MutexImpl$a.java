package kotlinx.coroutines.sync.MutexImpl$a;
import tb.l9p;
import kotlinx.coroutines.sync.MutexImpl;
import java.lang.Object;
import tb.dv6;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import tb.k9p;
import tb.v8p;
import tb.qww;
import tb.rr7;
import kotlin.coroutines.d;

public final class MutexImpl$a implements l9p	// class@0006c3 from classes11.dex
{
    public final l9p a;
    public final Object b;
    public final MutexImpl c;

    public void MutexImpl$a(MutexImpl p0,l9p p1,Object p2){
       super();
       this.c = p0;
       this.a = p1;
       this.b = p2;
    }
    public void b(Object p0){
       MutexImpl.y().set(this.c, this.b);
       this.a.b(p0);
    }
    public void c(v8p p0,int p1){
       this.a.c(p0, p1);
    }
    public void d(rr7 p0){
       this.a.d(p0);
    }
    public boolean e(Object p0,Object p1){
       boolean b;
       if (b = this.a.e(p0, p1)) {
          MutexImpl.y().set(this.c, this.b);
       }
       return b;
    }
    public d getContext(){
       return this.a.getContext();
    }
}
