package mtopsdk.mtop.domain.JsonTypeEnum;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class JsonTypeEnum extends Enum	// class@0007b9 from classes11.dex
{
    private String jsonType;
    private static final JsonTypeEnum[] $VALUES;
    public static IpChange $ipChange;
    public static final JsonTypeEnum JSON;
    public static final JsonTypeEnum ORIGINALJSON;

    static {
       JsonTypeEnum jsonTypeEnum = new JsonTypeEnum("JSON", 0, "json");
       JsonTypeEnum.JSON = jsonTypeEnum;
       JsonTypeEnum jsonTypeEnum1 = new JsonTypeEnum("ORIGINALJSON", 1, "originaljson");
       JsonTypeEnum.ORIGINALJSON = jsonTypeEnum1;
       JsonTypeEnum[] jsonTypeEnum2 = new JsonTypeEnum[]{jsonTypeEnum,jsonTypeEnum1};
       JsonTypeEnum.$VALUES = jsonTypeEnum2;
    }
    private void JsonTypeEnum(String p0,int p1,String p2){
       super(p0, p1);
       this.jsonType = p2;
    }
    public static Object ipc$super(JsonTypeEnum p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/domain/JsonTypeEnum");
    }
    public static JsonTypeEnum valueOf(String p0){
       IpChange $ipChange = JsonTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(JsonTypeEnum.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("5d3fffc8", objArray);
    }
    public static JsonTypeEnum[] values(){
       IpChange $ipChange = JsonTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return JsonTypeEnum.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("f384bf9", objArray);
    }
    public String getJsonType(){
       IpChange $ipChange = JsonTypeEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.jsonType;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("da5b7841", objArray);
    }
}
