package tb.a1m;
import com.taobao.android.AliImageCreatorInterface;
import com.taobao.phenix.intf.PhenixCreator;
import java.lang.Object;
import com.taobao.android.AliImageTicketInterface;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.q1m;
import tb.p1m;
import tb.xp0;
import tb.wp0;
import tb.s8d;
import tb.bq0;

public class a1m implements AliImageCreatorInterface	// class@001821 from classes5.dex
{
    public final PhenixCreator a;
    public static IpChange $ipChange;

    public void a1m(PhenixCreator p0){
       super();
       this.a = p0;
    }
    public AliImageTicketInterface a(){
       IpChange $ipChange = a1m.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new q1m(this.a.fetch());
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e9d70627", objArray);
    }
    public AliImageCreatorInterface c(xp0 p0){
       IpChange $ipChange = a1m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d169f7b8", objArray);
       }else {
          this.a.failListener(new wp0(p0));
          return this;
       }
    }
    public AliImageCreatorInterface succListener(xp0 p0){
       IpChange $ipChange = a1m.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("8873e714", objArray);
       }else {
          this.a.succListener(new bq0(p0));
          return this;
       }
    }
}
