package kotlinx.coroutines.channels.BufferedChannel$receiveCatchingOnNoWaiterSuspend$1;
import kotlin.coroutines.jvm.internal.ContinuationImpl;
import kotlinx.coroutines.channels.BufferedChannel;
import tb.ar4;
import java.lang.Object;
import tb.zi3;
import tb.dkf;
import kotlinx.coroutines.channels.e;

public final class BufferedChannel$receiveCatchingOnNoWaiterSuspend$1 extends ContinuationImpl	// class@0004cc from classes11.dex
{
    public int I$0;
    public long J$0;
    public Object L$0;
    public Object L$1;
    public int label;
    public Object result;
    public final BufferedChannel this$0;

    public void BufferedChannel$receiveCatchingOnNoWaiterSuspend$1(BufferedChannel p0,ar4 p1){
       this.this$0 = p0;
       super(p1);
    }
    public final Object invokeSuspend(Object p0){
       this.result = p0;
       this.label = this.label | Integer.MIN_VALUE;
       if ((p0 = BufferedChannel.F(this.this$0, null, 0, 0, this)) == dkf.d()) {
          return p0;
       }
       return e.a(p0);
    }
}
