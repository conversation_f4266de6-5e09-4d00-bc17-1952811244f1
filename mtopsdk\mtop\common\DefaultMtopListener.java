package mtopsdk.mtop.common.DefaultMtopListener;
import mtopsdk.mtop.common.MtopCallback$MtopCacheListener;
import mtopsdk.mtop.common.DefaultMtopCallback;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import mtopsdk.mtop.common.MtopCacheEvent;
import com.android.alibaba.ip.runtime.IpChange;
import mtopsdk.mtop.domain.MtopResponse;
import mtopsdk.mtop.common.MtopFinishEvent;
import mtopsdk.common.util.TBSdkLog$LogEnable;
import mtopsdk.common.util.TBSdkLog;

public class DefaultMtopListener extends DefaultMtopCallback implements MtopCallback$MtopCacheListener	// class@00079f from classes11.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x253000b2);
       t2o.a(0x253000b5);
    }
    public void DefaultMtopListener(){
       super();
    }
    public static Object ipc$super(DefaultMtopListener p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/common/DefaultMtopListener");
    }
    public void onCached(MtopCacheEvent p0,Object p1){
       IpChange $ipChange = DefaultMtopListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("1c0c28d9", objArray);
          return;
       }else if(p0 != null && (p0.getMtopResponse() != null && TBSdkLog.isLogEnable(TBSdkLog$LogEnable.DebugEnable))){
          TBSdkLog.d("mtopsdk.DefaultMtopListener", p0.seqNo, "[onCached]"+p0.getMtopResponse().toString());
       }
       return;
    }
}
