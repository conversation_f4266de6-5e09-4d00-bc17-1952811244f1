package kotlinx.serialization.internal.ClassValueCache$get$$inlined$getOrSet$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import kotlinx.serialization.internal.ClassValueCache;
import tb.wyf;
import java.lang.Object;
import tb.m020;
import tb.g1a;
import tb.x530;

public final class ClassValueCache$get$$inlined$getOrSet$1 extends Lambda implements d1a	// class@000736 from classes11.dex
{
    public final wyf $key$inlined;
    public final ClassValueCache this$0;

    public void ClassValueCache$get$$inlined$getOrSet$1(ClassValueCache p0,wyf p1){
       this.this$0 = p0;
       this.$key$inlined = p1;
       super(0);
    }
    public final Object invoke(){
       return new m020(this.this$0.b().invoke(this.$key$inlined));
    }
}
