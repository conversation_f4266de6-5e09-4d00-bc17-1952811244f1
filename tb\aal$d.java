package tb.aal$d;
import tb.aal;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;

public class aal$d	// class@001eb5 from classes10.dex
{
    public static IpChange $ipChange;
    public static final aal a;

    static {
       aal$d.a = new aal();
    }
    public static aal a(){
       IpChange $ipChange = aal$d.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return aal$d.a;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("f03de3ba", objArray);
    }
}
