package tb.a2p$a;
import tb.wnd;
import java.lang.Object;
import android.app.Activity;
import tb.ude;
import com.taobao.search.searchdoor.sf.widgets.SearchDoorContext;
import android.view.ViewGroup;
import tb.vfw;
import com.taobao.search.searchdoor.sf.widgets.activate.ActivateWidget;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.search.searchdoor.sf.widgets.searchbar.SearchBarWidget;
import java.lang.CharSequence;
import android.text.TextUtils;
import com.alibaba.ability.localization.Localization;
import com.taobao.search.searchdoor.sf.widgets.searchbar.I18nSearchBarWidget;
import tb.y4p;
import com.taobao.search.searchdoor.sf.widgets.searchbar.SubscribeSearchBarWidget;
import com.taobao.search.searchdoor.sf.widgets.searchbar.PopupSearchBarWidget;

public class a2p$a implements wnd	// class@001747 from classes9.dex
{
    public static IpChange $ipChange;

    public void a2p$a(){
       super();
    }
    public ActivateWidget a(Activity p0,ude p1,SearchDoorContext p2,ViewGroup p3,vfw p4){
       IpChange $ipChange = a2p$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4};
          return $ipChange.ipc$dispatch("f2364ba2", objArray);
       }else {
          ActivateWidget v6 = new ActivateWidget(p0, p1, p2, p3, p4);
          return v6;
       }
    }
    public SearchBarWidget b(Activity p0,ude p1,SearchDoorContext p2,ViewGroup p3,vfw p4){
       IpChange $ipChange = a2p$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4};
          return $ipChange.ipc$dispatch("6b094976", objArray);
       }else {
          String str = p2.q("g_mainChannel");
          if (TextUtils.isEmpty(str)) {
             str = p2.q("mainChannel");
          }
          if (Localization.o()) {
             I18nSearchBarWidget str1 = new I18nSearchBarWidget(p0, p1, p2, p3, p4);
             return str;
          }else if(TextUtils.equals(str, "dingyue") && y4p.j()){
             SubscribeSearchBarWidget str2 = new SubscribeSearchBarWidget(p0, p1, p2, p3, p4);
             return str;
          }else if(p2.F()){
             PopupSearchBarWidget str3 = new PopupSearchBarWidget(p0, p1, p2, p3, p4);
             return str;
          }else {
             SearchBarWidget str4 = new SearchBarWidget(p0, p1, p2, p3, p4);
             return str;
          }
       }
    }
}
