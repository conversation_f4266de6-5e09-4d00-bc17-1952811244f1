package kotlinx.serialization.internal.EnumDescriptor$elementDescriptors$2;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.lang.String;
import kotlinx.serialization.internal.EnumDescriptor;
import java.lang.Object;
import kotlinx.serialization.descriptors.a;
import java.lang.StringBuilder;
import kotlinx.serialization.internal.PluginGeneratedSerialDescriptor;
import kotlinx.serialization.descriptors.b$d;
import tb.ob40;
import tb.g1a;
import kotlinx.serialization.descriptors.SerialDescriptorsKt;

public final class EnumDescriptor$elementDescriptors$2 extends Lambda implements d1a	// class@00073c from classes11.dex
{
    public final int $elementsCount;
    public final String $name;
    public final EnumDescriptor this$0;

    public void EnumDescriptor$elementDescriptors$2(int p0,String p1,EnumDescriptor p2){
       this.$elementsCount = p0;
       this.$name = p1;
       this.this$0 = p2;
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final a[] invoke(){
       EnumDescriptor$elementDescriptors$2 t$elementsCo = this.$elementsCount;
       a[] uoaArray = new a[t$elementsCo];
       for (int i = 0; i < t$elementsCo; i = i + 1) {
          a[] uoaArray1 = new a[0];
          uoaArray[i] = SerialDescriptorsKt.d("".append(this.$name).append('.').append(this.this$0.e(i)).toString(), b$d.INSTANCE, uoaArray1, null, 8, null);
       }
       return uoaArray;
    }
}
