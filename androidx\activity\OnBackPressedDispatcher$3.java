package androidx.activity.OnBackPressedDispatcher$3;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.OnBackPressedDispatcher;
import java.lang.Object;
import tb.xhv;

public final class OnBackPressedDispatcher$3 extends Lambda implements d1a	// class@000458 from classes.dex
{
    public final OnBackPressedDispatcher this$0;

    public void OnBackPressedDispatcher$3(OnBackPressedDispatcher p0){
       this.this$0 = p0;
       super(0);
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       this.this$0.onBackPressed();
    }
}
