package androidx.activity.PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1;
import android.view.View$OnAttachStateChangeListener;
import tb.ozm;
import android.view.View;
import android.view.ViewTreeObserver$OnScrollChangedListener;
import android.view.View$OnLayoutChangeListener;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import android.graphics.Rect;
import androidx.activity.PipHintTrackerKt;
import kotlinx.coroutines.channels.i;
import android.view.ViewTreeObserver;

public final class PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1 implements View$OnAttachStateChangeListener	// class@000468 from classes.dex
{
    public final ozm $$this$callbackFlow;
    public final View$OnLayoutChangeListener $layoutChangeListener;
    public final ViewTreeObserver$OnScrollChangedListener $scrollChangeListener;
    public final View $view;

    public void PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1(ozm p0,View p1,ViewTreeObserver$OnScrollChangedListener p2,View$OnLayoutChangeListener p3){
       this.$$this$callbackFlow = p0;
       this.$view = p1;
       this.$scrollChangeListener = p2;
       this.$layoutChangeListener = p3;
       super();
    }
    public void onViewAttachedToWindow(View p0){
       ckf.g(p0, "v");
       this.$$this$callbackFlow.m(PipHintTrackerKt.access$trackPipAnimationHintView$positionInWindow(this.$view));
       this.$view.getViewTreeObserver().addOnScrollChangedListener(this.$scrollChangeListener);
       this.$view.addOnLayoutChangeListener(this.$layoutChangeListener);
    }
    public void onViewDetachedFromWindow(View p0){
       ckf.g(p0, "v");
       p0.getViewTreeObserver().removeOnScrollChangedListener(this.$scrollChangeListener);
       p0.removeOnLayoutChangeListener(this.$layoutChangeListener);
    }
}
