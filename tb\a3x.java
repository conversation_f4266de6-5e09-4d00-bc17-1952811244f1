package tb.a3x;
import tb.ou;
import tb.t2o;
import android.content.Context;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;

public class a3x extends ou	// class@001831 from classes5.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x38e001b7);
    }
    public void a3x(Context p0){
       super(p0);
    }
    public static Object ipc$super(a3x p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/android/detail/ttdetail/component/space/Weex2ComponentSpace");
    }
}
