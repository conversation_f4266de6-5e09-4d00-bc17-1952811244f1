package mtopsdk.mtop.upload.util.FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory$1;
import java.lang.Thread;
import mtopsdk.mtop.upload.util.FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory;
import java.lang.Runnable;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import android.os.Process;

public class FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory$1 extends Thread	// class@000818 from classes11.dex
{
    public final FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory this$0;
    public static IpChange $ipChange;

    public void FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory$1(FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory p0,Runnable p1,String p2){
       this.this$0 = p0;
       super(p1, p2);
    }
    public static Object ipc$super(FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory$1 p0,String p1,Object[] p2){
       if (p1.hashCode() != 0x5c510192) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in mtopsdk/mtop/upload/util/FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory$1");
       }
       super.run();
       return null;
    }
    public void run(){
       IpChange $ipChange = FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          Process.setThreadPriority(FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory.access$000(this.this$0));
          super.run();
          return;
       }
    }
}
