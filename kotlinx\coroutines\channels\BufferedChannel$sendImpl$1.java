package kotlinx.coroutines.channels.BufferedChannel$sendImpl$1;
import tb.y1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.zi3;
import java.lang.Number;
import java.lang.Void;
import java.lang.IllegalStateException;
import java.lang.String;

public final class BufferedChannel$sendImpl$1 extends Lambda implements y1a	// class@0004ce from classes11.dex
{
    public static final BufferedChannel$sendImpl$1 INSTANCE;

    static {
       BufferedChannel$sendImpl$1.INSTANCE = new BufferedChannel$sendImpl$1();
    }
    public void BufferedChannel$sendImpl$1(){
       super(4);
    }
    public Object invoke(Object p0,Object p1,Object p2,Object p3){
       return this.invoke(p0, p1.intValue(), p2, p3.longValue());
    }
    public final Void invoke(zi3 p0,int p1,Object p2,long p3){
       throw new IllegalStateException("unexpected");
    }
}
