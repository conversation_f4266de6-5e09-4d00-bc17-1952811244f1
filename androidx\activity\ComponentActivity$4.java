package androidx.activity.ComponentActivity$4;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.activity.ComponentActivity;
import java.lang.Object;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Lifecycle$Event;
import java.lang.String;
import tb.ckf;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;

public final class ComponentActivity$4 implements LifecycleEventObserver	// class@000435 from classes.dex
{
    public final ComponentActivity this$0;

    public void ComponentActivity$4(ComponentActivity p0){
       this.this$0 = p0;
       super();
    }
    public void onStateChanged(LifecycleOwner p0,Lifecycle$Event p1){
       ckf.g(p0, "source");
       ckf.g(p1, "event");
       ComponentActivity.access$ensureViewModelStore(this.this$0);
       this.this$0.getLifecycle().removeObserver(this);
    }
}
