package zoloz.ap.com.toolkit.R$string;
import com.taobao.taobao.R$string;
import java.lang.Object;

public final class R$string	// class@001103 from classes11.dex
{
    public static int alert_timeout_error_title;
    public static int btn_exit;
    public static int btn_retry;
    public static int network_error_exit;
    public static int network_error_msg;
    public static int network_error_retry;
    public static int network_error_title;
    public static int system_error_got_it;
    public static int system_error_msg;
    public static int system_error_title;
    public static int title_back;

    static {
       R$string.alert_timeout_error_title = R$string.alert_timeout_error_title;
       R$string.btn_exit = R$string.btn_exit;
       R$string.btn_retry = R$string.btn_retry;
       R$string.network_error_exit = R$string.network_error_exit;
       R$string.network_error_msg = R$string.network_error_msg;
       R$string.network_error_retry = R$string.network_error_retry;
       R$string.network_error_title = R$string.network_error_title;
       R$string.system_error_got_it = R$string.system_error_got_it;
       R$string.system_error_msg = R$string.system_error_msg;
       R$string.system_error_title = R$string.system_error_title;
       R$string.title_back = R$string.title_back;
    }
    public void R$string(){
       super();
    }
}
