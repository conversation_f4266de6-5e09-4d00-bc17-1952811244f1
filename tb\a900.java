package tb.a900;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import com.taobao.android.weex_uikit.ui.MUSView;

public abstract class a900	// class@001773 from classes9.dex
{
    public boolean a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x33200653);
    }
    public void a900(){
       super();
    }
    public final void a(){
       int i = 1;
       IpChange $ipChange = a900.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("707fe601", objArray);
          return;
       }else {
          this.a = i;
          return;
       }
    }
    public final boolean b(){
       IpChange $ipChange = a900.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6ff5f452", objArray).booleanValue();
    }
    public boolean c(){
       int i = 0;
       IpChange $ipChange = a900.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("85a6628a", objArray).booleanValue();
       }
       return i;
    }
    public abstract void d(MUSView p0);
}
