package tb.aaa;
import tb.t2o;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.Boolean;
import android.app.Application;
import com.taobao.tao.Globals;
import android.content.Context;
import com.taobao.android.ab.api.ABGlobal;
import java.lang.StringBuilder;
import tb.vp9;

public class aaa	// class@001b16 from classes8.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x2ee0004a);
    }
    public static boolean a(){
       String str = "linkx";
       String str1 = "GlobalAbUtil === link_afc_id_opt_off === ";
       IpChange $ipChange = aaa.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("faea78e3", objArray).booleanValue();
       }else {
          boolean b = true;
          try{
             boolean b1 = ABGlobal.b(Globals.getApplication(), "taobao", "tbspeed", "link_afc_id_opt_off");
             vp9.b(str, str1+b1);
             return (b1 ^ 0x01);
          }catch(java.lang.Exception e0){
             vp9.b(e0, "GlobalAbUtil === link_afc_id_opt_off === error");
             return b;
          }
       }
    }
    public static boolean b(){
       IpChange $ipChange = aaa.$ipChange;
       int i = 0;
       if (!$ipChange instanceof IpChange) {
          return ABGlobal.b(Globals.getApplication(), "taobao", "tbspeed", "link_launch_opt");
       }
       Object[] objArray = new Object[i];
       return $ipChange.ipc$dispatch("fe54a64d", objArray).booleanValue();
    }
    public static boolean c(){
       IpChange $ipChange = aaa.$ipChange;
       int i = 0;
       if (!$ipChange instanceof IpChange) {
          return ABGlobal.b(Globals.getApplication(), "taobao", "tbspeed", "afcPreFetch2");
       }
       Object[] objArray = new Object[i];
       return $ipChange.ipc$dispatch("7904cc69", objArray).booleanValue();
    }
    public static boolean d(){
       IpChange $ipChange = aaa.$ipChange;
       int i = 0;
       if (!$ipChange instanceof IpChange) {
          return ABGlobal.b(Globals.getApplication(), "taobao", "tbspeed", "afcPreFetch");
       }
       Object[] objArray = new Object[i];
       return $ipChange.ipc$dispatch("67d79141", objArray).booleanValue();
    }
    public static boolean e(){
       String str = "linkx";
       String str1 = "GlobalAbUtil === link_ut_sync_opt_off === ";
       IpChange $ipChange = aaa.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("3722a46", objArray).booleanValue();
       }else {
          try{
             boolean b = ABGlobal.b(Globals.getApplication(), "taobao", "tbspeed", "link_ut_sync_opt_off");
             vp9.b(str, str1+b);
             return (b ^ 0x01);
          }catch(java.lang.Exception e0){
             vp9.b(e0, "GlobalAbUtil === link_ut_sync_opt_off === error");
             return v4;
          }
       }
    }
    public static boolean f(){
       String str = "linkx";
       String str1 = "GlobalAbUtil === link_h5url_rewriter === ";
       IpChange $ipChange = aaa.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("923c2473", objArray).booleanValue();
       }else {
          try{
             boolean b = ABGlobal.b(Globals.getApplication(), "taobao", "tbspeed", "link_h5url_rewriter");
             vp9.b(str, str1+b);
             return b;
          }catch(java.lang.Exception e0){
             vp9.b(e0, "GlobalAbUtil === link_h5url_rewriter === error");
             return v4;
          }
       }
    }
    public static boolean g(){
       IpChange $ipChange = aaa.$ipChange;
       int i = 0;
       if (!$ipChange instanceof IpChange) {
          return ABGlobal.b(Globals.getApplication(), "taobao", "tbspeed", "linkInOrder");
       }
       Object[] objArray = new Object[i];
       return $ipChange.ipc$dispatch("852d10bb", objArray).booleanValue();
    }
}
