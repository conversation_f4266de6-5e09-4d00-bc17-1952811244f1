package tb.a3h$e;
import java.lang.Runnable;
import tb.a3h;
import com.taobao.trtc.api.TrtcDefines$i;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.a3h$v0;

public class a3h$e implements Runnable	// class@001e6d from classes10.dex
{
    public final TrtcDefines$i a;
    public final float b;
    public final a3h c;
    public static IpChange $ipChange;

    public void a3h$e(a3h p0,TrtcDefines$i p1,float p2){
       this.c = p0;
       this.a = p1;
       this.b = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = a3h$e.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(a3h.y(this.c) != null){
          a3h.y(this.c).f(this.a, this.b);
       }
       return;
    }
}
