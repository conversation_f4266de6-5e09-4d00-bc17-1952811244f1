package kotlinx.coroutines.channels.BufferedChannel$b;
import tb.qww;
import tb.q23;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import kotlinx.coroutines.c;
import tb.v8p;

public final class BufferedChannel$b implements qww	// class@0004c0 from classes11.dex
{
    public final q23 a;
    public final c b;

    public void BufferedChannel$b(q23 p0){
       super();
       this.a = p0;
       ckf.e(p0, "null cannot be cast to non-null type kotlinx.coroutines.CancellableContinuationImpl<kotlin.Boolean>");
       this.b = p0;
    }
    public final q23 a(){
       return this.a;
    }
    public void c(v8p p0,int p1){
       this.b.c(p0, p1);
    }
}
