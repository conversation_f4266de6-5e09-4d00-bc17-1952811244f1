package mtopsdk.mtop.upload.util.FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory;
import java.util.concurrent.ThreadFactory;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicInteger;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;
import java.lang.Runnable;
import java.lang.Thread;
import java.lang.StringBuilder;
import mtopsdk.common.util.StringUtils;
import mtopsdk.mtop.upload.util.FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory$1;

public class FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory implements ThreadFactory	// class@000819 from classes11.dex
{
    private final AtomicInteger mCount;
    private int priority;
    private String type;
    public static IpChange $ipChange;

    static {
       t2o.a(0x2590001d);
    }
    public void FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory(int p0,String p1){
       super();
       this.priority = 10;
       this.type = "";
       this.mCount = new AtomicInteger();
       this.priority = p0;
       this.type = p1;
    }
    public static int access$000(FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory p0){
       IpChange $ipChange = FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.priority;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("482bb3cf", objArray).intValue();
    }
    public Thread newThread(Runnable p0){
       IpChange $ipChange = FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d8079a58", objArray);
       }else {
          StringBuilder str = new StringBuilder(32)+"FileUpload ";
          if (StringUtils.isNotBlank(this.type)) {
             str = str+this.type+" ";
          }
          return new FileUploadThreadPoolExecutorFactory$FileUploadThreadFactory$1(this, p0, str+"Thread:"+this.mCount.getAndIncrement());
       }
    }
}
