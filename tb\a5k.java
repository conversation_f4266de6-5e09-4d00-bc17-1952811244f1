package tb.a5k;
import com.taobao.tao.flexbox.layoutmanager.core.j;
import com.taobao.tao.flexbox.layoutmanager.core.TNodeView$l;
import com.taobao.tao.flexbox.layoutmanager.core.TNodeView$o;
import com.taobao.tao.flexbox.layoutmanager.core.Component;
import tb.t2o;
import java.util.HashMap;
import tb.a5k$a;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.tao.flexbox.layoutmanager.core.e;
import tb.jfw;
import com.taobao.tao.flexbox.layoutmanager.core.n$f;
import java.lang.Boolean;
import java.lang.Number;
import com.taobao.tao.flexbox.layoutmanager.core.b;
import com.taobao.tao.flexbox.layoutmanager.core.TNodeView;
import com.taobao.tao.flexbox.layoutmanager.core.n;
import tb.a5k$c;
import java.util.Map;
import tb.hk8;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Class;
import com.taobao.tao.flexbox.layoutmanager.core.n$e;
import java.util.List;
import java.lang.Integer;
import com.taobao.tao.flexbox.layoutmanager.view.tabbar.PreloadDelegate;
import android.content.Context;
import com.taobao.tao.flexbox.layoutmanager.core.o;
import tb.akt;
import tb.rbi;
import com.taobao.tao.flexbox.layoutmanager.core.n$g;
import tb.tfs;
import tb.a5k$b;
import android.app.Application;
import com.taobao.tao.flexbox.layoutmanager.view.tabbar.PreloadDelegate$b;

public class a5k extends Component implements j, TNodeView$l, TNodeView$o	// class@001763 from classes9.dex
{
    public boolean a;
    public PreloadDelegate b;
    public TNodeView c;
    public Map d;
    public final b e;
    public static IpChange $ipChange;

    static {
       t2o.a(0x20100122);
       t2o.a(0x20100252);
       t2o.a(0x201002b9);
       t2o.a(0x201002bd);
    }
    public void a5k(){
       super();
       this.a = false;
       this.d = new HashMap();
       this.e = new a5k$a(this);
    }
    public static View h(a5k p0){
       IpChange $ipChange = a5k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.view;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("ed6d2c68", objArray);
    }
    public static View i(a5k p0){
       IpChange $ipChange = a5k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.view;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("5bf43da9", objArray);
    }
    public static Object ipc$super(a5k p0,String p1,Object[] p2){
       int i = 3;
       int i1 = 2;
       int i2 = 0;
       switch (p1.hashCode()){
           case 0x8d2801c9:
             super.onLayoutComplete();
             return null;
           case 0xd23a2ffb:
             return super.getAttributeHandler(p2[i2]);
           case 0x3c8e65c2:
             super.onLayoutChanged(p2[i2].intValue(), p2[1].intValue(), p2[i1].intValue(), p2[i].intValue(), p2[4].booleanValue());
             return null;
           case 0x3ec06b7a:
             super.detach(p2[i2].booleanValue());
             return null;
           case 0x79d14f29:
             super.onInitAttrs(p2[i2], p2[1], p2[i1], p2[i]);
             return null;
           default:
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in com/taobao/tao/flexbox/layoutmanager/component/NodeComponent");
       }
    }
    public void detach(boolean p0){
       a5k tc;
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("3ec06b7a", objArray);
          return;
       }else if((tc = this.c) != null){
          tc.setViewLayoutCallback(null);
       }
       if (this.n() != null) {
          if (this.viewParams.A0 != null) {
             this.sendMessage(386, this.n(), "onwilldisappear", null, null, null);
          }else {
             this.sendMessage(130, this.n(), "onpagedisappear", null, null, null);
          }
       }
       super.detach(p0);
       return;
    }
    public jfw generateViewParams(){
       return this.m();
    }
    public b getAttributeHandler(String p0){
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d23a2ffb", objArray);
       }else if(TextUtils.equals(p0, "error-page")){
          return this.e;
       }else {
          return super.getAttributeHandler(p0);
       }
    }
    public n j(Class p0,n$e p1,boolean p2){
       n on;
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Boolean(p2)};
          return $ipChange.ipc$dispatch("c4304561", objArray);
       }else if((on = this.n()) != null){
          return on.z(p0, p1, p2);
       }else {
          return null;
       }
    }
    public void k(Object p0,int p1,List p2,boolean p3,boolean p4){
       n on;
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,new Boolean(p3),new Boolean(p4)};
          $ipChange.ipc$dispatch("9874890a", objArray);
          return;
       }else if((on = this.n()) != null){
          on.B(p0, p1, p2, p3, p4);
       }
       return;
    }
    public void l(n$e p0,List p1,int p2){
       n on;
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2)};
          $ipChange.ipc$dispatch("2ff9bf77", objArray);
          return;
       }else if((on = this.n()) != null){
          on.C(p0, p1, p2);
       }
       return;
    }
    public a5k$c m(){
       IpChange $ipChange = a5k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new a5k$c();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("82eaa3ac", objArray);
    }
    public n n(){
       a5k tc;
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("59438703", objArray);
       }else if((tc = this.c) != null){
          return tc.getRootNode();
       }else if((tc = this.b) != null){
          return tc.h();
       }else {
          return null;
       }
    }
    public TNodeView o(Context p0){
       rbi a;
       rbi b;
       Map map;
       a5k td;
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("8fce11fe", objArray);
       }else {
          Component tviewParams = this.viewParams;
          this.c = TNodeView.create(p0, this.node.P(), tviewParams.w0, tviewParams.x0, this);
          if (akt.v1()) {
             Component tmeasureResu = this.measureResult;
             if ((a = tmeasureResu.a) > null && (b = tmeasureResu.b) > null) {
                this.c.prelayout(a, b);
             }
          }
          if ((map = this.node.a0()) != null && (td = this.d) != null) {
             td.putAll(map);
          }
          this.c.setPendingArgs(this.d);
          this.c.setViewLayoutCallback(this);
          return this.c;
       }
    }
    public View onCreateView(Context p0){
       return this.o(p0);
    }
    public boolean onHandleMessage(n$g p0){
       n$g e;
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("1e782cf4", objArray).booleanValue();
       }else {
          n$g d = p0.d;
          d.hashCode();
          switch (d.hashCode()){
              case 0xb21389e3:
                if (d.equals("onstart")) {
                   i = 0;
                }else {
                }
                break;
              case 0xbc886fd3:
                if (d.equals("onpagemsg")) {
                }else {
                }
                break;
              case 0xc118edbe:
                if (d.equals("onpagescrollstatechanged")) {
                   i = 2;
                }else {
                }
                break;
              case 0xc3ae0e61:
                if (d.equals("onstop")) {
                   i = 3;
                }else {
                }
                break;
              case 0xdf13e092:
                if (d.equals("onwilldisappear")) {
                   i = 4;
                }else {
                }
                break;
              case 0x671d30ef:
                if (d.equals("onforcerefresh")) {
                   i = 5;
                }else {
                }
                break;
              case 0x7e855b06:
                if (d.equals("onwillappear")) {
                   i = 6;
                }else {
                }
                break;
              default:
                i = -1;
          }
          switch (i){
              case 0:
                if (this.n() != null) {
                   this.sendMessage(130, this.n(), d, null, p0.e, null);
                }
                break;
              case 1:
              case 5:
              case 2:
                if (this.n() != null) {
                   this.sendMessage(2, this.n(), d, null, p0.e, p0.g);
                }
                break;
              case 3:
                if (this.n() != null) {
                   this.sendMessage(130, this.n(), d, null, p0.e, null);
                }
                break;
              case 4:
                if (this.n() != null) {
                   if (this.viewParams.A0 != null) {
                      this.sendMessage(386, this.n(), d, null, p0.e, null);
                   }else {
                      this.sendMessage(130, this.n(), "onpagedisappear", null, p0.e, null);
                   }
                }
                break;
              case 6:
                if (this.n() != null) {
                   if (this.viewParams.A0 != null) {
                      this.sendMessage(130, this.n(), d, null, p0.e, null);
                   }else {
                      this.sendMessage(130, this.n(), "onpageappear", null, p0.e, null);
                   }
                }else if((e = p0.e) != null && !e.isEmpty()){
                   this.d = p0.e;
                }
                break;
              default:
          }
          return 0;
       }
    }
    public boolean onHandleTNodeMessage(n p0,n p1,String p2,String p3,Map p4,hk8 p5){
       int i = 0;
       IpChange $ipChange = a5k.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return i;
       }
       Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4,p5};
       return $ipChange.ipc$dispatch("abab8f80", objArray).booleanValue();
    }
    public void onInitAttrs(e p0,View p1,jfw p2,n$f p3){
       this.p(p0, p1, p2, p3);
    }
    public void onLayoutChanged(int p0,int p1,int p2,int p3,boolean p4){
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),new Integer(p1),new Integer(p2),new Integer(p3),new Boolean(p4)};
          $ipChange.ipc$dispatch("3c8e65c2", objArray);
          return;
       }else {
          super.onLayoutChanged(p0, p1, p2, p3, p4);
          tfs.f("Node onLayoutChanged, node:"+this.node+" oldW: "+p0+" oldH:"+p1+" newW:"+p2+" newH:"+p3);
          return;
       }
    }
    public void onLayoutComplete(){
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("8d2801c9", objArray);
          return;
       }else {
          super.onLayoutComplete();
          this.r();
          return;
       }
    }
    public void onLayoutCompleted(n p0){
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6e800489", objArray);
          return;
       }else {
          p0.v1(this);
          p0.w1(7, new a5k$b(this));
          return;
       }
    }
    public void onLayoutError(){
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("834776a6", objArray);
       }
       return;
    }
    public void p(e p0,TNodeView p1,a5k$c p2,n$f p3){
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("9933078c", objArray);
          return;
       }else {
          super.onInitAttrs(p0, p1, p2, p3);
          this.e.c(p0, p1, p2, p3);
          return;
       }
    }
    public void q(){
       int i = 1;
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("e9484ef5", objArray);
          return;
       }else if(this.n() != null && this.a == null){
          this.a = i;
          if (this.viewParams.A0 != null) {
             this.sendMessage(130, this.n(), "onwillappear", null, this.d, null);
          }else {
             this.sendMessage(130, this.n(), "onpageappear", null, this.d, null);
          }
       }
       return;
    }
    public final void r(){
       IpChange $ipChange = a5k.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("ebc09c56", objArray);
          return;
       }else if(this.node.N() instanceof Application && (akt.G1() && this.b == null)){
          Component tviewParams = this.viewParams;
          PreloadDelegate $ipChange1 = new PreloadDelegate(this.node.N(), this.node.P(), tviewParams.w0, null, tviewParams.x0, false, 1, null);
          this.b = $ipChange;
          tviewParams = this.measureResult;
          $ipChange.m(tviewParams.a, tviewParams.b);
       }
       return;
    }
}
