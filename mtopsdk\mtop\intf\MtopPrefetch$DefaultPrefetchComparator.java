package mtopsdk.mtop.intf.MtopPrefetch$DefaultPrefetchComparator;
import mtopsdk.mtop.intf.MtopPrefetch$IPrefetchComparator;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import mtopsdk.common.util.StringUtils;
import org.json.JSONObject;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;
import mtopsdk.mtop.domain.MtopRequest;
import mtopsdk.mtop.intf.MtopBuilder;
import mtopsdk.mtop.intf.MtopPrefetch$CompareResult;
import tb.w4j;
import mtopsdk.mtop.intf.MtopPrefetch;

public class MtopPrefetch$DefaultPrefetchComparator implements MtopPrefetch$IPrefetchComparator	// class@0007de from classes11.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x253000fe);
       t2o.a(0x25300102);
    }
    public void MtopPrefetch$DefaultPrefetchComparator(){
       super();
    }
    private boolean compareJsonStr(String p0,String p1,List p2){
       int i = 1;
       IpChange $ipChange = MtopPrefetch$DefaultPrefetchComparator.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("233c2c94", objArray).booleanValue();
       }else if(!StringUtils.isBlank(p0) && !StringUtils.isBlank(p1)){
          if (p0.equals(p1)) {
             return i;
          }
          JSONObject jSONObject = new JSONObject(p0);
          JSONObject jSONObject1 = new JSONObject(p1);
          HashMap hashMap = new HashMap();
          HashMap hashMap1 = new HashMap();
          Iterator iterator = jSONObject.keys();
          while (iterator.hasNext()) {
             String str = iterator.next();
             if (StringUtils.isNotBlank(str) && (p2 == null && p2.contains(str))) {
                hashMap.put(str, jSONObject.getString(str));
             }
          }
          Iterator iterator1 = jSONObject1.keys();
          while (iterator1.hasNext()) {
             String str1 = iterator1.next();
             if (StringUtils.isNotBlank(str1) && (p2 == null && p2.contains(str1))) {
                hashMap1.put(str1, jSONObject1.getString(str1));
             }
          }
          if (hashMap.size() != hashMap1.size()) {
             return 0;
          }
          Iterator iterator2 = hashMap.keySet().iterator();
          while (iterator2.hasNext()) {
             String str2 = iterator2.next();
             if (StringUtils.isNotBlank(str2) && !this.compareJsonStr(hashMap.get(str2), hashMap1.get(str2), p2)) {
                i = false;
                break ;
             }
          }
          return i;
       }else {
          return 0;
       }
    }
    private boolean isSameRequest(MtopRequest p0,MtopRequest p1,List p2){
       int i = 0;
       IpChange $ipChange = MtopPrefetch$DefaultPrefetchComparator.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("3d52df9b", objArray).booleanValue();
       }else if(!StringUtils.isBlank(p0.getKey()) && p0.getKey().equals(p1.getKey())){
          if (p0.isNeedEcode() != p1.isNeedEcode()) {
             return i;
          }
          if (p0.isNeedSession() != p1.isNeedSession()) {
             return i;
          }
          return this.compareJsonStr(p0.getData(), p1.getData(), p2);
       }else {
          return i;
       }
    }
    public MtopPrefetch$CompareResult compare(MtopBuilder p0,MtopBuilder p1){
       IpChange $ipChange = MtopPrefetch$DefaultPrefetchComparator.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("cca63c2e", objArray);
       }else {
          MtopPrefetch$CompareResult uCompareResu = new MtopPrefetch$CompareResult();
          uCompareResu.setSame(this.isSameRequest(p0.getMtopContext().b, p1.getMtopContext().b, p1.getMtopPrefetch().whiteListParams));
          return uCompareResu;
       }
    }
}
