package tb.a1x;
import tb.t2o;
import java.lang.Object;
import tb.bbs;
import java.lang.String;
import tb.uq;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import tb.k8s;
import com.alibaba.fastjson.JSONObject;
import kotlin.Pair;
import tb.jpu;
import java.util.Map;
import tb.v3i;
import tb.xhv;
import tb.a1x$c;
import tb.a1x$b;
import java.lang.Boolean;

public final class a1x	// class@001e5d from classes10.dex
{
    public static IpChange $ipChange;
    public static final a1x INSTANCE;

    static {
       t2o.a(0x34c0001d);
       a1x.INSTANCE = new a1x();
    }
    public void a1x(){
       super();
    }
    public final void a(bbs p0,String p1,String p2,uq p3){
       object oobject = p0;
       object oobject1 = p1;
       object oobject2 = p2;
       object oobject3 = p3;
       IpChange $ipChange = a1x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,oobject,oobject1,oobject2,oobject3};
          $ipChange.ipc$dispatch("2dff0fcc", objArray);
          return;
       }else {
          ckf.g(p0, "instance");
          ckf.g(p1, "traceId");
          ckf.g(oobject2, "socketId");
          ckf.g(oobject3, "callback");
          JSONObject jSONObject = new JSONObject();
          jSONObject.put("__mega_context__", v3i.f(jpu.a("instanceID", oobject2)));
          String str = p1;
          k8s.j().d(p0, "themis", str, "websocket", "close", jSONObject, p3);
          jSONObject = new JSONObject();
          jSONObject.put("ability", "websocket");
          jSONObject.put("__mega_context__", v3i.f(jpu.a("instanceID", oobject2)));
          k8s.j().d(p0, "themis", str, "ability", "destroy", jSONObject, a1x$c.INSTANCE);
          return;
       }
    }
    public final void b(bbs p0,String p1,String p2,String p3,Map p4,Map p5,uq p6){
       object oobject = p0;
       object oobject1 = p1;
       object oobject2 = p2;
       object oobject3 = p3;
       object oobject4 = p4;
       object oobject5 = p5;
       object oobject6 = p6;
       IpChange $ipChange = a1x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,oobject,oobject1,oobject2,oobject3,oobject4,oobject5,oobject6};
          $ipChange.ipc$dispatch("331e4730", objArray);
          return;
       }else {
          ckf.g(oobject, "instance");
          ckf.g(oobject1, "url");
          ckf.g(oobject2, "traceId");
          ckf.g(oobject3, "socketId");
          ckf.g(oobject6, "callback");
          JSONObject jSONObject = new JSONObject();
          jSONObject.put("ability", "websocket");
          jSONObject.put("__mega_context__", v3i.f(jpu.a("instanceID", oobject3)));
          k8s.j().d(p0, "themis", p2, "ability", "create", jSONObject, a1x$b.INSTANCE);
          k8s ok8s = k8s.j();
          jSONObject = new JSONObject();
          jSONObject.put("__mega_context__", v3i.f(jpu.a("instanceID", oobject3)));
          jSONObject.put("url", oobject1);
          if (oobject4 != null) {
             jSONObject.put("headers", oobject4);
          }
          if (oobject5 != null) {
             jSONObject.put("data", oobject5);
          }
          ok8s.d(p0, "themis", p2, "websocket", "connect", jSONObject, p6);
          return;
       }
    }
    public final void c(bbs p0,String p1,String p2,boolean p3,String p4,uq p5){
       object oobject = p0;
       object oobject1 = p1;
       object oobject2 = p2;
       object oobject3 = p4;
       object oobject4 = p5;
       IpChange $ipChange = a1x.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,oobject,oobject1,oobject2,new Boolean(p3),oobject3,oobject4};
          $ipChange.ipc$dispatch("76f3ebfc", objArray);
          return;
       }else {
          ckf.g(p0, "instance");
          ckf.g(p1, "traceId");
          ckf.g(p2, "data");
          ckf.g(p4, "socketId");
          ckf.g(oobject4, "callback");
          JSONObject jSONObject = new JSONObject();
          jSONObject.put("__mega_context__", v3i.f(jpu.a("instanceID", p4)));
          jSONObject.put("isBuffer", Boolean.valueOf(p3));
          jSONObject.put("data", p2);
          k8s.j().d(p0, "themis", p1, "websocket", "send", jSONObject, p5);
          return;
       }
    }
}
