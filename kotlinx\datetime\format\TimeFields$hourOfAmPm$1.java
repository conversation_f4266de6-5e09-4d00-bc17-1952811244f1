package kotlinx.datetime.format.TimeFields$hourOfAmPm$1;
import kotlin.jvm.internal.MutablePropertyReference1Impl;
import tb.fw40;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import java.lang.Integer;

public final class TimeFields$hourOfAmPm$1 extends MutablePropertyReference1Impl	// class@0006f0 from classes11.dex
{
    public static final TimeFields$hourOfAmPm$1 INSTANCE;

    static {
       TimeFields$hourOfAmPm$1.INSTANCE = new TimeFields$hourOfAmPm$1();
    }
    public void TimeFields$hourOfAmPm$1(){
       super(fw40.class, "hourOfAmPm", "getHourOfAmPm\(\)Ljava/lang/Integer;", 0);
    }
    public Object get(Object p0){
       return p0.q();
    }
    public void set(Object p0,Object p1){
       p0.u(p1);
    }
}
