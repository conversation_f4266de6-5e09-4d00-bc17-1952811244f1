package tb.a21;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import tb.b21;
import tb.jhb;
import tb.c21;

public class a21	// class@00183d from classes7.dex
{
    public static IpChange $ipChange;

    public static void a(){
       IpChange $ipChange = a21.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("7900fd98", objArray);
          return;
       }else {
          c21.o(b21.s());
          return;
       }
    }
}
