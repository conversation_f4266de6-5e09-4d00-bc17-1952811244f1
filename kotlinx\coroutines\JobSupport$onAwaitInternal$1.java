package kotlinx.coroutines.JobSupport$onAwaitInternal$1;
import tb.w1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import kotlinx.coroutines.JobSupport;
import java.lang.Class;
import java.lang.String;
import java.lang.Object;
import tb.k9p;
import tb.xhv;

public final class JobSupport$onAwaitInternal$1 extends FunctionReferenceImpl implements w1a	// class@0004a9 from classes11.dex
{
    public static final JobSupport$onAwaitInternal$1 INSTANCE;

    static {
       JobSupport$onAwaitInternal$1.INSTANCE = new JobSupport$onAwaitInternal$1();
    }
    public void JobSupport$onAwaitInternal$1(){
       super(3, JobSupport.class, "onAwaitInternalRegFunc", "onAwaitInternalRegFunc\(Lkotlinx/coroutines/selects/SelectInstance;Ljava/lang/Object;\)V", 0);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       this.invoke(p0, p1, p2);
       return xhv.INSTANCE;
    }
    public final void invoke(JobSupport p0,k9p p1,Object p2){
       JobSupport.z(p0, p1, p2);
    }
}
