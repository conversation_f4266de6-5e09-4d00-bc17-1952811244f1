package mtopsdk.mtop.intf.MtopPrefetch$IPrefetchCallback$PrefetchCallbackType;

public interface abstract MtopPrefetch$IPrefetchCallback$PrefetchCallbackType	// class@0007e0 from classes11.dex
{
    public static final String TYPE_CLEAR = "TYPE_CLEAR";
    public static final String TYPE_EXPIRE = "TYPE_EXPIRE";
    public static final String TYPE_FULL = "TYPE_FULL";
    public static final String TYPE_HIT = "TYPE_HIT";
    public static final String TYPE_MERGE = "TYPE_MERGE";
    public static final String TYPE_MISS = "TYPE_MISS";

}
